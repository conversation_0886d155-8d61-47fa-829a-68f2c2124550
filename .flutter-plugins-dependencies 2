{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.1/", "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.5/", "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_messaging-6.0.16/", "dependencies": []}, {"name": "flutter_facebook_login", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_facebook_login-3.0.0/", "dependencies": []}, {"name": "google_sign_in", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/google_sign_in-4.5.9/", "dependencies": []}, {"name": "image_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.7+21/", "dependencies": []}, {"name": "permission_handler", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/permission_handler-5.0.1+1/", "dependencies": []}, {"name": "shared_preferences", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences-0.5.12+4/", "dependencies": []}, {"name": "url_launcher", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher-5.7.10/", "dependencies": []}, {"name": "webview_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/webview_flutter-0.3.24/", "dependencies": []}], "android": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.1/", "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.5/", "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_messaging-6.0.16/", "dependencies": []}, {"name": "flutter_facebook_login", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_facebook_login-3.0.0/", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_plugin_android_lifecycle-1.0.11/", "dependencies": []}, {"name": "google_sign_in", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/google_sign_in-4.5.9/", "dependencies": []}, {"name": "image_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.7+21/", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "permission_handler", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/permission_handler-5.0.1+1/", "dependencies": []}, {"name": "shared_preferences", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences-0.5.12+4/", "dependencies": []}, {"name": "url_launcher", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher-5.7.10/", "dependencies": []}, {"name": "webview_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/webview_flutter-0.3.24/", "dependencies": []}], "macos": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.1/", "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.5/", "dependencies": []}, {"name": "shared_preferences_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_macos-0.0.1+11/", "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_macos-0.0.1+9/", "dependencies": []}], "linux": [{"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider_linux-0.0.1+2/", "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_linux-0.0.2+4/", "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_linux-0.0.1+4/", "dependencies": []}], "windows": [{"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider_windows-0.0.4+3/", "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_windows-0.0.2+2/", "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_windows-0.0.1+3/", "dependencies": []}], "web": [{"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth_web-0.1.3+1/", "dependencies": []}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core_web-0.1.1+2/", "dependencies": []}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/google_sign_in_web-0.9.2/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_web-0.1.2+7/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_web-0.1.5+1/", "dependencies": []}]}, "dependencyGraph": [{"name": "firebase_auth", "dependencies": ["firebase_core", "firebase_auth_web"]}, {"name": "firebase_auth_web", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": []}, {"name": "flutter_facebook_login", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_web"]}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_linux", "shared_preferences_macos", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_macos", "dependencies": []}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "url_launcher", "dependencies": ["url_launcher_web", "url_launcher_linux", "url_launcher_macos", "url_launcher_windows"]}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "webview_flutter", "dependencies": []}], "date_created": "2021-01-22 21:23:38.578429", "version": "1.22.5"}