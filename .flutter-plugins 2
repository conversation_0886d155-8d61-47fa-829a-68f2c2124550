# This is a generated file; do not edit or check into version control.
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth-0.16.1/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_auth_web-0.1.3+1/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core-0.4.5/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_core_web-0.1.1+2/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/firebase_messaging-6.0.16/
flutter_facebook_login=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_facebook_login-3.0.0/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/flutter_plugin_android_lifecycle-1.0.11/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/google_sign_in-4.5.9/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/google_sign_in_web-0.9.2/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/image_picker-0.6.7+21/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider_linux-0.0.1+2/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/path_provider_windows-0.0.4+3/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/permission_handler-5.0.1+1/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences-0.5.12+4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_linux-0.0.2+4/
shared_preferences_macos=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_macos-0.0.1+11/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_web-0.1.2+7/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/shared_preferences_windows-0.0.2+2/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher-5.7.10/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_linux-0.0.1+4/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_macos-0.0.1+9/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_web-0.1.5+1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/url_launcher_windows-0.0.1+3/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dartlang.org/webview_flutter-0.3.24/
