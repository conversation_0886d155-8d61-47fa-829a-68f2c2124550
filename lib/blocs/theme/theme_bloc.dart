import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../../utils/config.dart';
import '../../utils/resource/colors.dart';

part 'theme_event.dart';
part 'theme_state.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  static const String _themeKey = 'theme_mode';

  ThemeBloc() : super(LightThemeState()) {
    on<LoadSavedThemeEvent>(_onLoadSavedTheme);
    on<ToggleThemeEvent>(_onToggleTheme);
    on<SetLightThemeEvent>(_onSetLightTheme);
    on<SetDarkThemeEvent>(_onSetDarkTheme);
  }

  Future<void> _onLoadSavedTheme(
      LoadSavedThemeEvent event, Emitter<ThemeState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isDarkMode = prefs.getBool(_themeKey) ?? false;

      if (isDarkMode) {
        emit(DarkThemeState());
      } else {
        emit(LightThemeState());
      }
    } catch (e) {
      // If there's an error loading preferences, default to light theme
      emit(LightThemeState());
    }
  }

  Future<void> _onToggleTheme(
      ToggleThemeEvent event, Emitter<ThemeState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (state is LightThemeState) {
        await prefs.setBool(_themeKey, true);
        emit(DarkThemeState());
      } else {
        await prefs.setBool(_themeKey, false);
        emit(LightThemeState());
      }
    } catch (e) {
      // If there's an error saving preferences, still emit the new state
      if (state is LightThemeState) {
        emit(DarkThemeState());
      } else {
        emit(LightThemeState());
      }
    }
  }

  Future<void> _onSetLightTheme(
      SetLightThemeEvent event, Emitter<ThemeState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_themeKey, false);
      emit(LightThemeState());
    } catch (e) {
      emit(LightThemeState());
    }
  }

  Future<void> _onSetDarkTheme(
      SetDarkThemeEvent event, Emitter<ThemeState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_themeKey, true);
      emit(DarkThemeState());
    } catch (e) {
      emit(DarkThemeState());
    }
  }
}
