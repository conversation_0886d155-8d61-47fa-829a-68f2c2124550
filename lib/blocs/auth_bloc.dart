import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:injector/injector.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/email_request.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/request/auth_request/signup_request.dart';
import 'package:masterg/data/models/request/auth_request/swayam_login_request.dart';
import 'package:masterg/data/models/request/auth_request/update_user_request.dart';
import 'package:masterg/data/models/response/auth_response/change_password_response.dart';
import 'package:masterg/data/models/response/auth_response/login_response.dart';
import 'package:masterg/data/models/response/auth_response/sign_up_response.dart';
import 'package:masterg/data/models/response/auth_response/swayam_login_response.dart';
import 'package:masterg/data/models/response/auth_response/verify_otp_resp.dart';
import 'package:masterg/data/models/response/home_response/app_version_response.dart';
import 'package:masterg/data/models/response/home_response/category_response.dart';
import 'package:masterg/data/models/response/home_response/city_state_response.dart';
import 'package:masterg/data/models/response/home_response/user_info_response.dart';
import 'package:masterg/data/repositories/auth_repository.dart';
import 'package:masterg/data/repositories/home_repository.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/data/models/response/auth_response/only_verify_otp_response.dart';

abstract class AuthEvent {
  AuthEvent([List event = const []]) : super();
}

class LoginUser extends AuthEvent {
  final LoginRequest request;

  LoginUser({required this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class SignUpEvent extends AuthEvent {
  final SignUpRequest request;

  SignUpEvent({required this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class AppVersionEvent extends AuthEvent {
  String? deviceType;

  AppVersionEvent({this.deviceType}) : super([deviceType]);

  List<Object> get props => throw UnimplementedError();
}

class VerifyOtpEvent extends AuthEvent {
  final EmailRequest request;

  VerifyOtpEvent({required this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class OnlyVerifyOtpEvent extends AuthEvent {
  final EmailRequest request;

  OnlyVerifyOtpEvent({required this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class CategoryEvent extends AuthEvent {
  int? contentType;

  CategoryEvent({this.contentType}) : super([contentType]);

  List<Object> get props => throw UnimplementedError();
}

class UserProfileEvent extends AuthEvent {
  UserProfileEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

abstract class AuthState {
  AuthState([List states = const []]) : super();

  List<Object> get props => [];
}

class CategoryState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CategoryResp? response;
  String? error;

  CategoryState(this.state, {this.response, this.error});
}

class AppVersionState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  AppVersionResp? response;
  String? error;

  AppVersionState(this.state, {this.response, this.error});
}

class LoginState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  LoginResponse? response;
  String? error;

  LoginState(this.state, {this.response, this.error});
}

class SignUpState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SignUpResponse? response;
  String? error;

  SignUpState(this.state, {this.response, this.error});
}

class VerifyOtpState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  VerifyOtpResp? response;
  String? error;
  int? status;

  VerifyOtpState(this.state, {this.response, this.error, this.status});
}

class OnlyVerifyOtpState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  OnlyVerifyOtpResponse? response;
  String? error;
  int? status;

  OnlyVerifyOtpState(this.state, {this.response, this.error, this.status});
}

class UpdateUserState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SignUpResponse? response;
  String? error;

  UpdateUserState(this.state, {this.response, this.error});
}

class StateState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CityStateResp? response;
  String? error;

  StateState(this.state, {this.response, this.error});
}

class StateEvent extends AuthEvent {
  StateEvent() : super([]);

  List<Object> get props => throw UnimplementedError();
}

class UpdateUserEvent extends AuthEvent {
  final UpdateUserRequest? request;

  UpdateUserEvent({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class CityState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  CityStateResp? response;
  String? error;

  CityState(this.state, {this.response, this.error});
}

class CityEvent extends AuthEvent {
  int stateId;

  CityEvent(this.stateId) : super([stateId]);

  List<Object> get props => throw UnimplementedError();
}

class SwayamLoginState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  SwayamLoginResponse? response;
  List<dynamic>? error;

  SwayamLoginState(this.state, {this.response, this.error});
}

class SignUp extends AuthEvent {
  final SignUpRequest? request;

  SignUp({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class PvmSwayamLogin extends AuthEvent {
  final SwayamLoginRequest? request;

  PvmSwayamLogin({this.request}) : super([request]);

  List<Object> get props => throw UnimplementedError();
}

class UserProfileState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  UserInfoResponse? response;
  String? error;

  UserProfileState(this.state, {this.response, this.error});
}

class ChangePasswordEvent extends AuthEvent {
  Map<String, String> data;

  ChangePasswordEvent({required this.data}) : super([data]);

  List<Object> get props => throw UnimplementedError();
}

class ChangePasswordState extends AuthState {
  ApiStatus state;

  ApiStatus get apiState => state;
  ChangePasswordResponse? response;
  String? error;

  ChangePasswordState(this.state, {this.response, this.error});
}

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final homeRepository = Injector.appInstance.get<HomeRepository>();
  final authRepository = Injector.appInstance.get<AuthRepository>();

  AuthBloc(super.initialState) {
    on<LoginUser>((event, emit) async {
      try {
        emit(LoginState(ApiStatus.LOADING));
        final response = await authRepository.loginCall(request: event.request);
        if (response.status == 1) {
          emit(LoginState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA sdfsd ::: $response");
          emit(LoginState(ApiStatus.ERROR, response: response));
        }
      } catch (e, stacktrace) {
        Log.v("Expection DATA : $stacktrace");
        emit(LoginState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());

    on<ChangePasswordEvent>((event, emit) async {
      try {
        emit(ChangePasswordState(ApiStatus.LOADING));
        final response = await authRepository.changePassword(event.data);
        if (response.status == 1) {
          emit(ChangePasswordState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA  buec ::: ${response.toJson()}");
          emit(ChangePasswordState(ApiStatus.ERROR, response: response));
        }
      } catch (e, stacktrace) {
        Log.v("Expection DATA : $stacktrace");
        emit(ChangePasswordState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<UserProfileEvent>((event, emit) async {
      try {
        emit(UserProfileState(ApiStatus.LOADING));
        final response = await homeRepository.getSwayamUserProfile();
        if (response.data != null) {
          emit(UserProfileState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(UserProfileState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(UserProfileState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<PvmSwayamLogin>((event, emit) async {
      try {
        emit(SwayamLoginState(ApiStatus.LOADING));
        final response =
            await authRepository.swayamLoginCall(request: event.request);
        emit(SwayamLoginState(ApiStatus.SUCCESS, response: response));
      } catch (e, stacktrace) {
        Log.v("ERROR DATA : $stacktrace");
        emit(LoginState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<StateEvent>((event, emit) async {
      try {
        emit(StateState(ApiStatus.LOADING));
        final response = await authRepository.getStateList();
        if (response?.data != null) {
          emit(StateState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(StateState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(StateState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<CityEvent>((event, emit) async {
      try {
        emit(CityState(ApiStatus.LOADING));
        final response = await authRepository.getCityList(event.stateId);
        if (response.data != null) {
          emit(CityState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(CityState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CityState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<UpdateUserEvent>((event, emit) async {
      try {
        emit(UpdateUserState(ApiStatus.LOADING));
        final response =
            await authRepository.updateUser(request: event.request);
        if (response?.status == 1) {
          Log.v("sign up data");
          emit(UpdateUserState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(UpdateUserState(ApiStatus.ERROR, error: 'error'));
        }
      } catch (e) {
        Log.v("ERROR DATA : ");
        emit(UpdateUserState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<SignUpEvent>((event, emit) async {
      try {
        emit(SignUpState(ApiStatus.LOADING));
        final response =
            await authRepository.signUpCall(request: event.request);
        if (response.status == 1) {
          emit(SignUpState(ApiStatus.SUCCESS, response: response));
        } else {
          emit(SignUpState(
            ApiStatus.ERROR,
            error: response.error?.first,
          ));
        }
      } catch (e) {
        Log.v("Expection DATA : ");
        emit(SignUpState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<VerifyOtpEvent>((event, emit) async {
      try {
        emit(VerifyOtpState(ApiStatus.LOADING));
        final response = await authRepository.verifyOtp(request: event.request);
        if (response.status == 1) {
          emit(VerifyOtpState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRORe4 DATA ::: ${response.status}");
          emit(VerifyOtpState(ApiStatus.ERROR,
              error: response.error != null && response.error!.isNotEmpty
                  ? response.error!.first
                  : Strings.somethingWentWrong,
              status: response.status));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(
            VerifyOtpState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<OnlyVerifyOtpEvent>((event, emit) async {
      try {
        emit(OnlyVerifyOtpState(ApiStatus.LOADING));
        final response =
            await authRepository.onlyVerifyOtp(request: event.request);
        if (response.status == 1) {
          emit(OnlyVerifyOtpState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERRORe4 DATA ::: ${response.status}");
          emit(OnlyVerifyOtpState(ApiStatus.ERROR,
              error: response.error != null && response.error!.isNotEmpty
                  ? response.error!.first
                  : Strings.somethingWentWrong,
              status: response.status));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(OnlyVerifyOtpState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<CategoryEvent>((event, emit) async {
      try {
        emit(CategoryState(ApiStatus.LOADING));
        final response = await homeRepository.getCategory();
        if (response.data != null) {
          emit(CategoryState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(CategoryState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e) {
        Log.v("ERROR DATA : $e");
        emit(CategoryState(ApiStatus.ERROR, error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
    on<AppVersionEvent>((event, emit) async {
      try {
        emit(AppVersionState(ApiStatus.LOADING));
        final response =
            await authRepository.getAppVeriosn(deviceType: event.deviceType);

        log('version api resp: not 5 ${response.toJson()}');
        if (response.data != null) {
          emit(AppVersionState(ApiStatus.SUCCESS, response: response));
        } else {
          Log.v("ERROR DATA ::: $response");
          emit(AppVersionState(ApiStatus.ERROR,
              error: Strings.somethingWentWrong));
        }
      } catch (e, stacktrace) {
        Log.v("ERROR DATA : $stacktrace");
        emit(AppVersionState(ApiStatus.ERROR,
            error: Strings.somethingWentWrong));
      }
    }, transformer: concurrent());
  }
}
