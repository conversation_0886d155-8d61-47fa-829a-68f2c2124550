part of 'dashboard_content_cubit.dart';

sealed class DashboardContentState extends Equatable {
  const DashboardContentState();

  @override
  List<Object> get props => [];
}

final class DashboardContentInitial extends DashboardContentState {}

final class DashboardContentLoading extends Dashboard<PERSON>ontentState {}

final class DashboardContentLoaded extends DashboardContentState {
  final DashboardContentResponse response;

  const DashboardContentLoaded(this.response);

  @override
  List<Object> get props => [response];
}

final class DashboardContentError extends DashboardContentState {
  final String message;

  const DashboardContentError(this.message);

  @override
  List<Object> get props => [message];
}
