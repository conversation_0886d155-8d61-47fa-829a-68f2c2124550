import 'dart:developer';

import 'package:android_play_install_referrer/android_play_install_referrer.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/pages/gcarvaan/post/gcarvaan_post_id_page.dart';
import 'package:masterg/pages/preboarding_pages/participation_register.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/singularis/competition/competition_detail.dart';
import 'package:masterg/pages/singularis/wow_studio.dart';
import 'package:masterg/utils/utility.dart';

import '../main.dart';

class AppLinkRoute {
  static Null handlePlayStoreUtm(ReferrerDetails detail) {
    if (detail.installReferrer.toString().contains('utm_source=p_id')) {
      try {
        List<String> list = detail.installReferrer.toString().split('&');
        String utmSourceName = list[0].split('=')[1]; // p_id=7893
        int utmSourceValue =
            int.tryParse(list[0].split('=')[2]) ?? -1; // p_id=7893

        if (utmSourceValue != -1 && utmSourceName == 'p_id') {
          if (UserSession.userToken == null || UserSession.userToken == '') {
            navigatorKey.currentState?.push(MaterialPageRoute(
                builder: (context) => ParticiaptionRegister(
                      programId: int.parse('$utmSourceValue'),
                    )));
          } else {
            navigatorKey.currentState?.push(MaterialPageRoute(
                builder: (context) => CompetitionDetail(
                      competitionId: int.tryParse('$utmSourceValue'),
                    )));
          }
        }
      } catch (e) {
        return null;
      }
    }
  }

  static Null handleAppStoreUtm(String detail) {
    if (detail.contains('utm_source=p_id')) {
      try {
        List<String> list = detail.split('?');
        String utmSourceName = list[0].split('=')[1]; // p_id=7893
        int utmSourceValue =
            int.tryParse(list[0].split('=')[2]) ?? -1; // p_id=7893

        if (utmSourceValue != -1 && utmSourceName == 'p_id') {
          if (UserSession.userToken == null || UserSession.userToken == '') {
            navigatorKey.currentState?.push(MaterialPageRoute(
                builder: (context) => ParticiaptionRegister(
                      programId: int.parse('$utmSourceValue'),
                    )));
          } else {
            navigatorKey.currentState?.push(MaterialPageRoute(
                builder: (context) => CompetitionDetail(
                      competitionId: int.tryParse('$utmSourceValue'),
                    )));
          }
        }
      } catch (e) {
        return null;
      }
    }
  }

  static Null handleRoute({required String route}) {
    if (UserSession.userToken == null || UserSession.userToken == '') {
      return null;
    }

    List<String> params = Utility.decodePath(route.toString());

    if (route.toString().contains('eventParticipation')) {
      try {
        var uri = Uri.parse(route.toString());
        String? id = uri.queryParameters["p_id"];
        if (id != null) {
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) => CompetitionDetail(
                    competitionId: int.parse(id),
                  )));
        }
      } on FormatException {
        // can not parse url
      }
    }

    try {
      switch (params[0]) {
        case 'g-carvaan':
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) => GCarvaanPostIdPage(
                    postId: int.parse(params[1]),
                  )));

          break;
        case 'reel':
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) =>
                  ReelScreen(appendReelId: int.parse(params[1]))));
          break;

        case 'wow-studio':
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) => WowStudio(postId: int.parse(params[1]))));

          break;

        case 'competition-detail':
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) => CompetitionDetail(
                    competitionId: int.parse(params[1]),
                  )));

          break;

        case 'eventParticipation':
          navigatorKey.currentState?.push(MaterialPageRoute(
              builder: (context) => CompetitionDetail(
                    competitionId: int.parse(params[1]),
                  )));

          break;
      }
    } catch (e, stacktrace) {
      log("app link is $e and $stacktrace");
    }
  }
}


//Event:- https://mec.edu.om/en/new-landing-page-new-created/?eventParticipation=1&p_id=8076