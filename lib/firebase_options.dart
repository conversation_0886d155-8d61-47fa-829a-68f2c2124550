// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBqiUsN7rhf8jFmkRbe8W4UsBV5FvcBrbg',
    appId: '1:618200044908:web:297164103439ad80f70f49',
    messagingSenderId: '618200044908',
    projectId: 'singularis-wow',
    authDomain: 'singularis-wow.firebaseapp.com',
    storageBucket: 'singularis-wow.appspot.com',
    measurementId: 'G-MPT7M1R17T',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBKbZu8G2pjkEc7Nfx2aRbi1pF_Fz_DBbE',
    appId: '1:618200044908:android:09fa41b5ba12a141f70f49',
    messagingSenderId: '618200044908',
    projectId: 'singularis-wow',
    storageBucket: 'singularis-wow.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDUuYXmC5owvHYOzweW2VsBAs9Lhfll4js',
    appId: '1:618200044908:ios:34606bc7e88056f5f70f49',
    messagingSenderId: '618200044908',
    projectId: 'singularis-wow',
    storageBucket: 'singularis-wow.appspot.com',
    androidClientId: '618200044908-9udrdu2o6vtst9elisc6kp4o1nu85jhd.apps.googleusercontent.com',
    iosClientId: '618200044908-jcl1l8k9dhi80s8l7hqcr4sqvhqq03jj.apps.googleusercontent.com',
    iosBundleId: 'com.singulariswow',
  );
}