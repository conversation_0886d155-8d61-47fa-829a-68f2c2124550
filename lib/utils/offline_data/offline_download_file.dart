import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/offline_data/url_model.dart';
import 'package:masterg/utils/offline_data/view_offline_vedio_page.dart';
import 'package:path_provider/path_provider.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';

import '../../data/providers/video_player_provider.dart';
import '../../pages/ghome/video_player_screen.dart';

class OfflineDownloadFile extends StatefulWidget {
  final String? videoURL;
  final String? videoThumbnail;
  final String? view;
  final String? des;
  final String? id;
  const OfflineDownloadFile(
      {super.key,
      this.videoURL,
      this.videoThumbnail,
      this.view,
      this.des,
      this.id});

  @override
  State<OfflineDownloadFile> createState() => _OfflineDownloadFileState();
}

class _OfflineDownloadFileState extends State<OfflineDownloadFile> {
  late final Dio dio;
  double _progress = 0.0;
  String? _videoFilePath;

  @override
  void initState() {
    super.initState();
    dio = Dio();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _downloadAndSaveVideo() async {
    //String url = widget.videoURL!;
    String url = '';
    debugPrint('videoURL===${widget.videoURL}');
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String savePath = '${appDocDir.path}/video.mp4';

      await dio.download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            double progress = (received / total * 100);
            // Update UI with download progress
            setState(() {
              _progress = progress;
            });
          }
        },
      );
      debugPrint('File downloaded to: $savePath');
      setState(() {
        _videoFilePath = savePath;
      });

      // Save the file path to Hive
      final box = await Hive.openBox<URLModel>('videos_offline');
      //box.put('video_path', savePath);
      //box.add(URLModel(savePath+'::'+widget.videoThumbnail!));
      box.add(URLModel(
          savePath, widget.videoThumbnail!, widget.view!, widget.des!, 11));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Video downloaded and saved successfully'),
        ),
      );
    } catch (e) {
      debugPrint('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error downloading and saving video'),
        ),
      );
    }
  }

  Future<void> _playVideoFromHive() async {
    // Retrieve the file path from Hive
    final box = await Hive.openBox<String>('videos_offline');
    String? videoPath = box.get('video_path');

    if (videoPath != null) {
      setState(() {
        _videoFilePath = videoPath;
      });
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(
            videoFilePath: _videoFilePath!,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Video not found in Hive database'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Download and Play Video'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_progress > 0 && _progress < 100)
              LinearProgressIndicator(
                value: _progress / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _downloadAndSaveVideo,
              child: Text("Download and Save Video"),
            ),
            ElevatedButton(
              onPressed: _playVideoFromHive,
              child: Text("Play Video from Hive"),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ViewOfflineVideoPage(),
                  ),
                );
                //Hive.deleteBoxFromDisk('videos_offline');
                /*final urlBox = Hive.box<URLModel>('videos_offline');
                var allItems = urlBox.values.toList();
               debugPrint('videos_offline====${allItems[0].url}');*/
              },
              child: Text("View Hive List"),
            ),
          ],
        ),
      ),
    );
  }
}

class VideoPlayerScreen extends StatefulWidget {
  final String videoFilePath;

  const VideoPlayerScreen({super.key, required this.videoFilePath});

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  @override
  void initState() {
    debugPrint(widget.videoFilePath);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<VideoPlayerProvider>(
          create: (context) => VideoPlayerProvider(false),
        ),
      ],
      child: Consumer<VideoPlayerProvider>(
          builder: (context, value, child) => Scaffold(
                appBar: AppBar(
                  title: Text('Video Player'),
                ),
                body: CustomVideoPlayer(
                  maintainAspectRatio: true,
                  url: widget.videoFilePath,
                  showPlayButton: true,
                  wowStudioIcon: true,
                  isLocalVideo: true,
                ),
              )),
    );
  }
}

/*
CustomVideoPlayer(
maintainAspectRatio: true,
url: widget.videoFilePath,
showPlayButton: true,
wowStudioIcon: true,
),*/
