import 'package:location/location.dart';
import 'dart:async';

class LocationManager {
  static Future<LocationData?> initLocationService() async {
    var location = Location();
    LocationData _locationData;
    // LocationData? currentLocation;
    // String address = "";

    if (!await location.serviceEnabled()) {
      if (!await location.requestService()) {
        return null;
      }
    }

    var permission = await location.hasPermission();
    if (permission == PermissionStatus.denied) {
      permission = await location.requestPermission();
      if (permission != PermissionStatus.granted) {
        return null;
      }
    }
    _locationData = await location.getLocation();
    // currentLocation = _locationData;
    location.onLocationChanged.listen((LocationData currentLocation) {
      _locationData = currentLocation;
    });

    return _locationData;
  }

  static Future<LocationData?> getLocation() async {
    LocationData? currentLocation;
    initLocationService().then((value) {
      LocationData? location = value;
      currentLocation = location;
    });

    return (currentLocation);
  }
}
