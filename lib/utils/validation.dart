import 'package:easy_localization/easy_localization.dart';

// String? validateEmail(String value) {
//   String pattern =
//       r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{3,3}))$';
//   RegExp regExp = new RegExp(pattern);
//   if (value == null || value.trim().isEmpty) {
//     return null;
//   }

//   return null;
// }

// String? validateEmail(String? value) {
//   String pattern =
//       r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{3,3}))$';
//   RegExp regExp = new RegExp(pattern);
//   if (value == null || value.trim().isEmpty) {
//     return null;
//   } else if (!RegExp(
//           r"^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$")
//       .hasMatch(value)) {
//     return tr('enter_valid_email');
//   }

//   return null;
// }
// String? validateEmail(String? value) {
//   // General email validation pattern
//   String pattern =
//       r'^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,})$';

//   RegExp regExp = new RegExp(pattern);

//   if (value == null || value.trim().isEmpty) {
//     return tr('enter_email');
//   } else if (!regExp.hasMatch(value)) {
//     return tr('enter_valid_email');
//   }

//   return null;
// }

String? validateEmail(String? value) {
  // General email validation pattern
  String pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$';

  RegExp regExp = RegExp(pattern);

  if (value == null || value.trim().isEmpty) {
    //return tr('plz_enter_an_email');
    return ("");
  } else if (!regExp.hasMatch(value)) {
    return tr('plz_enter_valid_email');
  }
  return null;
}

// String? validatePassword(String value) {
//   String pattern = r'^(?=.*[0-9])(?=.*[a-z]).{7,}$';
//   RegExp regExp = RegExp(pattern);
//   if (value == null || value.trim().isEmpty) {
//     return tr('password_required');
//   } else if (value.contains(" ")) {
//     return tr('space_not_allowed');
//   } else
//     return null;
// }

String? validatePassword1(String value) {
  if (value.trim().isEmpty) {
    return tr('password_required');
  } else {
    return null;
  }
}

String? validatePassword(String? value) {
  // RegExp regex =
  //     RegExp(r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{4,}$');
  if (value!.isEmpty) {
    //return tr('please_enter_password');
    return ('');
  } else {
    if (value.length < 8) {
      return tr('pass_valid'); //Password Must be more than 8 characters
    } else {
      return null;
    }
  }
}
