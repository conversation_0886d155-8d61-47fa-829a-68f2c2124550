import 'package:flutter/material.dart';
import 'package:masterg/utils/config.dart';

extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${(a * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(r * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(g * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(b * 255.0).round().toRadixString(16).padLeft(2, '0')}';
}

class ColorConstants {
  static const primaryColorLight = MaterialColor(
    0xff1A1A1D,
    <int, Color>{
      50: Color(0xff1A1A1D),
      100: Color(0xff1A1A1D),
      200: Color(0xff1A1A1D),
      300: Color(0xff1A1A1D),
      400: Color(0xff1A1A1D),
      500: Color(0xff1A1A1D),
      600: Color(0xff1A1A1D),
      700: Color(0xff1A1A1D),
      800: Color(0xff1A1A1D),
      900: Color(0xff1A1A1D),
    },
  );

  static const primaryBlue = Color(0xff3CA4D2);
  static const backgroundColor = Color(0xffF2F2F2);
  static const headingTitle = Color(0xff0E1638);
  static const subHeadingTitle = Color(0xff2F374E);
  static const bodyText = Color(0xff727C95);
  static const lebelText = Color(0xffCED4E7);
  static const dividerColor1 = Color(0xffCED4E7);
  static const dividerColor2 = Color(0xffF4F7FD);
  static const darkBackground = Color(0xff161616);
  static const darkButton = Color(0xff272727);
  static const wowPrimaryColor = Color(0xffF94A29);
  static const white = Colors.white;
  static const primaryDark = Color(0xff263367);
  static const textFieldBg = Color(0xffF2F2F2);

  static const black = Color(0xff000000);
  static const pendingGrey = Color(0xffA7A7A7);
  static const grey = Color(0xffF0F0F0);
  static const darkGrey = Color(0xFF303030);
  static const green = Color(0xff3EBDA0);
  static const redBg = Color(0xFFff3d3d);
  static const red = Color(0xFFEB5757);
  static const bottomGrey = Color(0xffF8F8F8);
  static const hintGrey = Color(0xffACACAC);
  static const inactiveTab = Color(0xFFBFBFBF);
  static const activeTab = Color(0xFFffd500);
  static const yellowActiveTab = Color(0xFFffd500);
  static const yellow = Color(0xFFFDB515);
  static const orange3 = Color(0xFFFF2452);
  static const viewAll = Color(0xFFBD9D50);
  static const orange4 = Color(0xFFFF9100);
  static const startGreyBg = Color(0xFFE0E0E0);
  static const activeTabUnderline = Color(0xFF12AAEB);
  static const textDarkBlack = Color(0xff1c2555);
  static const bgColor = Color(0xffFAFAFA);
  static const searchFilled = Color(0xff194250);
  static const selectedPage = Color(0xffF6BA17);
  static const unselectedPage = Color(0xff11576F);
  static const grey1 = Color(0xff333333);
  static const grey2 = Color(0xff4F4F4F);
  static const grey3 = Color(0xff828282);
  static const grey10 = Color(0xffCED4E7);

  static const grey4 = Color(0xffBDBDBD);
  static const grey5 = Color(0xffc7c7c7);
  static const grey6 = Color(0xff929BA3);
  static const courseBg = Color(0xff333333);
  static const sectionDivider = Color(0xffF5F5F5);
  static const divider = Color(0xffEFEFEF);
  static const notificationDateGrey = Color(0xff9D9A9A);
  static const orange = Color(0xffff8d29); //Color(0xFFFF8D29)
  static const darkBlue = Color(0xff1c2555);
  static const greyOutline = Color(0xff757575);
  static const purple = Color(0xff2c2738);
  static const starColor = Color(0xfff9414d);
  static const pillBg = Color(0xfffff4db);
  static const allBg = Color(0xffD4D4D4);
  static const unselectedButton = Color(0xffC8D0E3);

  //Continue button color
  static const continueColor = Color(0xff043140);
  // static const darkGrey = Color(0xff444444);
  static const bgGrey = Color(0xffF2F2F2);

  static const iconBgGrey = Color(0xffE5E5E5);
  static const bgLigtGrey = Color(0xffebeeff);

  static const textDarkBlue = Color(0xff043140);
  static const bgBlueScreen = Color(0xff043140);
  static const bgBlueBtn = Color(0xff12AAEB);
  static const bgDarkBlueBtn = Color(0xff104152);
  static const bgDarkBlueBtn2 = Color(0xff043140);
  static const shadowColor = Color(0x0000002B);
  static const documentQuoteBg = Color(0xffFDF1D0);
  static const otpText = Color(0xff101a5c);

  static const disableColor = Color(0xffe2e7ff);
  static const selectedGreen = Color(0xff0ebdab);

  static const color5f6687 = Color(0xff5f6687);

  static const listColor = Color(0xffFFF5F5);
  static const dashboardBgColor = Color(0xffF3F3F3);

  ///For Job
  static const jobBgColor = Color(0xffF2F2F2);
  static const highlightsCardColor2 = Color(0xffF4900C);

  static const gradientRed = Color(0xffFF2252);
  static const gradientOrange = Color(0xffFC7B04);

  ///Tab Bar Color
  static const selected = Color(0xffFC7B04);

  //ASSESSMENT COLORS
  static const answered = Color(0xFFFF9D5C);
  static const notAnswered = Color(0xFFFFEB3B);
  static const answeredReviews = Color(0xFF2D75DD);

  static const novoice = Color(0xFFFED944);
  static const learner = Color(0xFFFF9A00);
  static const master = Color(0xFFFE8B66);
  static const expert = Color(0xFF61ABCA);
  static const leader = Color(0xFF629BCA);

  static const appbarColor = Color(0xff2c73d9);
  static const buttoncolor = Color(0xff2c73d9);
  static const dashboardApplyColor = Color(0xff263367);

  static const buildPortfolio1 = Color(0xffF4900C);
  static const buildPortfolio2 = Color(0xffFFFFFF);

  // Color primaryColors() {
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // Color primaryColorbtnAlways() {
  //   // return Colors.red;
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // Color buttonColor() {
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // Color primaryForgroundColor() {
  //   return HexColor.fromHex(APK_DETAILS['theme_forground_color']!);
  // }

  // Color gradientLeft {
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // Color gradientRight {
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // static Color bottomNavigation() {
  //   return HexColor.fromHex('ffffff');
  // }

  // static Color selectedColor() {
  //   return HexColor.fromHex(APK_DETAILS['theme_color']!);
  // }

  // static Color bottomNavigationSelected() {
  //   return HexColor.fromHex(
  //       '${APK_DETAILS['theme_color']}'); // to configuration config file
  //   //return HexColor.fromHex('004E90');
  // }

  // static Color bottomNavigationUnSelected() {
  //   return HexColor.fromHex('727C95');
  // }

  /// Theme-aware color methods
  /// Get surface color based on theme mode
  static Color surfaceColor(bool isDarkMode) {
    return isDarkMode ? const Color(0xFF1E1E1E) : white;
  }

  /// Get background color based on theme mode
  // static Color backgroundColor(bool isDarkMode) {
  //   return isDarkMode ? const Color(0xFF121212) : white;
  // }

  /// Get card background color based on theme mode
  static Color cardBackgroundColor(bool isDarkMode) {
    return isDarkMode ? const Color(0xFF1E1E1E) : white;
  }

  /// Get app bar background color based on theme mode
  static Color appBarBackgroundColor(bool isDarkMode) {
    return isDarkMode
        ? const Color(0xFF1F1F1F)
        : HexColor.fromHex(APK_DETAILS['theme_color']!);
  }

  /// Get text field background color based on theme mode
  static Color textFieldBackgroundColor(bool isDarkMode) {
    return isDarkMode ? const Color(0xFF2A2A2A) : textFieldBg;
  }

  /// Get body text color based on theme mode
  static Color bodyTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white70 : bodyText;
  }

  /// Get heading text color based on theme mode
  static Color headingTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white : headingTitle;
  }

  /// Get sub-heading text color based on theme mode
  static Color subHeadingTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white70 : subHeadingTitle;
  }

  /// Get label text color based on theme mode
  static Color labelTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white60 : lebelText;
  }

  /// Get divider color based on theme mode
  static Color dividerColor(bool isDarkMode) {
    return isDarkMode ? Colors.white24 : dividerColor1;
  }

  /// Get icon color based on theme mode
  static Color iconColor(bool isDarkMode) {
    return isDarkMode ? Colors.white70 : bodyText;
  }

  /// Get hint text color based on theme mode
  static Color hintTextColor(bool isDarkMode) {
    return isDarkMode ? Colors.white60 : hintGrey;
  }

  /// Get disabled color based on theme mode
  static Color disabledColor(bool isDarkMode) {
    return isDarkMode ? Colors.white38 : disableColor;
  }

  /// Get shadow color based on theme mode
  static Color shadowColors(bool isDarkMode) {
    return isDarkMode
        ? black.withValues(alpha: 0.3)
        : black.withValues(alpha: 0.1);
  }

  /// Get overlay color based on theme mode
  static Color overlayColor(bool isDarkMode) {
    return isDarkMode
        ? white.withValues(alpha: 0.1)
        : black.withValues(alpha: 0.1);
  }

  /// Get bottom navigation background color based on theme mode
  static Color bottomNavBackgroundColor(bool isDarkMode) {
    return isDarkMode ? const Color(0xFF1F1F1F) : white;
  }

  /// Get bottom navigation u  nselected color based on theme mode
  static Color bottomNavUnselectedColor(bool isDarkMode) {
    return isDarkMode ? Colors.white54 : inactiveTab;
  }
}


//viewAll , primaryDark, primaryDark , //job bottom color on dashboard dashboardApplyColor