import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/main.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class NetworkController extends GetxController {
  final InternetConnectionChecker _connectivity =
      InternetConnectionChecker.createInstance();

  @override
  void onInit() {
    super.onInit();

    _connectivity.onStatusChange.listen((status) {
      switch (status) {
        case InternetConnectionStatus.connected:
          log('InternetCheck: Data connection is available.');
          if (Get.isSnackbarOpen) {
            Get.closeCurrentSnackbar();
          }
          break;
        case InternetConnectionStatus.disconnected:
          log('InternetCheck: You are disconnected from the internet.');
          if (!Get.isSnackbarOpen) {
            Get.rawSnackbar(
                messageText: Text('please_connect_internet',
                        style: TextStyle(
                            color: navigatorKey
                                .currentContext!.appColors.primaryForeground,
                            fontSize: 14))
                    .tr(),
                isDismissible: false,
                duration: const Duration(days: 1),
                backgroundColor: const Color(0xFF3CA4D2),
                icon: Icon(
                  Icons.wifi_off,
                  color: const Color(0xFFFFFFFF),
                  size: 35,
                ),
                margin: EdgeInsets.zero,
                snackStyle: SnackStyle.GROUNDED);
          }
          break;
        case InternetConnectionStatus.slow:
          log('InternetCheck: Your internet connection is slow.');
          // Optionally, show a snackbar or handle slow connection here
          break;
      }
    });
  }
}
