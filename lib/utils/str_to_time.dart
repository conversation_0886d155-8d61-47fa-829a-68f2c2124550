import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'dart:ui' as ui;

import 'package:shimmer/shimmer.dart';

class StrToTime extends StatefulWidget {
  final String time;
  final String dateFormat;
  final String? appendString;
  final TextStyle? textStyle;
  const StrToTime(
      {super.key,
      required this.time,
      required this.dateFormat,
      required this.appendString,
      this.textStyle});

  @override
  State<StrToTime> createState() => _StrToTimeState();
}

class _StrToTimeState extends State<StrToTime> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return FutureBuilder<int>(
          future: Utility.strtotime(widget.time),
          builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                return Shimmer.fromColors(
                  baseColor: context.appColors.shimmerBase,
                  highlightColor: context.appColors.shimmerHighlight,
                  child: Container(
                      height: 14,
                      // margin: EdgeInsets.only(left: 2),
                      width: 40,
                      decoration: BoxDecoration(
                        color: context.appColors.surface,
                      )),
                );
              default:
                if (snapshot.hasError) {
                  return Text('');
                } else {
                  // return Text('${snapshot}');
                  return Text(
                    '${Utility.getTime(dateFormat: widget.dateFormat, timestamp: snapshot.data! * 1000)}${widget.appendString ?? ''}',
                    style: Styles.getRegularThemeStyle(context, size: 12),
                    // style: widget.textStyle ??
                    //     Styles.regular(
                    //         color: context.appColors.bodyText, size: 12),
                    textDirection: ui.TextDirection.ltr,
                  );
                }
            }
          },
        );
      },
    );
  }
}

class DateToTimeago extends StatefulWidget {
  final String time;
  const DateToTimeago({super.key, required this.time});

  @override
  State<DateToTimeago> createState() => _DateToTimeagoState();
}

class _DateToTimeagoState extends State<DateToTimeago> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<int>(
      future: Utility.strtotime(widget.time),
      builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
        switch (snapshot.connectionState) {
          case ConnectionState.waiting:
            return Text('');
          default:
            if (snapshot.hasError) {
              return Text('');
            } else {
              DateTime date = DateTime.fromMillisecondsSinceEpoch(
                snapshot.data! * 1000,
              );
              var now = DateTime.now();

              return Text(Utility().calculateTimeDifferenceBetween(
                  DateTime.parse(date.toString().substring(0, 19)),
                  now,
                  context));
            }
        }
      },
    );
  }
}
