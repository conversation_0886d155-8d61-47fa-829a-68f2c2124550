import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'styles.dart';

class CustomProgressIndicator extends StatelessWidget {
  final bool isLoading;
  final Color color;

  const CustomProgressIndicator(this.isLoading, this.color, {super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height - 200,
          color: color,
          child: Center(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                  color: context.isDarkMode ? Colors.black54 : Colors.black45,
                  borderRadius: BorderRadius.all(Radius.circular(10))),
              child: <PERSON>umn(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(
                    'loading',
                    style: Styles.bold(
                        size: 16, color: context.appColors.textWhite),
                  ).tr(),
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      context.primaryDark,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
