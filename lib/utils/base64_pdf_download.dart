import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'Log.dart';

class Base64PdfDownloadHelper {
  static Future<void> saveAndDownloadBase64Pdf(
      BuildContext context, String base64Pdf, String fileName) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;

    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }

    final bytes = base64Decode(base64Pdf);
    String localPath;

    final status = await Permission.storage.request();

    if (status.isGranted ||
        (android.version.sdkInt >= 30) ||
        (await Permission.storage.request()).isGranted) {
      if (Platform.isAndroid) {
        localPath = "/sdcard/Download";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }

      final file = File("$localPath/$fileName");

      // Show toast when download starts
      // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
      //   content: Text('Downloading Start'),
      // ));

      await file.writeAsBytes(bytes);

      // ScaffoldMessenger.of(context).showSnackBar(
      // const SnackBar(content: Text('Download Complete')),
      // );

      await OpenFilex.open(file.path);
    } else {
      Log.v('Permission Denied');
    }
  }
}
