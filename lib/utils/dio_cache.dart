import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/utils/utility.dart';

class CustomCacheInterceptor extends Interceptor {
  static const String _cacheKeyPrefix = 'custom_cache_';

  @override
  FutureOr onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // try {
    //   log("error while opening ");

    //   await getApplicationDocumentsDirectory().then((value) {
    //     Hive.init(value.path);
    //     Hive.openBox(DB.CONTENT);
    //     Hive.openBox(DB.ANALYTICS);
    //     Hive.openBox(DB.TRAININGS);
    //     Hive.openBox('theme');
    //   });
    // } catch (error) {
    //   log("error while opening for hive $error");
    // }

    bool isConnected;
    if (navigatorKey.currentContext != null) {
      isConnected = await Utility.checkNetwork(null);
    } else {
      isConnected = true;
    }

    // Use cache for GET requests only
    if (isConnected == false) {
      int value = Utility.getIndiaTime();
      currentIndiaTime = DateTime.fromMillisecondsSinceEpoch(value * 1000);

      if (options.method == 'GET') {
        String cacheKey = _generateCacheKey(options);
        dynamic cachedData = await _getFromCache(cacheKey);
        if (cachedData != null) {
          return handler.resolve(Response(
            requestOptions: options,
            data: cachedData,
            statusCode: 200,
          ));
        }
      }

      if (options.method == 'POST') {
        String cacheKey = _generateCacheKey(options);
        dynamic cachedData = await _getFromCache(cacheKey);
        if (cachedData != null) {
          return handler.resolve(Response(
            requestOptions: options,
            data: cachedData,
            statusCode: 200,
          ));
        }
      }
    }

    return handler.next(options);
  }

  @override
  FutureOr onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    // Save response to cache for GET requests only
    if (response.requestOptions.method == 'GET') {
      String cacheKey = _generateCacheKey(response.requestOptions);
      await _saveToCache(cacheKey, response.data);
    }

    return handler.next(response);
  }

  String _generateCacheKey(RequestOptions options) {
    // Customize this method to generate a unique cache key based on the request options
    return '$_cacheKeyPrefix${options.uri.toString()}';
  }

  Future<dynamic> _getFromCache(String key) async {
    String? jsonData = Preference.getString(key);
    log("cache get $jsonData", name: "dio_cache");

    return jsonData != null ? json.decode(jsonData) : null;
  }

  Future<void> _saveToCache(String key, dynamic data) async {
    try {
      Preference.setString(key, json.encode(data));
    } catch (e) {
      log("cache store error $e", name: "dio_cache");
    }
    log("cache store ", name: "dio_cache");
  }

  @override
  FutureOr onError(DioException err, ErrorInterceptorHandler handler) async {
    return handler.next(err);
  }
}
