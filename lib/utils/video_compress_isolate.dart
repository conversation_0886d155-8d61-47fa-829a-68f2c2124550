// import 'dart:developer';
import 'dart:isolate';

// import 'package:video_compress/video_compress.dart';

class FileCompressionIsolate {
  @pragma('vm:entry-point')
  static void compressIsolate(Map<String, dynamic> message) async {
    // final String filePath = message['filePath'];
    final SendPort sendPort = message['sendPort'];

    try {
      // VideoCompress.compressProgress$.subscribe((double progress) {
      //   sendPort.send({'progress': progress});
      // });

      // MediaInfo? mediaInfo = await VideoCompress.compressVideo(
      //   filePath,
      //   quality: VideoQuality.MediumQuality,
      //   deleteOrigin: false,
      // );

      // sendPort.send({'path': mediaInfo?.file?.path});

      // sendPort.send({'status': "Compression successful"});
    } catch (e) {
      // log("Media file compression exception: $stacktrace",
      //     name: "your_filename.dart");

      sendPort.send({'status': "Compression failed"});
    }
  }
}
