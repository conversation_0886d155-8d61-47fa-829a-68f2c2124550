import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:masterg/utils/styles.dart';

class CheckInternet extends StatefulWidget {
  final Widget body;
  final Function refresh;

  const CheckInternet({super.key, required this.body, required this.refresh});

  @override
  State<CheckInternet> createState() => _CheckInternetState();
}

class _CheckInternetState extends State<CheckInternet> {
  bool isConnected = true;
  bool isSnackBarShowing = false;

  @override
  void initState() {
    super.initState();
    // loop();
  }

  void loop() async {
    while (true) {
      await Future.delayed(const Duration(seconds: 6));
      isConnected =
          await InternetConnectionChecker.createInstance().hasConnection;

      if (isConnected) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        widget.refresh();
      } else if (isSnackBarShowing == false) {
        isSnackBarShowing = true;
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          duration: Duration(seconds: 50),
          content: Container(
              margin: EdgeInsets.only(bottom: 100, left: 4, right: 4),
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 4),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: context.appColors.grey2),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.wifi_off,
                    color: context.surfaceColor,
                  ),
                  SizedBox(width: 8),
                  Text(
                    "No Internet Connection!",
                    style: Styles.bold(color: context.surfaceColor),
                  )
                ],
              )),
        ));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          widget.body,
        ],
      ),
    );
  }
}
