import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/theme/theme_bloc.dart';
import 'theme_extensions.dart';

/// A wrapper widget that automatically rebuilds when theme changes
/// Use this to wrap any widget that needs to respond to theme changes
class ThemeAwareWidget extends StatelessWidget {
  final Widget Function(BuildContext context, ThemeState themeState) builder;

  const ThemeAwareWidget({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) => builder(context, themeState),
    );
  }
}

/// A theme-aware Scaffold that automatically uses the correct background color
class Theme<PERSON>wareScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? floatingActionButton;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? bottomNavigationBar;
  final Color? backgroundColor;
  final bool extendBodyBehindAppBar;
  final bool extendBody;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Key? scaffoldKey;

  const ThemeAwareScaffold({
    super.key,
    this.appBar,
    this.body,
    this.floatingActionButton,
    this.drawer,
    this.endDrawer,
    this.bottomNavigationBar,
    this.backgroundColor,
    this.extendBodyBehindAppBar = false,
    this.extendBody = false,
    this.floatingActionButtonLocation,
    this.scaffoldKey,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) => Scaffold(
        key: scaffoldKey,
        appBar: appBar,
        body: body,
        floatingActionButton: floatingActionButton,
        drawer: drawer,
        endDrawer: endDrawer,
        bottomNavigationBar: bottomNavigationBar,
        backgroundColor: backgroundColor ?? context.backgroundColor,
        extendBodyBehindAppBar: extendBodyBehindAppBar,
        extendBody: extendBody,
        floatingActionButtonLocation: floatingActionButtonLocation,
      ),
    );
  }
}

/// A theme-aware Container that automatically uses theme colors
class ThemeAwareContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Decoration? decoration;
  final Color? color;
  final AlignmentGeometry? alignment;
  final BoxConstraints? constraints;
  final Matrix4? transform;
  final AlignmentGeometry? transformAlignment;
  final Clip clipBehavior;

  const ThemeAwareContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.decoration,
    this.color,
    this.alignment,
    this.constraints,
    this.transform,
    this.transformAlignment,
    this.clipBehavior = Clip.none,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) => Container(
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: decoration,
        color: color ?? context.surfaceColor,
        alignment: alignment,
        constraints: constraints,
        transform: transform,
        transformAlignment: transformAlignment,
        clipBehavior: clipBehavior,
        child: child,
      ),
    );
  }
}

/// A theme-aware Card that automatically uses theme colors
class ThemeAwareCard extends StatelessWidget {
  final Widget? child;
  final Color? color;
  final double? elevation;
  final ShapeBorder? shape;
  final bool borderOnForeground;
  final EdgeInsetsGeometry? margin;
  final Clip? clipBehavior;
  final bool semanticContainer;

  const ThemeAwareCard({
    super.key,
    this.child,
    this.color,
    this.elevation,
    this.shape,
    this.borderOnForeground = true,
    this.margin,
    this.clipBehavior,
    this.semanticContainer = true,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) => Card(
        color: color ?? context.appColors.cardBackground,
        elevation: elevation,
        shape: shape,
        borderOnForeground: borderOnForeground,
        margin: margin,
        clipBehavior: clipBehavior,
        semanticContainer: semanticContainer,
        child: child,
      ),
    );
  }
}

/// A theme-aware Text widget that automatically uses theme text colors
class ThemeAwareText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final Locale? locale;
  final bool? softWrap;
  final TextOverflow? overflow;
  final double? textScaleFactor;
  final int? maxLines;
  final String? semanticsLabel;
  final TextWidthBasis? textWidthBasis;
  final TextHeightBehavior? textHeightBehavior;
  final Color? textColor;
  final TextStyleType textType;

  const ThemeAwareText(
    this.text, {
    super.key,
    this.style,
    this.strutStyle,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.textColor,
    this.textType = TextStyleType.body,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) {
        Color defaultColor;
        switch (textType) {
          case TextStyleType.heading:
            defaultColor = context.headingTextColor;
            break;
          case TextStyleType.subHeading:
            defaultColor = context.subHeadingTextColor;
            break;
          case TextStyleType.body:
            defaultColor = context.bodyTextColor;
            break;
          case TextStyleType.label:
            defaultColor = context.hintTextColor;
            break;
        }

        return Text(
          text,
          style: (style ?? const TextStyle()).copyWith(
            color: textColor ?? defaultColor,
          ),
          strutStyle: strutStyle,
          textAlign: textAlign,
          textDirection: textDirection,
          locale: locale,
          softWrap: softWrap,
          overflow: overflow,
          textScaler: textScaleFactor != null
              ? TextScaler.linear(textScaleFactor!)
              : null,
          maxLines: maxLines,
          semanticsLabel: semanticsLabel,
          textWidthBasis: textWidthBasis,
          textHeightBehavior: textHeightBehavior,
        );
      },
    );
  }
}

/// A theme-aware AppBar that automatically uses theme colors
class ThemeAwareAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final double? elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final IconThemeData? iconTheme;
  final bool centerTitle;
  final double? titleSpacing;
  final double toolbarOpacity;
  final double bottomOpacity;
  final PreferredSizeWidget? bottom;
  final double? leadingWidth;
  final TextStyle? titleTextStyle;
  final SystemUiOverlayStyle? systemOverlayStyle;

  const ThemeAwareAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.elevation,
    this.backgroundColor,
    this.foregroundColor,
    this.iconTheme,
    this.centerTitle = false,
    this.titleSpacing,
    this.toolbarOpacity = 1.0,
    this.bottomOpacity = 1.0,
    this.bottom,
    this.leadingWidth,
    this.titleTextStyle,
    this.systemOverlayStyle,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) => AppBar(
        title: title,
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        elevation: elevation ?? 0,
        backgroundColor: backgroundColor ?? context.appColors.appBarBackground,
        foregroundColor: foregroundColor ?? context.primaryForegroundColor,
        iconTheme:
            iconTheme ?? IconThemeData(color: context.primaryForegroundColor),
        centerTitle: centerTitle,
        titleSpacing: titleSpacing,
        toolbarOpacity: toolbarOpacity,
        bottomOpacity: bottomOpacity,
        bottom: bottom,
        leadingWidth: leadingWidth,
        titleTextStyle: titleTextStyle ??
            TextStyle(
              color: context.primaryForegroundColor,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
        systemOverlayStyle: systemOverlayStyle,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// A reusable theme toggle widget that can be used in app bars or settings
class ThemeToggleWidget extends StatelessWidget {
  final ThemeToggleStyle style;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;
  final String? tooltip;

  const ThemeToggleWidget({
    super.key,
    this.style = ThemeToggleStyle.iconButton,
    this.iconSize,
    this.padding,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) {
        switch (style) {
          case ThemeToggleStyle.iconButton:
            return IconButton(
              icon: Icon(
                themeState.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                size: iconSize ?? 24,
              ),
              onPressed: () => _toggleTheme(context),
              tooltip: tooltip ??
                  (themeState.isDarkMode
                      ? 'Switch to Light Mode'
                      : 'Switch to Dark Mode'),
              padding: padding ?? const EdgeInsets.all(8.0),
            );
          case ThemeToggleStyle.switchStyle:
            return Switch(
              value: themeState.isDarkMode,
              onChanged: (_) => _toggleTheme(context),
            );
          case ThemeToggleStyle.animatedIcon:
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: IconButton(
                key: ValueKey(themeState.isDarkMode),
                icon: Icon(
                  themeState.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  size: iconSize ?? 24,
                ),
                onPressed: () => _toggleTheme(context),
                tooltip: tooltip ??
                    (themeState.isDarkMode
                        ? 'Switch to Light Mode'
                        : 'Switch to Dark Mode'),
                padding: padding ?? const EdgeInsets.all(8.0),
              ),
            );
        }
      },
    );
  }

  void _toggleTheme(BuildContext context) {
    context.read<ThemeBloc>().add(ToggleThemeEvent());
  }
}

/// Enum for different theme toggle widget styles
enum ThemeToggleStyle {
  iconButton,
  switchStyle,
  animatedIcon,
}

/// A theme-aware floating action button for theme toggle
class ThemeToggleFAB extends StatelessWidget {
  final double? size;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ThemeToggleFAB({
    super.key,
    this.size,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return ThemeAwareWidget(
      builder: (context, themeState) => FloatingActionButton(
        mini: size != null && size! < 40,
        backgroundColor: backgroundColor ?? context.primaryDark,
        foregroundColor: foregroundColor ?? context.primaryForegroundColor,
        onPressed: () => context.read<ThemeBloc>().add(ToggleThemeEvent()),
        tooltip: themeState.isDarkMode
            ? 'Switch to Light Mode'
            : 'Switch to Dark Mode',
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Icon(
            themeState.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            key: ValueKey(themeState.isDarkMode),
            size: size != null ? size! * 0.6 : 24,
          ),
        ),
      ),
    );
  }
}
