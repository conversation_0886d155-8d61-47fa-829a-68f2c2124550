import 'package:flutter/material.dart';

class CallOnceWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback onCallOnce;

  const CallOnceWidget(
      {super.key, required this.child, required this.onCallOnce});

  @override
  State<CallOnceWidget> createState() => _CallOnceWidgetState();
}

class _CallOnceWidgetState extends State<CallOnceWidget> {
  bool _hasBeenCalled = false;
  Widget? placeholderchild;

  @override
  void initState() {
    super.initState();
    placeholderchild = Container(child: widget.child);
    if (!_hasBeenCalled) {
      _hasBeenCalled = true;
      widget.onCallOnce();
    }
  }

  @override
  Widget build(BuildContext context) {
    return placeholderchild!;
  }
}
