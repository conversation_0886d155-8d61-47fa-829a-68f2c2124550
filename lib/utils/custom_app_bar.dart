import 'package:flutter/material.dart';
import 'theme/theme_extensions.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget child;
  final double height;
  final Color? backgroundColor;

  const CustomAppBar({
    super.key,
    required this.child,
    this.height = kToolbarHeight,
    this.backgroundColor,
  });

  @override
  Size get preferredSize => Size.fromHeight(height);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: preferredSize.height,
      color: backgroundColor ?? context.appColors.appBarBackground,
      alignment: Alignment.center,
      child: child,
    );
  }
}
