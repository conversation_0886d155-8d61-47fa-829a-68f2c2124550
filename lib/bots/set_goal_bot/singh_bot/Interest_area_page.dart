import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:masterg/blocs/theme/theme_bloc.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:shimmer/shimmer.dart';

import '../../../data/models/response/auth_response/oraganization_program_resp.dart';

class InterestAreaPage extends StatefulWidget {
  final bool? backEnable;
  final bool? moveToHome;
  final bool? singleSelection;
  final bool? returnValue;
  final int? fetchGoalList;

  const InterestAreaPage({
    super.key,
    this.backEnable,
    this.moveToHome = false,
    this.singleSelection = true,
    this.returnValue = false,
    this.fetchGoalList,
  });

  @override
  State<InterestAreaPage> createState() => _InterestAreaPageState();
}

class _InterestAreaPageState extends State<InterestAreaPage> {
  bool isInterestMapping = false;

  List<String>? interestMapResponse;
  List<OragnizationProgram>? programs_list;
  List<int?> selectProgramId = [];
  List<int?> selectProgramParentId = [];
  List<int> selectedPrograms = [];
  List<OragnizationProgram> joyCategoryList = [];
  bool isUpdating = false;
  List<Menu>? menuList;
  late Color foregroundColor;
  int? isParentLanguage =
      Preference.getInt(Preference.IS_PRIMARY_LANGUAGE) ?? 1;

  List<OragnizationProgram>? response;

  List<OragnizationProgram>? displayInterestList = [];

  String? selectInst;
  List<String?> listInterest = <String>[];

  @override
  void initState() {
    super.initState();
    foregroundColor = context.appColors.primaryForeground;
    // foregroundColor = context.appColors.primaryForgroundColor();
    _getInterestPrograms();
  }

  @override
  didChangeDependencies() {
    foregroundColor = context.appColors.primaryForeground;
    super.didChangeDependencies();
  }

  void _getInterestPrograms() {
    // BlocProvider.of<HomeBloc>(context).add(InterestEvent());
    BlocProvider.of<HomeBloc>(context)
        .add(OrganizationProgramListEvent(widget.fetchGoalList));
  }

  void _mapInterest(param) {
    BlocProvider.of<HomeBloc>(context)
        .add(MapInterestEvent(param: param, mapType: 'InterestArea'));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: MultiBlocListener(
            listeners: [
              BlocListener<HomeBloc, HomeState>(
                  listener: (BuildContext context, state) {
                if (state is MapInterestState) {
                  _handleMapInterestResponse(state);
                }
              }),
              BlocListener<HomeBloc, HomeState>(
                listener: (BuildContext context, state) {
                  if (state is PiDetailState) {
                    handlePiDetail(state);
                  }
                  if (state is OrganizationProgramListState) {
                    setState(() {
                      switch (state.apiState) {
                        case ApiStatus.LOADING:
                          Log.v("Loading....................");
                          break;
                        case ApiStatus.SUCCESS:
                          Log.v(
                              "Success....................interest area list ");
                          response = state.response?.data;
                          displayInterestList = response;

                          break;
                        case ApiStatus.ERROR:
                          Log.v(
                              "Error..........................${state.error}");

                          break;
                        case ApiStatus.INITIAL:
                          break;
                      }
                    });
                  }
                },
              ),
            ],
            child: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, state) {
                return Scaffold(
                  backgroundColor: context.appColors.grey,
                  body: SafeArea(
                    child: ScreenWithLoader(
                      isLoading: isUpdating,
                      body: SingleChildScrollView(
                        child: (displayInterestList != null &&
                                displayInterestList?.length != 0)
                            ? Padding(
                                padding: const EdgeInsets.only(
                                    left: 0.0,
                                    top: 0.0,
                                    right: 0.0,
                                    bottom: 0.0),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(height: 20),
                                      SizedBox(
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.69,
                                        child: techChips(context),
                                      ),
                                      Visibility(
                                        visible: true,
                                        child: InkWell(
                                            onTap: () {
                                              var selectedCategoryIds = '';
                                              displayInterestList
                                                  ?.forEach((element) {
                                                if ((int.tryParse(
                                                            '${element.isMapped}') ??
                                                        0) >
                                                    0) {
                                                  selectedCategoryIds +=
                                                      '${element.id},';
                                                }
                                              });

                                              selectedCategoryIds =
                                                  selectedCategoryIds.substring(
                                                      0,
                                                      selectedCategoryIds
                                                              .length -
                                                          1);
                                              _mapInterest(selectedCategoryIds);

                                              return;
                                            },
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                  left: 5.0,
                                                  top: 10.0,
                                                  right: 5.0,
                                                  bottom: 10.0),
                                              width: double.infinity,
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height *
                                                  WidgetSize.AUTH_BUTTON_SIZE,
                                              decoration: BoxDecoration(
                                                  color:
                                                      context.appColors.primary,
                                                  borderRadius:
                                                      BorderRadius.circular(5)),
                                              child: Center(
                                                  child:
                                                      Text('confirm_interest',
                                                          style: Styles.regular(
                                                            color:
                                                                foregroundColor,
                                                          )).tr()),
                                            )),
                                      ),
                                    ]),
                              )
                            : SizedBox(
                                height: MediaQuery.of(context).size.height,
                                child: SingleChildScrollView(
                                  child: Wrap(
                                      direction: Axis.horizontal,
                                      children: shimmerChips.toList()),
                                ),
                              ),
                      ),
                    ),
                  ),
                );
              },
            )));
  }

  void _handleMapInterestResponse(MapInterestState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isInterestMapping = true;
          isUpdating = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          interestMapResponse = state.response!.data;
          isUpdating = false;
          isInterestMapping = true;
          //Preference.SAVE_INTEREST
          _getInterestPrograms();
          if (widget.moveToHome == true) {
            getBottomNavigationBar();
          } else {
            AlertsWidget.alertWithOkBtn(
              context: context,
              onOkClick: () {
                if (widget.returnValue == false) {
                  Navigator.pop(context, true);
                } else {
                  //Navigator.pop(context, selectInst);
                  Navigator.pop(context, listInterest);
                }
              },
              text: "${state.response?.data?.first}",
            );
          }

          // var box = Hive.box("content");
          // JoyCategoryResponse joyCategoryResponse =
          // JoyCategoryResponse.fromJson(response.body);
          // box.put("joy_category",
          //     joyCategoryList..map((e) => e.toJson()).toList());

          break;
        case ApiStatus.ERROR:
          isUpdating = false;
          isInterestMapping = false;
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_interest_error": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          isUpdating = false;
          break;
      }
    });
  }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");
          //isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success....................${portfolioState.response?.data?.toJson()}");

          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null) {
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          }
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null) {
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');
          }

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null) {
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');
          }

          setState(() {});
          getBottomNavigationBar(); //hide 18 jun 2024
          break;

        case ApiStatus.ERROR:
          getBottomNavigationBar(); //hide 18 jun 2024
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'select_interest', parameters: {
            "map_pi_interest_error": portfolioState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Iterable<Widget> get shimmerChips sync* {
    for (int i = 0; i < 15; i++) {
      yield Padding(
        padding: const EdgeInsets.only(top: 20),
        child: Shimmer.fromColors(
          baseColor: context.appColors.shimmerBase,
          highlightColor: context.appColors.shimmerHighlight,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: width(context) * 0.2,
                  width: width(context) * 0.2,
                  decoration: BoxDecoration(
                      color: context.appColors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 10.0),
                child: Container(
                  height: 10,
                  width: width(context) * 0.30,
                  margin: const EdgeInsets.only(top: 10),
                  decoration: BoxDecoration(
                      color: context.appColors.grey,
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget techChips(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return GridView.builder(
            shrinkWrap: true,
            physics: ScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              mainAxisSpacing: 4,
              crossAxisSpacing: 2,
              childAspectRatio: 1,
              crossAxisCount: 2,
            ),
            itemCount: displayInterestList?.length,
            itemBuilder: (context, i) {
              return InkWell(
                onTap: () {
                  Log.v('map is ${displayInterestList?[i].isMapped}');
                  if (widget.singleSelection == false) {
                    setState(() {
                      if ((int.tryParse(
                                  '${displayInterestList![i].isMapped}') ??
                              0) >
                          0) {
                        displayInterestList![i].isMapped = 0;
                      } else {
                        displayInterestList![i].isMapped = 1;
                      }
                      setState(() {
                        if (selectProgramId
                            .contains(displayInterestList![i].id)) {
                          selectProgramId.remove(displayInterestList![i].id);
                        } else {
                          selectProgramId.add(displayInterestList![i].id);
                        }
                      });

                      listInterest.clear();
                      listInterest.add(displayInterestList![i].interestarea);
                      listInterest.add(displayInterestList![i].id.toString());
                    });
                  } else {
                    setState(() {
                      if ((int.tryParse(
                                  '${displayInterestList![i].isMapped}') ??
                              0) ==
                          1) {
                        displayInterestList![i].isMapped = 0;
                      } else {
                        displayInterestList![i].isMapped = 1;
                      }
                      setState(() {
                        if (selectProgramId
                            .contains(displayInterestList![i].id)) {
                          selectProgramId.remove(displayInterestList![i].id);
                        } else {
                          selectProgramId.add(displayInterestList![i].id);
                        }
                      });
                    });
                  }
                },
                child: Column(
                  children: [
                    Stack(
                      children: [
                        Container(
                          margin: EdgeInsets.all(0),
                          width: 100,
                          height: 100,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image.network(
                              '${displayInterestList?[i].iconUrl}',
                              fit: BoxFit.cover,
                              errorBuilder: (context, url, error) {
                                return Image.asset(
                                  'assets/images/blank.png',
                                  height: 50,
                                  width: 50,
                                );
                              },
                              loadingBuilder: (BuildContext context,
                                  Widget child,
                                  ImageChunkEvent? loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  child: Container(
                                      height: 45,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 45,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                        shape: BoxShape.circle,
                                      )),
                                );
                              },
                            ),
                          ),
                        ),
                        if (widget.singleSelection == false) ...[
                          if ((int.tryParse(
                                      '${displayInterestList![i].isMapped}') ??
                                  0) >
                              0)
                            Positioned(
                                right: -3,
                                top: -3,
                                child: SvgPicture.asset(
                                    'assets/images/interest_selected.svg')),
                        ] else ...[
                          if ((int.tryParse(
                                      '${displayInterestList![i].isMapped}') ??
                                  0) ==
                              1)
                            Positioned(
                                right: -3,
                                top: -3,
                                child: SvgPicture.asset(
                                    'assets/images/interest_selected.svg')),
                        ]
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 0, right: 0, top: 5),
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Text(displayInterestList![i].interestarea ?? "",
                            style: Styles.bold(
                              size: 11,
                              //lineHeight: 1.2,
                              color: context.appColors.grey3,
                            ),
                            maxLines: 2,
                            textAlign: TextAlign.center),
                      ),
                    ),
                  ],
                ),
              );
            });
      },
    );
  }
}
