//import 'package:dialog_flowtter/dialog_flowtter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/bots/set_goal_bot/dialogFlow_messages.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class DialogFlowBot extends StatefulWidget {
  final String? question;
  const DialogFlowBot({Key? key, this.question}) : super(key: key);

  @override
  _DialogFlowBotState createState() => _DialogFlowBotState();
}

class _DialogFlowBotState extends State<DialogFlowBot> {
  //DialogFlowtter? dialogFlowtter;
  final TextEditingController _controller = TextEditingController();

  List<Map<String, dynamic>> messages = [];
  Color themeColor = Color.fromARGB(255, 190, 183, 249);

  @override
  void initState() {
    // DialogFlowtter.fromFile()
    //     .then((instance) => dialogFlowtter = instance)
    //     .then((value) {
    //   if (widget.question != null) {
    //     sendMessage('${widget.question}');
    //   }else {
    //     if (Preference.getString(Preference.AGE_GROUP) == 'teen') {
    //       try{
    //         sendMessage('teen ' +
    //             '${Utility().decrypted128(
    //                 '${Preference.getString(Preference.FIRST_NAME)}')}');
    //       }catch(e){
    //         Log.v(e);
    //       }
    //     }else {
    //       try{
    //         sendMessage('${Utility().decrypted128(
    //             '${Preference.getString(Preference.AGE_GROUP)}')} ' +
    //             '${Utility().decrypted128(
    //                 '${Preference.getString(Preference.FIRST_NAME)}')}');
    //       }catch(e){
    //         Log.v(e);
    //       }
    //
    //       //sendMessage('${Utility().decrypted128('${Preference.getString(Preference.AGE_GROUP)}')}');
    //     }
    //   }
    // });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: context.appColors.background,
        appBar: AppBar(
          titleTextStyle: Styles.getBoldThemeStyle(context),
          iconTheme: IconThemeData(color: context.appColors.textBlack),
          centerTitle: true,
          title: Transform.translate(
              offset: Offset(-20, 0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                Image.asset(
                  'assets/images/bot_asset.png',
                  width: width(context) * 0.12,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'JUNO',
                        style: Styles.semibold(size: 14),
                      ),
                      Text(
                        'Active',
                        style: Styles.getRegularThemeStyle(context, size: 10),
                      ),
                    ],
                  ),
                ),
              ])),
          elevation: 0.5,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  // Color.fromARGB(120, 146, 187, 227),
                  // Color.fromARGB(255, 28, 115, 142),
                  // themeColor,
                  // themeColor,
                  context.appColors.surface,
                  context.appColors.surface,
                ],
              ),
            ),
          ),
        ),
        body: Stack(
          children: [
            Positioned(
                right: -10,
                top: -150,
                child: Transform.scale(
                  scale: 2,
                  child: SvgPicture.asset(
                    'assets/images/bot_pattern.svg',
                    width: width(context),
                  ),
                )),
            Container(
              child: Column(
                mainAxisAlignment: messages.length == 1
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //chatbot.gif
                  if (messages.length == 1) ...[
                    Spacer(),
                    Image.asset(
                      'assets/images/bot_asset.png',
                      width: width(context) * 0.4,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Hello! ${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}👋\n I am JUNO, your career guide. What career path are you considering?.',
                        // '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['msg']}',
                        style: Styles.getBoldThemeStyle(context),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Spacer(),
                  ] else ...[
                    Expanded(
                        child: MessagesScreen(
                      sendValue: (value) {
                        Preference.setString(Preference.SETUP_GOAL, value);

                        /// sendMessage(value);
                      },
                      messages: messages,
                      blankPage: widget.question == null,
                    )),
                  ],
                  //messages.toString().contains('Thanks for connecting') == true

                  //messages[messages.length-1]['message'].text.text[0] == 'Thanks for connecting' ?
                  messages.toString().contains('Thanks for connecting') == true
                      ? InkWell(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            margin: EdgeInsets.only(bottom: 20),
                            child: Text(
                              'Finish',
                              style: TextStyle(
                                  color: context.appColors.unselectedPage),
                            ),
                          ),
                        )
                      : Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Color(0xff007071),
                                Color(0xff007071),
                              ],
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _controller,
                                  style: TextStyle(
                                      color: context.appColors.textWhite),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: 'Type a message...',
                                    hintStyle: TextStyle(
                                      color: context.appColors.textWhite,
                                      fontFamily: 'Cera Pro',
                                    ),
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  //sendMessage(_controller.text);
                                  _controller.clear();
                                },
                                icon: const Icon(Icons.send_rounded),
                                color: context.appColors.textWhite,
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
          ],
        ));
  }

  // sendMessage(String text) async {
  //   print('Message is $text');
  //   try {
  //     if (text.isEmpty) {
  //     } else {
  //       setState(() {
  //         addMessage(Message(text: DialogText(text: [text])), true);
  //       });
  //
  //       DetectIntentResponse? response = await dialogFlowtter?.detectIntent(
  //         queryInput: QueryInput(
  //           text: TextInput(text: text),
  //         ),
  //       );
  //       if (response?.message == null) return;
  //       setState(() {
  //         addMessage(response!.message!);
  //       });
  //     }
  //   } catch (e) {
  //     print('Error occurred in sendMessage: $e');
  //   }
  // }

  // addMessage(Message message, [bool isUserMessage = false]) {
  //   messages.add({'message': message, 'isUserMessage': isUserMessage});
  // }
}
