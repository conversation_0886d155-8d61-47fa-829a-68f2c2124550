//import 'package:dialog_flowtter/dialog_flowtter.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/gestures.dart';

class MessagesScreen extends StatefulWidget {
  final List messages;
  final bool blankPage;
  final Function? sendValue;
  const MessagesScreen(
      {Key? key,
      required this.messages,
      required this.blankPage,
      this.sendValue})
      : super(key: key);

  @override
  MessagesScreenState createState() => MessagesScreenState();
}

class MessagesScreenState extends State<MessagesScreen> {
  late ScrollController listScrollController = ScrollController();
  scrollToBottom() {
    listScrollController.jumpTo(listScrollController.position.maxScrollExtent);
  }

  Future<void> scrollListToEND() async {
    await Future.delayed(const Duration(milliseconds: 700));
    listScrollController.animateTo(
      listScrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 700),
      curve: Curves.ease,
    );

    print('scrolled list to end');
  }

  String getFormattedTime() {
    DateTime now = DateTime.now();
    String formattedTime = DateFormat.Hms().format(now);
    return formattedTime;
  }

  @override
  void initState() {
    super.initState();
    listScrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    listScrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (listScrollController.offset >=
            listScrollController.position.maxScrollExtent &&
        !listScrollController.position.outOfRange) {}
  }

  String? selectedOption;
  @override
  Widget build(BuildContext context) {
    // String getFormattedDate() {
    //   DateTime now = DateTime.now();
    //   DateTime formattedDate = now;
    //   DateTime tomorrowDate = now.add(const Duration(days: 1));

    //   if (formattedDate == now) {
    //     return 'Today';
    //   } else if (formattedDate == tomorrowDate) {
    //     return 'Tomorrow';
    //   } else {
    //     String formattedDate = DateFormat.yMMMMd().format(now);
    //     return formattedDate;
    //   }
    // }

    // DateTime now = DateTime.now();
    // String formattedTime = DateFormat('h:mm a').format(now);
    // String time = getFormattedTime();
    // String formattedDate = DateFormat.yMMMMd().format(now);

    // var w = MediaQuery.of(context).size.width;
    // var h = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Column(
        children: [
          SizedBox(height: 10),
          Flexible(
            child: ListView.builder(
                controller: listScrollController,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  //Message msg = widget.messages[index]['message'];
                  if (index == widget.messages.length - 1) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      scrollListToEND();
                    });
                  }
                  if (widget.blankPage &&
                      index == 0 &&
                      widget.messages[index]['isUserMessage'])
                    return SizedBox();

                  return Container(
                    margin: const EdgeInsets.all(4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: widget.messages[index]['isUserMessage']
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      children: [
                        // Image.asset(
                        //   'assets/images/chatBot.gif',
                        //   width: width(context) * 0.06,
                        // ),
                        const SizedBox(
                          width: 4,
                        ),
                        // Container(
                        //     padding: EdgeInsets.symmetric(
                        //         vertical: widget.messages[index]
                        //                 ['isUserMessage']
                        //             ? 10
                        //             : 14,
                        //         horizontal: 14),
                        //     decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.only(
                        //           bottomLeft: const Radius.circular(
                        //             20,
                        //           ),
                        //           topRight: const Radius.circular(20),
                        //           bottomRight: Radius.circular(
                        //               widget.messages[index]['isUserMessage']
                        //                   ? 0
                        //                   : 20),
                        //           topLeft: Radius.circular(
                        //               widget.messages[index]['isUserMessage']
                        //                   ? 20
                        //                   : 0),
                        //         ),
                        //         color: widget.messages[index]['isUserMessage']
                        //             ? const Color(0xff9DB2BF)
                        //             : const Color(0xffDDE6ED)),
                        //     constraints: widget.messages[index]['isUserMessage']
                        //         ? null
                        //         : BoxConstraints(maxWidth: w * 2 / 3),
                        //     child: widget.messages[index]['message']?.text
                        //                 ?.text[0] == null
                        //         ? InkWell(
                        //             onTap: () {
                        //               launchUrl(Uri.parse(
                        //                   '${msg.payload?['richContent'][0][0]['actionLink']}'));
                        //             },
                        //             child: SingleChildScrollView(
                        //               child: Column(
                        //                 children: [
                        //                   Text(
                        //                     '${msg.payload?['msg']}',
                        //                     //'Hello! ${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}👋\n I am Rashid, your career guide. What career path are you considering?.',
                        //                     // '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['msg']}',
                        //                     style: Styles.getRegularThemeStyle(context, size: 14),
                        //                     textAlign: TextAlign.center,
                        //                   ),
                        //                   SizedBox(height: 20,),
                        //                   if (msg.payload?['options'] !=
                        //                           null &&
                        //                       msg
                        //                               .payload?['options']
                        //                               .length !=
                        //                           0) ...[
                        //                     SizedBox(
                        //                       child: ListView.builder(
                        //                           physics:
                        //                               NeverScrollableScrollPhysics(),
                        //                           shrinkWrap: true,
                        //                           itemCount: msg
                        //                               .payload?['options']
                        //                               .length,
                        //                           itemBuilder: (c, i) {
                        //                             return InkWell(
                        //                               onTap: () {
                        //                                 /*if (selectedOption != null) {
                        //                                   return;
                        //                                 }*/
                        //                                 setState(() {
                        //                                   selectedOption =
                        //                                       '${msg.payload?['options'][i]}';
                        //                                 });
                        //                                 widget.sendValue!(
                        //                                     '${msg.payload?['options'][i]}');
                        //                               },
                        //                               child: Container(
                        //                                 decoration: BoxDecoration(
                        //                                     border: selectedOption ==
                        //                                             '${msg.payload?['options'][i]}'
                        //                                         ? Border.all(
                        //                                             color: context.appColors
                        //                                                 .primaryColorbtnAlways())
                        //                                         : null,
                        //                                     color:
                        //                                         context.appColors
                        //                                             .WHITE,
                        //                                     borderRadius:
                        //                                         BorderRadius
                        //                                             .circular(
                        //                                                 4)),
                        //                                 margin: const EdgeInsets
                        //                                         .symmetric(
                        //                                     vertical: 8,
                        //                                     horizontal: 4),
                        //                                 padding:
                        //                                     const EdgeInsets
                        //                                             .symmetric(
                        //                                         vertical: 8,
                        //                                         horizontal: 8),
                        //                                 child: Row(
                        //                                     mainAxisAlignment:
                        //                                         MainAxisAlignment
                        //                                             .spaceBetween,
                        //                                     children: [
                        //                                       Text(
                        //                                         '${msg.payload?['options'][i]}',
                        //                                         style: Styles
                        //                                             .regular(
                        //                                                 size:
                        //                                                     12),
                        //                                       ),
                        //                                       Icon(
                        //                                         Icons
                        //                                             .north_east,
                        //                                         size: 12,
                        //                                       )
                        //                                     ]),
                        //                               ),
                        //                             );
                        //                           }),
                        //                     ),
                        //                   ] else ...[
                        //                     SizedBox(
                        //                       child: Column(
                        //                         children: [
                        //                           ListView.builder(
                        //                             physics:
                        //                                 NeverScrollableScrollPhysics(),
                        //                             shrinkWrap: true,
                        //                             itemCount: msg
                        //                                 .payload?['options'].length,
                        //                             itemBuilder: (c, i) {
                        //                               if (msg.payload?['options'] != null &&
                        //                                   msg.payload?['options'].length > i) {
                        //                                 return InkWell(
                        //                                   onTap: () {
                        //                                     if (selectedOption !=
                        //                                         null) {
                        //                                       return;
                        //                                     }
                        //                                     setState(() {
                        //                                       selectedOption =
                        //                                           '${msg.payload?['options'][i]}';
                        //                                     });
                        //                                     widget.sendValue!(
                        //                                         '${msg.payload?['options'][i]}');
                        //                                   },
                        //                                   child: Container(
                        //                                     decoration: BoxDecoration(
                        //                                         border: selectedOption ==
                        //                                                 '${msg.payload?['options'][i]}'
                        //                                             ? Border.all(
                        //                                                 color: context.appColors
                        //                                                     .primaryColorbtnAlways())
                        //                                             : null,
                        //                                         color:
                        //                                             context.appColors
                        //                                                 .WHITE,
                        //                                         borderRadius:
                        //                                             BorderRadius
                        //                                                 .circular(
                        //                                                     4)),
                        //                                     margin:
                        //                                         const EdgeInsets
                        //                                                 .symmetric(
                        //                                             vertical: 8,
                        //                                             horizontal:
                        //                                                 4),
                        //                                     padding:
                        //                                         const EdgeInsets
                        //                                                 .symmetric(
                        //                                             vertical: 8,
                        //                                             horizontal:
                        //                                                 8),
                        //                                     child: Row(
                        //                                         mainAxisAlignment:
                        //                                             MainAxisAlignment
                        //                                                 .spaceBetween,
                        //                                         children: [
                        //                                           Text(
                        //                                             '${msg.payload?['options'][i]}',
                        //                                             style: Styles
                        //                                                 .regular(
                        //                                                     size:
                        //                                                         12),
                        //                                           ),
                        //                                           Icon(
                        //                                             Icons
                        //                                                 .north_east,
                        //                                             size: 12,
                        //                                           )
                        //                                         ]),
                        //                                   ),
                        //                                 );
                        //                               } else {
                        //                                 return SizedBox();
                        //                               }
                        //                             },
                        //                           ),
                        //                         ],
                        //                       ),
                        //                     ),
                        //                   ],
                        //                   const SizedBox(height: 10),
                        //                   Text(''),
                        //                   SizedBox(
                        //                     height: 20,
                        //                     width: 600,
                        //                     child: Column(
                        //                         crossAxisAlignment:
                        //                             CrossAxisAlignment.start,
                        //                         children: [
                        //                           Row(
                        //                             mainAxisAlignment:
                        //                                 MainAxisAlignment.end,
                        //                             children: [
                        //                               Text(formattedTime,
                        //                                   style: TextStyle(
                        //                                       color:
                        //                                           Colors.grey)),
                        //                             ],
                        //                           )
                        //                         ]),
                        //                   ),
                        //                 ],
                        //               ),
                        //             ))
                        //         : Column(
                        //             crossAxisAlignment:
                        //                 CrossAxisAlignment.start,
                        //             mainAxisAlignment: MainAxisAlignment.start,
                        //             children: [
                        //               if (!widget.messages[index]
                        //                   ['isUserMessage'])
                        //                 Text(
                        //                   'JUNO',
                        //                   style: Styles.bold(
                        //                       size: 14,
                        //                       color: context.appColors
                        //                           .BG_DARK_BLUE_BTN),
                        //                 ),
                        //               SizedBox(height: 4),
                        //               ClickableTextWidget(
                        //                   text:
                        //                       '${widget.messages[index]['message'].text.text[0]}'),
                        //             ],
                        //           )),
                      ],
                    ),
                  );
                },
                itemCount: widget.messages.length),
          ),
        ],
      ),
    );
  }
}

// chipList() {
//   return Wrap(
//     spacing: 6.0,
//     runSpacing: 6.0,
//     children: <Widget>[
//       _buildChip('Gamer', const Color(0xFFff6666)),
//       _buildChip('Hacker', const Color(0xFF007f5c)),
//       _buildChip('Developer', const Color(0xFF5f65d3)),
//       _buildChip('Racer', const Color(0xFF19ca21)),
//       _buildChip('Traveller', const Color(0xFF60230b)),
//     ],
//   );
// }

// Widget _buildChip(String label, Color color) {
//   return Chip(
//     labelPadding: const EdgeInsets.all(2.0),
//     avatar: CircleAvatar(
//       backgroundColor: Colors.white70,
//       child: Text(label[0].toUpperCase()),
//     ),
//     label: Text(
//       label,
//       style: const TextStyle(
//         color: Colors.white,
//       ),
//     ),
//     backgroundColor: color,
//     elevation: 6.0,
//     shadowColor: Colors.grey[60],
//     padding: const EdgeInsets.all(8.0),
//   );
// }

class ClickableLink extends StatelessWidget {
  final String text;
  final String time;
  final bool isUserMessage;

  const ClickableLink(
      {Key? key,
      required this.text,
      required this.isUserMessage,
      required this.time})
      : super(key: key);

  void openLink(String url) {
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "http://$url";
      print('url is hel;lo $url');
    }
    //  launchUrl(Uri.parse(url));

    launchUrl(Uri.parse(url));
  }

  @override
  Widget build(BuildContext context) {
    final pattern = RegExp(r'https?://\S+');
    final matches = pattern.allMatches(text);
    final List<TextSpan> spans = [];

    int start = 0;

    for (final match in matches) {
      final linkText = match.group(0);
      if (linkText != null) {
        final linkStart = match.start;
        final linkEnd = match.end;

        if (start < linkStart) {
          spans.add(TextSpan(text: text.substring(start, linkStart)));
        }

        spans.add(
          TextSpan(
            text: linkText,
            style: const TextStyle(
              color: Colors.blue,
              decoration: TextDecoration.underline,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () =>
                  openLink(linkText.replaceAll(')', '').replaceAll('(', '')),
          ),
        );

        start = linkEnd;
      }
    }

    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }
    //     spans.add(

    //   TextSpan(
    //     text: ' $time', // Include the formatted time here
    //     style: TextStyle(
    //       fontSize: 12,
    //       // fontStyle: FontStyle.italic,
    //     ),
    //   ),
    // );

// Text.rich(
//   TextSpan(
//     children: [
//       TextSpan(text: 'Hello '),
//       TextSpan(
//         text: 'bold',
//         style: TextStyle(fontWeight: FontWeight.bold),
//       ),
//       TextSpan(text: ' world!'),
//     ],
//   ),
// );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isUserMessage)
          Text(
            'Edulyst Venture',
          ),
        const SizedBox(height: 10),
        RichText(
          text: TextSpan(children: spans),
        ),
        const SizedBox(width: 5),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              time,
              style: TextStyle(fontSize: 10, color: Colors.grey),
            ),
            SizedBox(width: 5),
            if (isUserMessage)
              Icon(
                Icons.done_all,
                color: Colors.blue,
                size: 18,
              ),
          ],
        ),
      ],
    );
  }
}
