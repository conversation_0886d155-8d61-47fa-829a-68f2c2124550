// import 'dart:developer';

//import 'package:dialog_flowtter/dialog_flowtter.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';

// class BotScreen extends StatefulWidget {
//   final String? question;
//   final bool fromDashboard;
//   final List<Menu>? menuList;

//   const BotScreen(
//       {Key? key, this.question, required this.fromDashboard, this.menuList})
//       : super(key: key);

//   @override
//   _BotScreenState createState() => _BotScreenState();
// }

// class _BotScreenState extends State<BotScreen>
//     with SingleTickerProviderStateMixin {
//   String? userName;
//   SpeechToText _speechToText = SpeechToText();
//   bool _speechEnabled = false;
//   String _lastWords = '';
//   List<String> stopperWords = [
//     "a's",
//     "able",
//     "about",
//     "above",
//     "according",
//     "accordingly",
//     "across",
//     "actually",
//     "after",
//     "afterwards",
//     "again",
//     "against",
//     "ain't",
//     "all",
//     "allow",
//     "allows",
//     "almost",
//     "alone",
//     "along",
//     "already",
//     "also",
//     "although",
//     "always",
//     "am",
//     "among",
//     "amongst",
//     "an",
//     "and",
//     "another",
//     "any",
//     "anybody",
//     "anyhow",
//     "anyone",
//     "anything",
//     "anyway",
//     "anyways",
//     "anywhere",
//     "apart",
//     "appear",
//     "appreciate",
//     "appropriate",
//     "are",
//     "aren't",
//     "around",
//     "as",
//     "aside",
//     "ask",
//     "asking",
//     "associated",
//     "at",
//     "available",
//     "away",
//     "awfully",
//     "be",
//     "became",
//     "because",
//     "become",
//     "becomes",
//     "becoming",
//     "been",
//     "before",
//     "beforehand",
//     "behind",
//     "being",
//     "believe",
//     "below",
//     "beside",
//     "besides",
//     "best",
//     "better",
//     "between",
//     "beyond",
//     "both",
//     "brief",
//     "but",
//     "by",
//     "c'mon",
//     "c's",
//     "came",
//     "can",
//     "can't",
//     "cannot",
//     "cant",
//     "cause",
//     "causes",
//     "certain",
//     "certainly",
//     "changes",
//     "clearly",
//     "co",
//     "com",
//     "come",
//     "comes",
//     "concerning",
//     "consequently",
//     "consider",
//     "considering",
//     "contain",
//     "containing",
//     "contains",
//     "corresponding",
//     "could",
//     "couldn't",
//     "course",
//     "currently",
//     "definitely",
//     "described",
//     "despite",
//     "did",
//     "didn't",
//     "different",
//     "do",
//     "does",
//     "doesn't",
//     "doing",
//     "don't",
//     "done",
//     "down",
//     "downwards",
//     "during",
//     "each",
//     "edu",
//     "eg",
//     "eight",
//     "either",
//     "else",
//     "elsewhere",
//     "enough",
//     "entirely",
//     "especially",
//     "et",
//     "etc",
//     "even",
//     "ever",
//     "every",
//     "everybody",
//     "everyone",
//     "everything",
//     "everywhere",
//     "ex",
//     "exactly",
//     "example",
//     "except",
//     "far",
//     "few",
//     "fifth",
//     "first",
//     "five",
//     "followed",
//     "following",
//     "follows",
//     "for",
//     "former",
//     "formerly",
//     "forth",
//     "four",
//     "from",
//     "further",
//     "furthermore",
//     "get",
//     "gets",
//     "getting",
//     "given",
//     "gives",
//     "go",
//     "goes",
//     "going",
//     "gone",
//     "got",
//     "gotten",
//     "greetings",
//     "had",
//     "hadn't",
//     "happens",
//     "hardly",
//     "has",
//     "hasn't",
//     "have",
//     "haven't",
//     "having",
//     "he",
//     "he's",
//     "hello",
//     "help",
//     "hence",
//     "her",
//     "here",
//     "here's",
//     "hereafter",
//     "hereby",
//     "herein",
//     "hereupon",
//     "hers",
//     "herself",
//     "hi",
//     "him",
//     "himself",
//     "his",
//     "hither",
//     "hopefully",
//     "how",
//     "howbeit",
//     "however",
//     "i'd",
//     "i'll",
//     "i'm",
//     "i've",
//     "ie",
//     "if",
//     "ignored",
//     "immediate",
//     "in",
//     "inasmuch",
//     "inc",
//     "indeed",
//     "indicate",
//     "indicated",
//     "indicates",
//     "inner",
//     "insofar",
//     "instead",
//     "into",
//     "inward",
//     "is",
//     "isn't",
//     "it",
//     "it'd",
//     "it'll",
//     "it's",
//     "its",
//     "itself",
//     "just",
//     "keep",
//     "keeps",
//     "kept",
//     "know",
//     "known",
//     "knows",
//     "last",
//     "lately",
//     "later",
//     "latter",
//     "latterly",
//     "least",
//     "less",
//     "lest",
//     "let",
//     "let's",
//     "like",
//     "liked",
//     "likely",
//     "little",
//     "look",
//     "looking",
//     "looks",
//     "ltd",
//     "mainly",
//     "many",
//     "may",
//     "maybe",
//     "me",
//     "mean",
//     "meanwhile",
//     "merely",
//     "might",
//     "more",
//     "moreover",
//     "most",
//     "mostly",
//     "much",
//     "must",
//     "my",
//     "myself",
//     "name",
//     "namely",
//     "nd",
//     "near",
//     "nearly",
//     "necessary",
//     "need",
//     "needs",
//     "neither",
//     "never",
//     "nevertheless",
//     "new",
//     "next",
//     "nine",
//     "no",
//     "nobody",
//     "non",
//     "none",
//     "noone",
//     "nor",
//     "normally",
//     "not",
//     "nothing",
//     "novel",
//     "now",
//     "nowhere",
//     "obviously",
//     "of",
//     "off",
//     "often",
//     "oh",
//     "ok",
//     "okay",
//     "old",
//     "on",
//     "once",
//     "one",
//     "ones",
//     "only",
//     "onto",
//     "or",
//     "other",
//     "others",
//     "otherwise",
//     "ought",
//     "our",
//     "ours",
//     "ourselves",
//     "out",
//     "outside",
//     "over",
//     "overall",
//     "own",
//     "particular",
//     "particularly",
//     "per",
//     "perhaps",
//     "placed",
//     "please",
//     "plus",
//     "possible",
//     "presumably",
//     "probably",
//     "provides",
//     "que",
//     "quite",
//     "qv",
//     "rather",
//     "rd",
//     "re",
//     "really",
//     "reasonably",
//     "regarding",
//     "regardless",
//     "regards",
//     "relatively",
//     "respectively",
//     "right",
//     "said",
//     "same",
//     "saw",
//     "say",
//     "saying",
//     "says",
//     "second",
//     "secondly",
//     "see",
//     "seeing",
//     "seem",
//     "seemed",
//     "seeming",
//     "seems",
//     "seen",
//     "self",
//     "selves",
//     "sensible",
//     "sent",
//     "serious",
//     "seriously",
//     "seven",
//     "several",
//     "shall",
//     "she",
//     "should",
//     "shouldn't",
//     "since",
//     "six",
//     "so",
//     "some",
//     "somebody",
//     "somehow",
//     "someone",
//     "something",
//     "sometime",
//     "sometimes",
//     "somewhat",
//     "somewhere",
//     "soon",
//     "sorry",
//     "specified",
//     "specify",
//     "specifying",
//     "still",
//     "sub",
//     "such",
//     "sup",
//     "sure",
//     "t's",
//     "take",
//     "taken",
//     "tell",
//     "tends",
//     "th",
//     "than",
//     "thank",
//     "thanks",
//     "thanx",
//     "that",
//     "that's",
//     "thats",
//     "the",
//     "their",
//     "theirs",
//     "them",
//     "themselves",
//     "then",
//     "thence",
//     "there",
//     "there's",
//     "thereafter",
//     "thereby",
//     "therefore",
//     "therein",
//     "theres",
//     "thereupon",
//     "these",
//     "they",
//     "they'd",
//     "they'll",
//     "they're",
//     "they've",
//     "think",
//     "third",
//     "this",
//     "thorough",
//     "thoroughly",
//     "those",
//     "though",
//     "three",
//     "through",
//     "throughout",
//     "thru",
//     "thus",
//     "to",
//     "together",
//     "too",
//     "took",
//     "toward",
//     "towards",
//     "tried",
//     "tries",
//     "truly",
//     "try",
//     "trying",
//     "twice",
//     "two",
//     "un",
//     "under",
//     "unfortunately",
//     "unless",
//     "unlikely",
//     "until",
//     "unto",
//     "up",
//     "upon",
//     "us",
//     "use",
//     "used",
//     "useful",
//     "uses",
//     "using",
//     "usually",
//     "value",
//     "various",
//     "very",
//     "via",
//     "viz",
//     "vs",
//     "want",
//     "wants",
//     "was",
//     "wasn't",
//     "way",
//     "we",
//     "we'd",
//     "we'll",
//     "we're",
//     "we've",
//     "welcome",
//     "well",
//     "went",
//     "were",
//     "weren't",
//     "what",
//     "what's",
//     "whatever",
//     "when",
//     "whence",
//     "whenever",
//     "where",
//     "where's",
//     "whereafter",
//     "whereas",
//     "whereby",
//     "wherein",
//     "whereupon",
//     "wherever",
//     "whether",
//     "which",
//     "while",
//     "whither",
//     "who",
//     "who's",
//     "whoever",
//     "whole",
//     "whom",
//     "whose",
//     "why",
//     "will",
//     "willing",
//     "wish",
//     "with",
//     "within",
//     "without",
//     "won't",
//     "wonder",
//     "would",
//     "wouldn't",
//     "yes",
//     "yet",
//     "you",
//     "you'd",
//     "you'll",
//     "you're",
//     "you've",
//     "your",
//     "yours",
//     "yourself",
//     "yourselves",
//     "zero",
//   ];
//   String? foundKey;
//   DialogFlowtter? dialogFlowtter;
//   TextEditingController _controller = TextEditingController();
//   int insertIndex = 1;
//   List<Map<String, dynamic>> messages = [
//     // {
//     //   'type': 'bot',
//     //   'msg':
//     //       'Hello there! 👋\nI\'m Rashid, your career guide.Are you currently studying or working?',
//     // }
//   ];

//   dynamic botQuestionList = {
//     'teen': [
//       {
//         'msg':
//             'Hi ${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}! 👋\nI\'m JUNO, here to guide you in your career journey. What are you aiming for?',
//         'matching_keys': [
//           "Data Analysis",
//           "Machine Learning",
//           "ML",
//           "Artificial Intelligence",
//           "AI",
//           "Data Mining",
//           "Big Data",
//           "Data Visualization",
//           "NLP",
//           "Deep Learning",
//           "Data Engineering",
//           "Data Warehousing",
//           "Text Analytics",
//           "Pattern Recognition",
//           "Recommendation"
//         ],
//         'error': [
//           "We're analyzing global data to align with your goal & aspirations.",
//           "I apologize, but I couldn't locate relevant information at this time.",
//           "Would you like to explore any other areas of interest? Let me know what you're curious about, and I'll be happy to assist you further!",
//         ]
//       },
//       {
//         'arr': [
//           {
//             'msg':
//                 'That\'s fantastic, ${Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)}')}! #key have incredible potential. Let\'s take a look at how widely recognized they are globally.',
//             'graph': 81 //domain id
//           },
//           {
//             'msg':
//                 'Which career path are you considering? Here are some of the most sought-after job roles in the field of data science.',
//             "options": [
//               "Data Scientist",
//               "Data Analyst",
//               "Machine Learning Engineer",
//               "Data Engineer",
//               "Business Intelligence Analyst",
//             ]
//           }
//         ]
//       },
//       {
//         'arr': [
//           // {
//           //   'msg':
//           //       'Great choice, Ajay! Let me give you an overview of what it entails, including responsibilities, earning potential, and educational pathways.',
//           //   'graph': 81, //domain id,
//           //   'detail': 'true'
//           // },
//           {
//             'msg':
//                 'From your profile, I can see that you are a student of 9th grade and excel in subjects like Science, Maths, Computers, English, and Arabic. Do you have any other interests?',
//           },
//         ],
//       },
//       {
//         'msg':
//             'Wonderful! Technology plays a vital role in data science. Let\'s explore how this passion aligns with your aspirations and career objectives.'
//       }
//     ],
//     'young': [
//       {
//         'msg':
//             'Hello Fatima! 👋\nI\'m JUNO, your career guide. What career path are you considering?',
//         'matching_keys': [
//           "Supply Chain Management",
//           "Transportation",
//           "Inventory",
//           "Distribution",
//           "Shipping",
//           "Procurement",
//           "Route Optimization",
//           "Logistics Network",
//           "Logistics"
//         ],
//         'error': [
//           "We're analyzing global data to align with your goal & aspirations.",
//           "I apologize, but I couldn't locate relevant information at this time.",
//           "Would you like to explore any other areas of interest? Let me know what you're curious about, and I'll be happy to assist you further!",
//         ]
//       },
//       {
//         'arr': [
//           {
//             'msg':
//                 'Great choice! Let me show you the global scope of the logistics field.',
//             'graph': 30 //domain id
//           },
//           {
//             'msg':
//                 'I see you\'re currently studying Mechanical Science. Have you considered a career in that field?'
//           },
//         ]
//       },
//       {
//         'arr': [
//           {
//             'msg':
//                 'That\'s understandable. What specific role are you considering within logistics? Here are some popular options:',
//             "options": [
//               "Logistics Manager",
//               "Distribution Manager",
//               "Inventory Control Manager",
//               "Route Planner",
//               "Logistics Engineer",
//             ]
//           },
//         ]
//       },
//       {
//         'msg':
//             'Excellent! As a Supply Chain Analyst, you\'ll be involved in analyzing engineering needs, forecasting, and planning requirements within the manufacturing and logistics sectors.'
//       }
//     ],
//     'working': [
//       {
//         'msg':
//             'Hello Ahmed! 👋\nI\'m JUNO, your career guide. What are your career aspirations?',
//         'matching_keys': [
//           "tech",
//           "technology",
//           "IT",
//           "Computer",
//           "Software",
//           "upskill"
//         ],
//         'error': [
//           "We're analyzing global data to align with your goal & aspirations.",
//           "I apologize, but I couldn't locate relevant information at this time.",
//           "Would you like to explore any other areas of interest? Let me know what you're curious about, and I'll be happy to assist you further!",
//         ]
//       },
//       {
//         'arr': [
//           {
//             'msg':
//                 'That\'s ambitious! Let\'s explore the path to achieving that goal.',
//             'graph': 9 //domain id
//           },
//           {
//             'msg':
//                 'I noticed you\'re currently working as a software developer. Have you considered advancing into a leadership role within the technology sector?',
//           },
//         ]
//       },
//       {
//         'arr': [
//           {
//             'msg':
//                 " That's a great step forward! What specific role are you aiming for as a technology leader? Here are some options:",
//             "options": [
//               "Technology Manager",
//               "IT Director",
//               "Chief Technology Officer (CTO)",
//               "Engineering Manager",
//               "Product Manager",
//               "Technical Lead",
//             ]
//           }
//         ]
//       },
//       {
//         'msg':
//             'Excellent choice! As a Chief Technology Officer (CTO), you\'ll be responsible for setting the technical vision of your organization and leading its technological development. Let\'s explore how you can prepare for this exciting career path.'
//       },
//     ],
//   };

//   Color themeColor = Color.fromARGB(255, 190, 183, 249);
//   late AnimationController _animationController;
//   late Animation<Color?> _colorAnimation;

//   @override
//   void initState() {
//     userName = Preference.getString(Preference.USERNAME);
//     messages.add({
//       'type': 'bot',
//       'msg':
//           '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['msg']}',
//       'detail':
//           '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['detail']}'
//     });
//     _animationController = AnimationController(
//       vsync: this,
//       duration: Duration(seconds: 1), // Set the duration of the animation
//     );

//     // Define a tween that interpolates between two colors
//     _colorAnimation = ColorTween(
//       begin: Colors.red.withValues(alpha:0.3), // Starting color
//       end: context.appColors.error, // Ending color
//     ).animate(_animationController);
//     _stopListening();
//     // Start the animation
//     _animationController.repeat(reverse: true);

//     //handleSendMsg('AI');

//     super.initState();
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     _stopListening();
//     super.dispose();
//   }

//   /// This has to happen only once per app
//   void _initSpeech() async {
//     try {
//      debugPrint('something wrong start');
//       _speechEnabled = await _speechToText.initialize();
//     } catch (e) {
//      debugPrint('something wrong $e');
//     }
//     _startListening();
//     setState(() {});
//   }

//   /// Each time to start a speech recognition session
//   void _startListening() async {
//     if (!_speechEnabled) {
//       _initSpeech();
//     } else {
//       await _speechToText.listen(
//         onResult: _onSpeechResult,
//         listenFor: Duration(seconds: 30), // Listen for 30 seconds
//         pauseFor: Duration(seconds: 2),
//       );
//       setState(() {});
//     }
//   }

//   /// Manually stop the active speech recognition session
//   /// Note that there are also timeouts that each platform enforces
//   /// and the SpeechToText plugin supports setting timeouts on the
//   /// listen method.
//   void _stopListening() async {
//     await _speechToText.stop();
//     setState(() {});
//   }

//   /// This is the callback that the SpeechToText plugin calls when
//   /// the platform returns recognized words.
//   void _onSpeechResult(SpeechRecognitionResult result) {
//     setState(() {
//       _lastWords = result.recognizedWords;
//       _controller = TextEditingController(text: _lastWords);
//     });

//     if (result.finalResult) {
//       setState(() {
//         _lastWords = '';
//         _stopListening();
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       appBar: AppBar(
//         automaticallyImplyLeading: widget.fromDashboard,
//         titleTextStyle: Styles.getBoldThemeStyle(context),
//         iconTheme: IconThemeData(color: context.appColors.textBlack),
//         title: Transform.translate(
//           offset: Offset(-20, 0),
//           child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
//             Image.asset(
//               'assets/images/bot_asset.png',
//               width: width(context) * 0.12,
//             ),
//             Padding(
//               padding: const EdgeInsets.only(left: 8.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'JUNO',
//                     style: Styles.getSemiboldThemeStyle(context, size: 14),
//                   ),
//                   Text(
//                     'Active',
//                     style: Styles.getRegularThemeStyle(context,size: 10),
//                   ),
//                 ],
//               ),
//             ),
//             Spacer(),
//             if (!widget.fromDashboard)
//               InkWell(
//                   onTap: () {
//                     if (widget.fromDashboard) {
//                       Navigator.pop(context);
//                     } else {
//                       Navigator.pushAndRemoveUntil(
//                           context,
//                           NextPageRoute(
//                               HomePage(
//                                 bottomMenu: widget.menuList,
//                               ),
//                               isMaintainState: true),
//                           (route) => false);
//                     }
//                   },
//                   child: Text(
//                     'Skip',
//                     style: Styles.getRegularThemeStyle(context),
//                   )),
//           ]),
//         ),
//         elevation: 0.5,
//         flexibleSpace: Container(
//           decoration: const BoxDecoration(
//             gradient: LinearGradient(
//               begin: Alignment.topCenter,
//               end: Alignment.bottomCenter,
//               colors: [
//                 // Color.fromARGB(120, 146, 187, 227),
//                 // Color.fromARGB(255, 28, 115, 142),
//                 // themeColor,
//                 // themeColor,
//                 Colors.white,
//                 Colors.white,
//               ],
//             ),
//           ),
//         ),
//       ),
//       body: Stack(
//         children: [
//           Positioned(
//               right: -10,
//               top: -150,
//               child: Transform.scale(
//                 scale: 2,
//                 child: SvgPicture.asset(
//                   'assets/images/bot_pattern.svg',
//                   width: width(context),
//                 ),
//               )),
//           Container(
//             width: width(context),
//             child: Column(
//               mainAxisAlignment: messages.length == 1
//                   ? MainAxisAlignment.center
//                   : MainAxisAlignment.start,
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 if (messages.length == 1) ...[
//                   Spacer(),
//                   Image.asset(
//                     'assets/images/bot_asset.png',
//                     width: width(context) * 0.4,
//                   ),
//                   Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Text(
//                       '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][0]['msg']}',
//                       style: Styles.getBoldThemeStyle(context),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   Spacer(),
//                 ] else ...[
//                   Expanded(
//                       child: BotMessagesScreen(
//                     name: foundKey,
//                     messages: messages,
//                     sendValue: (value) {
//                       Preference.setString(Preference.SETUP_GOAL, value);
//                       sendMsg(value);
//                     },
//                   )),
//                 ],
//                 botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//                             .length <=
//                         insertIndex
//                     ? InkWell(
//                         onTap: () {
//                           if (widget.fromDashboard) {
//                             Navigator.of(context).pop();
//                           } else {
//                             Navigator.pushAndRemoveUntil(
//                                 context,
//                                 NextPageRoute(
//                                     HomePage(
//                                       bottomMenu: widget.menuList,
//                                     ),
//                                     isMaintainState: true),
//                                 (route) => false);
//                           }
//                         },
//                         child: Container(
//                           margin: const EdgeInsets.symmetric(
//                               vertical: 26, horizontal: 40),
//                           padding: const EdgeInsets.symmetric(
//                               vertical: 10, horizontal: 20),
//                           decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(40),
//                               color: const Color(0xff007071)),
//                           child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 Text(
//                                   'Explore your learning Roadmap',
//                                   style: Styles.semiBoldWhite(),
//                                 ),
//                                 SizedBox(width: 10),
//                                 Icon(
//                                   Icons.arrow_forward,
//                                   color: context.appColors.white,
//                                 )
//                               ]),
//                         ),
//                       )
//                     : Container(
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 8.0, vertical: 16),
//                         child: SizedBox(
//                           child: Row(
//                             children: [
//                               Container(
//                                 height: 45,
//                                 width: width(context) * 0.76,
//                                 padding: const EdgeInsets.all(8),
//                                 decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(6),
//                                   border: Border.all(color: Color(0xffCED4E7)),
//                                   color: context.appColors.white,
//                                 ),
//                                 child: Row(
//                                   //crossAxisAlignment: CrossAxisAlignment.center,
//                                   children: [
//                                     Expanded(
//                                       child: Center(
//                                         child: TextField(
//                                           onTap: () {
//                                             _lastWords = '';
//                                           },
//                                           controller: _controller,
//                                           style: const TextStyle(
//                                             color: Colors.black,
//                                           ),
//                                           decoration: const InputDecoration(
//                                             contentPadding:
//                                                 EdgeInsets.symmetric(
//                                                     horizontal: 4,
//                                                     vertical:
//                                                         9), // Remove padding
//                                             border: InputBorder.none,
//                                             hintText: 'Type a message...',
//                                             hintStyle: TextStyle(
//                                               color: Colors.black,
//                                               fontFamily: 'Cera Pro',
//                                             ),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     AnimatedBuilder(
//                                       animation: _animationController,
//                                       builder: (context, child) {
//                                         // Interpolate color between blue and red based on the animation value
//                                         Color color = Color.lerp(
//                                           Colors.red.withValues(alpha:0.3),
//                                           context.appColors.error,
//                                           _animationController.value,
//                                         )!;

//                                         return IconButton(
//                                           padding: const EdgeInsets.all(0),
//                                           icon: Icon(
//                                             Icons.mic,
//                                             color: _speechToText.isNotListening
//                                                 ? null
//                                                 : color,
//                                           ),
//                                           onPressed:
//                                               _speechToText.isNotListening
//                                                   ? _startListening
//                                                   : _stopListening,
//                                         );
//                                       },
//                                     )
//                                   ],
//                                 ),
//                               ),

//                               // Container(
//                               //   height: 50,
//                               //   width: width(context) * 0.78,
//                               //   padding: const EdgeInsets.all(8),
//                               //   // margin: EdgeInsets.all(8),
//                               //   decoration: BoxDecoration(
//                               //     borderRadius: BorderRadius.circular(10),
//                               //     border: Border.all(color: Color(0xffCED4E7)),
//                               //     color: context.appColors.white,
//                               //   ),
//                               //   child: TextField(
//                               //     controller: _controller,
//                               //     style: const TextStyle(color: Colors.black),
//                               //     decoration: const InputDecoration(
//                               //       border: InputBorder.none,
//                               //       hintText: 'Type a message...',
//                               //       hintStyle: TextStyle(
//                               //         color: Colors.black,
//                               //         // Color(0xffC2C2C2),
//                               //         fontFamily: 'Cera Pro',
//                               //       ),
//                               //     ),
//                               //   ),
//                               // ),

//                               Container(
//                                 height: 45,
//                                 width: 55,
//                                 margin: EdgeInsets.all(8),
//                                 decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(6),
//                                   border: Border.all(color: Color(0xffCED4E7)),
//                                   color: Color(0xff007071),
//                                 ),
//                                 child: IconButton(
//                                   onPressed: () async {
//                                     if (_controller.text == '') {
//                                       return;
//                                     }
//                                     handleSendMsg(_controller.text);
//                                   },
//                                   icon: const Icon(Icons.send_rounded),
//                                   color: Colors.white,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> handleSendMsg(String msg) async {
//     List<String> matching_keys = [];
//     try {
//       matching_keys = botQuestionList[
//               '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//           [insertIndex - 1]['matching_keys'];
//     } catch (e) {}

//     if (matching_keys.length != 0) {
//       foundKey = getContextWord(msg, stopperWords, matching_keys);

//       if (foundKey == null || foundKey == '') {
//         setState(() {
//           messages.add({
//             'type': 'user',
//             'msg': msg,
//           });
//         });

//         //get the context of msg
//         _controller.clear();

//         for (int i = 0;
//             i <
//                 botQuestionList[
//                             '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//                         [insertIndex - 1]['error']
//                     .length;
//             i++) {
//           await Future.delayed(Duration(seconds: 1));
//           setState(() {
//             messages.add({
//               'type': 'bot',
//               'msg': botQuestionList[
//                       '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//                   [insertIndex - 1]['error'][i],
//             });
//           });
//         }
//       } else
//         sendMsg(msg);
//     } else {
//       sendMsg(msg);
//     }
//   }

//   String? getContextWord(
//       String query, List<String> stopperWords, List<String> matchingKeys) {
//     List<String> words = query.split(" ");
//     List<String> cleanedWords = [];

//     for (String word in words) {
//       if (!stopperWords.contains(word)) {
//         cleanedWords.add(word);
//       }
//     }
//     List<String> finalWords = [];
//     for (int i = 0; i < cleanedWords.length - 1; i++) {
//       finalWords.add("${cleanedWords[i]} ${cleanedWords[i + 1]}");
//     }
//     finalWords.addAll(cleanedWords);

//     String foundKey = "";
//     for (String s in finalWords) {
//       if (matchingKeys.any((key) {
//         if (key.toLowerCase() == s.toLowerCase()) {
//           foundKey = key;
//           return true;
//         }
//         return false;
//       })) {
//         return foundKey;
//       }
//     }

//     return null;
//   }

//   void sendMsg(String str) async {
//     setState(() {
//       messages.add({
//         'type': 'user',
//         'msg': str,
//       });
//     });

//     //get the context of msg

//     _controller.clear();
//     await Future.delayed(Duration(seconds: 1));
//     try {
//       if (botQuestionList[
//                   '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//               [insertIndex]['arr'] !=
//           null) {
//         dynamic list = botQuestionList[
//                 '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//             [insertIndex]['arr'];
//         for (int i = 0; i < list.length; i++) {
//           setState(() {
//             messages.add({
//               'type': 'bot',
//               'msg': '${list[i]['msg']}'.replaceAll("#key", foundKey ?? ""),
//               "options": list[i]['options'],
//               "graph": list[i]['graph'],
//               'detail': list[i]['detail']
//             });
//           });
//           await Future.delayed(Duration(seconds: 3));
//         }
//       } else {
//         setState(() {
//           messages.add({
//             'type': 'bot',
//             'msg':
//                 '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][insertIndex]['msg']}',
//             "options": botQuestionList[
//                     '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//                 [insertIndex]['options'],
//             "graph": botQuestionList[
//                     '${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}']
//                 [insertIndex]['graph'],
//             'detail':
//                 '${botQuestionList['${Preference.getString(Preference.AGE_GROUP).toString().toLowerCase()}'][insertIndex]['detail']}'
//           });
//         });
//       }
//     } catch (e, stacktrace) {
//       log("Wheil adding $stacktrace");
//     }
//     insertIndex++;
//   }
//   // sendMessage(String text) async {
//   //   if (text.isEmpty) {
//   //     //debugPrint('Message is empty');
//   //   } else {
//   //     setState(() {
//   //       messages.add(text);
//   //     });
//   //   }
//   // }

//   addMessage(Message message, [bool isUserMessage = false]) {
//     // messages.add({'message': message, 'isUserMessage': isUserMessage});
//   }
// }
