import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/bots/faq_question_list.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class BotFAQPage extends StatelessWidget {
  const BotFAQPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: context.backgroundColor,
          appBar: AppBar(
            iconTheme: IconThemeData(color: context.headingTextColor),
            titleTextStyle: Styles.getBoldThemeStyle(context),
            elevation: 0,
            backgroundColor: context.backgroundColor,
            title: Text('FAQs'),
          ),
          body: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FAQContainer(
                  title: 'How to use WOW',
                  icon: Icons.info,
                  bgColor: context.gradientColors.first,
                  onPressed: () {
                    Navigator.push(context, NextPageRoute(FAQQuestionPage()));
                    // Add the action when the first container is clicked
                    debugPrint('How to use WOW clicked!');
                  },
                ),
                SizedBox(width: 16.0),
                FAQContainer(
                  title: 'Customer Support',
                  icon: Icons.support,
                  bgColor: context.successColor,
                  onPressed: () {
                    // Add the action when the second container is clicked
                    debugPrint('Customer Support clicked!');
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class FAQContainer extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;
  final Color bgColor;

  const FAQContainer(
      {super.key,
      required this.title,
      required this.icon,
      required this.onPressed,
      required this.bgColor});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return GestureDetector(
          onTap: onPressed,
          child: Container(
            width: 160.0,
            height: 160.0,
            decoration: BoxDecoration(
              color: context.surfaceColor,
              borderRadius: BorderRadius.circular(10.0),
              boxShadow: [
                BoxShadow(
                  color: context.appColors.shadow,
                  spreadRadius: 2,
                  blurRadius: 3,
                  offset: const Offset(1, -1), // changes position of shadow
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    width: 40,
                    height: 40,
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: bgColor, borderRadius: BorderRadius.circular(6)),
                    child: Icon(
                      icon,
                      color: context.backgroundColor,
                      size: 30.0,
                    )),
                const SizedBox(height: 10.0),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: context.headingTextColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
