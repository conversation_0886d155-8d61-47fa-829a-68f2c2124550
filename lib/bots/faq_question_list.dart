import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/bots/bot.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class FAQQuestionPage extends StatelessWidget {
  final List<String> questions = [
    'How to reset password?',
    'How to delete account?',
    'How to enroll in a course?',
    'How to contact customer support?',
    'How to update personal information?',
    'How to change email address?',
  ];

  FAQQuestionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: context.backgroundColor,
          floatingActionButton: FloatingActionButton(
              backgroundColor: context.highlightCard1Color,
              child: Icon(Icons.chat_outlined),
              onPressed: () {
                // Navigator.push(context, NextPageRoute(Bot()));
              }),
          appBar: AppBar(
            iconTheme: IconThemeData(color: context.headingTextColor),
            titleTextStyle: Styles.getBoldThemeStyle(context),
            elevation: 0,
            backgroundColor: context.backgroundColor,
            title: Text(
              'How to use WOW',
              style: Styles.getBoldThemeStyle(context),
            ),
          ),
          body: ListView.builder(
            itemCount: questions.length,
            itemBuilder: (context, index) {
              return FAQItem(
                question: questions[index],
                onPressed: () {
                  // Handle the action when a question is clicked
                  print('Question clicked: ${questions[index]}');
                  Navigator.push(
                      context,
                      NextPageRoute(Bot(
                        question: questions[index],
                      )));
                },
              );
            },
          ),
        );
      },
    );
  }
}

class FAQItem extends StatelessWidget {
  final String question;
  final VoidCallback onPressed;

  const FAQItem({super.key, required this.question, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return InkWell(
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: context.dividerColor),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  question,
                  style: Styles.getRegularThemeStyle(context, size: 16),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: context.appColors.grey3,
                  size: 15,
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
