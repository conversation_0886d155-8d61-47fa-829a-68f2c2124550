import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shimmer/shimmer.dart';
import '../../blocs/bloc_manager.dart';
import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../data/models/response/home_response/competition_response.dart';
import '../../utils/styles.dart';
import '../../utils/constant.dart';
import '../../utils/str_to_time.dart';
import '../../utils/utility.dart';
import '../custom_pages/screen_with_loader.dart';
import '../singularis/competition/competition_detail.dart';

class EventListingPage extends StatefulWidget {
  const EventListingPage({super.key});

  @override
  State<EventListingPage> createState() => _EventListingPageState();
}

class _EventListingPageState extends State<EventListingPage>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;
  bool? competitionLoading;
  CompetitionResponse? competitionResponse;

  @override
  void initState() {
    getCompetitionList(false, '', competitionType: 'event');

    //for live text animation
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    );

    super.initState();
  }

  void getCompetitionList(bool isFilter, String? ids,
      {String? competitionType, bool? callMyActivity}) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionListEvent(
        isPopular: false,
        isFilter: isFilter,
        ids: ids,
        competitionType: competitionType,
        callMyActivity: false));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is CompetitionListState) {
                _handleCompetitionListResponse(state);
              }
            },
            child: Scaffold(
              appBar: AppBar(
                iconTheme: IconThemeData(
                  color: context.appColors.textBlack,
                ),
                elevation: 0.0,
                backgroundColor: context.appColors.surface,
                title: Text('events',
                        style: TextStyle(color: context.appColors.textBlack))
                    .tr(),
              ),
              backgroundColor: context.appColors.jobBackground,
              body: ScreenWithLoader(
                isLoading: competitionLoading,
                body: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: _content(),
                ),
              ),
            )));
  }

  Container _content() {
    List<Competition?>? filterEventList = competitionResponse?.event;
    return Container(
        color: context.appColors.surface,
        //height: MediaQuery.of(context).size.height * 0.735,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            if (competitionResponse != null &&
                competitionResponse?.event?.length != 0)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    competitionLoading == false
                        ? filterEventList != null && filterEventList.isNotEmpty
                            ? ListView.builder(
                                shrinkWrap: true,
                                physics: BouncingScrollPhysics(),
                                itemCount: filterEventList.length,
                                itemBuilder: (BuildContext context, int index) {
                                  //if(filterEventList?[index]?.progressStatus?.toLowerCase() != 'past') {
                                  return InkWell(
                                      onTap: () {
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    CompetitionDetail(
                                                        competitionId:
                                                            competitionResponse
                                                                ?.event?[index]
                                                                ?.id,
                                                        isEvent: true))).then(
                                            (value) {
                                          //topScoringUser();
                                          // getCompetitionList(false, '');
                                          //getDomainList();
                                        });
                                      },
                                      child: renderCompetitionCard(
                                          filterEventList[index]?.image ?? '',
                                          filterEventList[index]?.name ?? '',
                                          filterEventList[index]?.organizedBy ??
                                              '',
                                          '${filterEventList[index]?.competitionLevel}',
                                          '${filterEventList[index]?.gScore ?? 0}',
                                          '${filterEventList[index]?.startDate}',
                                          '${filterEventList[index]?.endDate}',
                                          '${filterEventList[index]?.progressStatus}',
                                          true,
                                          '${filterEventList[index]?.location}'));
                                })
                            : Container(
                                height: height(context) * 0.1,
                                color: context.appColors.surface,
                                width: double.infinity,
                                child: Center(
                                    child: Text(
                                  'no_event_found',
                                  style: Styles.getRegularThemeStyle(context,
                                      size: 14),
                                ).tr()))
                        : Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            enabled: true,
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemBuilder: (_, __) => Container(
                                padding: EdgeInsets.symmetric(vertical: 8),
                                width: MediaQuery.of(context).size.width,
                                child: Container(
                                  decoration: BoxDecoration(
                                      color: context.appColors.surface,
                                      borderRadius: BorderRadius.circular(8)),
                                  width: double.infinity,
                                  height: 80,
                                ),
                              ),
                              itemCount: 2,
                            ),
                          ),
                  ],
                ),
              ),
            //event end
          ],
        ));
  }

  Stack renderCompetitionCard(
      String? competitionImg,
      String? name,
      String? companyName,
      String? difficulty,
      String? gScore,
      String? startdate,
      String? endDate,
      String? progressStatus,
      bool enableProgressStatus,
      String? location) {
    return Stack(
      children: [
        Container(
          //height: 116,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: context.appColors.surface,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 2),
              ),
            ],
          ),
          child: Row(children: [
            SizedBox(
              width: width(context) * 0.21,
              height: height(context) * 0.10,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: competitionImg ?? '',
                  errorWidget: (context, url, error) => Image.asset(
                    'assets/images/comp_emp.png',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SizedBox(
                    width: width(context) * 0.6,
                    child: Text(
                      name ?? '',
                      style: Styles.bold(
                        color: context.appColors.headingTitle,
                        size: 16,
                        lineHeight: 1,
                      ),
                      maxLines: 1,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(
                    height: 2,
                  ),
                  if (companyName != '')
                    SizedBox(
                      width: width(context) * 0.4,
                      child: Text(
                        companyName ?? '',
                        style: Styles.semibold(
                            size: 13, color: context.appColors.grey3),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  enableProgressStatus == true
                      ? SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 2.0),
                          child: Row(
                            children: [
                              Text(difficulty ?? '',
                                  style: Styles.regular(
                                      color: context.appColors.green,
                                      size: 12)),
                              SizedBox(
                                width: 4,
                              ),
                              Text('•',
                                  style: Styles.regular(
                                      color: context.appColors.grey2,
                                      size: 12)),
                              SizedBox(
                                width: 4,
                              ),
                              SizedBox(
                                  height: 15,
                                  child: Image.asset('assets/images/coin.png')),
                              SizedBox(
                                width: 4,
                              ),
                              Text('$gScore ${tr('points')}',
                                  style: Styles.regular(
                                      color: context.appColors.orange4,
                                      size: 12)),

                              /*Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Text('$progressStatus',
                              style: Styles.regular(
                                  color: context.appColors.error, size: 15)),
                        ),*/
                            ],
                          ),
                        ),

                  //TODO: Show Live
                  enableProgressStatus == true
                      ? progressStatus != 'null' &&
                              progressStatus?.toLowerCase() == 'live'
                          ? Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    'assets/images/live_icon.svg',
                                    fit: BoxFit.fitHeight,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5.0),
                                    child: progressStatus?.toLowerCase() ==
                                            'live'
                                        ? FadeTransition(
                                            opacity: _animation,
                                            child: Text(
                                              'live',
                                              style: Styles.bold(
                                                  color:
                                                      context.appColors.error,
                                                  size: 16),
                                            ).tr(),
                                          )
                                        : Text('$progressStatus',
                                            style: Styles.bold(
                                                color: context.appColors.error,
                                                size: 16)),
                                  )
                                ],
                              ),
                            )
                          : SizedBox()
                      : SizedBox(),

                  //TODO: Show Location
                  location != 'null'
                      ? Padding(
                          padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.location_on,
                                color: context.appColors.bodyText,
                                size: 16,
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              Text(location ?? '',
                                  style: Styles.regular(
                                      size: 12,
                                      lineHeight: 1,
                                      color: context.appColors.bodyText)),
                            ],
                          ),
                        )
                      : SizedBox(),

                  //TODO:Show Date
                  enableProgressStatus == true &&
                          progressStatus?.toLowerCase() == 'live'
                      ? SizedBox()
                      : Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.calendar_month,
                              color: context.appColors.bodyText,
                              size: 16,
                            ),
                            SizedBox(
                              width: 3,
                            ),
                            StrToTime(
                              time: startdate!,
                              dateFormat: ' dd-MMM-yy ',
                              appendString:
                                  Utility().isRTL(context) ? '' : tr('to'),
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: context.appColors.bodyText),
                            ),
                            StrToTime(
                              time: endDate!,
                              dateFormat: ' dd-MMM-yy ',
                              appendString:
                                  Utility().isRTL(context) ? tr('to') : '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: context.appColors.textBlack),
                            ),
                          ],
                        ),

                  //TODO: Show Time
                  enableProgressStatus == true &&
                          progressStatus?.toLowerCase() == 'live'
                      ? SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                color: context.appColors.bodyText,
                                size: 16,
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              StrToTime(
                                time: startdate!,
                                dateFormat: ' hh:mm a ',
                                appendString:
                                    Utility().isRTL(context) ? '' : tr('to'),
                                textStyle: Styles.regular(
                                    size: 12,
                                    lineHeight: 1,
                                    color: context.appColors.bodyText),
                              ),
                              StrToTime(
                                time: endDate!,
                                dateFormat: ' hh:mm a ',
                                appendString:
                                    Utility().isRTL(context) ? tr('to') : '',
                                textStyle: Styles.regular(
                                    size: 12,
                                    lineHeight: 1,
                                    color: context.appColors.bodyText),
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
          ]),
        ),
        Positioned(
            right: Utility().isRTL(context) ? null : 12,
            left: Utility().isRTL(context) ? 2 : null,
            top: 10,
            bottom: 10,
            child: Icon((Icons.arrow_forward_ios), size: 20)),
      ],
    );
  }

  void _handleCompetitionListResponse(CompetitionListState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          competitionLoading = true;

          break;
        case ApiStatus.SUCCESS:
          competitionResponse = state.competitonResponse;
          competitionLoading = false;

          break;
        case ApiStatus.ERROR:
          competitionLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
