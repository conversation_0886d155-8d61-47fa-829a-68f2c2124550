
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';

import '../../local/pref/Preference.dart';
import '../../utils/config.dart';
import '../auth_pages/terms_and_condition_page.dart';

class TimeTablePage extends StatefulWidget {
  const TimeTablePage({super.key});

  @override
  State<TimeTablePage> createState() => _TimeTablePageState();
}

class _TimeTablePageState extends State<TimeTablePage> {
  @override
  Widget build(BuildContext context) {
    return TermsAndCondition(
      url:
      '${APK_DETAILS['time_table_url']}${Preference.getInt(
              Preference.USER_ID)}',
      title: tr('time_table'),
      appBarEnable: false,
    );
  }
}
