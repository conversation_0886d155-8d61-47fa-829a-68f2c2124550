import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:shimmer/shimmer.dart';

class AnalyticsLoader extends StatelessWidget {
  const AnalyticsLoader({super.key, this.direction = Axis.vertical});
  final Axis direction;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        double screenWidth = MediaQuery.of(context).size.width;
        return Container(
          height: 200,
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Shimmer.fromColors(
            baseColor: context.shimmerBaseColor,
            highlightColor: context.shimmerHighlightColor,
            enabled: true,
            child: ListView.separated(
              separatorBuilder: (ctx, index) {
                return Divider(
                  thickness: 1,
                  color: context.dividerColor,
                  height: 1,
                );
              },
              scrollDirection: direction,
              itemBuilder: (_, __) => SizedBox(
                height: 40,
                width: screenWidth,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        height: 25,
                        color: context.surfaceColor,
                      ),
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Expanded(
                      flex: 5,
                      child: Container(
                        height: 25,
                        color: context.surfaceColor,
                      ),
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Expanded(
                      child: Container(
                        height: 25,
                        color: context.surfaceColor,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Container(
                        height: 25,
                        color: context.surfaceColor,
                      ),
                    ),
                  ],
                ),
              ),
              itemCount: 6,
            ),
          ),
        );
      },
    );
  }
}
