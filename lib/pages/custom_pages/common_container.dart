import 'package:flutter/material.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/feedback_page.dart';
import 'package:masterg/pages/notification_list_page.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/resource/images.dart';

class CommonContainer extends StatelessWidget {
  Widget? child, drawerWidget;
  String? title;
  bool? isBackShow = false, isFloatIconVisible, isSkipEnable;
  bool? isLoading;

  Function? onBackPressed = () {};
  void Function()? floatIconTap;
  Function? onSkipClicked = () {};

  Color? bgChildColor;
  IconData? floatIcon;
  String? backImage;
  bool? isNotification;
  Widget? belowTitle;
  bool? isScrollable;
  bool? isContainerHeight;
  bool? scrollReverse;
  bool? isTopPadding;

  bool isDrawerEnable;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  GlobalKey<ScaffoldState>? scafKey;

  Color? bgColor;

  CommonContainer(
      {super.key,
      this.child,
      this.title = "",
      this.isBackShow = false,
      this.isLoading = false,
      this.bgChildColor,
      this.bgColor,
      this.onBackPressed,
      this.isFloatIconVisible = false,
      this.floatIcon,
      this.scafKey,
      this.isDrawerEnable = false,
      this.drawerWidget,
      this.belowTitle,
      this.isSkipEnable = false,
      this.isNotification = false,
      this.onSkipClicked,
      this.isScrollable = false,
      this.isContainerHeight = true,
      this.scrollReverse = false,
      this.isTopPadding = true,
      this.floatIconTap});

  @override
  Widget build(BuildContext context) {
    bgChildColor ??= context.appColors.bgGrey;

    bgColor ??= context.appColors.primaryDark;

    return SafeArea(
      child: Scaffold(
        key: scafKey ?? _scaffoldKey,
        resizeToAvoidBottomInset: false,
        backgroundColor: bgColor,
        drawer: isDrawerEnable ? drawerWidget : Drawer(),
        body: Builder(builder: (context) {
          return isScrollable == true
              ? SingleChildScrollView(
                  reverse: scrollReverse!,
                  physics: !isScrollable!
                      ? NeverScrollableScrollPhysics()
                      : AlwaysScrollableScrollPhysics(),
                  child: isContainerHeight == true
                      ? Container(
                          width: MediaQuery.of(context).size.width,
                          height: MediaQuery.of(context).size.height,
                          decoration: BoxDecoration(
                            color: context.appColors.primaryDark,
                          ),
                          child: ScreenWithLoader(
                            body: Column(
                              children: [
                                Column(
                                  children: [
                                    _getTitleWidget(context),
                                    Container(
                                      child: belowTitle,
                                    )
                                  ],
                                ),
                                Expanded(child: _getMainBody())
                              ],
                            ),
                            isLoading: isLoading,
                          ),
                        )
                      : Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: context.appColors.primaryDark,
                          ),
                          child: ScreenWithLoader(
                            isContainerHeight: isContainerHeight!,
                            body: Column(
                              children: [
                                Column(
                                  children: [
                                    _getTitleWidget(context),
                                    Container(
                                      child: belowTitle,
                                    )
                                  ],
                                ),
                                _getMainBody()
                              ],
                            ),
                            isLoading: isLoading,
                          ),
                        ),
                )
              : Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(
                    color: context.appColors.primaryDark,
                  ),
                  child: ScreenWithLoader(
                    body: Column(
                      children: [
                        Column(
                          children: [
                            _getTitleWidget(context),
                            Container(
                              child: belowTitle,
                            )
                          ],
                        ),
                        Expanded(child: _getMainBody())
                      ],
                    ),
                    isLoading: isLoading,
                  ),
                );
        }),
        floatingActionButton: isFloatIconVisible!
            ? FloatingActionButton(
                onPressed: floatIconTap,
                backgroundColor: context.appColors.primaryDark,
                child: Icon(floatIcon),
              )
            : null,
      ),
    );
  }

  Container _getTitleWidget(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 80,
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: isDrawerEnable == false ? null : EdgeInsets.only(top: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
              onTap: () {
                if (isDrawerEnable != true) {
                  Navigator.pop(context);
                } else {
                  showDialog(
                      context: context,
                      builder: (_) => AlertDialog(
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10.0))),
                            content: Builder(
                              builder: (context) {
                                var height =
                                    MediaQuery.of(context).size.height * 0.55;
                                var width = MediaQuery.of(context).size.width;

                                return SizedBox(
                                  height: height - 20,
                                  width: width - 20,
                                  child: SingleChildScrollView(
                                      child: Column(
                                    children: [
                                      ListTile(
                                        title: const Text('Idea Factory'),
                                        onTap: () {
                                          Navigator.push(
                                                  context,
                                                  NextPageRoute(FeedBackPage(
                                                    isViewAll: true,
                                                  )))
                                              .then((value) =>
                                                  Navigator.pop(context));
                                        },
                                      ),
                                    ],
                                  )),
                                );
                              },
                            ),
                          ));
                }
              },
              child: isBackShow!
                  ? isDrawerEnable
                      ? Image(
                          image: AssetImage(Images.SWAYAM_SPLASH_LOGO),
                          width: 38,
                          height: 38,
                        )
                      : Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                              color: context.appColors.surface,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          child: Icon(
                            Icons.arrow_back_ios_rounded,
                            color: context.appColors.darkBlue,
                          ),
                        )
                  : Container(
                      padding: EdgeInsets.all(10),
                    )),
          Expanded(
            child: Text(
              title!,
              textAlign: TextAlign.center,
              style: Styles.boldWhite(size: 20),
            ),
          ),
          isNotification == true
              ? InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (BuildContext context) =>
                                NotificationListPage()));
                  },
                  child: Container(
                    padding: EdgeInsets.all(isNotification == true ? 0 : 0),
                    child: Visibility(
                      visible: isNotification == true,
                      child: Icon(
                        Icons.notifications,
                        color: context.appColors.textWhite,
                      ),
                    ),
                  ),
                )
              : InkWell(
                  onTap: () {
                    if (onSkipClicked != null) onSkipClicked!();
                  },
                  child: Container(
                    padding: EdgeInsets.all(isSkipEnable == true ? 0 : 20),
                    child: Visibility(
                      visible: false,
                      child: Text(
                        "Skip",
                        style: Styles.lightWhite(size: 16),
                      ),
                    ),
                  ),
                )
        ],
      ),
    );
  }

  Container _getMainBody() {
    return Container(
      margin: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          color: bgChildColor,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(25), topLeft: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.only(top: isTopPadding == true ? 20 : 0),
        child: isLoading == true ? Container() : child,
      ),
    );
  }

  // SizedBox _size({double height = 20, double width = 0}) {
  //   return SizedBox(
  //     height: height,
  //     width: width,
  //   );
  // }
}
