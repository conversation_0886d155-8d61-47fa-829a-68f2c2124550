import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class CustomSliderPage extends StatefulWidget {
  final double sliderWidth;
  final Function sendPercentage;
  final double initialPercentage;

  const CustomSliderPage(
      {super.key,
      required this.sliderWidth,
      required this.sendPercentage,
      required this.initialPercentage});
  @override
  State<CustomSliderPage> createState() => _CustomSliderPageState();
}

class _CustomSliderPageState extends State<CustomSliderPage> {
  double _value = 0.1;
  double _barWidth = 0;
  final double _handlerSize = 15;
  double? _minX;
  double? _maxX;
  int? rank = 1;
  bool isRTL = false;

  @override
  void initState() {
    super.initState();

    _value = widget.initialPercentage;
    _barWidth = widget.sliderWidth;
    _minX = _handlerSize / 2;
    _maxX = _barWidth - _handlerSize / 2;
    rank = (_value * 100).toInt() ~/ 25 + 1;
  }

  void _onDragStart(DragStartDetails details) {
    setState(() {
      RenderBox box = context.findRenderObject() as RenderBox;
      Offset offset = box.globalToLocal(details.globalPosition);
      _updateValue(offset.dx);
    });
  }

  void _onDragUpdate(DragUpdateDetails details) {
    setState(() {
      RenderBox box = context.findRenderObject() as RenderBox;
      Offset offset = box.globalToLocal(details.globalPosition);

      _updateValue(offset.dx);
    });
  }

  void _updateValue(double xPosition) {
    double clampedX = xPosition.clamp(_minX!, _maxX!);
    if (isRTL) clampedX = _maxX! - (clampedX - _minX!);
    setState(() {
      _value = (clampedX - _minX!) / (_maxX! - _minX!);
      rank = (_value * 100).toInt() ~/ 25 + 1;
      widget.sendPercentage((_value * 100).toInt());
    });
  }

  Color? getRankColor(int rank) {
    Color? color;
    switch (rank) {
      case 1:
        color = context.appColors.novoice;
        break;
      case 2:
        color = context.appColors.learner;
        break;
      case 3:
        color = context.appColors.master;
        break;
      case 4:
        color = context.appColors.expert;
        break;
      case 5:
        color = context.appColors.leader;
        break;
    }
    return color;
  }

  @override
  Widget build(BuildContext context) {
    isRTL = Utility().isRTL(context);
    return Center(
      child: Stack(
        children: [
          Transform.translate(
              offset: Offset(_barWidth * 0.0, -20),
              child: Text('novice',
                      style: Styles.semibold(
                          size: 12,
                          color: rank! >= 1
                              ? context.appColors.novoice
                              : context.appColors.grey3))
                  .tr()),
          Transform.translate(
              offset: Offset(
                  isRTL ? -(_barWidth * 0.25 - 20) : _barWidth * 0.25 - 20,
                  -20),
              child: Text('learner',
                      style: Styles.semibold(
                          size: 12,
                          color: rank! >= 2
                              ? context.appColors.learner
                              : context.appColors.grey3))
                  .tr()),
          Transform.translate(
              offset: Offset(
                  isRTL ? -(_barWidth * 0.50 - 20) : _barWidth * 0.50 - 20,
                  -20),
              child: Text('master',
                      style: Styles.semibold(
                          size: 12,
                          color: rank! >= 3
                              ? context.appColors.master
                              : context.appColors.grey3))
                  .tr()),
          Transform.translate(
              offset: Offset(
                  isRTL ? -(_barWidth * 0.75 - 20) : _barWidth * 0.75 - 20,
                  -20),
              child: Text('expert',
                      style: Styles.semibold(
                          size: 12,
                          color: rank! >= 4
                              ? context.appColors.expert
                              : context.appColors.grey3))
                  .tr()),
          Transform.translate(
              offset: Offset(
                  isRTL ? -(_barWidth * 1.0 - 40) : _barWidth * 1.0 - 40, -20),
              child: Text('leader',
                      style: Styles.semibold(
                          size: 12,
                          color: rank! >= 5
                              ? context.appColors.leader
                              : context.appColors.grey3))
                  .tr()),
          GestureDetector(
            onHorizontalDragStart: _onDragStart,
            onHorizontalDragUpdate: _onDragUpdate,
            child: Container(
              width: _barWidth,
              height: _handlerSize,
              decoration: BoxDecoration(
                color: context.appColors.surface,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Stack(
                children: [
                  Container(
                    width: _value * _barWidth,
                    height: _handlerSize,
                    decoration: BoxDecoration(
                      color: getRankColor(rank!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  Transform.translate(
                      offset: Offset(
                          isRTL
                              ? -(_barWidth * (0.25 + 0.035) - 20)
                              : _barWidth * (0.25 + 0.035) - 20,
                          0),
                      child: VerticalDivider(
                        color: Color(0xffECECEC),
                        thickness: 2,
                      )),
                  Transform.translate(
                      offset: Offset(
                          isRTL
                              ? -(_barWidth * (0.50 + 0.035) - 20)
                              : _barWidth * (0.50 + 0.035) - 20,
                          0),
                      child: VerticalDivider(
                        color: Color(0xffECECEC),
                        thickness: 2,
                      )),
                  Transform.translate(
                      offset: Offset(
                          isRTL
                              ? -(_barWidth * (0.75 + 0.035) - 20)
                              : _barWidth * (0.75 + 0.035) - 20,
                          0),
                      child: VerticalDivider(
                        color: Color(0xffECECEC),
                        thickness: 2,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
