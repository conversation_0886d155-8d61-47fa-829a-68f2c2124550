import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';

class CoursesLoader extends StatelessWidget {
  const CoursesLoader({super.key, this.expanded = true});
  final bool expanded;

  @override
  Widget build(BuildContext context) {
    return expanded
        ? SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Column(
              children: [
                Container(
                  height: 67,
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 16.0),
                  child: BlocBuilder<ThemeBloc, ThemeState>(
                    builder: (context, themeState) => Shimmer.fromColors(
                      baseColor: context.appColors.shimmerBase,
                      highlightColor: context.appColors.shimmerHighlight,
                      enabled: true,
                      child: ListView.separated(
                        separatorBuilder: (ctx, index) {
                          return Divider(
                            thickness: 1,
                            color: Color(0x287F89C5),
                            height: 1,
                          );
                        },
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (_, __) => Container(
                          padding:
                              EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                          margin: EdgeInsets.only(right: 16),
                          decoration: BoxDecoration(
                            color: context.surfaceColor,
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                          ),
                          width: 172,
                        ),
                        itemCount: 6,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    color: context.isDarkMode
                        ? Colors.grey[900]
                        : Colors.grey[200],
                  ),
                )
              ],
            ),
          )
        : Container(
            height: 67,
            width: double.infinity,
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
            child: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themeState) => Shimmer.fromColors(
                baseColor: context.appColors.shimmerBase,
                highlightColor: context.appColors.shimmerHighlight,
                enabled: true,
                child: ListView.separated(
                  separatorBuilder: (ctx, index) {
                    return Divider(
                      thickness: 1,
                      color: Color(0x287F89C5),
                      height: 1,
                    );
                  },
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (_, __) => Container(
                    padding: EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                    margin: EdgeInsets.only(right: 16),
                    decoration: BoxDecoration(
                      color: context.surfaceColor,
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    width: 172,
                  ),
                  itemCount: 6,
                ),
              ),
            ),
          );
  }
}
