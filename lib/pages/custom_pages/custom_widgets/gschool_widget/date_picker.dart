import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class Calendar extends StatefulWidget {
  final Function? sendValue;

  const Calendar({super.key, this.sendValue});
  @override
  State<Calendar> createState() => _CalendarState();
}

class _CalendarState extends State<Calendar> {
  DateTime selectedDate = DateTime.now();
  DateTime todayDate = DateTime.now();
  final DateFormat formatter = DateFormat('yyyy-MM-dd');
  //DateTime startDate = DateTime(2022, 6, 1);
  DateTime startDate = DateTime(2023, 1, 1);
  late DateTime endDate;
  int? totalDays;

  late int currentDateSelectedIndex;
  bool isScrolled = false;
  late ScrollController scrollController;
  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];
  List<String> listOfDays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
  ];

  @override
  void initState() {
    currentDateSelectedIndex = selectedDate.difference(startDate).inDays;
    scrollController =
        ScrollController(initialScrollOffset: currentDateSelectedIndex * 70.0);

    endDate = DateTime(2028, 12, 31);
    totalDays = endDate.difference(startDate).inDays + 1;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    currentDateSelectedIndex = selectedDate.difference(startDate).inDays;

    super.didChangeDependencies();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final String formatted = formatter.format(todayDate);
    //string to date
    todayDate = DateTime.parse(formatted);

    currentDateSelectedIndex = selectedDate.difference(startDate).inDays;
    // if (!isScrolled) {
    //   Future.delayed(Duration(milliseconds: 1), () {}).then((value) async {
    //     scrollController.animateTo(
    //       currentDateSelectedIndex * 70.0,
    //       duration: Duration(microseconds: 1),
    //       curve: Curves.easeInOut,
    //     );
    //     widget.sendValue!(selectedDate);
    //   });
    //   isScrolled = true;
    // }

    int dayDiff = DateTime.parse(formatter.format(selectedDate))
        .difference(todayDate)
        .inDays;
    return Column(
      children: [
        Container(
            alignment: Alignment.centerLeft,
            child: Text(
              listOfDays[selectedDate.weekday - 1].toString() +
                  ', ' +
                  selectedDate.day.toString() +
                  ' ' +
                  listOfMonths[selectedDate.month - 1] +
                  ', ' +
                  selectedDate.year.toString(),
              style: Styles.regular(size: 14),
            )),
        Container(
            alignment: Alignment.centerLeft,
            child: Text(
              dayDiff == 0
                  ? tr('lbl_today')
                  : dayDiff == -1
                      ? tr('lbl_yesterday')
                      : dayDiff == 1
                          ? tr('lbl_tomorrow')
                          : dayDiff < -1
                              // ? "${dayDiff.abs()} days ago"
                              ? tr('lbl_days_ago', args: ['${dayDiff.abs()}'])
                              : tr('lbl_val_days', args: ['$dayDiff']),
              // : "$dayDiff days",
              style: Styles.getBoldThemeStyle(context, size: 18),
            )),
        SizedBox(height: 10),
        SizedBox(
            height: 95,
            child: ListView.separated(
              separatorBuilder: (BuildContext context, int index) {
                return SizedBox(width: 10);
              },
              itemCount: totalDays!,
              controller: scrollController,
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext context, int index) {
                return InkWell(
                  onTap: () {
                    setState(() {
                      currentDateSelectedIndex = index;
                      selectedDate = startDate.add(Duration(
                        days: index,
                      ));
                      widget.sendValue!(selectedDate);
                    });
                  },
                  child: Container(
                    height: 80,
                    width: 60,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: currentDateSelectedIndex == index
                          ? context.appColors.primary
                          : Colors.transparent,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          listOfMonths[
                                  startDate.add(Duration(days: index)).month -
                                      1]
                              .toString()
                              .substring(0, 3),
                          style: TextStyle(
                              fontSize: 16,
                              color: currentDateSelectedIndex == index
                                  ? context.appColors.primaryForeground
                                  : Colors.grey),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          startDate.add(Duration(days: index)).day.toString(),
                          style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.w700,
                              color: currentDateSelectedIndex == index
                                  ? context.appColors.primaryForeground
                                  : context.appColors.textBlack),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          listOfDays[
                                  startDate.add(Duration(days: index)).weekday -
                                      1]
                              .toString()
                              .substring(0, 3),
                          style: TextStyle(
                              fontSize: 16,
                              color: currentDateSelectedIndex == index
                                  ? context.appColors.primaryForeground
                                  : Colors.grey),
                        ),
                      ],
                    ),
                  ),
                );
              },
            )),
      ],
    );
  }
}
