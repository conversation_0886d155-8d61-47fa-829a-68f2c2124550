// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class CommonWebView extends StatefulWidget {
  final String? url;
  bool _isLoading = false;
  bool isLandScape, isLocal, fullScreen;

  CommonWebView(
      {this.url,
      Key? key,
      this.isLandScape = true,
      this.isLocal = false,
      this.fullScreen = false})
      : super(key: key);

  @override
  _CommonWebViewState createState() => _CommonWebViewState();
}

class _CommonWebViewState extends State<CommonWebView> {
  InAppWebViewController? _webViewController;
  String url = "";
  double progress = 0;

  @override
  void initState() {
    Log.v("url: ${formatUrl(widget.url!)}");

    super.initState();
    if (widget.isLandScape)
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeRight,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.portraitDown,
        DeviceOrientation.portraitUp,
      ]);
    if (widget.fullScreen)
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);
    super.dispose();
  }

  String formatUrl(String url) {
    if (url.startsWith('www.')) {
      url = url.replaceFirst('www.', 'https://');
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    return url;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: context.appColors.background,
        extendBodyBehindAppBar: false,
        // appBar: AppBar(
        //   backgroundColor: Colors.transparent,
        //   elevation: 0,
        //   leading: ,
        // ),
        body: ScreenWithLoader(
          isLoading: widget._isLoading,
          body: Stack(
            children: [
              InAppWebView(
                initialUrlRequest:
                    URLRequest(url: WebUri(formatUrl(widget.url!))),
                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  cacheEnabled: true,
                  userAgent:
                      "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
                  useOnLoadResource: true,
                  mediaPlaybackRequiresUserGesture: false,
                ),
                onWebViewCreated: (InAppWebViewController controller) {
                  _webViewController = controller;
                },
                onPermissionRequest: (InAppWebViewController controller,
                    PermissionRequest permissionRequest) async {
                  return PermissionResponse(
                      resources: permissionRequest.resources,
                      action: PermissionResponseAction.GRANT);
                },
                onLoadStart:
                    (InAppWebViewController controller, Uri? url) async {
                  // if(controller.)
                  if (url.toString().contains('g-home')) {
                    Navigator.pop(context, true);
                  }
                  setState(() {
                    this.url = url.toString();
                  });
                },
                onLoadStop:
                    (InAppWebViewController controller, Uri? url) async {
                  setState(() {
                    this.url = url.toString();
                  });
                },
                onReceivedError: (InAppWebViewController c,
                    WebResourceRequest r, WebResourceError e) {
                  print(
                      'the value is ${e.description} and uri ${r.url} and code ${e.type}');
                },
                onProgressChanged:
                    (InAppWebViewController controller, int progress) {
                  setState(() {
                    this.progress = progress / 100;
                    progress == 100
                        ? widget._isLoading = false
                        : widget._isLoading = true;
                  });
                },
              ),
              Positioned(
                top: 5,
                left: Utility().isRTL(context) ? null : 5,
                right: Utility().isRTL(context) ? 5 : null,
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      color: context.appColors.surface, shape: BoxShape.circle),
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context, true);
                    },
                    child: Transform.translate(
                      offset: Offset(4.5, 0),
                      child: Icon(
                        Icons.arrow_back_ios,
                        color: context.appColors.textBlack,
                      ),
                    ),
                    // child: Text('nice'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
