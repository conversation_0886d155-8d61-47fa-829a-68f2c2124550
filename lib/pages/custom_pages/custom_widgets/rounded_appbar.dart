import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class RoundedAppBar extends StatelessWidget {
  final Widget child;
  final double? appBarHeight;
  final List<Color>? gradientColors;

  const RoundedAppBar({
    super.key,
    required this.child,
    this.appBarHeight,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    // final colors = gradientColors ?? context.gradientColors;

    return Container(
      // height: appBarHeight ?? height(context) * 0.07,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: context.appColors.appBarBackground,
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(14), bottomRight: Radius.circular(14)),
      ),
      child: child,
    );
  }
}
