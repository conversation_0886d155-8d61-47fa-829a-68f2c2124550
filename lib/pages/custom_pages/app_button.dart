import 'package:flutter/material.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

// Custom App Button
class AppButton extends StatelessWidget {
  final Function? onTap;
  final bool isEnabled;
  final String? title;
  final Color? color;

  const AppButton(
      {super.key, this.isEnabled = true, this.onTap, this.title, this.color});

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      onPressed: isEnabled == true
          ? () {
              onTap;
            }
          : null,
      height: appButtonHeight,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5),
      ),
      color: color ?? context.primaryDark,
      disabledColor: (color ?? context.primaryDark).withValues(alpha: 0.5),
      enableFeedback: isEnabled,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title!,
            style: TextStyle(
              color: context.primaryForegroundColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
