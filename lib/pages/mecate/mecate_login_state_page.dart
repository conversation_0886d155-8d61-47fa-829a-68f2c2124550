import 'dart:async';

import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../../local/pref/Preference.dart';
import '../../utils/styles.dart';
import '../../utils/config.dart';

class MeCateLoginStatePage extends StatefulWidget {
  final String? title;
  const MeCateLoginStatePage({super.key, this.title});

  @override
  State<MeCateLoginStatePage> createState() => _MeCateLoginStatePageState();
}

class _MeCateLoginStatePageState extends State<MeCateLoginStatePage> {
  late final WebViewController _controller;
  bool isLoading = true;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: fetchUrl(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            appBar: AppBar(
              elevation: 0,
              backgroundColor: context.appColors.surface,
              iconTheme: IconThemeData(color: context.appColors.textBlack),
              title: Text(
                '${widget.title}'
                    .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
                style: Styles.semibold(color: context.appColors.textBlack),
              ),
            ),
            body: const Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(
              elevation: 0,
              backgroundColor: context.appColors.surface,
              iconTheme: IconThemeData(color: context.appColors.textBlack),
              title: Text(
                '${widget.title}'
                    .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
                style: Styles.semibold(color: context.appColors.textBlack),
              ),
            ),
            body: Center(child: Text('Error: \\${snapshot.error}')),
          );
        } else {
          final url = 'http://app.mecat.in/ssologin/${snapshot.data}';
          _controller = WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setBackgroundColor(context.appColors.surface)
            ..loadRequest(Uri.parse(url));
          return Scaffold(
            appBar: AppBar(
              elevation: 0,
              backgroundColor: context.appColors.surface,
              iconTheme: IconThemeData(color: context.appColors.textBlack),
              title: Text(
                '${widget.title}'
                    .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
                style: Styles.semibold(color: context.appColors.textBlack),
              ),
            ),
            body: WebViewWidget(controller: _controller),
          );
        }
      },
    );
  }

  void myDartFunction() {
    debugPrint('Dart function called from HTML page!');
  }

  Future<String> fetchUrl() async {
    String encEmail = '';
    if (Preference.getString(Preference.USER_EMAIL).toString().contains('@')) {
      encEmail = Utility()
          .encrypted128(Preference.getString(Preference.USER_EMAIL).toString());
    } else {
      encEmail = Preference.getString(Preference.USER_EMAIL).toString();
    }

    final String apiUrl = 'https://app.mecat.in/api/generate-token';
    final Map<String, dynamic> payload = {
      'encrypted_params': encEmail,
    };
    final http.Response response = await http.post(
      Uri.parse(apiUrl),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode(payload),
    );
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      final ssoToken = jsonData['data']['sso_token'];
      return ssoToken;
    } else {
      throw Exception('Failed to load URL');
    }
  }
}
