import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/get_certificates_resp.dart';
import 'package:masterg/pages/custom_pages/common_container.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/notification_list_page.dart';
import 'package:masterg/utils/log.dart';
import 'package:masterg/utils/strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class MyCertificatesPage extends StatefulWidget {
  final bool? isViewAll;

  const MyCertificatesPage({super.key, this.isViewAll});

  @override
  State<MyCertificatesPage> createState() => _MyCertificatesPageState();
}

class _MyCertificatesPageState extends State<MyCertificatesPage> {
  List<KpiCertificatesDatum> _getCertificatesResp = [];

  @override
  void initState() {
    super.initState();
    _getCertificatesListData();
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            Log.v("Loading....................GetCoursesState build");
            if (state is GetCertificatesState) _handleAnnouncmentData(state);
          },
          child:
              widget.isViewAll == true ? _verticalList() : _mainBody(context),
        ));
  }

  CommonContainer _verticalList() {
    return CommonContainer(
      isBackShow: false,
      isContainerHeight: false,
      isScrollable: true,
      // scrollReverse: true,
      bgChildColor: Color(0xFFEEEEF3),
      title: '${Strings.of(context)?.myCertificates}',
      isTopPadding: false,
      onBackPressed: () {
        Navigator.pop(context);
      },
      isNotification: true,
      onSkipClicked: () {
        Navigator.push(context, NextPageRoute(NotificationListPage()));
      },
      isLoading: false,
      child: _mainBody(context),
    );
  }

  ValueListenableBuilder<Box> _mainBody(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    // double screenHeight = MediaQuery.of(context).size.height;

    return ValueListenableBuilder(
      valueListenable: Hive.box(DB.ANALYTICS).listenable(),
      builder: (bc, Box box, child) {
        if (box.get("certificates") == null) {
          return CustomProgressIndicator(true, context.appColors.surface);
        }

        _getCertificatesResp = box
            .get("certificates")
            .map((e) =>
                KpiCertificatesDatum.fromJson(Map<String, dynamic>.from(e)))
            .cast<KpiCertificatesDatum>()
            .toList();

        return SizedBox(
          width: screenWidth,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 90,
                child: Stack(
                  children: [
                    Container(
                      width: screenWidth,
                      height: 70,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFFFFEB3B),
                            Color(0xFFFFD500),
                          ],
                        ),
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(25),
                          topLeft: Radius.circular(25),
                        ),
                      ),
                      padding: EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_getCertificatesResp.length} certificates',
                            style: Styles.textBold(
                              size: 20,
                              color: context.appColors.textDarkBlack,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      right: 25,
                      top: 0,
                      child: Image.asset(
                        "assets/images/silver_medal.png",
                        width: 90,
                        height: 90,
                        fit: BoxFit.fill,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 20, top: 8),
                child: Text(
                  '${Strings.of(context)?.Your_medals}',
                  style: Styles.textExtraBold(
                    size: 18,
                    color: Color(0xFFFF8D29),
                  ),
                ),
              ),
              SizedBox(
                height: 11,
              ),
              _getCertificatesResp.isEmpty
                  ? SizedBox(
                      height: MediaQuery.of(context).size.height / 2,
                      width: MediaQuery.of(context).size.width,
                      child: Center(
                        child: Text(
                          "There are no certificates available",
                          style: Styles.textBold(),
                        ),
                      ),
                    )
                  : SizedBox(
                      width: screenWidth,
                      child: _getList(),
                    ),
              SizedBox(
                height: 400,
              ),
            ],
          ),
        );
      },
    );
  }

  void _getCertificatesListData() {
    Log.v("Loading....................GetCoursesState_getHomeData");
    BlocProvider.of<HomeBloc>(context).add(GetCertificatesEvent());
  }

  void _handleAnnouncmentData(GetCertificatesState state) {
    var loginState = state;

    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................GetCoursesState");
        break;
      case ApiStatus.SUCCESS:
        Log.v("Success....................GetCoursesState");

        break;
      case ApiStatus.ERROR:
        Log.v("Error..........................GetCoursesState");
        Log.v("Error..........................${loginState.error}");
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  GroupedListView<dynamic, DateTime> _getList() {
    final now = DateTime.now();
    return GroupedListView<dynamic, DateTime>(
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      elements: _getCertificatesResp,
      groupBy: (element) => DateTime(element.year, element.month, now.day),
      groupSeparatorBuilder: (DateTime date) => Align(
        alignment: Alignment.center,
        child: Text(
          DateFormat("MMMM, yyyy").format(date),
          style: TextStyle(fontSize: 16),
        ),
      ),
      groupComparator: (value1, value2) => value2.compareTo(value1),
      itemBuilder: (context, dynamic element) => Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: Color(0xFFEBEEFF),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: context.appColors.surface,
                borderRadius: BorderRadius.all(
                  Radius.circular(8),
                ),
                boxShadow: [
                  BoxShadow(
                    blurRadius: 16,
                    color: Color(0x0B000000),
                  ),
                ],
              ),
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              element.certificateName ?? "",
                              style: Styles.textExtraBold(
                                size: 14,
                                color: context.appColors.textDarkBlack,
                              ),
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Spacer(),
                    Image.asset("assets/images/medal.png",
                        height: 50, width: 50)
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      useStickyGroupSeparators: true,
      floatingHeader: true,
      order: GroupedListOrder.ASC,
    );
  }
}
