import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/home_request/get_course_modules_request.dart';
import 'package:masterg/data/models/response/home_response/get_course_leaderboard_resp.dart'
    as leader_resp;
import 'package:masterg/data/models/response/home_response/get_course_modules_resp.dart'
    as module_resp;
import 'package:masterg/data/models/response/home_response/get_courses_resp.dart'
    as course_resp;
import 'package:masterg/pages/custom_pages/analytics_loader.dart';
import 'package:masterg/utils/log.dart';
import 'package:masterg/utils/strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/images.dart';

class MyAnalyticsPage extends StatefulWidget {
  final List<course_resp.ListElement>? getCoursesResp;
  final bool? isLoading;

  const MyAnalyticsPage({super.key, this.getCoursesResp, this.isLoading});

  @override
  State<MyAnalyticsPage> createState() => _MyAnalyticsPageState();
}

class _MyAnalyticsPageState extends State<MyAnalyticsPage> {
  final ScrollController _courseListScrollController = ScrollController();

  List<module_resp.Module>? _getCourseModulesResp;
  List<leader_resp.ListElement>? _getCourseLeaderboardResp;
  // bool _isCourseWiseLeaderboardLoading = true;
  // bool _isCourseWiseModuleLoading = true;

  int? _selectedCourse;
  // int? _selectedCourseIndex;

  bool _isLeaderBoard = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 200), () {
      if (widget.isLoading == true) {
        setState(() {
          _selectedCourse = widget.getCoursesResp?.first.id;
          // _selectedCourseIndex = 0;
        });
        _getCourseWiseModulesData(_selectedCourse.toString());
        _getCourseWiseLeaderboardData(_selectedCourse.toString());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            Log.v("Loading....................GetCoursesState build");
            if (state is GetCourseModulesState) {
              _handleCourseWiseModulesAnnouncmentData(state);
            }
            if (state is GetCourseLeaderboardState) {
              _handleCourseWiseLeaderboardAnnouncmentData(state);
            }
          },
          child: _buildBody(),
        ));
  }

  SingleChildScrollView _buildBody() {
    double screenWidth = MediaQuery.of(context).size.width;
    // double screenHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          return Container(
            width: screenWidth,
            padding: EdgeInsets.only(left: 20, top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${Strings.of(context)?.Course_level_Analysis}',
                  style: Styles.textExtraBold(
                    size: 18,
                    color: context.appColors.orange,
                    // color: Color(0xFFFF8D29),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                _courseList(),
                SizedBox(
                  height: 20,
                ),
                _isLeaderBoard
                    ? _courseWiseLeaderboardList()
                    : _courseWiseModuleAnalysisList(),
                SizedBox(
                  height: 50,
                ),
                SizedBox(
                  height: 100,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _courseList() {
    double screenWidth = MediaQuery.of(context).size.width;
    // double screenHeight = MediaQuery.of(context).size.height;
    return SizedBox(
      height: 67,
      width: screenWidth,
      child: ListView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: widget.getCoursesResp?.length ?? 0,
        controller: _courseListScrollController,
        itemBuilder: (ctx, index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCourse = widget.getCoursesResp?[index].id;
                // _selectedCourseIndex = index;
              });
              _getCourseWiseModulesData(_selectedCourse.toString());
              _getCourseWiseLeaderboardData(_selectedCourse.toString());
              if (index == 2) {
                setState(() {
                  _courseListScrollController.animateTo(
                      _courseListScrollController.position.maxScrollExtent,
                      duration: Duration(milliseconds: 500),
                      curve: Curves.easeOut);
                });
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 6, horizontal: 12),
              margin: EdgeInsets.only(right: 16),
              width: 172,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: _selectedCourse == widget.getCoursesResp?[index].id
                      ? context.appColors.cardBackground
                      : context.appColors.subtleOverlay,
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  border: Border.all(
                      color: context.appColors.subtleOverlay, width: 1),
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 16,
                      color: Color(0x0B000000),
                    ),
                  ]),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    child: SizedBox(
                      width: 32,
                      height: 32,
                      child: Image.network(
                        '${widget.getCoursesResp![index].image}',
                        fit: BoxFit.cover,
                        errorBuilder: (context, url, error) {
                          return Image.asset(Images.PLACE_HOLDER);
                        },
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 12,
                  ),
                  Flexible(
                    child: Text(
                      '${widget.getCoursesResp![index].name}',
                      style: _selectedCourse == widget.getCoursesResp![index].id
                          ? Styles.textExtraBold(
                              size: 12,
                              color: context.appColors.headingTitle,
                            )
                          : Styles.textSemiBold(
                              size: 12,
                              color: context.appColors.headingTitle,
                            ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Container _courseWiseLeaderboardList() {
    double screenWidth = MediaQuery.of(context).size.width;
    // double screenHeight = MediaQuery.of(context).size.height;

    return Container(
      margin: EdgeInsets.only(right: 20),
      padding: EdgeInsets.all(9),
      width: screenWidth,
      decoration: BoxDecoration(
        color: context.appColors.surface,
        borderRadius: BorderRadius.all(Radius.circular(10)),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 100,
            offset: const Offset(5, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: screenWidth,
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Color(0xFFEBEEFF),
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${Strings.of(context)?.Leaderboard}',
                  style: Styles.textBold(
                    size: 16,
                    color: context.appColors.textDarkBlack,
                  ),
                ),
                Spacer(),
                SizedBox(
                  width: 130,
                  child: Row(
                    children: [
                      Transform.scale(
                        scale: 0.5,
                        child: CupertinoSwitch(
                          activeTrackColor: Color(0xFF2D75DD),
                          value: _isLeaderBoard,
                          onChanged: (input) {
                            setState(() {
                              _isLeaderBoard = input;
                            });
                          },
                        ),
                      ),
                      Flexible(
                        child: Text(
                          '${Strings.of(context)?.Switch_to_modules}',
                          style: Styles.textBold(
                            size: 11,
                            color: context.appColors.textDarkBlack,
                          ),
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Padding(
            padding: EdgeInsets.all(9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: screenWidth * 0.07,
                  child: Text(
                    'S.no',
                    style: Styles.textSemiBold(
                      size: 12,
                      color: Color(0xFF2D75DD),
                    ),
                  ),
                ),
                SizedBox(
                  width: screenWidth * 0.36,
                  child: Text(
                    'Name',
                    style: Styles.textSemiBold(
                      size: 12,
                      color: Color(0xFF2D75DD),
                    ),
                  ),
                ),
                SizedBox(
                  width: screenWidth * 0.32,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '% completed',
                        style: Styles.textSemiBold(
                          size: 12,
                          color: Color(0xFF2D75DD),
                        ),
                      ),
                      Text(
                        '${Strings.of(context)?.Score}',
                        style: Styles.textSemiBold(
                          size: 12,
                          color: Color(0xFF2D75DD),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            width: screenWidth,
            child: ValueListenableBuilder(
                valueListenable: Hive.box("analytics").listenable(),
                builder: (bc, Box box, child) {
                  if (box.get(_selectedCourse.toString() +
                          AnalyticsType.COURSE_LEADERBOARD_TYPE_1) ==
                      null) {
                    return AnalyticsLoader();
                  }
                  _getCourseLeaderboardResp = box
                      .get(_selectedCourse.toString() +
                          AnalyticsType.COURSE_LEADERBOARD_TYPE_1)
                      .map((e) => leader_resp.ListElement.fromJson(
                          Map<String, dynamic>.from(e)))
                      .cast<leader_resp.ListElement>()
                      .toList();
                  return ListView.separated(
                    shrinkWrap: true,
                    itemCount: _getCourseLeaderboardResp!.length,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (ctx, index) {
                      return Padding(
                        padding: EdgeInsets.all(9),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: screenWidth * 0.07,
                              alignment: Alignment.center,
                              child: Text(
                                (index + 1).toString(),
                                style: Styles.textSemiBold(
                                  size: 12,
                                  color: Color(0xFF34323A),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: screenWidth * 0.43,
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: screenWidth * 0.28,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${_getCourseLeaderboardResp?[index].name}',
                                          style: Styles.textExtraBold(
                                            size: 14,
                                            color:
                                                context.appColors.headingTitle,
                                          ),
                                          maxLines: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: screenWidth * 0.23,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${_getCourseLeaderboardResp?[index].completion} %',
                                    style: Styles.textExtraBold(
                                      size: 12,
                                      color: Color(0xFF2D75DD),
                                    ),
                                  ),
                                  Text(
                                    "0.0",
                                    style: Styles.textExtraBold(
                                      size: 12,
                                      color: Color(0x287F89C5),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (ctx, index) {
                      return Divider(
                        thickness: 1,
                        color: Color(0x287F89C5),
                        height: 1,
                      );
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }

  Container _courseWiseModuleAnalysisList() {
    double screenWidth = MediaQuery.of(context).size.width;
    // double screenHeight = MediaQuery.of(context).size.height;
    return Container(
      margin: EdgeInsets.only(right: 20),
      padding: EdgeInsets.all(9),
      width: screenWidth,
      decoration: BoxDecoration(
        color: context.appColors.surface,
        borderRadius: BorderRadius.all(Radius.circular(10)),
        boxShadow: [
          BoxShadow(
            blurRadius: 16,
            color: Color(0x25000000),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: screenWidth,
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Color(0xFFEBEEFF),
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Module Analysis',
                  style: Styles.textBold(
                    size: 16,
                    color: context.appColors.textDarkBlack,
                  ),
                ),
                Spacer(),
                SizedBox(
                  width: 130,
                  child: Row(
                    children: [
                      Transform.scale(
                        scale: 0.5,
                        child: CupertinoSwitch(
                          activeTrackColor: Color(0xFF2D75DD),
                          value: _isLeaderBoard,
                          onChanged: (input) {
                            setState(() {
                              _isLeaderBoard = input;
                            });
                          },
                        ),
                      ),
                      Flexible(
                        child: Text(
                          'Switch to leaderboard',
                          style: Styles.textBold(
                            size: 11,
                            color: context.appColors.textDarkBlack,
                          ),
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Padding(
            padding: EdgeInsets.all(9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: screenWidth * 0.07,
                  child: Text(
                    'S.no',
                    style: Styles.textSemiBold(
                      size: 12,
                      color: Color(0xFF2D75DD),
                    ),
                  ),
                ),
                SizedBox(
                  width: screenWidth * 0.36,
                  child: Text(
                    'Module Name',
                    style: Styles.textSemiBold(
                      size: 12,
                      color: Color(0xFF2D75DD),
                    ),
                  ),
                ),
                SizedBox(
                  width: screenWidth * 0.32,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '% completed',
                        style: Styles.textSemiBold(
                          size: 12,
                          color: Color(0xFF2D75DD),
                        ),
                      ),
                      Text(
                        '${Strings.of(context)?.Score}',
                        style: Styles.textSemiBold(
                          size: 12,
                          color: Color(0xFF2D75DD),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            width: screenWidth,
            child: ValueListenableBuilder(
                valueListenable: Hive.box("analytics").listenable(),
                builder: (bc, Box box, child) {
                  if (box.get(_selectedCourse.toString() +
                          AnalyticsType.MODULE_TYPE_1) ==
                      null) {
                    return AnalyticsLoader();
                  }

                  _getCourseModulesResp = box
                      .get(_selectedCourse.toString() +
                          AnalyticsType.MODULE_TYPE_1)
                      .map((e) => module_resp.Module.fromJson(
                          Map<String, dynamic>.from(e)))
                      .cast<module_resp.Module>()
                      .toList();

                  return ListView.separated(
                    shrinkWrap: true,
                    itemCount: _getCourseModulesResp!.length,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (ctx, index) {
                      return Padding(
                        padding: EdgeInsets.all(9),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              width: screenWidth * 0.07,
                              child: Text(
                                (index + 1).toString(),
                                style: Styles.textSemiBold(
                                  size: 12,
                                  color: Color(0xFF34323A),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(
                              width: screenWidth * 0.43,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${_getCourseModulesResp?[index].name}',
                                    style: Styles.textExtraBold(
                                      size: 14,
                                      color: context.appColors.headingTitle,
                                    ),
                                    maxLines: 2,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: screenWidth * 0.23,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${_getCourseModulesResp?[index].completion} %',
                                    style: Styles.textExtraBold(
                                      size: 12,
                                      color: Color(0xFF2D75DD),
                                    ),
                                  ),
                                  Text(
                                    '0.0',
                                    style: Styles.textExtraBold(
                                      size: 12,
                                      color: Color(0xCC222222),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (ctx, index) {
                      return Divider(
                        thickness: 1,
                        color: Color(0x287F89C5),
                        height: 1,
                      );
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }

  void _getCourseWiseModulesData(String courseId) {
    Log.v(
        "Loading....................GetCourseModulesState_getCourseModulesData");
    BlocProvider.of<HomeBloc>(context).add(GetCourseModulesEvent(
        getCourseModulesReq: GetCourseModulesRequest(courseId)));
  }

  void _handleCourseWiseModulesAnnouncmentData(GetCourseModulesState state) {
    var loginState = state;
    // setState(() {
    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        // _isCourseWiseModuleLoading = true;
        Log.v("Loading....................GetCourseModulesState");
        break;
      case ApiStatus.SUCCESS:
        Log.v("Success....................GetCourseModulesState");
        // _isCourseWiseModuleLoading = false;

        _getCourseModulesResp = state.response?.data?.list?.first.modules;
        break;
      case ApiStatus.ERROR:
        // _isCourseWiseModuleLoading = false;
        Log.v("Error..........................GetCourseModulesState");
        Log.v("Error..........................${loginState.error}");
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  void _getCourseWiseLeaderboardData(String courseId) {
    Log.v(
        "Loading....................GetCourseModulesState_getCourseModulesData");
    BlocProvider.of<HomeBloc>(context).add(GetCourseLeaderboardEvent(
        getCourseModulesReq: GetCourseModulesRequest(courseId)));
  }

  void _handleCourseWiseLeaderboardAnnouncmentData(
      GetCourseLeaderboardState state) {
    var loginState = state;

    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        // _isCourseWiseLeaderboardLoading = true;
        Log.v("Loading....................GetCourseModulesState");
        break;
      case ApiStatus.SUCCESS:
        Log.v("Success....................GetCourseModulesState");
        // _isCourseWiseLeaderboardLoading = false;

        break;
      case ApiStatus.ERROR:
        // _isCourseWiseLeaderboardLoading = false;
        Log.v("Error..........................GetCourseModulesState");
        Log.v("Error..........................${loginState.error}");
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }
}
