import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/extensions/sized_box_extension.dart';
import 'package:masterg/utils/theme/theme_aware_widget.dart';
import 'package:flutter_svg/svg.dart';

import '../../blocs/theme/theme_bloc.dart';
import '../../utils/theme/theme_extensions.dart';

import '../../local/pref/Preference.dart';
import '../../utils/config.dart';
import '../auth_pages/terms_and_condition_page.dart';
import '../user_profile_page/delete_account_page.dart';
import 'setting_and_privacy_data.dart';

class SettingAndPrivacyPage extends StatefulWidget {
  const SettingAndPrivacyPage({super.key});

  @override
  State<SettingAndPrivacyPage> createState() => _SettingAndPrivacyPageState();
}

class _SettingAndPrivacyPageState extends State<SettingAndPrivacyPage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: context.backgroundColor,
          appBar: ThemeAwareAppBar(
            // backgroundColor: context.appColors.appBarBackground,
            elevation: 0,
            title: Text(
              'settings_privacy',
              style: TextStyle(color: context.primaryForegroundColor),
            ).tr(),
            iconTheme: IconThemeData(
              color: context.primaryForegroundColor,
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 30.0),
                  color: context.surfaceColor,
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: SettingAndPrivacyData.settingsData.length,
                    itemBuilder: (context, index) {
                      final item = SettingAndPrivacyData.settingsData[index];
                      return _buildSettingTile(item);
                    },
                    separatorBuilder: (context, index) => Divider(
                      color: context.dividerColor,
                    ),
                  ),
                ),
                20.height,
                Container(
                    color: context.surfaceColor,
                    padding: EdgeInsets.only(bottom: 10.0, top: 10.0),
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: SettingAndPrivacyData.privacyData.length,
                      itemBuilder: (context, index) {
                        final item = SettingAndPrivacyData.privacyData[index];
                        return _buildPrivacyTile(item);
                      },
                      separatorBuilder: (context, index) => Divider(
                        color: context.dividerColor,
                      ),
                    )),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingTile(Map<String, dynamic> item) {
    return ListTile(
      dense: true,
      trailing: item['isToggle'] ?? false
          ? BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, state) {
                return Switch(
                  value: state is DarkThemeState,
                  onChanged: (value) {
                    BlocProvider.of<ThemeBloc>(context, listen: false)
                        .add(ToggleThemeEvent());
                  },
                );
              },
            )
          : SizedBox(
              width: 100,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    tr(item['trailing'] ?? ''),
                    style: TextStyle(
                      fontSize: 12,
                      color: context.bodyTextColor,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: context.iconColor,
                    size: 15,
                  ),
                ],
              ),
            ),
      visualDensity: VisualDensity(vertical: -4, horizontal: -4),
      leading: Container(
          width: 30,
          height: 30,
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
              color: item['color'], borderRadius: BorderRadius.circular(6)),
          child: Icon(
            item['icon'],
            color: context.appColors.primaryForeground,
            size: 20,
          )),
      title: Text(
        tr(item['text']),
        style: TextStyle(color: context.bodyTextColor),
      ),
      onTap: () {},
    );
  }

  Widget _buildPrivacyTile(Map<String, dynamic> item) {
    return ListTile(
      dense: true,
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: context.iconColor,
        size: 15,
      ),
      visualDensity: VisualDensity(vertical: -4, horizontal: -4),
      leading: Container(
          width: 30,
          height: 30,
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
              color: item['color'], borderRadius: BorderRadius.circular(6)),
          child: item['icon'] is String
              ? SvgPicture.asset(
                  item['icon'],
                  colorFilter: ColorFilter.mode(
                      context.appColors.textWhite, BlendMode.srcIn),
                )
              : Icon(
                  item['icon'],
                  color: context.appColors.textWhite,
                  size: 20,
                )),
      title: Text(
        tr(item['text']).replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
        style: TextStyle(color: context.bodyTextColor),
      ),
      onTap: () {
        if (item['text'] == 't_c') {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => TermsAndCondition(
                        url: Preference.getString(Preference.TERMS_AND_CON_URL),
                        title: tr('t_c'),
                      ),
                  maintainState: false));
        } else if (item['text'] == 'about_singularis') {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => TermsAndCondition(
                        url: APK_DETAILS['about_url'],
                        title: tr('about_singularis'),
                      ),
                  maintainState: false));
        } else if (item['text'] == 'delete_account') {
          Navigator.pop(context);
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => DeleteAccountPage(
                      imageUrl:
                          '${Preference.getString(Preference.PROFILE_IMAGE)}')));
        }
      },
    );
  }
}
