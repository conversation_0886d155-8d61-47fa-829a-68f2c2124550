import 'dart:io';
import 'dart:isolate';
import 'dart:math';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/utility.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
import '../../data/models/response/home_response/test_attempt_response.dart';
import 'model/question.dart';

class InterviewAssessmentProvider extends ChangeNotifier {
  late int currentQuestionIndex;
  late int lastVisitedIndex;
  StopWatchTimer stopWatchTimer = StopWatchTimer();
  ScrollController scrollController = ScrollController();
  late final RecorderController recorderController;

  /// [textEditingController] store the current value of current text editining controller
  /// [pickedFile] store the current picked files

  late TextEditingController textEditingController;

  late String? pickedFile;
  late List<Question> questions = [];
  late List<TestAttemptBean> questionList = [];
  late bool loading;
  late int totalSecond;
  late Function onSubmit;
  late Function finalSubmit;
  late bool inputExpanded = false;
  late String title;
  late bool isUploading = false;
  late double floatingButtonHeight = 0;
  late List<BuildContext> dialogContexts = [];
  late bool isReview;
  bool pauseQuestionVideo = false;
  bool pickingFile = false;
  double compressionPer = 0.0;
  double uploadingPer = 0.0;
  bool currentAttemptChanged = false;
  bool isCompressingVideo = false;

  ReceivePort? compressionPort;

  PageController pageController = PageController();
  InterviewAssessmentProvider(
      List<TestAttemptBean> questionL,
      int durationInMin,
      Function submit,
      Function onFinalSubmit,
      String assessmentTitle,
      bool isReview) {
    currentQuestionIndex = 0;
    lastVisitedIndex = 0;
    stopWatchTimer.onStartTimer();
    this.isReview = isReview;
    textEditingController = TextEditingController();
    pickedFile = null;
    questionList = [...questionL];
    questions = List.generate(
        questionList.length,
        (index) => Question(
            userAnswerTxt: "",
            questionStatus: !this.isReview
                ? QuestionStatus.pending
                : ((questionList[index].question?.userFile == null ||
                                questionList[index].question?.userFile == '') &&
                            (questionList[index].question?.answerStatement ==
                                    null ||
                                questionList[index].question?.answerStatement ==
                                    '')) ==
                        true
                    ? QuestionStatus.skipped
                    : QuestionStatus.done,
            userUploadedFileUrl: [],
            reviewData: questionList[index].question?.toJson()));
    // totalSecond = 100000;
    // totalSecond = durationInMin * 60000;
    totalSecond = durationInMin * 60;
    onSubmit = submit;
    finalSubmit = onFinalSubmit;
    loading = false;
    title = assessmentTitle;
    isUploading = false;
    dialogContexts = [];
  }
  void updateCompressionPer({required double per}) {
    // Log.v("funcall updateCompressionPer");
    compressionPer = per;
    notifyListeners();
  }

  void setCompressing({required bool isCompressing}) {
    // Log.v("funcall setCompressing");
    isCompressingVideo = isCompressing;
    notifyListeners();
  }

  void updateCompressionPort(ReceivePort port) {
    compressionPort?.close();
    compressionPort = port;
    compressionPer = 0.0;
    notifyListeners();
  }

  void updateUploadingPer({required double per}) {
    Log.v("funcall updateUploadingPer");
    uploadingPer = per;
    notifyListeners();
  }

  void updateInputWidth({required bool isExpanded}) {
    Log.v("funcall updateInputWidth");
    inputExpanded = isExpanded;
    notifyListeners();
  }

  void addContext(BuildContext context) {
    Log.v("funcall addContext");
    debugPrint('provider conttext added');
    dialogContexts.add(context);
    notifyListeners();
  }

  void removeLastContext() {
    Log.v("funcall removeLastContext");
    debugPrint('provider conttext removed');

    dialogContexts.removeLast();
    notifyListeners();
  }

  Future<void> closeAllDialog() async {
    debugPrint('provider conttext ${dialogContexts.length}');

    for (BuildContext context in dialogContexts) {
      try {
        Navigator.of(context).pop();
      } catch (e) {}
    }
    // await VideoCompress.cancelCompression();
    compressionPort?.close();

    notifyListeners();
  }

  void setPickingFile({required bool picking}) {
    // Log.v("funcall setPickingFile");
    pickingFile = picking;
    notifyListeners();
  }

  void updateFloatingBtnHeight(double height) {
    // Log.v("funcall updateFloatingBtnHeight");
    floatingButtonHeight = height;
    notifyListeners();
  }

  void skip() async {
    Log.v("funcall skip");
    pickingFile = false;
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    changeCurrentAttemptStatus(isChanged: false);
    questions[currentQuestionIndex].questionStatus = QuestionStatus.skipped;
    if (currentQuestionIndex != questionList.length - 1) {
      inputExpanded = false;
      currentQuestionIndex++;
      lastVisitedIndex = max(lastVisitedIndex, currentQuestionIndex);
      pageController.jumpToPage(currentQuestionIndex);
      pickedFile = null;
      // textEditingController = TextEditingController();
      textEditingController = TextEditingController(
          text: questions[currentQuestionIndex].userAnswerTxt);
    }
    scrollController.animateTo(currentQuestionIndex.toDouble() + 50.0,
        duration: Duration(milliseconds: 500), curve: Curves.easeOut);

    //skip request
    // onSubmit(
    //     questionId:
    //         this.questionList[this.currentQuestionIndex].question?.questionId,
    //     userFile: '',
    //     userText: '',
    //     durationInSec: stopWatchTimer.secondTime.value);
    // if (this.currentQuestionIndex != this.questionList.length - 1) {
    //   this.currentQuestionIndex++;
    //   this.lastVisitedIndex =
    //       max(this.lastVisitedIndex, this.currentQuestionIndex);
    //   pageController.jumpToPage(this.currentQuestionIndex);
    //   pickedFile.clear();
    //   pickedFile.addAll(questions[currentQuestionIndex].userUploadedFileUrl);
    // }
    // scrollController.animateTo(this.currentQuestionIndex.toDouble() + 50.0,
    //     duration: Duration(milliseconds: 500), curve: Curves.easeOut);
    notifyListeners();
  }

  void addFile({required String filePath}) async {
    Log.v("funcall addFile");

    // if (Platform.isIOS)
    //   this.pickedFile = await Utility.saveTemporarily(File(filePath));
    // else
    //   this.pickedFile = filePath;
    pickedFile = await Utility.saveTemporarily(File(filePath));
    notifyListeners();
  }

  void saveNext() async {
    Log.v("funcall saveNext");
    pickingFile = false;
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    //save user uploaded local filess
    questions[currentQuestionIndex].userUploadedFileUrl = [];
    if (pickedFile != null) {
      questions[currentQuestionIndex].userUploadedFileUrl.add('$pickedFile');
    }
    questions[currentQuestionIndex].userAnswerTxt =
        textEditingController.value.text;

    changeCurrentAttemptStatus(isChanged: false);

    //hit api
    onSubmit(
        questionId: questionList[currentQuestionIndex].question?.questionId,
        userFile: pickedFile ?? '',
        userText: textEditingController.value.text,
        durationInSec: stopWatchTimer.secondTime.value);

    notifyListeners();
  }

  void removeFirstPickedFile() {
    Log.v("funcall removeFirstPickedFile");
    pickedFile = null;
    changeCurrentAttemptStatus(isChanged: true);
    notifyListeners();
  }

  void next() async {
    Log.v("funcall next");
    pickingFile = false;
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    if (currentQuestionIndex != questionList.length - 1) {
      currentQuestionIndex++;
      lastVisitedIndex = max(lastVisitedIndex, currentQuestionIndex);
      pageController.jumpToPage(currentQuestionIndex);
      try {
        pickedFile = questions[currentQuestionIndex].userUploadedFileUrl.first;
      } catch (e) {
        Log.v("file name is ficked exception $e");
        pickedFile = null;
      }
      textEditingController = TextEditingController(
          text: questions[currentQuestionIndex].userAnswerTxt);
    }
    scrollController.animateTo(currentQuestionIndex.toDouble() + 50.0,
        duration: Duration(milliseconds: 500), curve: Curves.easeOut);
    changeCurrentAttemptStatus(isChanged: false);

    notifyListeners();
  }

  void moveToIndex({required int questionIndex}) async {
    Log.v("funcall moveToIndex");
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    pickingFile = false;

    currentQuestionIndex = questionIndex;

    //allow access to any question

    lastVisitedIndex = questionIndex;

    //end

    textEditingController = TextEditingController(
        text: questions[currentQuestionIndex].userAnswerTxt);
    try {
      pickedFile = questions[currentQuestionIndex].userUploadedFileUrl.first;
    } catch (e) {
      Log.v("file name is ficked exception $e");
      pickedFile = null;
    }
    pageController.jumpToPage(currentQuestionIndex);
    changeCurrentAttemptStatus(isChanged: false);
    notifyListeners();
  }

  void reviewNext() {
    Log.v("funcall reviewNext");
    currentQuestionIndex =
        min(currentQuestionIndex + 1, questionList.length - 1);
    lastVisitedIndex = currentQuestionIndex;
    pageController.jumpToPage(currentQuestionIndex);
    notifyListeners();
  }

  void goforward() async {
    Log.v("funcall goforward");
    Log.v(() {
      String? files;
      for (var element in questions) {
        files = '$files\t${element.userUploadedFileUrl}';
      }
      return '\n\n\nlist $currentQuestionIndex start $files';
    }());
    pickingFile = false;
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    changeCurrentAttemptStatus(isChanged: false);
    if (currentQuestionIndex != questionList.length - 1) {
      inputExpanded = false;
      currentQuestionIndex++;

      textEditingController = TextEditingController(
          text: questions[currentQuestionIndex].userAnswerTxt);
      Log.v(
          "file name is ${questions[currentQuestionIndex].userUploadedFileUrl}");
      try {
        pickedFile = questions[currentQuestionIndex].userUploadedFileUrl.first;
      } catch (e) {
        Log.v("file name is ficked exception $e");
        pickedFile = null;
      }

      // pickedFile =
      //     this.questions[this.currentQuestionIndex].userUploadedFileUrl.first;
      pageController.jumpToPage(currentQuestionIndex);
      // pickedFile.clear();
      // pickedFile = questions[currentQuestionIndex].userUploadedFileUrl;
    }
    notifyListeners();
    Log.v(() {
      String? files;
      for (var element in questions) {
        files = '$files\t${element.userUploadedFileUrl}';
      }
      return '\n\n\nlist $currentQuestionIndex  end $files';
    }());
  }

  void reviewPrev() {
    Log.v("funcall reviewPrev");
    currentQuestionIndex = max(0, currentQuestionIndex - 1);
    lastVisitedIndex = currentQuestionIndex;
    pageController.jumpToPage(currentQuestionIndex);

    notifyListeners();
  }

  void reviewJump({required int index}) {
    Log.v("funcall reviewJump");
    currentQuestionIndex = index;
    lastVisitedIndex = currentQuestionIndex;
    changeCurrentAttemptStatus(isChanged: false);

    pageController.jumpToPage(currentQuestionIndex);
    notifyListeners();
  }

  void setloading() {
    Log.v("funcall setloading");
    loading = true;
    notifyListeners();
  }

  void loadingComplete() {
    Log.v("funcall loadingComplete");
    loading = false;
    notifyListeners();
  }

  void updateUploadingStatus({required isUploading}) {
    Log.v("funcall updateUploadingStatus");
    this.isUploading = isUploading;
    notifyListeners();
  }

  void pauseVideo() {
    Log.v("funcall pauseVideo");
    pauseQuestionVideo = true;
    notifyListeners();
  }

  void playVideo() {
    Log.v("funcall playVideo");
    pauseQuestionVideo = false;
    notifyListeners();
  }

  bool? isLast() {
    return currentQuestionIndex + 1 == questionList.length;
  }

  void changeCurrentAttemptStatus({required bool isChanged}) {
    // Log.v("funcall changeCurrentAttemptStatus");
    currentAttemptChanged = isChanged;
    notifyListeners();
  }

  void back() async {
    Log.v("funcall back");
    Log.v(() {
      String? files;
      for (var element in questions) {
        files = '$files\t${element.userUploadedFileUrl}';
      }
      return '\n\n\nlist $currentQuestionIndex start $files';
    }());
    Log.v('==> 1  ${questions.first.userUploadedFileUrl}');
    // await VideoCompress.cancelCompression();
    compressionPort?.close();
    inputExpanded = false;
    Log.v('==> 2  ${questions.first}');

    currentQuestionIndex--;
    changeCurrentAttemptStatus(isChanged: false);
    Log.v('==> 3  ${questions.first}');

    Log.v('==> 4  ${questions.first}');

    try {
      pickedFile = questions[currentQuestionIndex].userUploadedFileUrl.first;
    } catch (e) {
      Log.v("file name is ficked exception $e");
      pickedFile = null;
    }
    Log.v('==> 5  ${questions.first}');

    textEditingController = TextEditingController(
        text: questions[currentQuestionIndex].userAnswerTxt);
    Log.v('==> 6  ${questions.first}');

    pageController.jumpToPage(currentQuestionIndex);
    scrollController.animateTo(currentQuestionIndex.toDouble() - 50.0,
        duration: Duration(milliseconds: 500), curve: Curves.easeOut);
    notifyListeners();
    Log.v(() {
      String? files;
      for (var element in questions) {
        files = '$files\t${element.userUploadedFileUrl}';
      }
      return '\n\n\nlist $currentQuestionIndex end $files';
    }());

    // String getTimeLeft() {
    //   return '${this.stopWatchTimer.minuteTime.value}:${this.stopWatchTimer.secondTime.value - (this.stopWatchTimer.minuteTime.value * 60)}';
    // }

    // void dispose() {
    // Log.v("funcall dispose");
    //   super.dispose();
    //   this.stopWatchTimer.dispose();
    //   notifyListeners();
    // }
  }

  //  @override
  //   void dispose() {

  //    debugPrint('return path disspose');
  //   }
}
