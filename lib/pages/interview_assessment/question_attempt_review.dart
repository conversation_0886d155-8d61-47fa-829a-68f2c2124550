import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/providers/assessment_detail_provider.dart';
import 'package:masterg/pages/interview_assessment/model/question.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import '../custom_pages/screen_with_loader.dart';
import 'interview_provider.dart';
import 'dart:ui' as ui;

// ignore: must_be_immutable
class QuestionAttemptReview extends StatefulWidget {
  final InterviewAssessmentProvider interviewAssessmentProvider;
  final AssessmentDetailProvider? assessmentDetailProvider;
  final String? title;
  final bool isRTL;

  bool disableback;
  QuestionAttemptReview({
    super.key,
    required this.interviewAssessmentProvider,
    this.disableback = false,
    this.assessmentDetailProvider,
    this.title,
    required this.isRTL,
  });

  @override
  State<QuestionAttemptReview> createState() => _QuestionAttemptReviewState();
}

class _QuestionAttemptReviewState extends State<QuestionAttemptReview> {
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: widget.isRTL ? ui.TextDirection.rtl : ui.TextDirection.ltr,
      child: Scaffold(
        backgroundColor: context.appColors.background,
        appBar: AppBar(
            backgroundColor: context.appColors.surface,
            elevation: 0.4,
            leading: InkWell(
              onTap: () {
                if (widget.disableback == false) {
                  Navigator.of(context, rootNavigator: true).pop();
                }
              },
              child: Icon(
                widget.isRTL
                    ? Icons.arrow_back_ios_sharp
                    : Icons.arrow_back_ios_new_outlined,
                color: context.appColors.textBlack,
              ),
            ),
            title: Text(
              'video_interview',
              style: Styles.getRegularThemeStyle(context),
            ).tr()),
        body: ScreenWithLoader(
          isLoading: isLoading,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Text(
                  '${widget.title}',
                  style: Styles.bold(
                    color: context.appColors.textBlack,
                  ),
                ).tr(),
              ),
              Container(
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
                margin: EdgeInsets.symmetric(horizontal: 10, vertical: 60),
                child: Column(
                  children: [
                    Padding(
                        padding: const EdgeInsets.all(28.0),
                        child: Text(
                          'questions_attempt',
                          style: Styles.bold(
                            color: context.appColors.textBlack,
                          ),
                        ).tr()),
                    ConstrainedBox(
                      constraints:
                          BoxConstraints(maxHeight: height(context) * 0.33),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: GridView.builder(
                          // itemCount:42,
                          physics: BouncingScrollPhysics(),
                          itemCount: widget
                              .interviewAssessmentProvider.questions.length,
                          shrinkWrap: true,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 7),
                          itemBuilder: (BuildContext context, int index) {
                            return Container(
                              height: height(context) * 0.1,
                              width: height(context) * 0.1,
                              margin: EdgeInsets.all(5),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: widget
                                              .interviewAssessmentProvider
                                              .questions[index]
                                              .questionStatus ==
                                          QuestionStatus.done
                                      ? Color(0xff75D284)
                                      : Color(0xffFEA063),
                                  // color: Color(0xffFEA063),

                                  borderRadius:
                                      BorderRadius.all(Radius.circular(100))),
                              child: Text(
                                '${index + 1}',
                                style: Styles.bold(
                                  color: context.appColors.textWhite,
                                  size: 18,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Container(
                              height: 12,
                              width: 12,
                              margin: EdgeInsets.all(5),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Color(0xff75D284),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(100))),
                            ),
                            Text(
                              'question_you_submitted',
                              style: Styles.regular(
                                color: context.appColors.textBlack,
                                size: 12,
                              ),
                              textAlign: TextAlign.center,
                            ).tr()
                          ],
                        ),
                        SizedBox(width: 10),
                        Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Row(
                            children: [
                              Container(
                                height: 12,
                                width: 12,
                                margin: EdgeInsets.all(5),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    color: Color(0xffFEA063),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(100))),
                              ),
                              Text(
                                'question_not_attempt',
                                style: Styles.regular(
                                  color: context.appColors.textBlack,
                                  size: 12,
                                ),
                                textAlign: TextAlign.center,
                              ).tr()
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
              Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Text(
                    'are_you_sure_you_want_to_submit',
                    style: Styles.textBold(
                        color: context.appColors.primary, size: 20),
                  ).tr()),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      if (widget.disableback == false) {
                        Navigator.of(context, rootNavigator: true).pop(false);
                      }
                    },
                    child: Opacity(
                        opacity: widget.disableback ? 0.5 : 1,
                        child: Container(
                            height: 45,
                            width: 150,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border.all(),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5)),
                            ),
                            child: Text('cancel').tr())),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  InkWell(
                    onTap: () {
                      widget.interviewAssessmentProvider.closeAllDialog();
                      widget.interviewAssessmentProvider.finalSubmit();
                      widget.disableback = true;
                      Navigator.of(context, rootNavigator: true).pop();

                      // Navigator.pop(context, true);
                    },
                    child: Container(
                        height: 45,
                        width: 150,
                        // margin: EdgeInsets.only(left: 20.0),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: context.appColors.primary,
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                        ),
                        child: Text(
                          'submit',
                          style: TextStyle(color: context.appColors.textWhite),
                        ).tr()),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
