import 'dart:developer';
import 'dart:io';
import 'dart:isolate';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:camera/camera.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/data/models/response/home_response/test_attempt_response.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/interview_assessment/question_attempt_review.dart';
import 'package:masterg/pages/pdf_view_page.dart';
import 'package:masterg/pages/speech_assessment/assessment_speech.dart';
import 'package:masterg/pages/speech_assessment/audio_player.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import '../../main.dart';
import '../../utils/styles.dart';
import '../../utils/click_picker.dart';
import '../../utils/constant.dart';
import '../../utils/get_widget_size.dart';
import '../../utils/video_compress_isolate.dart';
import '../reels/video_recording/video_recording_camera_page.dart';
import 'interview_provider.dart';

enum PickFileType { camera, image, video, audio, document, any, none }

enum LocalFileType {
  Image,
  Video,
  Document,
  Audio,
}

class FloatingButton extends StatelessWidget {
  final RecorderController? recorderController;
  const FloatingButton({super.key, this.recorderController});

  final double buttonHeight = 35.0;
  final double buttonWidth = 120.0;
  final double fontSize = 14.0;

  LocalFileType? getFileType(String filePath) {
    final List<String> parts = filePath.split('.');
    if (parts.length > 1) {
      final String extension = parts.last;
      switch (extension.toLowerCase()) {
        case "jpg":
        case "jpeg":
        case "png":
          return LocalFileType.Image;
        case "mp4":
        case "avi":
        case "mkv":
        case "mov":
          return LocalFileType.Video;
        case "pdf":
        case "doc":
        case "txt":
          return LocalFileType.Document;
        case "mp3":
        case "wav":
        case "flac":
        case "m4a":
          return LocalFileType.Audio;
        default:
      }
    }
    return null;
  }

  void openRecordVideo(
      interviewAssessmentProvider, BuildContext context) async {
    interviewAssessmentProvider.textEditingController = TextEditingController();
    final scaffoldContext = context;

    await showDialog(
        context: context,
        builder: (context) {
          interviewAssessmentProvider.addContext(context);
          return VideoRecordingCameraPage(
            runOnRecordFinish: true,
            isFrontCamera: true,
            enableQualityDowngrade: true,
            infiniteRecording: false,
            //set default duration to 5 mins
            recordDuration: 5 * 60,
            onRecordFinish: (filePath, isRecorded) async {
              Log.v("file path is $filePath and is recorded $isRecorded");
              if (filePath == null) return;

              if (isRecorded) {
                interviewAssessmentProvider.changeCurrentAttemptStatus(
                    isChanged: true);
                interviewAssessmentProvider.addFile(filePath: filePath);
                interviewAssessmentProvider.textEditingController =
                    TextEditingController();
              }
              //check file size and compress it
              else {
                int fileSizeInBytes = File(filePath).lengthSync();
                int maxSizeInBytes = 225 * 1024 * 1024; // 200 MB
                if (fileSizeInBytes > maxSizeInBytes) {
                  // Handle file size exceeds limit

                  try {
                    ScaffoldMessenger.of(scaffoldContext).showSnackBar(
                        SnackBar(content: Text('file_size_message').tr()));
                  } catch (e) {
                    debugPrint('return path size done $e');
                  }
                  debugPrint('return path size done ');
                } else {
                  interviewAssessmentProvider
                      .updateCompressionPort(ReceivePort());
                  // ReceivePort port = ReceivePort();

                  FlutterIsolate? isolate;

                  try {
                    interviewAssessmentProvider.setPickingFile(picking: true);
                    interviewAssessmentProvider.setCompressing(
                        isCompressing: true);
                    isolate = await FlutterIsolate.spawn(
                        FileCompressionIsolate.compressIsolate, {
                      'filePath': filePath,
                      'sendPort':
                          interviewAssessmentProvider.compressionPort?.sendPort
                    });
                    interviewAssessmentProvider.setCompressing(
                        isCompressing: true);
                    interviewAssessmentProvider.compressionPort?.listen(
                        (dynamic message) async {
                      if (message is Map<String, dynamic>) {
                        log("message is $message");
                        if (message.containsKey('progress')) {
                          double progress = message['progress'];
                          // Handle progress updates if needed
                          interviewAssessmentProvider.updateCompressionPer(
                              per: progress);
                          log('Compression progress: $progress');
                        } else if (message.containsKey('path')) {
                          interviewAssessmentProvider.addFile(
                              filePath: '${message['path']}');
                          interviewAssessmentProvider.compressionPort?.close();
                        }
                      }
                    }, onDone: () async {
                      interviewAssessmentProvider.setPickingFile(
                          picking: false);
                      interviewAssessmentProvider.setCompressing(
                          isCompressing: false);
                      interviewAssessmentProvider.compressionPort?.close();
                      if (isolate != null) {
                        isolate?.kill();
                        isolate = null;
                      }
                    });
                  } catch (e, stacktrace) {
                    interviewAssessmentProvider.setPickingFile(picking: false);
                    log("media file is exception: $stacktrace",
                        name: "your_filename.dart");
                  }
                }
              }
            },
          );
        }).then((_) => interviewAssessmentProvider.removeLastContext());
  }

  void openAudioRecording(
      interviewAssessmentProvider, BuildContext context) async {
    interviewAssessmentProvider.textEditingController = TextEditingController();
    bool isRTL = Utility().isRTL(context);
    try {
      await showModalBottomSheet(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          context: context,
          isScrollControlled: true,
          enableDrag: false,
          backgroundColor: context.appColors.pureBlack,
          builder: (BuildContext context) {
            interviewAssessmentProvider.addContext(context);
            if (interviewAssessmentProvider.pickedFile == null) {
              interviewAssessmentProvider.changeCurrentAttemptStatus(
                  isChanged: true);
            }
            return Directionality(
              textDirection: Utility.setDirection(isRTL),
              child: FractionallySizedBox(
                heightFactor: 0.34,
                child: WaveRecord(
                  // isRTL: Utility().isRTL(context),
                  // isRTL: false,
                  onDelete: () {
                    interviewAssessmentProvider.removeFirstPickedFile();
                  },
                  onSave: (String path) {
                    interviewAssessmentProvider.addFile(filePath: path);
                  },
                ),
                // child: RecordAudio(
                //     onRecordEnd: (String filePath) {
                //   interviewAssessmentProvider.addFile(
                //       filePath: filePath);
                // }, onDelete: () {
                //   interviewAssessmentProvider
                //       .removeFirstPickedFile();
                // }),
              ),
            );
          }).then((_) => interviewAssessmentProvider.removeLastContext());
    } catch (e) {
      debugPrint("exception is $e");
    }
  }

  void openDocument(interviewAssessmentProvider, BuildContext context) async {
    interviewAssessmentProvider.textEditingController = TextEditingController();
    FileType fileType = FileType.custom;

    FilePickerResult? result;
    if (interviewAssessmentProvider.pickedFile == null) {
      interviewAssessmentProvider.changeCurrentAttemptStatus(isChanged: true);
    }
    try {
      if (Platform.isIOS) {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          allowedExtensions: ['pdf'],
          type: fileType,
        );
      } else {
        debugPrint("exception:: 1");

        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: fileType,
          allowedExtensions: ['pdf'],
          onFileLoading: (path) {},
        );
      }
    } on Exception catch (e) {
      // TODO
      debugPrint("exception:: $e");
    }
    if (result != null) {
      String selectedFilePath = result.files.first.path ?? '';
      if (selectedFilePath.endsWith('.pdf')) {
        interviewAssessmentProvider.addFile(filePath: selectedFilePath);
      } else {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('${tr('upload_only')} pdf')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final interviewAssessmentProvider =
        context.read<InterviewAssessmentProvider>();

    TestAttemptBean currentQuestion = interviewAssessmentProvider
        .questionList[interviewAssessmentProvider.currentQuestionIndex];
    Widget? attachFileWidget;
    PickFileType? pickFileType;

    BuildContext? dialogContext;

    switch (currentQuestion.question?.responseMedium) {
      case '1':
        attachFileWidget = SizedBox(
          width: width(context),
          child: textField(context,
              isEmail: true,
              controller: interviewAssessmentProvider.textEditingController,
              hintText: tr('type_your_answer'),
              validation: (value) {}, onChange: (value) {
            interviewAssessmentProvider.changeCurrentAttemptStatus(
                isChanged: true);
            if (!interviewAssessmentProvider.inputExpanded) {
              interviewAssessmentProvider.updateInputWidth(isExpanded: true);
            }
          }, onTap: () {
            interviewAssessmentProvider.updateInputWidth(isExpanded: true);
          }),
        );
        break;
      case '2':
        pickFileType = PickFileType.audio;
        attachFileWidget = fileTypeIcon(context, interviewAssessmentProvider,
            image: 'assets/images/audio.svg',
            text: 'upload_audio',
            pickFileType: pickFileType);

        break;
      case '3':
        pickFileType = PickFileType.video;
        attachFileWidget = fileTypeIcon(context, interviewAssessmentProvider,
            image: 'assets/images/photo_camera.svg',
            text: 'upload_video',
            pickFileType: pickFileType);

        break;
      case '4':
        pickFileType = PickFileType.any;

        attachFileWidget = Transform.rotate(
            angle: 26,
            child: Icon(
              Icons.attach_file_outlined,
              color: context.appColors.textBlack,
            ));
        // attachFileWidget = fileTypeIcon(context, interviewAssessmentProvider,
        //     text: 'upload_file', pickFileType: pickFileType);

        attachFileWidget = Row(
          children: [
            AnimatedContainer(
                curve: Curves.linear,
                width: width(context) *
                    (!interviewAssessmentProvider.inputExpanded ? 0.22 : 0.08),
                duration: Duration(milliseconds: 500),
                child: !interviewAssessmentProvider.inputExpanded
                    ? SizedBox(
                        height: 30,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          children: [
                            InkWell(
                              onTap: () async {
                                interviewAssessmentProvider.pauseVideo();

                                bool isRTL = Utility().isRTL(context);

                                if (interviewAssessmentProvider
                                        .textEditingController.text ==
                                    '') {
                                  openRecordVideo(
                                      interviewAssessmentProvider, context);
                                } else {
                                  AlertsWidget.showCustomDialog(
                                      isRTL: isRTL,
                                      context: context,
                                      title: tr('confirm'),
                                      text:
                                          'Are you sure your text response will be removed.',
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      enable: interviewAssessmentProvider
                                              .textEditingController.text !=
                                          '',
                                      onCancelClick: () {},
                                      onOkClick: () async {
                                        openRecordVideo(
                                            interviewAssessmentProvider,
                                            context);
                                      });
                                }

                                interviewAssessmentProvider.playVideo();
                              },
                              child: SvgPicture.asset(
                                'assets/images/missed_video_call.svg',
                                width: width(context) * 0.06,
                                height: width(context) * 0.06,
                                allowDrawingOutsideViewBox: true,
                              ),
                            ),
                            SizedBox(width: 6),
                            InkWell(
                              onTap: () async {
                                interviewAssessmentProvider.pauseVideo();
                                bool isRTL = Utility().isRTL(context);
                                if (interviewAssessmentProvider
                                        .textEditingController.text ==
                                    '') {
                                  openAudioRecording(
                                      interviewAssessmentProvider, context);
                                } else {
                                  AlertsWidget.showCustomDialog(
                                      isRTL: isRTL,
                                      context: context,
                                      title: tr('confirm'),
                                      text:
                                          'Are you sure your text response will be removed.',
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      onCancelClick: () {},
                                      onOkClick: () async {
                                        openAudioRecording(
                                            interviewAssessmentProvider,
                                            context);
                                      });
                                }

                                interviewAssessmentProvider.playVideo();
                              },
                              child: SvgPicture.asset(
                                'assets/images/keyboard_voice.svg',
                                width: width(context) * 0.06,
                                height: width(context) * 0.06,
                                allowDrawingOutsideViewBox: true,
                              ),
                            ),
                            SizedBox(width: 6),
                            InkWell(
                              onTap: () async {
                                interviewAssessmentProvider.pauseVideo();

                                bool isRTL = Utility().isRTL(context);

                                if (interviewAssessmentProvider
                                        .textEditingController.text ==
                                    '') {
                                  openDocument(
                                      interviewAssessmentProvider, context);
                                } else {
                                  AlertsWidget.showCustomDialog(
                                      isRTL: isRTL,
                                      context: context,
                                      title: tr('confirm'),
                                      text:
                                          'Are you sure your text response will be removed.',
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      onCancelClick: () {},
                                      onOkClick: () async {
                                        openDocument(
                                            interviewAssessmentProvider,
                                            context);
                                      });
                                }

                                interviewAssessmentProvider.playVideo();
                              },
                              child: SvgPicture.asset(
                                'assets/images/description.svg',
                                width: width(context) * 0.06,
                                height: width(context) * 0.06,
                                allowDrawingOutsideViewBox: true,
                              ),
                            ),
                          ],
                        ),
                      )
                    : InkWell(
                        onTap: () {
                          interviewAssessmentProvider.updateInputWidth(
                              isExpanded: false);
                        },
                        child: Container(
                          width: 60,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Icon(
                            Icons.arrow_forward_ios_outlined,
                            size: 20,
                          ),
                        ))),
            Spacer(),
            AnimatedContainer(
              curve: Curves.linear,
              width: width(context) *
                  (!interviewAssessmentProvider.inputExpanded ? 0.67 : 0.86),
              duration: Duration(milliseconds: 250),
              child: textField(
                context,
                isEmail: true,
                controller: interviewAssessmentProvider.textEditingController,
                hintText: tr('type_your_answer'),
                validation: (value) {},
                onChange: (value) {
                  interviewAssessmentProvider.changeCurrentAttemptStatus(
                      isChanged: true);
                  if (!interviewAssessmentProvider.inputExpanded) {
                    interviewAssessmentProvider.updateInputWidth(
                        isExpanded: true);
                  }
                },
                onTap: () {
                  interviewAssessmentProvider.updateInputWidth(
                      isExpanded: true);
                },
              ),
            )
          ],
        );

        break;
      default:
        attachFileWidget = SizedBox();
        pickFileType = PickFileType.none;
    }
    String answerTheQuestionText = 'answer_the_question';
    switch (currentQuestion.question?.responseMedium) {
      case '1':
        answerTheQuestionText = 'text';

        break;
      case '2':
        answerTheQuestionText = 'audio';

        break;
      case '3':
        answerTheQuestionText = 'video';

        break;
      case '4':
        answerTheQuestionText = 'below_format';

        break;
      default:
        attachFileWidget = SizedBox();
        pickFileType = PickFileType.none;
    }

    TextSpan getStyledText(String text, bool isBold, bool isRedStar) {
      return TextSpan(
        text: tr(text),
        style: TextStyle(
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          color: isRedStar ? Colors.red : Colors.black,
        ),
      );
    }

    return MeasureSize(
      onChange: (Size size) {
        interviewAssessmentProvider.updateFloatingBtnHeight(size.height);
      },
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: 80,
        ),
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
            color: context.appColors.surface,
            width: width(context),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  interviewAssessmentProvider.isReview
                      ? Text(
                          'user_submission',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ).tr()
                      : RichText(
                          text: TextSpan(
                            children: [
                              getStyledText(
                                  'answer_the_question', false, false),
                              TextSpan(
                                text: ' ',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              getStyledText(answerTheQuestionText, true, false),
                              TextSpan(
                                text: '*',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: context.appColors.error,
                                ),
                              )
                            ],
                          ),
                        ),
                  // SizedBox(height: 1),
                  // if (!interviewAssessmentProvider.isReview)
                  Divider(thickness: 1),
                  if (interviewAssessmentProvider.isReview) ...[
                    if ((interviewAssessmentProvider
                                    .questions[interviewAssessmentProvider
                                        .currentQuestionIndex]
                                    .reviewData['answer_statement'] !=
                                null &&
                            interviewAssessmentProvider
                                .questions[interviewAssessmentProvider
                                    .currentQuestionIndex]
                                .reviewData['answer_statement']
                                .isNotEmpty) &&
                        (interviewAssessmentProvider
                                    .questions[interviewAssessmentProvider
                                        .currentQuestionIndex]
                                    .reviewData['user_file'] !=
                                null &&
                            interviewAssessmentProvider
                                .questions[interviewAssessmentProvider
                                    .currentQuestionIndex]
                                .reviewData['user_file']
                                .isNotEmpty))
                      Divider(thickness: 1),
                    interviewAssessmentProvider
                                .questions[interviewAssessmentProvider
                                    .currentQuestionIndex]
                                .reviewData['user_file'] ==
                            ''
                        ? interviewAssessmentProvider
                                        .questions[interviewAssessmentProvider
                                            .currentQuestionIndex]
                                        .reviewData['answer_statement'] ==
                                    null ||
                                interviewAssessmentProvider
                                        .questions[interviewAssessmentProvider
                                            .currentQuestionIndex]
                                        .reviewData['answer_statement'] ==
                                    ''
                            ? SizedBox()
                            : Container(
                                height: 90,
                                width: width(context),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                    color: Color(0xffF4F7FD),
                                    borderRadius: BorderRadius.circular(8)),
                                child: SingleChildScrollView(
                                  child: Text(
                                    '${interviewAssessmentProvider.questions[interviewAssessmentProvider.currentQuestionIndex].reviewData['answer_statement']}',
                                  ),
                                ),
                              )
                        : Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 12),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: 1,
                                  itemBuilder: (context, index) {
                                    LocalFileType? fileType = getFileType(
                                        interviewAssessmentProvider
                                            .questions[
                                                interviewAssessmentProvider
                                                    .currentQuestionIndex]
                                            .reviewData['user_file']);
                                    String url =
                                        '${interviewAssessmentProvider.questions[interviewAssessmentProvider.currentQuestionIndex].reviewData['user_file']}';
                                    VideoPlayerController? controller;
                                    log("file type is $fileType");
                                    return InkWell(
                                      onTap: () async {
                                        interviewAssessmentProvider
                                            .pauseVideo();
                                        Widget? popWidget;

                                        switch (fileType) {
                                          case LocalFileType.Audio:
                                            popWidget = SizedBox(
                                                width: 300.0,
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment
                                                          .stretch,
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: <Widget>[
                                                    Center(
                                                        child: Container(
                                                            decoration: BoxDecoration(
                                                                color: context
                                                                    .appColors
                                                                    .surface,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            10)),
                                                            margin:
                                                                const EdgeInsets
                                                                    .all(10),
                                                            child:
                                                                AudioUrlPlayer(
                                                              source: Uri
                                                                  .encodeFull(
                                                                      url),
                                                              // source:
                                                              //     'https://filesamples.com/samples/audio/m4a/sample1.m4a',
                                                              isLocalFile:
                                                                  false,
                                                            ))),
                                                  ],
                                                ));
                                            break;
                                          case LocalFileType.Video:
                                            break;
                                          case LocalFileType.Document:
                                            popWidget = SizedBox(
                                                height: height(context),
                                                child: PDF(
                                                  enableSwipe: true,
                                                  swipeHorizontal: false,
                                                  pageFling: false,
                                                  fitPolicy: FitPolicy.BOTH,
                                                  onError: (error) {
                                                    debugPrint(
                                                        error.toString());
                                                  },
                                                  onPageError: (page, error) {
                                                    debugPrint(
                                                        '$page: ${error.toString()}');
                                                  },
                                                ).cachedFromUrl(url));
                                            break;
                                          case LocalFileType.Image:
                                            popWidget = Image.network(url);
                                            break;

                                          default:
                                        }
                                        if (fileType == LocalFileType.Video) {
                                          controller?.play();
                                          bool isRTL = Utility().isRTL(context);
                                          log("data context is $isRTL");
                                          await showDialog(
                                              context: context,
                                              useSafeArea: false,
                                              builder: (context) {
                                                interviewAssessmentProvider
                                                    .addContext(context);
                                                log("log the video check $url ");
                                                controller =
                                                    VideoPlayerController
                                                        .networkUrl(
                                                            Uri.parse(url));

                                                popWidget = FlickVideoPlayer(
                                                    flickVideoWithControls:
                                                        FlickVideoWithControls(
                                                      videoFit: BoxFit.contain,
                                                      controls:
                                                          FlickPortraitControls(
                                                        progressBarSettings:
                                                            FlickProgressBarSettings(),
                                                      ),
                                                    ),
                                                    flickManager: FlickManager(
                                                        videoPlayerController:
                                                            controller!));
                                                return Directionality(
                                                  textDirection:
                                                      Utility.setDirection(
                                                          isRTL),
                                                  child: Scaffold(
                                                    backgroundColor: context
                                                        .appColors
                                                        .textDarkBlack,
                                                    appBar: AppBar(
                                                      elevation: 0,
                                                      backgroundColor: context
                                                          .appColors
                                                          .textDarkBlack,
                                                      iconTheme: IconThemeData(
                                                          color: context
                                                              .appColors
                                                              .textWhite),
                                                    ),

                                                    // body: Text(
                                                    //     '${controller!.value.aspectRatio}'),

                                                    body: Directionality(
                                                      textDirection:
                                                          Utility.setDirection(
                                                              false),
                                                      child: SizedBox(
                                                          height:
                                                              height(context) *
                                                                  0.8,
                                                          child: AspectRatio(
                                                              aspectRatio:
                                                                  controller!
                                                                      .value
                                                                      .aspectRatio,
                                                              child:
                                                                  popWidget)),
                                                    ),
                                                  ),
                                                );
                                              }).then((_) async {
                                            await controller?.pause();
                                            interviewAssessmentProvider
                                                .removeLastContext();
                                          });
                                        } else if (popWidget != null &&
                                            fileType !=
                                                LocalFileType.Document) {
                                          await showDialog(
                                                  context: context,
                                                  builder: (BuildContext context) {
                                                    dialogContext = context;
                                                    interviewAssessmentProvider
                                                        .addContext(context);
                                                    return AlertDialog(
                                                        backgroundColor: Colors
                                                            .transparent,
                                                        shape: RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius.all(
                                                                    Radius.circular(
                                                                        10.0))),
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        0),
                                                        content: SizedBox(
                                                            width: 300.0,
                                                            child: Stack(
                                                              children: [
                                                                Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .stretch,
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: <Widget>[
                                                                    Center(
                                                                        child: Container(
                                                                            decoration:
                                                                                BoxDecoration(color: context.appColors.surface, borderRadius: BorderRadius.circular(10)),
                                                                            margin: const EdgeInsets.all(10),
                                                                            child: popWidget)),
                                                                  ],
                                                                ),
                                                                Positioned(
                                                                  right: 0,
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .end,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .end,
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .min,
                                                                    children: <Widget>[
                                                                      GestureDetector(
                                                                        onTap:
                                                                            () {
                                                                          Navigator.pop(
                                                                              context);
                                                                        },
                                                                        child:
                                                                            Container(
                                                                          height:
                                                                              30,
                                                                          width:
                                                                              30,
                                                                          decoration:
                                                                              BoxDecoration(
                                                                            color:
                                                                                context.appColors.surface,
                                                                            shape:
                                                                                BoxShape.circle,
                                                                          ),
                                                                          child:
                                                                              Icon(
                                                                            Icons.close_rounded,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            )));
                                                  })
                                              .then((_) =>
                                                  interviewAssessmentProvider
                                                      .removeLastContext());
                                        }
                                        if (fileType ==
                                            LocalFileType.Document) {
                                          controller?.play();
                                          bool isRTL = Utility().isRTL(context);
                                          log("data context is $isRTL");
                                          await showDialog(
                                                  context: context,
                                                  useSafeArea: false,
                                                  builder: (context) {
                                                    interviewAssessmentProvider
                                                        .addContext(context);
                                                    return Directionality(
                                                      textDirection:
                                                          Utility.setDirection(
                                                              isRTL),
                                                      child: Scaffold(
                                                        backgroundColor: context
                                                            .appColors
                                                            .textDarkBlack,
                                                        appBar: AppBar(
                                                          elevation: 0,
                                                          backgroundColor:
                                                              context.appColors
                                                                  .textDarkBlack,
                                                          iconTheme:
                                                              IconThemeData(
                                                                  color: context
                                                                      .appColors
                                                                      .textWhite),
                                                        ),
                                                        body: SizedBox(
                                                            height:
                                                                height(context),
                                                            child: Center(
                                                                child:
                                                                    popWidget)),
                                                      ),
                                                    );
                                                  })
                                              .then((_) =>
                                                  interviewAssessmentProvider
                                                      .removeLastContext());
                                          controller?.pause();
                                        }

                                        interviewAssessmentProvider.playVideo();

                                        // interviewAssessmentProvider
                                        //     .questionVideoController
                                        //     ?.play();
                                      },
                                      child: Container(
                                        margin: const EdgeInsets.symmetric(
                                            vertical: 4),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            border: Border.all()),
                                        child: Row(children: [
                                          Opacity(
                                            opacity: 0.5,
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(4.0),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: Color(0xffFEA063),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10.0), // Border radius
                                                ),
                                                height: 60,
                                                width: 60,
                                                child: fileType ==
                                                        LocalFileType.Video
                                                    // ? Icon(Icons.video_collection)
                                                    ? FutureBuilder<String?>(
                                                        future:
                                                            Utility.getThumnail(
                                                                url, context),
                                                        builder:
                                                            (context,
                                                                    snapshot) =>
                                                                snapshot.hasData
                                                                    ? Stack(
                                                                        children: [
                                                                          Positioned
                                                                              .fill(
                                                                            child: ClipRRect(
                                                                                borderRadius: BorderRadius.circular(10),
                                                                                child: Image.file(
                                                                                  File(
                                                                                    '${snapshot.data}',
                                                                                  ),
                                                                                  fit: BoxFit.cover,
                                                                                )),
                                                                          ),
                                                                          Center(
                                                                              child: Icon(
                                                                            Icons.play_circle_filled,
                                                                            size:
                                                                                32,
                                                                            color:
                                                                                context.appColors.textWhite,
                                                                          ))
                                                                        ],
                                                                      )
                                                                    : SizedBox(
                                                                        width:
                                                                            30,
                                                                        height:
                                                                            30,
                                                                        child:
                                                                            Center(
                                                                          /*child: Text(
                                                                    "Loading.....${snapshot.connectionState}")*/
                                                                          child:
                                                                              Image.asset('assets/images/blank.png'),
                                                                        ),
                                                                      ))
                                                    : fileType ==
                                                            LocalFileType.Image
                                                        ? Image.network(url)
                                                        : fileType ==
                                                                LocalFileType
                                                                    .Audio
                                                            ? Icon(Icons.mic)
                                                            : Icon(Icons
                                                                .document_scanner),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          SizedBox(
                                            width: width(context) * 0.70,
                                            child: Text(
                                              url.split('/').last,
                                              style:
                                                  Styles.getRegularThemeStyle(
                                                      context,
                                                      size: 12),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ]),
                                      ),
                                    );
                                  }),
                            ),
                          ),
                  ] else if (interviewAssessmentProvider.pickedFile !=
                      null) ...[
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 12),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 1,
                            itemBuilder: (context, index) {
                              LocalFileType? fileType = getFileType(
                                  interviewAssessmentProvider.pickedFile
                                      .toString());
                              File file = File(
                                  '${interviewAssessmentProvider.pickedFile}');
                              VideoPlayerController? controller;

                              return InkWell(
                                onTap: () async {
                                  interviewAssessmentProvider.pauseVideo();
                                  Widget? popWidget;
                                  switch (fileType) {
                                    case LocalFileType.Audio:
                                      popWidget = WaveBubble(
                                        path: file.path,
                                        isRTL: Utility().isRTL(context),
                                        onDelete: () {
                                          interviewAssessmentProvider
                                              .removeFirstPickedFile();
                                          Navigator.pop(dialogContext!);
                                        },
                                      );
                                      break;
                                    case LocalFileType.Video:
                                      log("log the video khjsdf $file ");
                                      controller =
                                          VideoPlayerController.file(file);

                                      popWidget = Directionality(
                                        textDirection:
                                            Utility.setDirection(false),
                                        child: AspectRatio(
                                          aspectRatio:
                                              controller!.value.aspectRatio,
                                          child: FlickVideoPlayer(
                                              flickVideoWithControls:
                                                  FlickVideoWithControls(
                                                videoFit: BoxFit.contain,
                                                controls: FlickPortraitControls(
                                                  progressBarSettings:
                                                      FlickProgressBarSettings(),
                                                ),
                                              ),
                                              flickManager: FlickManager(
                                                  videoPlayerController:
                                                      controller!)),
                                        ),
                                      );
                                      break;
                                    case LocalFileType.Document:
                                      popWidget = SizedBox(
                                          height: height(context),
                                          child: ViewPdfPage(
                                            path: file.path,
                                            hideAppbar: true,
                                          ));
                                      break;
                                    case LocalFileType.Image:
                                      popWidget = Image.file(file);
                                      break;

                                    default:
                                  }
                                  if (fileType == LocalFileType.Video) {
                                    controller?.play();
                                    bool isRTL = Utility().isRTL(context);
                                    log("data context is $isRTL");
                                    await showDialog(
                                            context: context,
                                            useSafeArea: false,
                                            builder: (context) {
                                              interviewAssessmentProvider
                                                  .addContext(context);
                                              return Directionality(
                                                textDirection:
                                                    Utility.setDirection(isRTL),
                                                child: Scaffold(
                                                  backgroundColor: context
                                                      .appColors.textDarkBlack,
                                                  appBar: AppBar(
                                                    elevation: 0,
                                                    backgroundColor: context
                                                        .appColors
                                                        .textDarkBlack,
                                                    iconTheme: IconThemeData(
                                                        color: context.appColors
                                                            .textWhite),
                                                  ),
                                                  body: SizedBox(
                                                      height:
                                                          height(context) * 0.8,
                                                      child: popWidget),
                                                ),
                                              );
                                            })
                                        .then((_) => interviewAssessmentProvider
                                            .removeLastContext());
                                    controller?.pause();
                                  } else if (popWidget != null &&
                                      fileType != LocalFileType.Document) {
                                    await showDialog(
                                            context: context,
                                            builder: (BuildContext context) {
                                              dialogContext = context;
                                              interviewAssessmentProvider
                                                  .addContext(context);
                                              return AlertDialog(
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.all(
                                                              Radius.circular(
                                                                  10.0))),
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          horizontal: 0),
                                                  content: SizedBox(
                                                      width: 300.0,
                                                      child: Stack(
                                                        children: [
                                                          Column(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .start,
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .stretch,
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: <Widget>[
                                                              Center(
                                                                  child: Container(
                                                                      decoration: BoxDecoration(
                                                                          color: context
                                                                              .appColors
                                                                              .surface,
                                                                          borderRadius: BorderRadius.circular(
                                                                              10)),
                                                                      margin: const EdgeInsets
                                                                          .all(
                                                                          10),
                                                                      child:
                                                                          popWidget)),
                                                            ],
                                                          ),
                                                          Positioned(
                                                            right: 0,
                                                            child: Column(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .end,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .end,
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .min,
                                                              children: <Widget>[
                                                                GestureDetector(
                                                                  onTap: () {
                                                                    Navigator.pop(
                                                                        context);
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    height: 30,
                                                                    width: 30,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: context
                                                                          .appColors
                                                                          .surface,
                                                                      shape: BoxShape
                                                                          .circle,
                                                                    ),
                                                                    child: Icon(
                                                                      Icons
                                                                          .close_rounded,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      )));
                                            })
                                        .then((_) => interviewAssessmentProvider
                                            .removeLastContext());
                                  }
                                  if (fileType == LocalFileType.Document) {
                                    controller?.play();
                                    bool isRTL = Utility().isRTL(context);
                                    log("data context is $isRTL");
                                    await showDialog(
                                            context: context,
                                            useSafeArea: false,
                                            builder: (context) {
                                              interviewAssessmentProvider
                                                  .addContext(context);
                                              return Directionality(
                                                textDirection:
                                                    Utility.setDirection(isRTL),
                                                child: Scaffold(
                                                  backgroundColor: context
                                                      .appColors.textDarkBlack,
                                                  appBar: AppBar(
                                                    elevation: 0,
                                                    backgroundColor: context
                                                        .appColors
                                                        .textDarkBlack,
                                                    iconTheme: IconThemeData(
                                                        color: context.appColors
                                                            .textWhite),
                                                  ),
                                                  body: SizedBox(
                                                      height: height(context),
                                                      child: Center(
                                                          child: popWidget)),
                                                ),
                                              );
                                            })
                                        .then((_) => interviewAssessmentProvider
                                            .removeLastContext());
                                    controller?.pause();
                                  }

                                  interviewAssessmentProvider.playVideo();

                                  // interviewAssessmentProvider
                                  //     .questionVideoController
                                  //     ?.play();
                                },
                                child: Container(
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 4),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all()),
                                  child: Row(children: [
                                    Opacity(
                                      opacity: 0.5,
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Color(0xffFEA063),
                                            borderRadius: BorderRadius.circular(
                                                10.0), // Border radius
                                          ),
                                          height: 60,
                                          width: 60,
                                          child: fileType == LocalFileType.Video
                                              // ? Icon(Icons.video_collection)
                                              ? FutureBuilder<String?>(
                                                  future: Utility.getThumnail(
                                                      file.path, context),
                                                  builder: (context,
                                                          snapshot) =>
                                                      snapshot.hasData
                                                          ? Stack(
                                                              children: [
                                                                Positioned.fill(
                                                                  child: ClipRRect(
                                                                      borderRadius: BorderRadius.circular(10),
                                                                      child: Image.file(
                                                                        File(
                                                                          '${snapshot.data}',
                                                                        ),
                                                                        fit: BoxFit
                                                                            .cover,
                                                                      )),
                                                                ),
                                                                Center(
                                                                    child: Icon(
                                                                  Icons
                                                                      .play_circle_filled,
                                                                  size: 32,
                                                                  color: context
                                                                      .appColors
                                                                      .textWhite,
                                                                ))
                                                              ],
                                                            )
                                                          : SizedBox(
                                                              width: 30,
                                                              height: 30,
                                                              child: Center(
                                                                /*child: Text(
                                                                    "Loading.....${snapshot.connectionState}")*/
                                                                child: Image.asset(
                                                                    'assets/images/blank.png'),
                                                              ),
                                                            ))
                                              : fileType == LocalFileType.Image
                                                  ? Image.file(file)
                                                  : fileType ==
                                                          LocalFileType.Audio
                                                      ? Icon(Icons.mic)
                                                      : Icon(Icons
                                                          .document_scanner),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                    SizedBox(
                                      width: width(context) * 0.50,
                                      child: Text(
                                        file.path.split('/').last,
                                        style: Styles.getRegularThemeStyle(
                                            context,
                                            size: 12),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Expanded(
                                      child: Opacity(
                                        opacity: 0.5,
                                        child: Container(
                                          height: 70,
                                          width: 70,
                                          decoration: BoxDecoration(
                                            color: Color(0xffCED4E7),

                                            borderRadius: BorderRadius.circular(
                                                10.0), // Border radius
                                          ),
                                          child: IconButton(
                                              onPressed: () {
                                                bool isRTL =
                                                    Utility().isRTL(context);
                                                AlertsWidget.showCustomDialog(
                                                    isRTL: isRTL,
                                                    context: context,
                                                    title: '',
                                                    text: tr(
                                                        'confirm_deletion_textone'),
                                                    icon:
                                                        'assets/images/circle_alert_fill.svg',
                                                    onOkClick: () async {
                                                      interviewAssessmentProvider
                                                          .removeFirstPickedFile();
                                                    });
                                              },
                                              icon: SvgPicture.asset(
                                                'assets/images/delete.svg',
                                                height: 20,
                                                colorFilter: context.isDarkMode
                                                    ? ColorFilter.mode(
                                                        context.appColors
                                                            .primaryForeground,
                                                        BlendMode.srcIn)
                                                    : null,
                                                // fit: BoxFit.cover,
                                                // width: width(context) * 0.5,
                                              )),
                                        ),
                                      ),
                                    )
                                  ]),
                                ),
                              );
                            }),
                      ),
                    ),
                  ],
                  if (interviewAssessmentProvider.pickedFile != null &&
                      interviewAssessmentProvider.isUploading)
                    StreamBuilder<double>(
                      stream: uploadProgressController.stream,
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          double progress = snapshot.data ?? 0.0;
                          return Stack(
                            children: [
                              Container(
                                width: width(context),
                                height: 8,
                                decoration: BoxDecoration(
                                    color: Color(0xffD9D9D9),
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                              Container(
                                width: width(context) * (progress / 100),
                                height: 8,
                                decoration: BoxDecoration(
                                    color: context.appColors.primary,
                                    borderRadius: BorderRadius.circular(10)),
                              )
                            ],
                          );
                          // return Text(
                          //     'Upload Progress: ${(progress).toStringAsFixed(2)}%');
                        } else {
                          return SizedBox();
                        }
                      },
                    ),
                  SizedBox(
                    height: 10,
                  ),

                  interviewAssessmentProvider.isCompressingVideo ||
                          interviewAssessmentProvider.pickingFile == true
                      ? Center(
                          child: SizedBox(
                              child: !interviewAssessmentProvider
                                      .isCompressingVideo
                                  ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'validate_file',
                                          style: Styles.getSemiboldThemeStyle(
                                              context),
                                        ).tr(),
                                        SizedBox(width: 10),
                                        SizedBox(
                                            width: 15,
                                            height: 15,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            )),
                                      ],
                                    )
                                  : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'preparing_your_file',
                                          style: Styles.getSemiboldThemeStyle(
                                              context),
                                        ).tr(),
                                        // SizedBox(width: 10),
                                        // SizedBox(
                                        //     width: 15,
                                        //     height: 15,
                                        //     child: CircularProgressIndicator(
                                        //       strokeWidth: 2,
                                        //     )),
                                        SizedBox(width: 10),
                                        Text(
                                          '${interviewAssessmentProvider.compressionPer.toString().split('.').first}%',
                                          style: Styles.getSemiboldThemeStyle(
                                              context),
                                        ),
                                      ],
                                    )),
                        )
                      : interviewAssessmentProvider.pickedFile == null
                          ? Padding(
                              padding: EdgeInsets.only(
                                right: Utility().isRTL(context) ? 10 : 0,
                              ),
                              child: attachFileWidget)
                          : SizedBox(),

                  SizedBox(
                    height: 10,
                  ),
                  Divider(thickness: 1),
                  Padding(
                    padding: interviewAssessmentProvider.isReview
                        ? const EdgeInsets.symmetric(horizontal: 12)
                        : const EdgeInsets.symmetric(horizontal: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        interviewAssessmentProvider.isReview
                            ? interviewAssessmentProvider
                                        .currentQuestionIndex ==
                                    0
                                ? SizedBox()
                                : InkWell(
                                    onTap: () async {
                                      interviewAssessmentProvider.reviewPrev();
                                    },
                                    child: Container(
                                      height: buttonHeight,
                                      width:
                                          interviewAssessmentProvider.isReview
                                              ? buttonWidth - 30
                                              : buttonWidth,
                                      margin: const EdgeInsets.only(
                                          right: 0, top: 10.0),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4, horizontal: 10),
                                      decoration: BoxDecoration(
                                          color: context.appColors.primary,
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                      child: Center(
                                        child: Text(
                                          'previous',
                                          style: Styles.semibold(
                                              size: fontSize,
                                              color:
                                                  context.appColors.textWhite),
                                        ).tr(),
                                      ),
                                    ),
                                  )
                            : IconButton(
                                onPressed: () {
                                  if (interviewAssessmentProvider
                                          .currentQuestionIndex ==
                                      0) {
                                    return;
                                  }
                                  interviewAssessmentProvider.back();
                                },
                                icon: interviewAssessmentProvider
                                            .currentQuestionIndex ==
                                        0
                                    ? SizedBox()
                                    : Icon(CupertinoIcons.back,
                                        color: context.appColors.lebelText)),
                        Expanded(child: SizedBox()),
                        if (!interviewAssessmentProvider.isReview)
                          InkWell(
                            onTap: () async {
                              bool isRTL = Utility().isRTL(context);
                              log("data context is $isRTL");
                              if (interviewAssessmentProvider
                                      .questions.length ==
                                  interviewAssessmentProvider
                                          .currentQuestionIndex +
                                      1) {
                                await showDialog(
                                    context: context,
                                    useSafeArea: false,
                                    builder: (context) {
                                      // interviewAssessmentProvider
                                      //     .addContext(context);
                                      return QuestionAttemptReview(
                                          isRTL: isRTL,
                                          title:
                                              interviewAssessmentProvider.title,
                                          interviewAssessmentProvider:
                                              interviewAssessmentProvider);
                                    }).then((value) {
                                  if (value == true) {
                                    // interviewAssessmentProvider.removeLastContext();
                                    Navigator.popUntil(
                                        context, (route) => route.isFirst);
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                      content:
                                          Text('assesssment_submitted').tr(),
                                    ));
                                  }
                                });
                              } else {
                                //revisit
                                if (interviewAssessmentProvider
                                        .currentQuestionIndex !=
                                    interviewAssessmentProvider
                                        .lastVisitedIndex) {
                                  interviewAssessmentProvider.goforward();
                                } else {
                                  //current visit
                                  interviewAssessmentProvider.skip();
                                }
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10.0),
                              child: Center(
                                child: Row(
                                  children: [
                                    Text(
                                      'skip',
                                      style: Styles.semibold(
                                          size: fontSize,
                                          color: context.appColors.lebelText),
                                    ).tr(),
                                    Icon(Icons.arrow_forward_ios_outlined,
                                        size: 10)
                                  ],
                                ),
                              ),
                            ),
                          ),
                        SizedBox(width: 15),
                        if (!(interviewAssessmentProvider.isReview &&
                            (interviewAssessmentProvider.currentQuestionIndex ==
                                interviewAssessmentProvider
                                        .questionList.length -
                                    1)))
                          InkWell(
                            onTap: () async {
                              if (interviewAssessmentProvider.isReview) {
                                interviewAssessmentProvider.reviewNext();
                                return;
                              }

                              if (interviewAssessmentProvider.isUploading ==
                                  true) {
                                return;
                              }

                              //revisit
                              if (interviewAssessmentProvider
                                          .currentQuestionIndex !=
                                      interviewAssessmentProvider
                                          .lastVisitedIndex &&
                                  interviewAssessmentProvider
                                          .currentAttemptChanged ==
                                      true) {
                                interviewAssessmentProvider.saveNext();
                                return;
                              } else if (interviewAssessmentProvider
                                          .currentQuestionIndex !=
                                      interviewAssessmentProvider
                                          .lastVisitedIndex &&
                                  interviewAssessmentProvider
                                          .currentAttemptChanged ==
                                      false) {
                                interviewAssessmentProvider.goforward();
                                return;
                              } else if (!(interviewAssessmentProvider
                                          .pickedFile !=
                                      null ||
                                  interviewAssessmentProvider
                                          .textEditingController.text !=
                                      '')) {
                                bool isRTL = Utility().isRTL(context);
                                log("data context is $isRTL");
                                if (interviewAssessmentProvider
                                        .questions.length ==
                                    interviewAssessmentProvider
                                            .currentQuestionIndex +
                                        1) {
                                  await showDialog(
                                      context: context,
                                      useSafeArea: false,
                                      builder: (context) {
                                        // interviewAssessmentProvider
                                        //     .addContext(context);
                                        return QuestionAttemptReview(
                                            isRTL: isRTL,
                                            title: interviewAssessmentProvider
                                                .title,
                                            interviewAssessmentProvider:
                                                interviewAssessmentProvider);
                                      }).then((value) {
                                    if (value == true) {
                                      // interviewAssessmentProvider.removeLastContext();
                                      Navigator.popUntil(
                                          context, (route) => route.isFirst);
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content:
                                            Text('assesssment_submitted').tr(),
                                      ));
                                    }
                                  });
                                } else {
                                  interviewAssessmentProvider.skip();
                                }
                                // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                //   content: Text('answer_question_skip').tr(),
                                // ));
                                return;
                              }
                              interviewAssessmentProvider.saveNext();

                              //  else {
                              //   interviewAssessmentProvider.setloading();
                              //   await Future.delayed(Duration(seconds: 1)).then(
                              //       (value) =>
                              //           interviewAssessmentProvider.moveNext());
                              //   interviewAssessmentProvider.loadingComplete();
                              // }
                            },
                            child: Container(
                              height: buttonHeight,
                              width: interviewAssessmentProvider.isReview
                                  ? buttonWidth - 30
                                  : buttonWidth,
                              margin:
                                  const EdgeInsets.only(right: 0, top: 10.0),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 10),
                              decoration: BoxDecoration(
                                  color: context.appColors.primary,
                                  borderRadius: BorderRadius.circular(6)),
                              child: Center(
                                child: Text(
                                  interviewAssessmentProvider.isReview
                                      ? 'next'
                                      : 'save_next',
                                  style: Styles.semibold(
                                      size: fontSize,
                                      color: context.appColors.textWhite),
                                ).tr(),
                              ),
                            ),
                          )
                      ],
                    ),
                  ),
                  SizedBox(height: height(context) * 0.01)
                ],
              ),
            )),
      ),
    );
  }
}

Widget textField(
  BuildContext context, {
  bool isEmail = false,
  TextEditingController? controller,
  String? hintText,
  bool obscureText = false,
  required Function(String) validation,
  Function()? onEyePress,
  required Function(String) onChange,
  required Function() onTap,
}) {
  return ConstrainedBox(
    constraints: const BoxConstraints(maxHeight: 100),
    child: Container(
      decoration: BoxDecoration(
          color: Color(0xffF4F7FD), borderRadius: BorderRadius.circular(8)),
      child: TextFormField(
        maxLines: null,
        textInputAction: TextInputAction.done,
        cursorColor: context.appColors.textBlack,
        style: Styles.regular(color: context.appColors.headingPrimaryColor),
        controller: controller,
        obscureText: obscureText,
        onTap: onTap,
        onChanged: onChange,
        decoration: InputDecoration(
          hintText: hintText,
          border: InputBorder.none,
          suffixIcon: onEyePress != null
              ? TapWidget(
                  onTap: () {
                    onEyePress();
                  },
                  child: Padding(
                      padding: const EdgeInsets.only(right: 5),
                      child: !obscureText
                          ? Icon(
                              Icons.remove_red_eye_outlined,
                              color: context.appColors.headingPrimaryColor,
                            )
                          : Icon(
                              Icons.visibility_off,
                              color: context.appColors.grey3,
                            )),
                )
              : null,
          focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
          hintStyle: Styles.regular(size: 14, color: context.appColors.grey3),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          // enabledBorder: OutlineInputBorder(
          //     borderRadius: BorderRadius.all(Radius.circular(10)),
          //     borderSide: BorderSide(color: context.appColors.grey3, width: 0)),
          // focusedBorder: OutlineInputBorder(
          //     borderRadius: BorderRadius.all(Radius.circular(10)),
          //     borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
          disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
          errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
        ),
      ),
    ),
  );
}

// pickFilePopUp(
//     Offset offset, context, InterviewAssessmentProvider provider) async {
//   final screenSize = MediaQuery.of(context).size;
//   double left = offset.dx - 40;
//   double top = offset.dy - 100;
//   double right = screenSize.width - left;
//   double bottom = screenSize.height - top;

//   showMenu<PickFileType>(
//     context: context,
//     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
//     position: RelativeRect.fromLTRB(left, top, right, bottom),
//     items: [
//       PopupMenuItem<PickFileType>(
//           child: fileTypeTitle(
//               image: 'assets/images/camera.svg', title: '${tr('camera')}'),
//           value: PickFileType.camera),
//       PopupMenuItem<PickFileType>(
//           child: fileTypeTitle(
//               image: 'assets/images/image.svg', title: '${tr('images')}'),
//           value: PickFileType.image),
//       PopupMenuItem<PickFileType>(
//           child: fileTypeTitle(
//               image: 'assets/images/video.svg', title: '${tr('video')}'),
//           value: PickFileType.video),
//       PopupMenuItem<PickFileType>(
//           child: fileTypeTitle(
//               image: 'assets/images/audio.svg', title: '${tr('audio')}'),
//           value: PickFileType.audio),
//       PopupMenuItem<PickFileType>(
//           child: fileTypeTitle(
//               image: 'assets/images/camera.svg', title: '${tr('document')}'),
//           value: PickFileType.document),
//     ],
//     elevation: 8.0,
//   ).then((value) async {
//     FilePickerResult? result;
//     FileType fileType = FileType.custom;
//     switch (value) {
//       case PickFileType.audio:
//         fileType = FileType.audio;
//         break;
//       case PickFileType.document:
//         fileType = FileType.custom;
//         break;
//       case PickFileType.video:
//         fileType = FileType.video;
//         break;
//       case PickFileType.image:
//         fileType = FileType.image;
//         break;
//       case PickFileType.camera:
//         final cameras = await availableCameras();
//         final firstCamera = cameras.first;
//         showDialog(
//             context: context,
//             builder: (context) => TakePictureScreen(
//                   camera: firstCamera,
//                   cameras: cameras,
//                 )).then((value) => provider.pickedFiles.add(value));

//         break;
//       default:
//     }
//     if (value == null) return;
//     if (Platform.isIOS) {
//       result = await FilePicker.platform.pickFiles(
//           allowMultiple: false, type: fileType, allowedExtensions: []);
//     } else {
//       result = await FilePicker.platform.pickFiles(
//         allowMultiple: false,
//         type: fileType,
//         onFileLoading: (path) {},
//       );
//     }

//     if (result != null) {
//       for (var e in result.files) {
//         provider.addFile(filePath: '${e.path}');
//       }
//     }
//   });
// }

Widget fileTypeTitle(BuildContext context, {String? image, String? title}) {
  return Row(
    children: [
      SvgPicture.asset(
        '$image',
        width: 20,
        height: 20,
        color: context.appColors.textBlack,
        allowDrawingOutsideViewBox: true,
      ),
      SizedBox(width: 10),
      Text('$title')
    ],
  );
}

Widget fileTypeIcon(BuildContext context,
    InterviewAssessmentProvider interviewAssessmentProvider,
    {String? image, String? text, required PickFileType pickFileType}) {
  return interviewAssessmentProvider.isCompressingVideo ||
          interviewAssessmentProvider.pickingFile == true
      ? Center(
          child: SizedBox(
              child: !interviewAssessmentProvider.isCompressingVideo
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'validate_file',
                          style: Styles.getSemiboldThemeStyle(context),
                        ).tr(),
                        SizedBox(width: 10),
                        SizedBox(
                            width: 15,
                            height: 15,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            )),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'preparing_your_file',
                          style: Styles.getSemiboldThemeStyle(context),
                        ).tr(),
                        // SizedBox(width: 10),
                        // SizedBox(
                        //     width: 15,
                        //     height: 15,
                        //     child: CircularProgressIndicator(
                        //       strokeWidth: 2,
                        //     )),
                        SizedBox(width: 10),
                        Text(
                          '${interviewAssessmentProvider.compressionPer.toString().split('.').first}%',
                          style: Styles.getSemiboldThemeStyle(context),
                        ),
                      ],
                    )),
        )
      : Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (image != null)
              InkWell(
                onTap: () async {
                  // FilePickerResult? result;
                  // FileType fileType = FileType.custom;

                  switch (pickFileType) {
                    case PickFileType.audio:
                      // fileType = FileType.audio;
                      interviewAssessmentProvider.pauseVideo();
                      // bool isRTL = Utility().isRTL(context);
                      await showModalBottomSheet(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              context: context,
                              enableDrag: false,
                              backgroundColor: Colors.black,
                              builder: (BuildContext context) {
                                interviewAssessmentProvider.addContext(context);

                                if (interviewAssessmentProvider.pickedFile ==
                                    null) {
                                  interviewAssessmentProvider
                                      .changeCurrentAttemptStatus(
                                          isChanged: true);
                                }
                                return FractionallySizedBox(
                                  heightFactor: 0.6,
                                  child: WaveRecord(
                                    // isRTL: isRTL,
                                    onDelete: () {
                                      interviewAssessmentProvider
                                          .removeFirstPickedFile();
                                    },
                                    onSave: (String path) {
                                      interviewAssessmentProvider.addFile(
                                          filePath: path);
                                    },
                                  ),
                                  // child: RecordAudio(onRecordEnd: (String filePath) {
                                  //   interviewAssessmentProvider.addFile(
                                  //       filePath: filePath);
                                  // }, onDelete: () {
                                  //   interviewAssessmentProvider.removeFirstPickedFile();
                                  // }),
                                );
                              })
                          .then((_) =>
                              interviewAssessmentProvider.removeLastContext());
                      interviewAssessmentProvider.playVideo();
                      break;
                    case PickFileType.video:
                      // fileType = FileType.video;
                      interviewAssessmentProvider.pauseVideo();

                      await showDialog(
                              context: context,
                              builder: (context) {
                                interviewAssessmentProvider.addContext(context);
                                final scaffoldContext = context;
                                return VideoRecordingCameraPage(
                                  runOnRecordFinish: true,
                                  isFrontCamera: true,
                                  enableQualityDowngrade: true,
                                  infiniteRecording: false,
                                  //set default duration to 5 mins
                                  recordDuration: 5 * 60,
                                  onRecordFinish: (filePath, isRecorded) async {
                                    if (filePath == null) return;

                                    if (isRecorded) {
                                      interviewAssessmentProvider
                                          .changeCurrentAttemptStatus(
                                              isChanged: true);
                                      interviewAssessmentProvider.addFile(
                                          filePath: filePath);
                                    }
                                    //file check and compress
                                    else {
                                      int fileSizeInBytes =
                                          File(filePath).lengthSync();
                                      int maxSizeInBytes =
                                          225 * 1024 * 1024; // 200 MB
                                      if (fileSizeInBytes > maxSizeInBytes) {
                                        // Handle file size exceeds limit

                                        ScaffoldMessenger.of(scaffoldContext)
                                            .showSnackBar(SnackBar(
                                                content:
                                                    Text('file_size_message')
                                                        .tr()));
                                      } else {
                                        interviewAssessmentProvider
                                            .updateCompressionPort(
                                                ReceivePort());
                                        // ReceivePort port = ReceivePort();

                                        FlutterIsolate? isolate;

                                        try {
                                          interviewAssessmentProvider
                                              .setPickingFile(picking: true);
                                          interviewAssessmentProvider
                                              .setCompressing(
                                                  isCompressing: true);
                                          isolate = await FlutterIsolate.spawn(
                                              FileCompressionIsolate
                                                  .compressIsolate,
                                              {
                                                'filePath': filePath,
                                                'sendPort':
                                                    interviewAssessmentProvider
                                                        .compressionPort
                                                        ?.sendPort
                                              });
                                          interviewAssessmentProvider
                                              .setCompressing(
                                                  isCompressing: true);
                                          interviewAssessmentProvider
                                              .compressionPort
                                              ?.listen((dynamic message) async {
                                            if (message
                                                is Map<String, dynamic>) {
                                              log("message is $message");
                                              if (message
                                                  .containsKey('progress')) {
                                                double progress =
                                                    message['progress'];
                                                // Handle progress updates if needed
                                                interviewAssessmentProvider
                                                    .updateCompressionPer(
                                                        per: progress);
                                                log('Compression progress: $progress');
                                              } else if (message
                                                  .containsKey('path')) {
                                                interviewAssessmentProvider
                                                    .addFile(
                                                        filePath:
                                                            '${message['path']}');
                                                interviewAssessmentProvider
                                                    .compressionPort
                                                    ?.close();
                                              }
                                            }
                                          }, onDone: () async {
                                            interviewAssessmentProvider
                                                .setPickingFile(picking: false);
                                            interviewAssessmentProvider
                                                .setCompressing(
                                                    isCompressing: false);
                                            interviewAssessmentProvider
                                                .compressionPort
                                                ?.close();
                                            if (isolate != null) {
                                              isolate?.kill();
                                              isolate = null;
                                            }
                                          });
                                        } catch (e, stacktrace) {
                                          interviewAssessmentProvider
                                              .setPickingFile(picking: false);
                                          log("media file is exception: $stacktrace",
                                              name: "your_filename.dart");
                                        }
                                      }
                                    }
                                  },
                                );
                              })
                          .then((_) =>
                              interviewAssessmentProvider.removeLastContext());
                      interviewAssessmentProvider.playVideo();
                      break;

                    case PickFileType.camera:
                      final cameras = await availableCameras();
                      final firstCamera = cameras.first;
                      interviewAssessmentProvider.pauseVideo();
                      await showDialog(
                              context: context,
                              builder: (context) {
                                interviewAssessmentProvider.addContext(context);

                                return TakePictureScreen(
                                  camera: firstCamera,
                                  cameras: cameras,
                                );
                              })
                          .then((value) => interviewAssessmentProvider.addFile(
                              filePath: value));
                      interviewAssessmentProvider.removeLastContext();
                      interviewAssessmentProvider.playVideo();
                      break;
                    default:
                    // fileType = FileType.any;
                  }
                  // interviewAssessmentProvider.questionVideoController?.play();
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 15.0),
                  child: SvgPicture.asset(
                    image,
                    width: 30,
                    height: 30,
                    color: context.appColors.textBlack,
                    allowDrawingOutsideViewBox: true,
                  ),
                ),
              ),
            InkWell(
              onTap: () async {
                interviewAssessmentProvider.updateCompressionPer(per: 0);
                FilePickerResult? result;
                FileType fileType = FileType.custom;
                List<String> allowedExtensions = [];
                switch (pickFileType) {
                  case PickFileType.audio:
                    fileType = FileType.audio;
                    allowedExtensions = ['mp3', 'm4a'];
                    break;
                  case PickFileType.document:
                    fileType = FileType.custom;
                    allowedExtensions = ['pdf'];
                    break;
                  case PickFileType.video:
                    fileType = FileType.video;
                    allowedExtensions = ['mp4', 'mov'];

                    break;
                  case PickFileType.image:
                    fileType = FileType.image;
                    allowedExtensions = ['png', 'jpg', 'jpeg'];

                    break;

                  default:
                    fileType = FileType.any;
                }
                interviewAssessmentProvider.pauseVideo();
                log("file pick $fileType");
                interviewAssessmentProvider.setPickingFile(picking: true);
                FilePicker.platform.clearTemporaryFiles();
                log("allowed extensions are $allowedExtensions");
                if (interviewAssessmentProvider.pickedFile == null) {
                  interviewAssessmentProvider.changeCurrentAttemptStatus(
                      isChanged: true);
                }

                try {
                  if (Platform.isIOS) {
                    result = await FilePicker.platform.pickFiles(
                        allowMultiple: false,
                        type: pickFileType == PickFileType.video
                            ? FileType.video
                            : FileType.custom,
                        withData: false,
                        onFileLoading: (path) {
                          interviewAssessmentProvider.playVideo();
                        },
                        allowedExtensions: pickFileType == PickFileType.video
                            ? null
                            : allowedExtensions);
                  } else {
                    result = await FilePicker.platform.pickFiles(
                        allowMultiple: false,
                        type: FileType.custom,
                        withData: false,
                        onFileLoading: (path) {
                          interviewAssessmentProvider.playVideo();
                        },
                        lockParentWindow: true,
                        allowedExtensions: allowedExtensions);
                  }

                  if (result != null) {
                    log("file not null");
                    PlatformFile e = result.files.first;
                    log("file not null 1");
                    if (!allowedExtensions
                        .contains(Utility.fileExtension(filePath: e.name))) {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text(
                              '${tr('upload_only')} ${allowedExtensions.toList().join(', ')}')));
                      interviewAssessmentProvider.setPickingFile(
                          picking: false);
                      return;
                    }

                    if (fileType == FileType.audio ||
                        fileType == FileType.video) {
                      int fileSizeInBytes = e.size;
                      int maxSizeInBytes = 225 * 1024 * 1024; // 200 MB
                      if (fileSizeInBytes > maxSizeInBytes) {
                        // Handle file size exceeds limit
                        ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('file_size_message').tr()));
                        interviewAssessmentProvider.setPickingFile(
                            picking: false);
                      } else {
                        if (fileType == FileType.video) {
                          interviewAssessmentProvider
                              .updateCompressionPort(ReceivePort());
                          // ReceivePort port = ReceivePort();

                          FlutterIsolate? isolate;

                          try {
                            isolate = await FlutterIsolate.spawn(
                                FileCompressionIsolate.compressIsolate, {
                              'filePath': '${e.path}',
                              'sendPort': interviewAssessmentProvider
                                  .compressionPort?.sendPort
                            });
                            interviewAssessmentProvider.setCompressing(
                                isCompressing: true);
                            interviewAssessmentProvider.compressionPort?.listen(
                                (dynamic message) async {
                              if (message is Map<String, dynamic>) {
                                log("message is $message");
                                if (message.containsKey('progress')) {
                                  double progress = message['progress'];
                                  // Handle progress updates if needed
                                  interviewAssessmentProvider
                                      .updateCompressionPer(per: progress);
                                  log('Compression progress: $progress');
                                } else if (message.containsKey('path')) {
                                  interviewAssessmentProvider.addFile(
                                      filePath: '${message['path']}');
                                  interviewAssessmentProvider.compressionPort
                                      ?.close();
                                }
                              }
                            }, onDone: () async {
                              interviewAssessmentProvider.setPickingFile(
                                  picking: false);
                              interviewAssessmentProvider.setCompressing(
                                  isCompressing: false);
                              interviewAssessmentProvider.compressionPort
                                  ?.close();
                              if (isolate != null) {
                                isolate?.kill();
                                isolate = null;
                              }
                            });
                          } catch (e, stacktrace) {
                            interviewAssessmentProvider.setPickingFile(
                                picking: false);
                            log("media file is exception: $stacktrace",
                                name: "your_filename.dart");
                          }

                          // try {
                          //   MediaInfo? mediaInfo =
                          //       await VideoCompress.compressVideo(
                          //     '${e.path}',
                          //     quality: VideoQuality.MediumQuality,
                          //     deleteOrigin: false, // It's false by default
                          //   );
                          //   // final info = await VideoCompress.getMediaInfo(videopath);
                          //   log("media file is ${mediaInfo!.filesize! / 1000000} MB",
                          //       name: "your_filename.dart");
                          //   interviewAssessmentProvider.addFile(
                          //       filePath: '${mediaInfo.path}');
                          // } catch (e, stacktrace) {
                          //   log("media file is exception: $stacktrace",
                          //       name: "your_filename.dart");
                          // }
                        } else {
                          //audio file

                          interviewAssessmentProvider.addFile(
                              filePath: '${e.path}');
                          interviewAssessmentProvider.setPickingFile(
                              picking: false);
                        }
                      }
                    } else {
                      //other than audio and video
                      interviewAssessmentProvider.addFile(
                          filePath: '${e.path}');
                      interviewAssessmentProvider.setPickingFile(
                          picking: false);
                    }
                  } else {
                    //on file selected
                    interviewAssessmentProvider.setPickingFile(picking: false);
                    interviewAssessmentProvider.setCompressing(
                        isCompressing: false);
                  }
                } on Exception catch (e) {
                  log("exception in file pick: $e");
                  interviewAssessmentProvider.setPickingFile(picking: false);
                  interviewAssessmentProvider.setCompressing(
                      isCompressing: false);
                }
                // finally {
                // interviewAssessmentProvider.playVideo();
                // interviewAssessmentProvider.setPickingFile(picking: false);
                // }

                // interviewAssessmentProvider.questionVideoController?.play();
              },
              child: Container(
                width: width(context) * (image != null ? 0.8 : 0.94),
                height: 35,
                decoration: BoxDecoration(
                    border: Border.all(color: context.appColors.lebelText),
                    borderRadius: BorderRadius.circular(5)),
                child: Center(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/upload_file.svg',
                      width: width(context) * 0.06,
                      height: width(context) * 0.06,
                      allowDrawingOutsideViewBox: true,
                    ),
                    SizedBox(width: 10),
                    Text(
                      '$text',
                      style: Styles.regular(
                          color: context.appColors.lebelText, size: 12),
                    ).tr(),
                  ],
                )),
              ),
            )
          ],
        );
}
