import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class ViewAllPage extends StatelessWidget {
  final Widget child;
  final String title;

  const ViewAllPage({super.key, required this.child, required this.title});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: context.appColors.bgGrey,
            elevation: 0.0,
            flexibleSpace: Container(
              color: context.surfaceColor,
            ),
            centerTitle: true,
            leading: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: context.appColors.headingText,
                )),
            title: Text(
              title,
              style: Styles.getBoldThemeStyle(context),
            ),
          ),
          body: child,
        );
      },
    );
  }
}
