import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class ExploreWorldOfWork extends StatelessWidget {
  const ExploreWorldOfWork({super.key});

  @override
  Widget build(BuildContext context) {
    List<String> list = [
      "Software",
      "Java",
      "Management",
      "Product",
      "Business",
      "Design",
      "Finaance"
    ];
    dynamic chips = List.generate(
        list.length,
        (int index) => BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, state) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  margin: const EdgeInsets.only(top: 6, bottom: 6, right: 6),
                  decoration: BoxDecoration(
                      color: context.appColors.grey3,
                      borderRadius: BorderRadius.circular(20)),
                  child: Text(list[index]),
                );
              },
            )).toList();
    return Container(
      padding: const EdgeInsets.all(16),
      color: context.appColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'explore',
            style: Styles.regular(
                size: 14, color: context.appColors.dashboardApplyColor),
          ).tr(),
          SizedBox(height: 2),
          Text(
            'the_world_of_work',
            style: Styles.bold(
                size: 18, color: context.appColors.dashboardApplyColor),
          ).tr(),
          SizedBox(height: 14),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 15),
            decoration: BoxDecoration(
                color: context.appColors.grey,
                borderRadius: BorderRadius.circular(21)),
            child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(Icons.search),
                  SizedBox(width: 12),
                  Text(
                    'search_job_title',
                    style: Styles.getRegularThemeStyle(
                      context,
                      size: 14,
                    ),
                  ).tr()
                ]),
          ),
          SizedBox(
            height: 10,
          ),
          Text(
            'popular_searches',
            style: Styles.getRegularThemeStyle(context, size: 12),
          ).tr(),
          SizedBox(
            height: 10,
          ),
          Wrap(
            children: chips,
          )
        ],
      ),
    );
  }
}
