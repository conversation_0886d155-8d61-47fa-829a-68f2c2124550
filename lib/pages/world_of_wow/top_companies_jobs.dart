import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/company_job_list_response.dart';
import 'package:masterg/data/models/response/home_response/company_list_response.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/singularis/job/job_details_page.dart';
import 'package:masterg/pages/world_of_wow/view_all_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:shimmer/shimmer.dart';

class TopCompanies extends StatefulWidget {
  final List<Company>? companies;
  final bool? showViewAll;
  const TopCompanies({super.key, this.companies, this.showViewAll = true});

  @override
  State<TopCompanies> createState() => _TopCompaniesState();
}

class _TopCompaniesState extends State<TopCompanies> {
  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {},
            child: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, state) {
                return Container(
                  margin: const EdgeInsets.only(top: 12),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  color: context.surfaceColor,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Text('opportunities_top_company',
                                  style: Styles.getSemiboldThemeStyle(context,
                                      size: 14))
                              .tr()),
                      ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: widget.showViewAll == true
                              ? min(3, widget.companies?.length ?? 0)
                              : widget.companies?.length ?? 0,
                          itemBuilder: (context, index) => Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: height(context) * 0.08,
                                        width: height(context) * 0.08,
                                        child: CachedNetworkImage(
                                          imageUrl:
                                              '${widget.companies?[index].thumbnail}',
                                          progressIndicatorBuilder: (context,
                                                  url, downloadProgress) =>
                                              Shimmer.fromColors(
                                            baseColor:
                                                context.appColors.shimmerBase,
                                            highlightColor: context
                                                .appColors.shimmerHighlight,
                                            child: Container(
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 10, vertical: 10),
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color: context.surfaceColor,
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Column(
                                        children: [
                                          Text(
                                            '${widget.companies?[index].name}',
                                            style: Styles.getBoldThemeStyle(
                                                context),
                                          ),
                                          SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.location_on_outlined,
                                                size: 16,
                                                color: context.appColors.grey3,
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 6.0, right: 6.0),
                                                child: Text(
                                                  '${widget.companies?[index].location}',
                                                  style: Styles.regular(
                                                      color: context
                                                          .appColors.grey3,
                                                      size: 11),
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      )
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        'Opportunities in ${widget.companies?[index].name}:',
                                        style: Styles.getRegularThemeStyle(
                                            context,
                                            size: 12),
                                      ),
                                      Text(
                                        '${widget.companies?[index].jobCount}',
                                        style: Styles.getSemiboldThemeStyle(
                                            context,
                                            size: 12),
                                      ),
                                      Spacer(),
                                      Text(
                                        'view_all',
                                        style: Styles.getRegularThemeStyle(
                                            context,
                                            size: 12),
                                      ).tr(),
                                    ],
                                  ),
                                  CompanyJobCard(
                                    company: widget.companies?[index],
                                  ),
                                  SizedBox(height: 8),
                                  if (index + 1 <
                                      min(3, widget.companies?.length ?? 0))
                                    Divider(
                                        color: context.appColors.darGrey
                                            .withValues(alpha: 0.2))
                                ],
                              )),
                      if (widget.companies?.length != 0 &&
                          widget.showViewAll == true)
                        Container(
                          color: context.surfaceColor,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          child: ShaderMask(
                              blendMode: BlendMode.srcIn,
                              shaderCallback: (Rect bounds) {
                                return LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: <Color>[
                                      context.appColors.gradientLeft,
                                      context.appColors.gradientRight,
                                    ]).createShader(bounds);
                              },
                              child: InkWell(
                                onTap: () => Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ViewAllPage(
                                            title:
                                                tr('opportunities_top_company'),
                                            child: TopCompanies(
                                              showViewAll: false,
                                              companies: widget.companies,
                                            )))).then((value) => null),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'show_all',
                                      style: Styles.getRegularThemeStyle(
                                          context,
                                          size: 12),
                                    ).tr(),
                                    SizedBox(width: 10),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: 14,
                                    )
                                  ],
                                ),
                              )),
                        ),
                    ],
                  ),
                );
              },
            )));
  }

  void getJobs() {
    try {
      BlocProvider.of<HomeBloc>(context).add(TopCompaniesEvent());
    } catch (e) {
      debugPrint('Exception $e');
    }
  }

  void handlejobs(TopCompaniesState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................TopCompanies");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................TopCompanies");

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'top_companies_jobs', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}

class CompanyJobCard extends StatefulWidget {
  final Company? company;
  const CompanyJobCard({super.key, this.company});

  @override
  State<CompanyJobCard> createState() => _CompanyJobCardState();
}

class _CompanyJobCardState extends State<CompanyJobCard> {
  List<Job>? jobs;
  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (BuildContext context) {
          getJobs();
        },
        child:
            BlocListener<HomeBloc, HomeState>(listener: (context, state) async {
          if (state is CompanyJobsListState) handlejobs(state);
        }, child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, state) {
            return Container(
              margin: EdgeInsets.only(top: 12),
              color: context.surfaceColor,
              child: ListView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: jobs?.length ?? 0,
                  itemBuilder: (context, index) => InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              NextPageRoute(JobDetailsPage(
                                title: jobs?[index].name,
                                description: jobs?[index].description,
                                location: jobs?[index].location,
                                skillNames: jobs?[index].skillNames,
                                companyName: jobs?[index].organizedBy,
                                domain: jobs?[index].domainName,
                                companyThumbnail: jobs?[index].image,
                                experience: jobs?[index].experience,
                                //jobListDetails: jobList,
                                id: jobs?[index].id,
                                jobStatus: jobs?[index].jobStatus,
                                jobStatusNumeric: int.parse(
                                    '${jobs?[index].jobStatusNumeric ?? 0}'),
                              ))).then((value) {});
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: context.appColors.darGrey
                                      .withValues(alpha: 0.2))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${jobs?[index].name}',
                                style: Styles.getSemiboldThemeStyle(context,
                                    size: 14),
                              ),
                              SizedBox(height: 6),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  // Image.asset(
                                  //   'assets/images/jobicon.png',
                                  //   height: 18,
                                  //   width: 18,
                                  // ),
                                  Icon(Icons.work_outline,
                                      size: 16, color: context.appColors.grey6),
                                  Padding(
                                    padding: EdgeInsets.only(
                                      left: Utility().isRTL(context) ? 0 : 5.0,
                                      right:
                                          Utility().isRTL(context) ? 5.0 : 0.0,
                                    ),
                                    child: Text('${tr('exp')}: ',
                                        style: Styles.regular(
                                            size: 12,
                                            color: context.appColors.grey6)),
                                  ),
                                  Text(
                                      '${jobs?[index].experience} ${tr('yrs')}',
                                      style: Styles.regular(
                                          size: 12,
                                          color: context.appColors.grey6)),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left:
                                            Utility().isRTL(context) ? 0 : 10.0,
                                        right: Utility().isRTL(context)
                                            ? 10.0
                                            : 0.0),
                                    child: Icon(
                                      Icons.location_on_outlined,
                                      size: 16,
                                      color: context.appColors.grey3,
                                    ),
                                  ),
                                  Text('${jobs?[index].location}',
                                      style: Styles.regular(
                                          size: 12,
                                          color: context.appColors.grey3)),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )),
            );
          },
        )));
  }

  void getJobs() {
    try {
      BlocProvider.of<HomeBloc>(context)
          .add(CompanyJobsListEvent(name: widget.company?.name));
    } catch (e) {
      debugPrint('Exception $e');
    }
  }

  void handlejobs(CompanyJobsListState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................TopCompanies");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................TopCompanies");

          jobs = state.response?.data;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'top_companies_jobs', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
