import 'package:get/get.dart';

class VideoResumeController extends GetxController {
  final count = 0.0.obs;
  final uploadingStatus = UploadingStatus.init.obs;
  final compressPer = 0.0.obs;
  double increment(double per) => count.value = per;
  double reset() => count.value = 0.0;

  void changeStatus(UploadingStatus status) {
    uploadingStatus.value = status;
  }

  void updateCompressPer({required double per}) {
    compressPer.value = per;
  }
}

enum UploadingStatus { init, process, end }
