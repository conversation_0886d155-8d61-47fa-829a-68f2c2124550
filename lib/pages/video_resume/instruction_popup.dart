import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

enum ResumeKeyStatus { loading, complete, error }

class InstructionPopup extends StatelessWidget {
  final Function onContinue;
  const InstructionPopup({super.key, required this.onContinue});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          return Scaffold(
              body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                        width: 330,
                        child: Text(
                          'resume_key_elements',
                          style: TextStyle(
                            color: context.appColors.headingText,
                            fontSize: 17,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                          ),
                        ).tr()),
                    _size(height: 10),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0),
                      child: SizedBox(
                          width: 330,
                          child: Text(
                            'resume_element_desc',
                            style: TextStyle(
                              color: context.appColors.headingTitle,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                            ),
                          ).tr()),
                    ),
                    _size(height: 20),
                    Text(
                      'mandatory',
                      style: TextStyle(
                        color: context.appColors.grey,
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ).tr(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        children: [
                          _size(height: 4),
                          _rowItem('name', context),
                          _size(height: 4),
                          _rowItem('email_address', context),
                          //_size(height: 4),
                          //_rowItem('professional',context),
                          _size(height: 4),
                          _rowItem('phone_number', context),
                          _size(height: 4),
                          _rowItem('address', context),
                          _size(height: 4),
                          _rowItem('resume_objective', context),
                          _size(height: 4),
                          _rowItem('education_qualifications', context),
                          _size(height: 4),
                          _rowItem('professional_experience', context),
                          _size(height: 4),
                          _rowItem('skills_abilities', context),
                          _size(height: 20),
                        ],
                      ),
                    ),
                    Text(
                      'optional',
                      style: TextStyle(
                        color: context.appColors.grey,
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ).tr(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        children: [
                          _size(height: 10),
                          _rowItem('achievements_awards', context),
                          _size(height: 10),
                          _rowItem('certifications', context),
                          _size(height: 10),
                          _rowItem('publications', context),
                          _size(height: 10),
                          _rowItem('languages', context),
                          _size(height: 40),
                        ],
                      ),
                    ),
                    Text(
                      'ensure_english_video',
                      style: Styles.getBoldThemeStyle(context),
                      textAlign: TextAlign.center,
                    ).tr(),
                    _size(height: 10),
                    InkWell(
                      onTap: () {
                        onContinue();
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        padding: const EdgeInsets.all(16),
                        decoration: ShapeDecoration(
                          color: context.appColors.primary,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    'continue',
                                    style: TextStyle(
                                      color:
                                          context.appColors.primaryForeground,
                                      fontSize: 14,
                                      fontFamily: 'Poppins',
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ).tr()
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ]),
            ),
          ));
        },
      ),
    );
  }

  Row _rowItem(String title, BuildContext context) {
    return Row(children: [
      Text('•',
          style: Styles.semibold(
              lineHeight: 1.4, color: context.appColors.grey3, size: 15)),
      _size(width: 10),
      Text(title,
              style: Styles.semibold(
                  lineHeight: 1.4, color: context.appColors.grey3, size: 15))
          .tr()
    ]);
  }

  SizedBox _size({double height = 20, double width = 0}) {
    return SizedBox(
      height: height,
      width: width,
    );
  }
}
