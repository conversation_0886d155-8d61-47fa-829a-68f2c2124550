import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/video_resume/preview_sample_resume_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';

class SampleResumePage extends StatefulWidget {
  final List<SampleResume>? sampleResumeResp;

  const SampleResumePage({
    super.key,
    required this.sampleResumeResp,
  });

  @override
  State<SampleResumePage> createState() => _SampleResumePageState();
}

class _SampleResumePageState extends State<SampleResumePage> {
  final ReceivePort _port = ReceivePort();

  @override
  void initState() {
    // TODO: implement initState

    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      setState(() {});
    });

    FlutterDownloader.registerCallback(downloadCallback);

    super.initState();
  }

  static void downloadCallback(String id, int status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, DownloadTaskStatus.fromInt(status), progress]);
  }

  @override
  void dispose() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
    super.dispose();
  }

  bool? isLoading = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(
              elevation: 0,
              backgroundColor: context.appColors.surface,
              title: Text('sample_resume',
                      style: Styles.getBoldThemeStyle(context))
                  .tr(),
              leading: BackButton(color: context.appColors.headingText),
            ),
            body: ScreenWithLoader(
              isLoading: isLoading,
              body: SingleChildScrollView(
                child: widget.sampleResumeResp != null &&
                        widget.sampleResumeResp?.length != 0
                    ? Column(
                        children: [
                          ListView.builder(
                            itemCount: widget.sampleResumeResp?.length ?? 0,
                            shrinkWrap: true,
                            physics: BouncingScrollPhysics(),
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Container(
                                  height: height(context) * 0.2,
                                  decoration: BoxDecoration(
                                    color: context.appColors.surface,
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            Colors.grey.withValues(alpha: 0.5),
                                        spreadRadius: 5,
                                        blurRadius: 7,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Stack(
                                    children: [
                                      // if (isLoading == false)
                                      Positioned(
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        top: 0,
                                        // child: Image.network(
                                        //     '${widget.sampleResumeResp?[index].image ?? ''}',
                                        //     errorBuilder:
                                        //         (context, error, stacktrace) =>
                                        //             Center(
                                        //               child: SizedBox(
                                        //                   child: Text(
                                        //                           'no_resume_found')
                                        //                       .tr()),
                                        //             ))
                                        child: CachedNetworkImage(
                                          imageUrl: widget
                                                  .sampleResumeResp?[index]
                                                  .image ??
                                              '',
                                          fit: BoxFit.fitHeight,
                                          placeholder: (context, url) => Center(
                                              child:
                                                  CircularProgressIndicator()),
                                          errorWidget: (context, url, error) =>
                                              SvgPicture.asset(
                                            'assets/images/cv_sempel.svg',
                                            width: 50,
                                            height: 50,
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 0,
                                        child: Container(
                                          width: width(context) * 0.92,
                                          height: 40,
                                          decoration: ShapeDecoration(
                                            gradient: LinearGradient(colors: [
                                              context.appColors.gradientLeft,
                                              context.appColors.gradientRight,
                                            ]),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.only(
                                                bottomLeft: Radius.circular(8),
                                                bottomRight: Radius.circular(8),
                                              ),
                                            ),
                                          ),
                                          child: Row(
                                            children: [
                                              Padding(
                                                padding:
                                                    Utility().isRTL(context)
                                                        ? EdgeInsets.only(
                                                            right: 10,
                                                            bottom: 5)
                                                        : const EdgeInsets.only(
                                                            left: 8.0,
                                                            bottom: 8),
                                                child: SizedBox(
                                                    width: 24,
                                                    height: 24,
                                                    child: Icon(
                                                      Icons
                                                          .picture_as_pdf_outlined,
                                                      size: 30,
                                                      color: context.appColors
                                                          .primaryForeground,
                                                    )),
                                              ),
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              Text(
                                                '',
                                                style: TextStyle(
                                                  color: context.appColors
                                                      .primaryForeground,
                                                  fontSize: 14,
                                                  fontFamily: 'Poppins',
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              const Spacer(),
                                              InkWell(
                                                  onTap: () {
                                                    log('pdf url is ${widget.sampleResumeResp?[index].url ?? ''}');

                                                    Navigator.push(
                                                        context,
                                                        NextPageRoute(PreviewSampleResume(
                                                            title: tr(
                                                                'preview_resume'),
                                                            previewUrl: widget
                                                                    .sampleResumeResp?[
                                                                        index]
                                                                    .image ??
                                                                '',
                                                            msg: tr(
                                                                'no_resume_found'))));
                                                  },
                                                  child: Icon(Icons.visibility,
                                                      color: context.appColors
                                                          .primaryForeground)),
                                              SizedBox(width: 10),
                                              InkWell(
                                                onTap: () {
                                                  if (widget
                                                          .sampleResumeResp?[
                                                              index]
                                                          .url ==
                                                      null) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(
                                                            const SnackBar(
                                                      content: Text(
                                                          'Resume is not Available'),
                                                    ));
                                                  } else {
                                                    sampleResumeDownload(widget
                                                            .sampleResumeResp?[
                                                                index]
                                                            .url ??
                                                        '');
                                                  }
                                                },
                                                child: Container(
                                                  width: 30,
                                                  height: 30,
                                                  decoration: ShapeDecoration(
                                                    shape:
                                                        RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        2)),
                                                  ),
                                                  child: SvgPicture.asset(
                                                    'assets/images/download_file.svg',
                                                    color: context.appColors
                                                        .primaryForeground,
                                                    height: 10,
                                                    width: 10,
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(
                                                width: 10,
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                        ],
                      )
                    : SizedBox(
                        height: 300,
                        child: Center(
                            child: Text('no_resume_found',
                                    style: Styles.getBoldThemeStyle(context))
                                .tr()),
                      ),
              ),
            ));
      },
    );
  }

  void shareUrl(String url) {
    if (url.isNotEmpty) {
      SharePlus.instance.share(ShareParams(text: url, subject: 'Sharing URL'));
    }
  }

  Future<void> sampleResumeDownload(String url) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      // final status =
      await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File("$localPath/${url.split('/').last}");
      if (!file.existsSync()) {
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        // final id =
        await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${url.split('/').last}");
        });
      } else {
        Utility.showSnackBar(scaffoldContext: context, message: 'file exists');
        OpenFilex.open("$localPath/${url.split('/').last}");
      }
    } else {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }
}
