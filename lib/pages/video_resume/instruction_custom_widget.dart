import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/utils/styles.dart';

import 'instruction_popup.dart';

class InstructionCustomWidget extends StatelessWidget {
  final String title;
  final ResumeKeyStatus status;

  const InstructionCustomWidget({
    super.key,
    required this.title,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (status == ResumeKeyStatus.loading)
            SizedBox(
                height: 18,
                width: 18,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Color(0xffA1A9C0),
                )),
          if (status == ResumeKeyStatus.complete)
            SvgPicture.asset(
              'assets/images/video_resume_key_complete.svg',
            ),
          if (status == ResumeKeyStatus.error)
            SvgPicture.asset('assets/images/video_resume_key_error.svg'),
          SizedBox(width: 10),
          Text(title.toLowerCase().replaceAll(' ', '_'),
              style: Styles.semibold(
                size: 15,
                lineHeight: 1.4,
                color: Color.fromARGB(255, 79, 79, 81),
              )).tr(),
        ],
      ),
    );
  }
}
