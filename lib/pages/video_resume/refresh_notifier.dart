import 'package:flutter/material.dart';

class RefreshNotifier extends ChangeNotifier {
  List<bool> isRefreshingList = [];

  void updateList(int len) {
    debugPrint("update length is $len");
    isRefreshingList = List.generate(len, (index) => false);
    notifyListeners();
  }

  void setRefreshing(int index) {
    isRefreshingList[index] = true;
    notifyListeners();
  }

  void setRefreshingStop(int index) {
    debugPrint("refresh_notifier.dart::: setRefreshingStop $index");

    isRefreshingList[index] = false;
    notifyListeners();
  }
}
