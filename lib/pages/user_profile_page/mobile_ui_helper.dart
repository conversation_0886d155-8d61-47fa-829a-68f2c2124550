import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:image_cropper/image_cropper.dart';

List<PlatformUiSettings>? buildUiSettings(BuildContext context) {
  return [
    AndroidUiSettings(
        toolbarTitle: '',
        toolbarColor: context.appColors.textBlack,
        toolbarWidgetColor: context.appColors.surface,
        hideBottomControls: true,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true),
    IOSUiSettings(
      title: '',
    ),
  ];
}
