import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';

import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/widget_size.dart';
import '../../blocs/bloc_manager.dart';
import '../../data/api/api_service.dart';
import '../../utils/Log.dart';

import '../custom_pages/screen_with_loader.dart';

class DeleteAccountPage extends StatefulWidget {
  final imageUrl;
  const DeleteAccountPage({super.key, this.imageUrl});

  @override
  State<DeleteAccountPage> createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<DeleteAccountPage> {
  bool isloading = false;
  int selectedOption = 0;
  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (BuildContext context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is RemoveAccountState) {
            handleRemoveAccount(state);
          }
        },
        child: Scaffold(
          backgroundColor: context.appColors.background,
          appBar: AppBar(
              centerTitle: true,
              backgroundColor: context.appColors.surface,
              elevation: 0.0,
              iconTheme: IconThemeData(color: context.appColors.textBlack),
              title: Text(
                'delete_account',
                style: Styles.getBoldThemeStyle(context),
              ).tr()),
          body: ScreenWithLoader(
            isLoading: isloading,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 20),
                widget.imageUrl != 'null'
                    ? ClipOval(
                        child: GestureDetector(
                          onTap: () {},
                          child: Image.network(
                            widget.imageUrl,
                            filterQuality: FilterQuality.low,
                            width: 100,
                            height: 100,
                            fit: BoxFit.fill,
                          ),
                        ),
                      )
                    : SvgPicture.asset(
                        'assets/images/default_user.svg',
                        width: 100,
                        height: 100,
                        allowDrawingOutsideViewBox: true,
                      ),
                SizedBox(height: 20),
                Container(
                    margin: EdgeInsets.only(
                        left: 20, right: 10, top: 10, bottom: 10),
                    child: Text(
                      'deactivate_your_account',
                      style: Styles.getBoldThemeStyle(context, size: 16),
                      textAlign: TextAlign.center,
                    ).tr()),
                SizedBox(height: 10),
                InkWell(
                  onTap: () {
                    selectedOption = 1;
                    setState(() {});
                  },
                  child: infoCard(
                      Icons.visibility,
                      tr('deactivate_your_account_temporary'),
                      tr('your_profile_learning_hidden'),
                      context,
                      selected: selectedOption == 1),
                ),
                InkWell(
                    onTap: () {
                      selectedOption = 2;
                      setState(() {});
                    },
                    child: infoCard(
                        Icons.info_sharp,
                        tr('deactivate_your_account_permanent'),
                        tr('your_profile_permanently_deleted'),
                        context,
                        selected: selectedOption == 2)),
                Expanded(child: SizedBox()),
                InkWell(
                    onTap: () {
                      if (selectedOption == 2) {
                        showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return Dialog(
                                  shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(20.0)),
                                  child: Container(
                                    height: 230,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 20),
                                    decoration: BoxDecoration(),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text('are_you_sure_delete_acc',
                                                style: Styles.getBoldThemeStyle(
                                                    context),
                                                textAlign: TextAlign.center)
                                            .tr(),
                                        SizedBox(height: 10),
                                        Divider(),
                                        InkWell(
                                          onTap: () {
                                            deleteAccount();
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            'continue_deleting_account',
                                            textAlign: TextAlign.center,
                                            style: Styles.bold(
                                                color: context.appColors.error),
                                          ).tr(),
                                        ),
                                        Divider(),
                                        SizedBox(height: 10),
                                        InkWell(
                                            onTap: () {
                                              Navigator.pop(context);
                                            },
                                            child: Text('cancel',
                                                    style: Styles
                                                        .getRegularThemeStyle(
                                                            context,
                                                            size: 14))
                                                .tr())
                                      ],
                                    ),
                                  ));
                            });
                      } else if (selectedOption == 1) {
                        showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return Dialog(
                                  shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(20.0)),
                                  child: Container(
                                    height: height(context) * 0.26,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 20),
                                    decoration: BoxDecoration(),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text('are_you_sure_deactivate_acc',
                                                style: Styles.getBoldThemeStyle(
                                                    context),
                                                textAlign: TextAlign.center)
                                            .tr(),
                                        SizedBox(height: 10),
                                        Divider(),
                                        InkWell(
                                          onTap: () {
                                            deActivateAccount();
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            'continue_deactivating_account',
                                            textAlign: TextAlign.center,
                                            style: Styles.bold(
                                                color: context.appColors.error),
                                          ).tr(),
                                        ),
                                        Divider(),
                                        SizedBox(height: 10),
                                        InkWell(
                                            onTap: () {
                                              Navigator.pop(context);
                                            },
                                            child: Text('cancel',
                                                    style: Styles
                                                        .getRegularThemeStyle(
                                                            context,
                                                            size: 14))
                                                .tr())
                                      ],
                                    ),
                                  ));
                            });
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 12.0, right: 12.0, top: 10),
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height *
                          WidgetSize.AUTH_BUTTON_SIZE,
                      decoration: BoxDecoration(
                          gradient: selectedOption != 0
                              ? LinearGradient(colors: [
                                  context.appColors.gradientLeft,
                                  context.appColors.gradientRight,
                                ])
                              : LinearGradient(colors: [
                                  context.appColors.unselectedButton,
                                  context.appColors.unselectedButton,
                                ]),
                          borderRadius: BorderRadius.circular(10)),
                      child: Center(
                          child: Text(
                        'continue',
                        style: Styles.regular(
                          color: context.appColors.textWhite,
                        ),
                      ).tr()),
                    )),
                SizedBox(
                  height: 20,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget infoCard(
      IconData icon, String title, String desc, BuildContext context,
      {bool selected = false}) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.12,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
              color: selected == true
                  ? context.appColors.primaryDark
                  : context.appColors.textBlack,
              width: 2)),
      margin: EdgeInsets.only(left: 20, right: 10, top: 10, bottom: 10),
      padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon),
          SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: Styles.getBoldThemeStyle(context, size: 14),
              ),
              SizedBox(
                  width: MediaQuery.of(context).size.width * 0.7,
                  child: Text(
                    desc,
                    style: Styles.getRegularThemeStyle(context, size: 12),
                    softWrap: true,
                    maxLines: 3,
                  )),
            ],
          )
        ],
      ),
    );
  }

  void deActivateAccount() {
    BlocProvider.of<HomeBloc>(context)
        .add(RemoveAccountEvent(type: 'deactivate'));
  }

  void deleteAccount() {
    BlocProvider.of<HomeBloc>(context).add(RemoveAccountEvent(type: 'delete'));
  }

  void handleRemoveAccount(RemoveAccountState state) {
    var removeState = state;
    // setState(() {
    switch (removeState.apiState) {
      case ApiStatus.LOADING:
        isloading = true;
        Log.v("Loading....................Remove Account State");
        break;
      case ApiStatus.SUCCESS:
        Log.v("Success....................Remove Account State");
        isloading = false;

        AlertsWidget.showCustomDialog(
            context: context,
            title: "",
            text: '${removeState.response?.data![0]}',
            icon: 'assets/images/circle_alert_fill.svg',
            showCancel: false,
            onOkClick: () async {
              Utility.logoutUser(context);
            });

        break;
      case ApiStatus.ERROR:
        isloading = false;
        Log.v("Error..........................Remove Account State");

        FirebaseAnalytics.instance
            .logEvent(name: 'delete_account_page', parameters: {
          "ERROR": '${state.response?.error}',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }
}
