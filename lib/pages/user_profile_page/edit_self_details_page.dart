import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import '../../blocs/bloc_manager.dart';
import '../../blocs/home_bloc.dart';
import '../../data/api/api_service.dart';
import '../../local/pref/Preference.dart';
import '../../utils/Log.dart';
import '../../utils/styles.dart';
import '../../utils/utility.dart';
import '../../utils/widget_size.dart';
import '../custom_pages/screen_with_loader.dart';
import '../custom_pages/alert_widgets/alerts_widget.dart';

class EditSelfDetailsPage extends StatefulWidget {
  final String? name;
  final String? email;
  final Function? onCalledBack;

  const EditSelfDetailsPage(
      {super.key, this.name, this.email, this.onCalledBack});

  @override
  State<EditSelfDetailsPage> createState() => _EditSelfDetailsPageState();
}

class _EditSelfDetailsPageState extends State<EditSelfDetailsPage> {
  final fullNameController = TextEditingController();
  final emailController = TextEditingController();
  bool _isLoading = false;
  late Color primaryForeGroundColor;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    fullNameController.text = widget.name!;
    emailController.text = widget.email!;
    primaryForeGroundColor = context.appColors.primaryForeground;
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    primaryForeGroundColor = context.appColors.primaryForeground;
  }

  void _updateUserProfileImage(String? name, String? email) async {
    if (!_formKey.currentState!.validate()) return;

    BlocProvider.of<HomeBloc>(context)
        .add(UpdateUserProfileImageEvent(name: name, email: email));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is UpdateUserProfileImageState) {
            _handleUpdateUserProfileImageResponse(state);
          }
        },
        child: Scaffold(
          backgroundColor: Colors.grey[200],
          appBar: AppBar(
            elevation: 0,
            leading: BackButton(color: primaryForeGroundColor),
            title: Text(
              tr('edit_profile'),
              style: TextStyle(color: primaryForeGroundColor, fontSize: 16),
            ),
            backgroundColor: context.appColors.primary,
            actions: [
              IconButton(
                  onPressed: () {
                    AlertsWidget.showCustomDialog(
                        context: context,
                        title: tr('confirm_message'),
                        icon: 'assets/images/circle_alert_fill.svg',
                        onOkClick: () async {
                          Utility.logoutUser(context);
                        });
                  },
                  icon: Icon(
                    Icons.logout,
                    color: primaryForeGroundColor,
                  ))
            ],
          ),
          body: SafeArea(
            child: ScreenWithLoader(
              isLoading: _isLoading,
              body: _makeBody(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _makeBody() {
    return Form(
      key: _formKey,
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        margin: EdgeInsets.only(left: 20.0, right: 20.0, top: 40.0),
        child: Column(
          children: [
            TextFormField(
              controller: fullNameController,
              style: Styles.getRegularThemeStyle(context),
              maxLength: 100,
              decoration: InputDecoration(
                labelText: tr('name'),
                hintText: tr('enter_full_name'),
                helperStyle: Styles.regular(color: context.appColors.grey4),
                counterText: "",
                suffixIconConstraints: BoxConstraints(minWidth: 0),
                prefixIcon: Icon(
                  Icons.person,
                  color: Colors.black54,
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: context.appColors.primary, width: 1.5),
                ),
              ),
              onChanged: (value) {
                setState(() {});
              },
              validator: (value) {
                if (value!.isEmpty) return tr('enter_full_name');

                return null;
              },
            ),
            SizedBox(height: 40),
            TextFormField(
              controller: emailController,
              style: Styles.getRegularThemeStyle(context),
              decoration: InputDecoration(
                labelText: tr('enter_email'),
                hintText: tr('enter_email_address'),
                helperStyle: Styles.regular(color: context.appColors.grey4),
                counterText: "",
                prefixIcon:
                    Icon(Icons.email, color: context.appColors.textBlack),
                enabledBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: context.appColors.primary, width: 1.5),
                ),
              ),
              onChanged: (value) {
                setState(() {});
              },
              validator: (value) {
                if (value == '') return tr('email_required');
                int index = value?.length as int;

                if (value![index - 1] == '.') return tr('enter_valid_mail');

                if (!RegExp(
                        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                    .hasMatch(value)) {
                  return tr('enter_valid_mail');
                }

                return null;
              },
            ),
            SizedBox(height: 50),
            InkWell(
                onTap: () {
                  Preference.setString(Preference.FIRST_NAME,
                      fullNameController.text.toString());
                  _updateUserProfileImage(fullNameController.text.toString(),
                      emailController.text.toString());
                },
                child: Container(
                  width: double.infinity,
                  height: MediaQuery.of(context).size.height *
                      WidgetSize.AUTH_BUTTON_SIZE,
                  margin: EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                  decoration: BoxDecoration(
                      color: context.appColors.primary,
                      borderRadius: BorderRadius.circular(10)),
                  child: Center(
                      child: Text(
                    'update_profile',
                    style: Styles.regular(
                      color: primaryForeGroundColor,
                    ),
                  ).tr()),
                )),
          ],
        ),
      ),
    );
  }

  void _handleUpdateUserProfileImageResponse(
      UpdateUserProfileImageState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          Preference.setString(Preference.PROFILE_IMAGE,
              '${state.response!.data!.profileImage}');
          Preference.setString(
              Preference.USER_EMAIL, '${state.response!.data!.email}');
          Preference.setString(
              Preference.FIRST_NAME, '${state.response!.data!.name}');

          Navigator.pop(context, true);
          widget.onCalledBack!();
          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'edit_self_details', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
