import 'dart:convert';

import 'package:flutter/material.dart';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import '../../data/api/api_constants.dart';
import '../../data/models/response/auth_response/user_session.dart';
import '../../utils/utility.dart';
import 'model/brand_model.dart';

class BrandFilterPage extends StatefulWidget {
  final Function onCalledBack;

  const BrandFilterPage(this.onCalledBack, {super.key});

  @override
  State<BrandFilterPage> createState() => _BrandFilterPageState();
}

class Debouncer {
  final int? milliseconds;
  VoidCallback? action;
  Timer? _timer;

  Debouncer({this.milliseconds});

  void run(VoidCallback action) {
    if (null != _timer) {
      _timer!.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds!), action);
  }
}

class _BrandFilterPageState extends State<BrandFilterPage> {
  final titleController = TextEditingController();
  final _debouncer = Debouncer(milliseconds: 500);
  List<BrandModel> filteredUsers = [];
  List<BrandModel> addressListData = <BrandModel>[];

  bool flagIndicator = false;
  String strCity = '', strState = '', strZipCode = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Brand Name'),
        backgroundColor: context.appColors.primary,
      ),
      body: Column(children: <Widget>[
        TextField(
          controller: titleController,
          autofocus: true,
          decoration: InputDecoration(
            prefixIcon: Icon(Icons.search_rounded),
            contentPadding: EdgeInsets.all(15.0),
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            hintText: 'Enter Brand Name',
          ),
          onChanged: (string) {
            _debouncer.run(() {
              setState(() {
                flagIndicator = true;

                fetchProducts(string.toString().trim());
              });
            });
          },
        ),
        flagIndicator == true
            ? SizedBox(
                height: 25,
                width: 25,
                child: CircularProgressIndicator(),
              )
            : SizedBox(),
        ListView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.all(10.0),
          itemCount: filteredUsers.length,
          itemBuilder: (BuildContext context, int index) {
            return Card(
              child: Padding(
                padding: EdgeInsets.all(10.0),
                child: InkWell(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Text(
                        filteredUsers[index].title.toString(),
                        style: TextStyle(
                          fontSize: 16.0,
                          color: context.appColors.pureBlack,
                        ),
                      ),
                      Image.network(
                        addressListData[index].image.toString(),
                        filterQuality: FilterQuality.low,
                        width: 80,
                        height: 37,
                        fit: BoxFit.fill,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) {
                            return child;
                          }
                          return Center(
                              child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: context.appColors.primary,
                                  )));
                        },
                      ),
                    ],
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onCalledBack(filteredUsers[index].title.toString(),
                        filteredUsers[index].image, filteredUsers[index].id);
                  },
                ),
              ),
            );
          },
        ),
        titleController.text.isNotEmpty
            ? Column(
                children: [
                  Text(
                    'We could not find your brand in our list.',
                    style: Styles.getRegularThemeStyle(context, size: 12),
                  ),
                  GestureDetector(
                    onTap: () {
                      if (titleController.text.isNotEmpty) {
                        Navigator.of(context).pop();
                        widget.onCalledBack(
                            titleController.text.toString(), '', 0);
                      } else {
                        Utility.showSnackBar(
                            scaffoldContext: context,
                            message: 'Please enter brand name.');
                      }
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 7, horizontal: 49),
                      margin: EdgeInsets.only(top: 20),
                      decoration: BoxDecoration(
                          border: Border.all(),
                          borderRadius: BorderRadius.circular(8)),
                      child: Text('Add your brand',
                          style:
                              Styles.getRegularThemeStyle(context, size: 14)),
                    ),
                  ),
                ],
              )
            : SizedBox(),
      ]),
    );
  }

  Future<List<BrandModel>> fetchProducts(String strBrandName) async {
    addressListData.clear();
    String url =
        '${ApiConstants().PRODUCTION_BASE_URL()}/${ApiConstants.BRAND_SEARCH}?key=$strBrandName';

    final response = await http.post(
      Uri.parse(url),
      headers: {
        "Authorization": "Bearer ${UserSession.userToken}",
        ApiConstants.API_KEY: ApiConstants().APIKeyValue()
      },
    );

    Map parsedJson = json.decode(response.body);
    if (response.statusCode == 200) {
      flagIndicator = false;

      var resultsData = parsedJson['data'] as List;
      for (int i = 0; i < resultsData.length; i++) {
        setState(() {
          addressListData.add(BrandModel.fromJson(resultsData[i]));

          filteredUsers = addressListData;
        });
      }

      if (resultsData.isEmpty) {
        setState(() {
          flagIndicator = false;
        });
      }
    } else {
      throw Exception('Unable to fetch products from the REST API');
    }
    return addressListData;
  }
}
