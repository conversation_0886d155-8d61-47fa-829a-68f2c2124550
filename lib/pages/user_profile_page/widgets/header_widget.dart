import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class HeaderWidget extends StatefulWidget {
  final double _height;
  final bool _showIcon;
  final IconData _icon;

  const HeaderWidget(this._height, this._showIcon, this._icon, {super.key});

  @override
  State<HeaderWidget> createState() =>
      _HeaderWidgetState(_height, _showIcon, _icon);
}

class _HeaderWidgetState extends State<HeaderWidget> {
  final double _height;
  final bool _showIcon;
  final IconData _icon;

  _HeaderWidgetState(this._height, this._showIcon, this._icon);

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Stack(
          children: [
            ClipPath(
              clipper: <PERSON>hape<PERSON>lipper(offsets: [
                Offset(width / 5, _height),
                Offset(width / 10 * 5, _height - 60),
                Offset(width / 5 * 4, _height + 20),
                Offset(width, _height - 18)
              ]),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [
                        //Theme.of(context).primaryDark.withValues(alpha:0.3),
                        //Theme.of(context).accentColor.withValues(alpha:0.7),
                        context.appColors.orange.withValues(alpha: 0.3),
                        context.appColors.selectedPage.withValues(alpha: 0.7),
                      ],
                      begin: const FractionalOffset(0.0, 0.0),
                      end: const FractionalOffset(1.0, 0.0),
                      stops: [0.0, 1.0],
                      tileMode: TileMode.clamp),
                ),
              ),
            ),
            ClipPath(
              clipper: ShapeClipper(offsets: [
                Offset(width / 3, _height + 20),
                Offset(width / 10 * 8, _height - 60),
                Offset(width / 5 * 4, _height - 60),
                Offset(width, _height - 20)
              ]),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [
                        //Theme.of(context).primaryDark.withValues(alpha:0.4),
                        //Theme.of(context).accentColor.withValues(alpha:0.4),
                        context.appColors.orange.withValues(alpha: 0.4),
                        context.appColors.selectedPage.withValues(alpha: 0.4),
                      ],
                      begin: const FractionalOffset(0.0, 0.0),
                      end: const FractionalOffset(1.0, 0.0),
                      stops: [0.0, 1.0],
                      tileMode: TileMode.clamp),
                ),
              ),
            ),
            ClipPath(
              clipper: ShapeClipper(offsets: [
                Offset(width / 5, _height),
                Offset(width / 2, _height - 40),
                Offset(width / 5 * 4, _height - 80),
                Offset(width, _height - 20)
              ]),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                      colors: [
                        //Theme.of(context).primaryDark,
                        //Theme.of(context).accentColor,
                        context.appColors.orange,
                        context.appColors.selectedPage,
                      ],
                      begin: const FractionalOffset(0.0, 0.0),
                      end: const FractionalOffset(1.0, 0.0),
                      stops: [0.0, 1.0],
                      tileMode: TileMode.clamp),
                ),
              ),
            ),
            Visibility(
              visible: _showIcon,
              child: SizedBox(
                height: _height - 40,
                child: Center(
                  child: Container(
                    margin: EdgeInsets.all(20),
                    padding: EdgeInsets.only(
                      left: 5.0,
                      top: 20.0,
                      right: 5.0,
                      bottom: 20.0,
                    ),
                    decoration: BoxDecoration(
                      // borderRadius: BorderRadius.circular(20),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(100),
                        topRight: Radius.circular(100),
                        bottomLeft: Radius.circular(60),
                        bottomRight: Radius.circular(60),
                      ),
                      border: Border.all(
                          width: 5, color: context.appColors.primaryForeground),
                    ),
                    child: Icon(
                      _icon,
                      color: context.appColors.primaryForeground,
                      size: 40.0,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class ShapeClipper extends CustomClipper<Path> {
  final List<Offset> offsets;
  ShapeClipper({this.offsets = const []});
  @override
  Path getClip(Size size) {
    var path = Path();

    path.lineTo(0.0, size.height - 20);

    // path.quadraticBezierTo(size.width/5, size.height, size.width/2, size.height-40);
    // path.quadraticBezierTo(size.width/5*4, size.height-80, size.width, size.height-20);

    path.quadraticBezierTo(
        offsets[0].dx, offsets[0].dy, offsets[1].dx, offsets[1].dy);
    path.quadraticBezierTo(
        offsets[2].dx, offsets[2].dy, offsets[3].dx, offsets[3].dy);

    // path.lineTo(size.width, size.height-20);
    path.lineTo(size.width, 0.0);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
