import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/utility.dart';

import '../../../utils/config.dart';

class SocialPage extends StatefulWidget {
  final dynamic social;
  const SocialPage({super.key, this.social});

  @override
  State<SocialPage> createState() => _SocialPageState();
}

class _SocialPageState extends State<SocialPage> {
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController linkedinController = TextEditingController();
  TextEditingController behanceController = TextEditingController();
  TextEditingController dribbleController = TextEditingController();
  TextEditingController instaController = TextEditingController();
  TextEditingController fbController = TextEditingController();
  TextEditingController twitterController = TextEditingController();
  TextEditingController pintrestController = TextEditingController();
  TextEditingController siteController = TextEditingController();
  TextEditingController otherController = TextEditingController();
  bool mobileHidden = false;
  bool emailHidden = false;
  bool isAddPortfolioLoading = false;
  List<String> blockedPhone = ["1", "2", "3", "4", "0"];

  @override
  void initState() {
    // phoneController = TextEditingController(
    //     text: Utility().decrypted128(
    //         '${widget.social?.mob_num ?? Preference.getString(Preference.PHONE)}'));
    phoneController = TextEditingController(
        text: Utility().decrypted128(
            '${widget.social?['mob_num'] ?? Preference.getString(Preference.PHONE)}'));

    print('widget.social----${widget.social}');
    if (widget.social != null) {
      emailController = TextEditingController(
          text: Utility().decrypted128(
              '${widget.social?['email'] ?? Preference.getString(Preference.USER_EMAIL)}'));
      linkedinController =
          TextEditingController(text: widget.social['linkedin']);
      behanceController = TextEditingController(text: widget.social['bee']);
      dribbleController = TextEditingController(text: widget.social['dribble']);
      instaController = TextEditingController(text: widget.social?['insta']);
      fbController = TextEditingController(text: widget.social['facebook']);
      twitterController = TextEditingController(text: widget.social['twitter']);
      pintrestController =
          TextEditingController(text: widget.social['pinterest']);
      siteController = TextEditingController(text: widget.social['site_url']);
      otherController = TextEditingController(text: widget.social['other']);
      setValue();
    }

    super.initState();
  }

  void setValue() {
    mobileHidden = widget.social['mobNumHidden'] == 1;
    emailHidden = widget.social['emailHidden'] == 1;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Scaffold(
            backgroundColor: context.appColors.surface,
            appBar: AppBar(
                elevation: 0.0,
                backgroundColor: context.appColors.surface,
                centerTitle: true,
                title: Text(
                  'contact_social',
                  style: Styles.getBoldThemeStyle(context, size: 14),
                ).tr(),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Icon(
                        Icons.close,
                        color: context.appColors.headingText,
                      ),
                    ),
                  )
                ]),
            body: ScreenWithLoader(
              isLoading: isAddPortfolioLoading,
              body: BlocManager(
                  initState: (value) {},
                  child: BlocListener<HomeBloc, HomeState>(
                    listener: (context, state) async {
                      if (state is AddSocialState) handleAddSocial(state);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            TextField(
                              controller: phoneController,
                              keyboardType: TextInputType.number,
                              maxLength: APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec"
                                  ? 8
                                  : 10,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        width: 1, color: Color(0xffE5E5E5)),
                                    borderRadius: BorderRadius.circular(10)),
                                prefixIcon: SizedBox(
                                  width: 30,
                                  height: 1,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/call.svg',
                                        color: context.appColors.primary,
                                      ),
                                      VerticalDivider(
                                        thickness: 1,
                                        color: Color(0xffE5E5E5),
                                      ),
                                    ],
                                  ),
                                ),
                                hintText: tr('enter_mobile_number'),
                              ),
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            // InkWell(
                            //   onTap: () {
                            //     setState(() {
                            //       mobileHidden = !mobileHidden;
                            //     });
                            //   },
                            //   child: Padding(
                            //     padding: const EdgeInsets.all(4.0),
                            //     child: Row(
                            //       children: [
                            //         mobileHidden
                            //             ? SvgPicture.asset(
                            //                 'assets/images/close_eye.svg',

                            //                 color: context.appColors.primary,
                            //                 )
                            //             : Icon(
                            //                 Icons.remove_red_eye,
                            //                 size: 15,
                            //                 color: context.appColors.gradientLeft,
                            //               ),
                            //         VerticalDivider(),
                            //         Text(
                            //           'hide_contact',
                            //           style: TextStyle(
                            //               fontSize: 10,
                            //               fontWeight: FontWeight.w400,
                            //               color: Color(context.appColors.subHeadingTitle)),
                            //         ).tr()
                            //       ],
                            //     ),
                            //   ),
                            // ),
                            const SizedBox(
                              height: 10,
                            ),
                            TextField(
                              keyboardType: TextInputType.emailAddress,
                              controller: emailController,
                              decoration: InputDecoration(
                                fillColor: context.appColors.surface,
                                filled: true,
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        width: 1,
                                        color: context.appColors.grey6),
                                    borderRadius: BorderRadius.circular(10)),
                                prefixIcon: SizedBox(
                                  width: 30,
                                  height: 1,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/email.svg',
                                        color: context.appColors.primary,
                                      ),
                                      VerticalDivider(
                                        thickness: 1,
                                        color: Color(0xffE5E5E5),
                                      ),
                                    ],
                                  ),
                                ),
                                hintText: tr('enter_email_address'),
                              ),
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            // InkWell(
                            //     onTap: () {
                            //       setState(() {
                            //         emailHidden = !emailHidden;
                            //       });
                            //     },
                            //     child: Padding(
                            //       padding: const EdgeInsets.all(4.0),
                            //       child: Row(
                            //         children: [
                            //           emailHidden
                            //               ? SvgPicture.asset(
                            //                   'assets/images/close_eye.svg',  color: context.appColors.primary,)
                            //               : Icon(
                            //                   Icons.remove_red_eye,
                            //                   size: 15,
                            //                   color: context.appColors.gradientLeft,
                            //                 ),
                            //           VerticalDivider(),
                            //           Text(
                            //             'hide_email',
                            //             style: TextStyle(
                            //                 fontSize: 10,
                            //                 fontWeight: FontWeight.w400,
                            //                 color: Color(context.appColors.subHeadingTitle)),
                            //           ).tr()
                            //         ],
                            //       ),
                            //     )),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                children: [
                                  Text(
                                    "social",
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ).tr()
                                ],
                              ),
                            ),
                            customField(
                                imgPath: 'assets/images/google.png',
                                hintText: 'Enter your website',
                                controller: siteController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/linkedin.png',
                                hintText: 'linkedin.com/',
                                controller: linkedinController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/behance.png',
                                hintText: 'behance.net/',
                                controller: behanceController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/dribble.png',
                                hintText: 'dribbble.com/',
                                controller: dribbleController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/instagram.png',
                                hintText: 'instagram.com/',
                                controller: instaController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/facebook.png',
                                hintText: 'facebook.com/',
                                controller: fbController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/twitter.png',
                                hintText: 'twitter.com/',
                                controller: twitterController,
                                context: context),
                            customField(
                                imgPath: 'assets/images/pinterest.png',
                                hintText: 'pinterest.com/',
                                controller: pintrestController,
                                context: context),
                            const SizedBox(
                              height: 15,
                            ),
                            InkWell(
                              onTap: () {
                                if (APK_DETAILS["package_name"] ==
                                    "com.singulariswow.mec") {
                                  if (phoneController.text.toString().length !=
                                          8 ||
                                      blockedPhone.contains(phoneController.text
                                          .toString()
                                          .split('')
                                          .first)) {
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                    return;
                                  }
                                } else {
                                  if (phoneController.text.toString().length !=
                                          10 ||
                                      blockedPhone.contains(phoneController.text
                                          .toString()
                                          .split('')
                                          .first)) {
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                    return;
                                  }
                                }

                                if (phoneController.value.text == '') {
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                    content: Text('phone_no_required').tr(),
                                  ));
                                } else if (emailController.value.text == '') {
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                    content: Text('email_required').tr(),
                                  ));
                                } else {
                                  Map<String, dynamic> data = {};
                                  data["mob_num"] = phoneController.value.text;
                                  data["email"] = emailController.value.text;
                                  data["linkedin"] =
                                      linkedinController.value.text;
                                  data["bee"] = behanceController.value.text;
                                  data["dribble"] =
                                      dribbleController.value.text;
                                  data["insta"] = instaController.value.text;
                                  data["facebook"] = fbController.value.text;
                                  data["twitter"] =
                                      twitterController.value.text;
                                  data["pinterest"] =
                                      pintrestController.value.text;
                                  data["other"] = "";
                                  data["site_url"] = siteController.value.text;
                                  data["mob_num_hidden"] =
                                      mobileHidden == true ? 'on' : 'off';
                                  data["email_hidden"] =
                                      emailHidden == true ? 'on' : 'off';

                                  addSocail(data);
                                }
                              },
                              child: Container(
                                height: height(context) * 0.06,
                                width: width(context) * 0.8,
                                padding: const EdgeInsets.all(10.0),
                                margin: const EdgeInsets.all(20.0),
                                decoration: BoxDecoration(
                                    color: context.appColors.pureBlack,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(21))),
                                child: Center(
                                  child: Text(
                                    'save',
                                    style: TextStyle(
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w500,
                                        color: context
                                            .appColors.primaryForeground),
                                    textAlign: TextAlign.center,
                                  ).tr(),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  )),
            ));
      },
    );
  }

  Widget customField(
      {required String imgPath,
      required String hintText,
      required TextEditingController controller,
      required BuildContext context}) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              fillColor: context.appColors.surface,
              filled: true,
              border: OutlineInputBorder(
                  borderSide:
                      BorderSide(width: 1, color: context.appColors.grey6),
                  borderRadius: BorderRadius.circular(10)),
              prefixIcon: SizedBox(
                width: 40,
                height: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 3.0),
                      child: Image.asset(
                        imgPath,
                      ),
                    ),
                    // SvgPicture.asset(imgPath),

                    VerticalDivider(
                      thickness: 1,
                      color: context.appColors.divider,
                    ),
                  ],
                ),
              ),
              hintText: hintText,
            ),
          ),
        );
      },
    );
  }

  void addSocail(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddSocialEvent(data: data));
  }

  void handleAddSocial(AddSocialState state) {
    var addPortfolioState = state;
    setState(() {
      switch (addPortfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v(" Add social Portfolio....................");
          isAddPortfolioLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add Social....................");

          if (addPortfolioState.response?.status == 1) {
            Preference.setString(Preference.PHONE, phoneController.value.text);
            Preference.setString(
                Preference.USER_EMAIL, emailController.value.text);
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('info_update_sucss').tr(),
            ));
          } else if (addPortfolioState.response?.status == 0) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('${addPortfolioState.response?.error?.first}'),
            ));
          }

          isAddPortfolioLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Social....................");
          isAddPortfolioLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${addPortfolioState.response?.error?.first}'),
          ));
          FirebaseAnalytics.instance.logEvent(name: 'social_page', parameters: {
            "ERROR": '${addPortfolioState.response?.error?.first}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
