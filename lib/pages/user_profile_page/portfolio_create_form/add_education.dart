import 'dart:io';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class AddEducation extends StatefulWidget {
  final CommonProfession? education;

  final bool? isEditMode;
  const AddEducation({super.key, this.isEditMode = false, this.education});

  @override
  State<AddEducation> createState() => _AddActivitiesState();
}

class _AddActivitiesState extends State<AddEducation> {
  TextEditingController schoolController = TextEditingController();
  TextEditingController degreeController = TextEditingController();
  TextEditingController descController = TextEditingController();
  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  DateTime selectedDate = DateTime.now();
  bool isAddEducationLoading = false;
  File? uploadImg;
  final _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    setValues();
    super.initState();
  }

  void setValues() {
    if (widget.isEditMode == true) {
      schoolController =
          TextEditingController(text: widget.education?.institute);
      degreeController = TextEditingController(text: widget.education?.title);
      descController =
          TextEditingController(text: widget.education?.description);
      startDate = TextEditingController(text: widget.education?.startDate);
      endDate = TextEditingController(text: widget.education?.endDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is AddActivitiesState) handleAddEducation(state);
            },
            child: Scaffold(
                backgroundColor: context.appColors.background,
                appBar: AppBar(
                  backgroundColor: context.appColors.surface,
                  elevation: 0,
                  leading: SizedBox(),
                  centerTitle: true,
                  title: Text(
                      widget.isEditMode == true
                          ? tr('edit_education')
                          : tr('add_education'),
                      style: Styles.getBoldThemeStyle(context, size: 14)),
                  actions: [
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(Icons.close_outlined,
                            color: context.appColors.headingPrimaryColor)),
                  ],
                ),
                body: ScreenWithLoader(
                  isLoading: isAddEducationLoading,
                  body: SafeArea(
                    child: Padding(
                        padding: const EdgeInsets.only(top: 0.0),
                        child: SingleChildScrollView(
                            child: Form(
                          key: _formKey,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "${tr('school')}*",
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: CustomTextField(
                                      validate: true,
                                      validationString:
                                          tr('please_enter_school'),
                                      controller: schoolController,
                                      maxChar: 50,
                                      hintText:
                                          tr('education_school_placeholder')),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "${tr('degree')}*",
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: CustomTextField(
                                      validate: true,
                                      validationString:
                                          tr('please_enter_degree'),
                                      controller: degreeController,
                                      maxChar: 50,
                                      hintText:
                                          tr('education_degree_placeholder')),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "${tr('start_date')}*",
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    try {
                                      selectDate(context, startDate);
                                    } catch (e) {
                                      startDate = TextEditingController();
                                      selectDate(context, startDate);
                                    }
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Container(
                                      width: width(context),
                                      height: height(context) * 0.07,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                        border: Border.all(
                                            width: 1.0,
                                            color: const Color(0xffE5E5E5)),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(10.0)),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Text(
                                              startDate.value.text != ''
                                                  ? startDate.value.text
                                                  : tr('select_date'),
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      startDate.value.text != ''
                                                          ? context.appColors
                                                              .textBlack
                                                          : context
                                                              .appColors.grey3),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 8.0, left: 8.0),
                                            child: SvgPicture.asset(
                                                'assets/images/selected_calender.svg'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    tr('end_date'),
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    if (startDate.value.text != '') {
                                      try {
                                        selectDate(context, endDate,
                                            startDate: selectedDate);
                                      } catch (e) {
                                        endDate = TextEditingController();
                                        selectDate(context, endDate,
                                            startDate: selectedDate);
                                      }
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content:
                                            Text(tr('Plz_start_date_first')),
                                      ));
                                    }
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Container(
                                      width: width(context),
                                      height: height(context) * 0.07,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                        border: Border.all(
                                            width: 1.0,
                                            color: const Color(0xffE5E5E5)),
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(10.0)),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Text(
                                              endDate.value.text != ''
                                                  ? endDate.value.text
                                                  : tr('select_date'),
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      endDate.value.text != ''
                                                          ? context.appColors
                                                              .textBlack
                                                          : context
                                                              .appColors.grey3),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                right: 8.0, left: 8.0),
                                            child: SvgPicture.asset(
                                                'assets/images/selected_calender.svg'),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    'description',
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ).tr(),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: CustomTextField(
                                    validate: true,
                                    validationString: tr('write_your_post'),
                                    controller: descController,
                                    maxLine: 6,
                                    maxChar: 500,
                                    hintText: tr('description_msg'),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "${tr('featured_image')}*",
                                    style: Styles.regular(
                                        size: 14,
                                        color: context
                                            .appColors.headingPrimaryColor),
                                  ),
                                ),
                                SizedBox(height: 2),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: InkWell(
                                    onTap: () async {
                                      FilePickerResult? result;

                                      if (Platform.isIOS) {
                                        result =
                                            await FilePicker.platform.pickFiles(
                                          allowMultiple: false,
                                          type: FileType.image,
                                        );
                                        if (result != null) {
                                          setState(() {
                                            uploadImg =
                                                File(result!.files.first.path!);
                                          });
                                        }
                                      } else {
                                        final pickedFileC =
                                            await ImagePicker().pickImage(
                                          source: ImageSource.gallery,
                                          imageQuality: 100,
                                        );
                                        if (pickedFileC != null) {
                                          setState(() {
                                            uploadImg = File(pickedFileC.path);
                                          });
                                        }
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        ShaderMask(
                                            blendMode: BlendMode.srcIn,
                                            shaderCallback: (Rect bounds) {
                                              return LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: <Color>[
                                                    context
                                                        .appColors.gradientLeft,
                                                    context
                                                        .appColors.gradientRight
                                                  ]).createShader(bounds);
                                            },
                                            child: Row(
                                              children: [
                                                SvgPicture.asset(
                                                    'assets/images/upload_icon.svg'),
                                                Text(
                                                  'upload_image',
                                                  style:
                                                      Styles.getBoldThemeStyle(
                                                          context,
                                                          size: 12),
                                                ).tr(),
                                              ],
                                            )),
                                        SizedBox(
                                          width: 4,
                                        ),
                                        SizedBox(
                                          width: width(context) * 0.6,
                                          child: Text(
                                              uploadImg != null
                                                  ? '${uploadImg?.path.split('/').last}'
                                                  : widget.education
                                                          ?.imageName ??
                                                      tr('upload_only_image'),
                                              maxLines: 2,
                                              softWrap: true,
                                              style: TextStyle(
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      context.appColors.grey3)),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: PortfolioCustomButton(
                                      clickAction: () async {
                                        if (!_formKey.currentState!
                                            .validate()) {
                                          return;
                                        }

                                        try {
                                          DateFormat("yyyy-MM-dd")
                                              .parse(startDate.value.text);
                                        } catch (e) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(
                                                        'please_choose_start_date')
                                                    .tr()),
                                          );
                                          return;
                                        }
                                        try {
                                          DateFormat("yyyy-MM-dd")
                                              .parse(endDate.value.text);
                                        } catch (e) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(
                                                        'please_choose_end_date')
                                                    .tr()),
                                          );
                                          return;
                                        }

                                        try {
                                          DateFormat("yyyy-MM-dd")
                                              .parse(endDate.value.text);
                                        } catch (e) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                          'end_date_must_lg_then_start')
                                                      .tr()));
                                          return;
                                        }
                                        DateTime startD =
                                            DateFormat("yyyy-MM-dd")
                                                .parse(startDate.value.text);
                                        DateTime endD = DateFormat("yyyy-MM-dd")
                                            .parse(endDate.value.text);

                                        if (startD.isAfter(endD) ||
                                            startD == endD) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                          'end_date_must_lg_then_start')
                                                      .tr()));
                                          return;
                                        }

                                        if (startDate.value.text == '') {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(
                                                        'please_choose_start_date')
                                                    .tr()),
                                          );
                                        } else if (endDate.value.text == '') {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(
                                                        'please_choose_end_date')
                                                    .tr()),
                                          );
                                        } else if (_formKey.currentState!
                                            .validate()) {
                                          Map<String, dynamic> data = {};
                                          setState(() {
                                            isAddEducationLoading = true;
                                          });
                                          try {
                                            if (widget.isEditMode == true) {
                                              if (uploadImg?.path != null) {
                                                String? fileName = uploadImg
                                                    ?.path
                                                    .split('/')
                                                    .last;
                                                data['certificate'] =
                                                    await MultipartFile.fromFile(
                                                        '${uploadImg?.path}',
                                                        filename: fileName);
                                                await Utility()
                                                    .s3UploadFile(
                                                        '${uploadImg?.path}')
                                                    .then((value) => data[
                                                        'certificate_cdn']);
                                              }
                                            } else if (startDate.value.text ==
                                                '') {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                content: Text(
                                                        'please_choose_end_date')
                                                    .tr(),
                                              ));
                                            } else {
                                              String? fileName = uploadImg?.path
                                                  .split('/')
                                                  .last;
                                              data['certificate'] =
                                                  await MultipartFile.fromFile(
                                                      '${uploadImg?.path}',
                                                      filename: fileName);
                                              await Utility()
                                                  .s3UploadFile(
                                                      '${uploadImg?.path}')
                                                  .then((value) =>
                                                      data['certificate_cdn']);
                                            }
                                            data["activity_type"] = "Education";
                                            data["title"] =
                                                degreeController.value.text;
                                            data["description"] =
                                                descController.value.text;
                                            data["start_date"] =
                                                startDate.value.text;
                                            data["end_date"] =
                                                endDate.value.text;
                                            data["institute"] =
                                                schoolController.value.text;
                                            data["professional_key"] = widget
                                                        .isEditMode ==
                                                    true
                                                ? "education_${widget.education?.id}"
                                                : "new_professional";
                                            data["edit_url_professional"] =
                                                widget.isEditMode == true &&
                                                        uploadImg?.path == null
                                                    ? widget
                                                        .education?.imageName
                                                    : "";

                                            addEducation(data);
                                          } catch (e) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                  content: Text(
                                                          'plz_add_feature_img')
                                                      .tr()),
                                            );
                                          }

                                          setState(() {
                                            isAddEducationLoading = false;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                )
                              ]),
                        ))),
                  ),
                ))));
  }

  void addEducation(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddActivitiesEvent(data: data));
  }

  void handleAddEducation(AddActivitiesState state) {
    var addActivitiesState = state;
    setState(() {
      switch (addActivitiesState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add Education....................");
          isAddEducationLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add Education....................");
          isAddEducationLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(widget.isEditMode == true
                      ? 'education_updated_successfully'
                      : 'education_added_successfully')
                  .tr()));
          Navigator.pop(context);
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Education....................");
          isAddEducationLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'add_education', parameters: {
            "ERROR": '${addActivitiesState.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> selectDate(
      BuildContext context, TextEditingController controller,
      {DateTime? startDate}) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate, // Refer step 1
      firstDate: startDate ?? DateTime(1900),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        controller.text =
            Utility.convertDateFormat(selectedDate, format: 'yyyy-MM-dd');
      });
    }
  }
}
