import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/skill_rating_resp.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_skill.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/my_skill_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:page_transition/page_transition.dart';

class MySkill extends StatefulWidget {
  final showAddButton;
  final userId;
  const MySkill({super.key, this.showAddButton, this.userId});

  @override
  State<MySkill> createState() => _MySkillState();
}

class _MySkillState extends State<MySkill> {
  bool isSkillRating = false;

  SkillRatingResponse? skillResponse;

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {
          skillRating();
        },
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is SkillRatingState) {
                handleSkillRating(state);
              }
            },
            child: Column(
              children: [
                Container(
                  color: context.appColors.surface,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Column(
                      children: [
                        if (!widget.showAddButton)
                          SizedBox(
                            height: 10,
                          ),
                        Row(
                          children: [
                            Text(
                              'skill_and_rating',
                              style:
                                  Styles.getBoldThemeStyle(context, size: 14),
                            ).tr(),
                            Spacer(),
                            if (widget.showAddButton)
                              IconButton(
                                  onPressed: () async {
                                    await Navigator.push(
                                            context,
                                            PageTransition(
                                                duration:
                                                    Duration(milliseconds: 350),
                                                reverseDuration:
                                                    Duration(milliseconds: 350),
                                                type: PageTransitionType
                                                    .bottomToTop,
                                                child: AddSkill()))
                                        .then((value) => skillRating());
                                  },
                                  icon: Icon(Icons.add)),
                            if (skillResponse != null &&
                                skillResponse?.data?.length != 0 &&
                                widget.showAddButton)
                              InkWell(
                                  onTap: (() async {
                                    await Navigator.push(
                                        context,
                                        PageTransition(
                                            duration:
                                                Duration(milliseconds: 350),
                                            reverseDuration:
                                                Duration(milliseconds: 350),
                                            type:
                                                PageTransitionType.bottomToTop,
                                            child: MySkillList(
                                              skillResponse: skillResponse,
                                            ))).then((value) => skillRating());
                                  }),
                                  child:
                                      Icon(Icons.arrow_forward_ios_outlined)),
                          ],
                        ),
                        Divider(),
                      ],
                    ),
                  ),
                ),
                skillResponse == null ||
                        skillResponse?.data?.length == 0 ||
                        widget.userId != null
                    ? Container(
                        height: height(context) * 0.25,
                        padding: const EdgeInsets.only(bottom: 54, top: 30),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset('assets/images/empty_skill.svg'),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'add_your',
                                ).tr(),
                                SizedBox(
                                  width: 4,
                                ),
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: InkWell(
                                        onTap: () async {
                                          if (widget.userId == null) {
                                            await Navigator.push(
                                                    context,
                                                    PageTransition(
                                                        duration: Duration(
                                                            milliseconds: 350),
                                                        reverseDuration:
                                                            Duration(
                                                                milliseconds:
                                                                    350),
                                                        type: PageTransitionType
                                                            .bottomToTop,
                                                        child: AddSkill()))
                                                .then((value) => skillRating());
                                          }
                                        },
                                        child: Text(
                                          'skills',
                                          style: Styles.getBoldThemeStyle(
                                              context,
                                              size: 14),
                                        ).tr()))
                              ],
                            )
                            // Text(
                            //   'add_your_skill',
                            //   style: Styles.getSemiboldThemeStyle(context, size: 14),
                            // ).tr()
                          ],
                        ),
                      )
                    : ListView.builder(
                        physics: NeverScrollableScrollPhysics(),
                        scrollDirection: Axis.vertical,
                        shrinkWrap: true,
                        itemCount: min(5, skillResponse?.data?.length ?? 0),
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: CustomRating(
                                progressWidth:
                                    MediaQuery.of(context).size.width * 0.9,
                                title: '${skillResponse?.data?[index].name}',
                                percentage: int.parse(
                                    '${skillResponse?.data?[index].selfProficiency ?? '0'}'),
                                onClick: () {}),
                          );
                        },
                      )
              ],
            )));
  }

  void skillRating() {
    BlocProvider.of<HomeBloc>(context).add(SkillRatingEvent());
  }

  void handleSkillRating(SkillRatingState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Skill List....................");
          isSkillRating = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Skill List....................");
          skillResponse = state.response;
          isSkillRating = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error  Skill List....................");
          isSkillRating = false;
          FirebaseAnalytics.instance.logEvent(name: 'my_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
