import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class OpenToWork extends StatelessWidget {
  const OpenToWork({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(title: Text('skill & rating')),
            body: Container(
                width: width(context),
                height: height(context) * 0.02,
                decoration: BoxDecoration(
                  color: context.appColors.background,
                  border: Border.all(width: 0, color: context.appColors.grey3),
                  borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                ),
                child: Column(children: [
                  ListView.builder(
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      itemCount: 1,
                      itemBuilder: (context, index) {
                        return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Card(
                                color: context.appColors.bgLightGrey,
                                elevation: 0.0,
                                child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: ListTile(
                                      title: Padding(
                                        padding:
                                            const EdgeInsets.only(left: 3.0),
                                        child: Text(
                                          "master",
                                          style: Styles.textBold(
                                              color: context
                                                  .appColors.headingText),
                                        ),
                                      ),
                                      trailing: Column(
                                        children: [
                                          Row(children: [
                                            Text('master',
                                                style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                            SizedBox(width: 5),
                                            Text('percent',
                                                style: TextStyle(
                                                    fontSize: 10,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                          ]),
                                        ],
                                      ),
                                    ))));
                      })
                ])));
      },
    );
  }
}
