import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/skill_rating_resp.dart';
import 'package:masterg/data/models/response/home_response/skill_suggestion_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/choose_skill_rating.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class AddSkill extends StatefulWidget {
  final bool? isEditMode;
  final Skill? skill;
  const AddSkill({super.key, this.isEditMode = false, this.skill});

  @override
  State<AddSkill> createState() => _AddSkillState();
}

class _AddSkillState extends State<AddSkill> {
  TextEditingController titleController = TextEditingController();
  TextEditingController percentController = TextEditingController();

  File? uploadCerti;
  bool? isAddSkillLoading = false;
  final _formKey = GlobalKey<FormState>();
  int? percentage = 10;

  @override
  void initState() {
    debugPrint('print ${widget.skill?.toJson()}');
    updateData();
    super.initState();
  }

  void updateData() {
    if (widget.isEditMode == true) {
      setState(() {
        titleController = TextEditingController(text: '${widget.skill?.name}');
        percentage = widget.skill?.selfProficiency;
        selectedId = widget.skill?.skillId;
      });
    }
  }

  final List<String> _kOptions = [];
  SkillSuggestionResponse? skillSuggestionResponse;
  int? selectedId;
  String? selectedSkill;

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {
          if (widget.isEditMode == false) getSkillSuggestion();
        },
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is AddSkillState) handleAddSkill(state);
              if (state is SkillSuggestionState) handleSkillSuggestion(state);
            },
            child: Scaffold(
                backgroundColor: context.appColors.background,
                appBar: AppBar(
                  backgroundColor: context.appColors.surface,
                  elevation: 0,
                  leading: SizedBox(),
                  centerTitle: true,
                  title: Text(
                    widget.isEditMode == true
                        ? 'edit_skill'
                        : 'add_and_rate_your_skill_level',
                    style: Styles.bold(
                        size: 14, color: context.appColors.headingPrimaryColor),
                  ).tr(),
                  actions: [
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(Icons.close_outlined,
                            color: context.appColors.headingPrimaryColor)),
                  ],
                ),
                body: ScreenWithLoader(
                  isLoading: isAddSkillLoading,
                  body: Padding(
                    padding: const EdgeInsets.only(top: 0.0),
                    child: SizedBox(
                      height: height(context) * 0.6,
                      child: SingleChildScrollView(
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: SingleChildScrollView(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                        Text(
                                          "${tr('title_skill')}*",
                                          style: Styles.regular(
                                              size: 14,
                                              color: context.appColors
                                                  .headingPrimaryColor),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Autocomplete<String>(
                                          initialValue: widget.isEditMode ==
                                                  true
                                              ? TextEditingValue(
                                                  text: '${widget.skill?.name}')
                                              : null,
                                          fieldViewBuilder: (BuildContext
                                                  context,
                                              TextEditingController
                                                  textEditingController,
                                              FocusNode focusNode,
                                              VoidCallback onFieldSubmitted) {
                                            return TextField(
                                              enabled:
                                                  widget.isEditMode == false,
                                              controller: textEditingController,
                                              focusNode: focusNode,
                                              style: TextStyle(fontSize: 16),
                                              decoration: InputDecoration(
                                                hintStyle: Styles.regular(
                                                    size: 14,
                                                    color: context
                                                        .appColors.grey3),
                                                border: OutlineInputBorder(
                                                    borderSide:
                                                        const BorderSide(
                                                            width: 1,
                                                            color: Color(
                                                                0xffE5E5E5)),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                hintText: tr('search_skill'),
                                                prefixIcon: Icon(Icons.search),
                                              ),
                                              onChanged: (text) {
                                                if (text.trim() ==
                                                    selectedSkill) {
                                                  return;
                                                }
                                                setState(() {
                                                  selectedId = null;
                                                });
                                              },
                                              onSubmitted: (text) =>
                                                  onFieldSubmitted(),
                                            );
                                          },
                                          optionsBuilder: (TextEditingValue
                                              textEditingValue) {
                                            if (textEditingValue.text == '') {
                                              return const Iterable<
                                                  String>.empty();
                                            }
                                            return _kOptions
                                                .where((String option) {
                                              return option
                                                  .toLowerCase()
                                                  .contains(textEditingValue
                                                      .text
                                                      .toLowerCase());
                                            });
                                          },
                                          onSelected: (String selection) {
                                            selectedId = skillSuggestionResponse
                                                ?.data
                                                ?.where((element) =>
                                                    element.name == selection)
                                                .first
                                                .id;
                                            selectedSkill = selection;
                                          },
                                        ),
                                        const SizedBox(
                                          height: 20,
                                        ),
                                        Text(
                                          'rate_your_skill',
                                          style: Styles.regular(
                                              size: 14,
                                              color: context.appColors
                                                  .headingPrimaryColor),
                                        ).tr(),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Container(
                                          height: 60,
                                          padding:
                                              const EdgeInsets.only(top: 20),
                                          margin:
                                              const EdgeInsets.only(top: 20),
                                          decoration: BoxDecoration(
                                              color: context
                                                  .appColors.backgroundSurface,
                                              borderRadius:
                                                  BorderRadius.circular(2)),
                                          child: CustomSliderPage(
                                            initialPercentage:
                                                widget.isEditMode == true
                                                    ? widget.skill!
                                                            .selfProficiency! /
                                                        100
                                                    : 0.1,
                                            sliderWidth: width(context) * 0.9,
                                            sendPercentage: (int percent) {
                                              percentage = percent;
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          height: 15,
                                        ),
                                        Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            'drag_to_select_your_level',
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors.grey3),
                                          ).tr(),
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        SizedBox(
                                          height: 20,
                                        ),
                                        PortfolioCustomButton(
                                          clickAction: () async {
                                            if (selectedId == null) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                content: Text(
                                                  'search_existing_skills',
                                                  style: Styles.regular(
                                                      size: 14,
                                                      color: context
                                                          .appColors.textWhite),
                                                ).tr(),
                                              ));
                                              return;
                                            }
                                            Map<String, dynamic> data = {};
                                            data['skill_id'] = selectedId;
                                            data['self_proficiency'] =
                                                percentage;

                                            debugPrint('print $data');
                                            BlocProvider.of<HomeBloc>(context,
                                                    listen: false)
                                                .add(AddSkillEvent(data: data));
                                          },
                                        ),
                                      ]))),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ))));
  }

  void AddSkill(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context, listen: false)
        .add(AddActivitiesEvent(data: data));
  }

  void getSkillSuggestion() {
    BlocProvider.of<HomeBloc>(context, listen: false)
        .add(SkillSuggestionEvent());
  }

  void handleAddSkill(AddSkillState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Skill....................");
          isAddSkillLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add  Skill....................");
          isAddSkillLoading = false;

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${state.response?.data}'),
          ));
          Navigator.pop(context);

          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Skill....................");
          isAddSkillLoading = false;
          FirebaseAnalytics.instance.logEvent(name: 'add_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleSkillSuggestion(SkillSuggestionState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Skill....................");
          isAddSkillLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add  Skill....................");
          try {
            skillSuggestionResponse = state.response;
            List<String?> tempList =
                skillSuggestionResponse!.data!.map((e) => e.name).toList();
            Log.v("Success Add  Skill.................... ${_kOptions.length}");

            if (tempList.isNotEmpty) {
              for (var skill in tempList) {
                _kOptions.add(skill!);
              }
            }
            isAddSkillLoading = false;
            Log.v("Success Add  Skill.................... ${_kOptions.length}");
          } catch (e, stacktrace) {
            debugPrint("seme woring $stacktrace");
          }

          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Skill....................");
          isAddSkillLoading = false;
          FirebaseAnalytics.instance.logEvent(name: 'add_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
