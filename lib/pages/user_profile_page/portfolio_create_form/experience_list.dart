import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_experience.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:page_transition/page_transition.dart';

class ExperienceList extends StatefulWidget {
  final List<CommonProfession>? experience;
  final String? baseUrl;

  const ExperienceList({super.key, this.baseUrl, required this.experience});

  @override
  State<ExperienceList> createState() => _ExperienceListState();
}

class _ExperienceListState extends State<ExperienceList> {
  bool isExperienceLoading = false;
  List<CommonProfession>? experience;
  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  @override
  void initState() {
    List<CommonProfession> l2 = widget.experience!
        .where((element) => !(element.currentlyWorkHere == 'true' ||
            element.currentlyWorkHere == 'on'))
        .toList();
    l2.sort((a, b) => b.endDate.compareTo(a.startDate));

    experience = [
      ...widget.experience!.where((element) =>
          element.currentlyWorkHere == 'true' ||
          element.currentlyWorkHere == 'on'),
      ...l2
    ];

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is SingularisDeletePortfolioState) {
                handleSingularisDeletePortfolioState(state);
              }

              if (state is PortfolioState) {
                handlePortfolioState(state);
              }
            },
            child: Scaffold(
                backgroundColor: context.appColors.background,
                appBar: AppBar(
                  title: Text("experience",
                          style: Styles.getBoldThemeStyle(context))
                      .tr(),
                  elevation: 0,
                  backgroundColor: context.appColors.surface,
                  leading: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: context.appColors.textBlack,
                      )),
                  actions: [
                    IconButton(
                        onPressed: () async {
                          Navigator.push(
                                  context,
                                  PageTransition(
                                      duration: Duration(milliseconds: 350),
                                      reverseDuration:
                                          Duration(milliseconds: 350),
                                      type: PageTransitionType.bottomToTop,
                                      child: AddExperience()))
                              .then((value) => updatePortfolioList());
                        },
                        icon: Icon(
                          Icons.add,
                          color: context.appColors.textBlack,
                        )),
                  ],
                ),
                body: ScreenWithLoader(
                  isLoading: isExperienceLoading,
                  body: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: SizedBox(
                        height: height(context) * 0.9,
                        width: width(context),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: ListView.builder(
                              itemCount: experience?.length,
                              itemBuilder: (BuildContext context, int index) {
                                String startDateString =
                                    "${experience?[index].startDate}";

                                DateTime startDate = DateFormat("yyyy-MM-dd")
                                    .parse(startDateString);

                                DateTime endDate = DateTime.now();
                                if (experience?[index].endDate != '') {
                                  String endDateString =
                                      "${experience?[index].endDate}";
                                  endDate = DateFormat("yyyy-MM-dd")
                                      .parse(endDateString);
                                }

                                return Container(
                                  margin: EdgeInsets.only(right: 10),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child: SizedBox(
                                                width: width(context) * 0.2,
                                                height: width(context) * 0.2,
                                                child: CachedNetworkImage(
                                                  imageUrl:
                                                      "${widget.baseUrl}${experience?[index].imageName}",
                                                  fit: BoxFit.cover,
                                                  progressIndicatorBuilder: (context,
                                                          url,
                                                          downloadProgress) =>
                                                      Container(
                                                          width: width(context) *
                                                              0.2,
                                                          height: width(context) *
                                                              0.2,
                                                          padding:
                                                              EdgeInsets.all(8),
                                                          decoration: BoxDecoration(
                                                              color: context
                                                                  .appColors
                                                                  .divider,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8)),
                                                          child: SvgPicture.asset(
                                                              'assets/images/extra.svg')),
                                                  errorWidget: (context, url,
                                                          error) =>
                                                      Container(
                                                          width: width(context) *
                                                              0.2,
                                                          height: width(context) *
                                                              0.2,
                                                          padding:
                                                              EdgeInsets.all(8),
                                                          decoration: BoxDecoration(
                                                              color: context
                                                                  .appColors
                                                                  .divider,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8)),
                                                          child: SvgPicture.asset(
                                                              'assets/images/extra.svg')),
                                                ),
                                              )),
                                          SizedBox(width: 6),
                                          SizedBox(
                                            width: width(context) * 0.75,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(
                                                      width: width(context) *
                                                          0.578,
                                                      child: Text(
                                                        '${experience?[index].title}',
                                                        style: Styles
                                                            .getBoldThemeStyle(
                                                                context,
                                                                size: 16),
                                                      ),
                                                    ),
                                                    InkWell(
                                                      onTap: () async {
                                                        await Navigator.push(
                                                                context,
                                                                PageTransition(
                                                                    duration: Duration(
                                                                        milliseconds:
                                                                            300),
                                                                    reverseDuration:
                                                                        Duration(
                                                                            milliseconds:
                                                                                300),
                                                                    type: PageTransitionType
                                                                        .bottomToTop,
                                                                    child: AddExperience(
                                                                        isEditMode:
                                                                            true,
                                                                        experience:
                                                                            experience?[
                                                                                index])))
                                                            .then((value) =>
                                                                updatePortfolioList());
                                                      },
                                                      child: SvgPicture.asset(
                                                          'assets/images/edit_portfolio.svg',
                                                          colorFilter: context
                                                                  .isDarkMode
                                                              ? ColorFilter.mode(
                                                                  context
                                                                      .appColors
                                                                      .primaryForeground,
                                                                  BlendMode
                                                                      .srcIn)
                                                              : null),
                                                    ),
                                                    SizedBox(
                                                      width: 20,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        AlertsWidget
                                                            .showCustomDialog(
                                                                context:
                                                                    context,
                                                                title: '',
                                                                text: tr(
                                                                    'confirm_deletion_textone'),
                                                                icon:
                                                                    'assets/images/circle_alert_fill.svg',
                                                                onOkClick:
                                                                    () async {
                                                                  deletePortfolio(
                                                                      experience![
                                                                              index]
                                                                          .id);
                                                                });
                                                      },
                                                      child: SvgPicture.asset(
                                                        'assets/images/delete.svg',
                                                        colorFilter: context
                                                                .isDarkMode
                                                            ? ColorFilter.mode(
                                                                context
                                                                    .appColors
                                                                    .primaryForeground,
                                                                BlendMode.srcIn)
                                                            : null,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 4,
                                                ),
                                                Text(
                                                  '${experience?[index].institute}',
                                                  style: Styles
                                                      .getRegularThemeStyle(
                                                          context,
                                                          size: 14),
                                                ),
                                                SizedBox(
                                                  height: 4,
                                                ),
                                                experience?[index]
                                                                .currentlyWorkHere ==
                                                            'true' ||
                                                        experience?[index]
                                                                .currentlyWorkHere ==
                                                            'on'
                                                    ? Text(
                                                        '${tr('${experience?[index].employmentType}')}  •  ${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}  -  ${tr('present')}',
                                                        style: Styles
                                                            .getRegularThemeStyle(
                                                                context,
                                                                size: 12),
                                                      )
                                                    : Text(
                                                        '${tr('${experience?[index].employmentType}')} • ${calculateTimeDifferenceBetween(startDate, endDate)} • ${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}  -  '
                                                        ' ${listOfMonths[endDate.month - 1].substring(0, 3)} ${endDate.year.toString().substring(2, 4)}',
                                                        style: Styles
                                                            .getRegularThemeStyle(
                                                                context,
                                                                size: 12),
                                                      )
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      ReadMoreText(
                                        viewMore: tr('view_more'),
                                        text:
                                            '${experience?[index].description}',
                                        color: context.appColors.grey3,
                                      ),
                                      if (index != experience?.length) Divider()
                                    ],
                                  ),
                                );
                              }),
                        )),
                  ),
                ))));
  }

  void deletePortfolio(int id) {
    BlocProvider.of<HomeBloc>(context)
        .add(SingularisDeletePortfolioEvent(portfolioId: id));
  }

  void handleSingularisDeletePortfolioState(
      SingularisDeletePortfolioState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Delete Experience....................");
          isExperienceLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Delete  Experience....................");
          isExperienceLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('experience_deleted_successfully').tr()),
          );
          updatePortfolioList();
          break;
        case ApiStatus.ERROR:
          Log.v("Error Delete Experience....................");
          isExperienceLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_experience', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void updatePortfolioList() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading....................");
          isExperienceLoading = true;
          setState(() {});

          break;
        case ApiStatus.SUCCESS:
          Log.v("PortfolioState Success....................");
          experience = portfolioState.response?.data.experience;
          List<CommonProfession> l2 = experience!
              .where((element) => !(element.currentlyWorkHere == 'true' ||
                  element.currentlyWorkHere == 'on'))
              .toList();
          l2.sort((a, b) => b.endDate.compareTo(a.startDate));

          experience = [
            ...experience!.where((element) =>
                element.currentlyWorkHere == 'true' ||
                element.currentlyWorkHere == 'on'),
            ...l2
          ];
          isExperienceLoading = false;

          setState(() {});
          break;

        case ApiStatus.ERROR:
          isExperienceLoading = false;
          setState(() {});

          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_experience', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  String calculateTimeDifferenceBetween(DateTime startDate, DateTime endDate) {
    int seconds = endDate.difference(startDate).inSeconds;
    if (seconds < 60) {
      if (seconds.abs() < 4) return tr('now');
      return '${seconds.abs()} ${tr('second')}';
    } else if (seconds >= 60 && seconds < 3600)
      return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
    else if (seconds >= 3600 && seconds < 86400)
      return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
    else {
      // convert day to month
      int days = startDate.difference(endDate).inDays.abs();
      if (days < 30 && days > 7) {
        return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
      }
      if (days > 30) {
        int month = (startDate.difference(endDate).inDays ~/ 30).abs();
        return '$month ${tr('month')}';
      } else {
        return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
      }
    }
  }
}
