import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_extra_act.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:page_transition/page_transition.dart';
import 'package:shimmer/shimmer.dart';

class ExtraActivitiesList extends StatefulWidget {
  final List<CommonProfession> activities;
  final String? baseUrl;
  const ExtraActivitiesList(
      {super.key, required this.activities, this.baseUrl});

  @override
  State<ExtraActivitiesList> createState() => _ExtraActivitiesListState();
}

class _ExtraActivitiesListState extends State<ExtraActivitiesList> {
  bool? isActivitieLoading = false;
  List<CommonProfession>? activities;
  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  @override
  void initState() {
    activities = widget.activities;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: context.appColors.background,
        appBar: AppBar(
          title: Text('extra_curricular_activites',
                  style: Styles.getBoldThemeStyle(context))
              .tr(),
          elevation: 0,
          backgroundColor: context.appColors.surface,
          leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios,
                color: context.appColors.textBlack,
              )),
          actions: [
            IconButton(
                onPressed: () async {
                  Navigator.push(
                          context,
                          PageTransition(
                              duration: Duration(milliseconds: 350),
                              reverseDuration: Duration(milliseconds: 350),
                              type: PageTransitionType.bottomToTop,
                              child: AddActivities()))
                      .then((value) => updatePortfolioList());
                },
                icon: Icon(
                  Icons.add,
                  color: context.appColors.textBlack,
                )),
          ],
        ),
        body: BlocManager(
            initState: (context) {},
            child: BlocListener<HomeBloc, HomeState>(
                listener: (context, state) {
                  if (state is PortfolioState) {
                    handlePortfolioState(state);
                  }
                  if (state is SingularisDeletePortfolioState) {
                    handleSingularisDeletePortfolioState(state);
                  }
                },
                child: ScreenWithLoader(
                  isLoading: isActivitieLoading,
                  body: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 14.0),
                    child: SizedBox(
                        height: height(context) * 0.9,
                        width: width(context),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 8),
                          child: ListView.builder(
                              itemCount: activities?.length,
                              itemBuilder: (BuildContext context, int index) {
                                String startDateString =
                                    "${activities?[index].startDate}";

                                DateTime startDate =
                                    DateFormat("yyy-MM-dd", 'en_IN')
                                        .parse(startDateString);

                                return Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (index != 0)
                                      SizedBox(
                                        height: 10,
                                      ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: width(context) * 0.2,
                                          height: width(context) * 0.2,
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            child: CachedNetworkImage(
                                              imageUrl:
                                                  "${widget.baseUrl}${activities?[index].imageName}",
                                              fit: BoxFit.cover,
                                              progressIndicatorBuilder:
                                                  (context, url,
                                                          downloadProgress) =>
                                                      Shimmer.fromColors(
                                                baseColor: context
                                                    .appColors.shimmerBase,
                                                highlightColor: context
                                                    .appColors.shimmerHighlight,
                                                enabled: true,
                                                child: Container(
                                                  width: width(context) * 0.2,
                                                  height: width(context) * 0.2,
                                                  color:
                                                      context.appColors.surface,
                                                ),
                                              ),
                                              errorWidget: (context, url,
                                                      error) =>
                                                  Container(
                                                      width:
                                                          width(context) * 0.2,
                                                      height:
                                                          width(context) * 0.2,
                                                      padding:
                                                          EdgeInsets.all(8),
                                                      decoration: BoxDecoration(
                                                          color: context
                                                              .appColors
                                                              .divider,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8)),
                                                      child: SvgPicture.asset(
                                                          'assets/images/extra.svg')),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 6),
                                        SizedBox(
                                          // color: context.appColors.error,
                                          width: width(context) * 0.74,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  SizedBox(
                                                    width:
                                                        width(context) * 0.55,
                                                    child: Transform.translate(
                                                      offset: Offset(0, -3),
                                                      child: Text(
                                                        '${activities?[index].title}',
                                                        style: Styles
                                                            .getBoldThemeStyle(
                                                                context,
                                                                size: 14),
                                                      ),
                                                    ),
                                                  ),
                                                  Transform.translate(
                                                    offset: Offset(
                                                        Utility().isRTL(context)
                                                            ? -13
                                                            : 18,
                                                        0),
                                                    child: InkWell(
                                                      onTap: () async {
                                                        await Navigator.push(
                                                            context,
                                                            PageTransition(
                                                                duration: Duration(
                                                                    milliseconds:
                                                                        300),
                                                                reverseDuration:
                                                                    Duration(
                                                                        milliseconds:
                                                                            300),
                                                                type: PageTransitionType
                                                                    .bottomToTop,
                                                                child:
                                                                    AddActivities(
                                                                  isEditMode:
                                                                      true,
                                                                  activity:
                                                                      activities?[
                                                                          index],
                                                                ))).then((value) =>
                                                            updatePortfolioList());
                                                      },
                                                      child: SvgPicture.asset(
                                                          'assets/images/edit_portfolio.svg',
                                                          colorFilter: context
                                                                  .isDarkMode
                                                              ? ColorFilter.mode(
                                                                  context
                                                                      .appColors
                                                                      .primaryForeground,
                                                                  BlendMode
                                                                      .srcIn)
                                                              : null),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 20,
                                                  ),
                                                  Transform.translate(
                                                      offset: Offset(
                                                          Utility().isRTL(
                                                                  context)
                                                              ? -15
                                                              : 20,
                                                          0),
                                                      child: InkWell(
                                                        onTap: () {
                                                          AlertsWidget
                                                              .showCustomDialog(
                                                                  context:
                                                                      context,
                                                                  title: '',
                                                                  text: tr(
                                                                      'confirm_deletion_textone'),
                                                                  icon:
                                                                      'assets/images/circle_alert_fill.svg',
                                                                  onOkClick:
                                                                      () async {
                                                                    deletePortfolio(
                                                                        activities![index]
                                                                            .id);
                                                                  });
                                                        },
                                                        child: SvgPicture.asset(
                                                          'assets/images/delete.svg',
                                                          colorFilter: context
                                                                  .isDarkMode
                                                              ? ColorFilter.mode(
                                                                  context
                                                                      .appColors
                                                                      .primaryForeground,
                                                                  BlendMode
                                                                      .srcIn)
                                                              : null,
                                                        ),
                                                      )),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 4,
                                              ),
                                              Text(
                                                '${activities?[index].institute}',
                                                style:
                                                    Styles.getRegularThemeStyle(
                                                        context,
                                                        size: 14),
                                              ),
                                              SizedBox(
                                                height: 4,
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    '${activities?[index].curricularType} • ',
                                                    style: Styles
                                                        .getRegularThemeStyle(
                                                      context,
                                                      size: 12,
                                                    ),
                                                  ),
                                                  Text(
                                                    '${Utility.ordinal(startDate.day)} ${listOfMonths[startDate.month - 1]} ${startDate.year}',
                                                    style: Styles
                                                        .getRegularThemeStyle(
                                                      context,
                                                      size: 12,
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    SizedBox(
                                      height: 4,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    ReadMoreText(
                                      viewMore: tr('view_more'),
                                      text: '${activities?[index].description}',
                                      color: context.appColors.grey3,
                                    ),
                                    if (index != activities?.length) Divider()
                                  ],
                                );
                              }),
                        )),
                  ),
                ))));
  }

  void deletePortfolio(int id) {
    BlocProvider.of<HomeBloc>(context)
        .add(SingularisDeletePortfolioEvent(portfolioId: id));
  }

  void handleSingularisDeletePortfolioState(
      SingularisDeletePortfolioState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Delete Activities....................");
          isActivitieLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Delete  Activities....................");
          isActivitieLoading = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content:
                  Text('curricular_activities_deleted_successfully').tr()));
          updatePortfolioList();

          break;
        case ApiStatus.ERROR:
          Log.v("Error Delete Activities....................");
          isActivitieLoading = false;

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void updatePortfolioList() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading....................");
          isActivitieLoading = false;

          setState(() {});

          break;
        case ApiStatus.SUCCESS:
          Log.v("PortfolioState Success....................");
          activities = portfolioState.response?.data.extraActivities;
          activities?.sort((a, b) => b.startDate.compareTo(a.startDate));

          isActivitieLoading = false;

          setState(() {});
          break;

        case ApiStatus.ERROR:
          isActivitieLoading = false;

          setState(() {});

          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_extra_activities', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
