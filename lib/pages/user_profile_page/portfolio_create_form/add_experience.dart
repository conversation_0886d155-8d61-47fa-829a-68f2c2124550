import 'dart:io';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class AddExperience extends StatefulWidget {
  final bool? isEditMode;
  final CommonProfession? experience;

  const AddExperience({super.key, this.isEditMode, this.experience});

  @override
  State<AddExperience> createState() => _AddExperienceState();
}

class _AddExperienceState extends State<AddExperience> {
  TextEditingController titleController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController descController = TextEditingController();
  String employmentType = "";
  bool? isclicked = false;
  TextEditingController? startDate = TextEditingController();
  TextEditingController? endDate = TextEditingController();
  DateTime selectedDate = DateTime.now();
  bool? isAddExperienceLoading = false;
  File? uploadCerti;

  final _formKey = GlobalKey<FormState>();
  File? file;

  @override
  void initState() {
    if (widget.isEditMode == true) {
      titleController =
          TextEditingController(text: '${widget.experience?.title}');
      nameController =
          TextEditingController(text: '${widget.experience?.institute}');
      descController =
          TextEditingController(text: '${widget.experience?.description}');
      startDate =
          TextEditingController(text: '${widget.experience?.startDate}');
      endDate = TextEditingController(text: '${widget.experience?.endDate}');
      employmentType = '${widget.experience?.employmentType}';
      isclicked = widget.experience?.currentlyWorkHere == 'true' ||
              widget.experience?.currentlyWorkHere == 'on'
          ? true
          : false;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (value) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is AddActivitiesState) handleAddExperience(state);
            },
            child: Scaffold(
                backgroundColor: context.appColors.background,
                appBar: AppBar(
                  backgroundColor: context.appColors.surface,
                  elevation: 0,
                  leading: SizedBox(),
                  centerTitle: true,
                  title: Text(
                    widget.isEditMode == true
                        ? 'edit_experience'
                        : 'add_experience',
                    style: Styles.bold(
                        size: 14, color: context.appColors.headingPrimaryColor),
                  ).tr(),
                  actions: [
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(Icons.close_outlined,
                            color: context.appColors.headingPrimaryColor)),
                  ],
                ),
                body: ScreenWithLoader(
                    isLoading: isAddExperienceLoading,
                    body: Padding(
                        padding: const EdgeInsets.only(top: 0.0),
                        child: SizedBox(
                          height: height(context) * 0.9,
                          child: SingleChildScrollView(
                              child: Form(
                            key: _formKey,
                            child: Column(children: [
                              Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 14),
                                  child: SingleChildScrollView(
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "${tr('position_title')}*",
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors
                                                    .headingPrimaryColor),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          CustomTextField(
                                              validate: true,
                                              maxChar: 50,
                                              validationString:
                                                  tr('plz_enter_pos_title'),
                                              controller: titleController,
                                              hintText: tr(
                                                  'experience_position_placeholder')),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Text(
                                            "${tr('company_name')}*",
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors
                                                    .headingPrimaryColor),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          CustomTextField(
                                              validate: true,
                                              maxChar: 50,
                                              validationString:
                                                  tr('plz_enter_com_name'),
                                              controller: nameController,
                                              hintText: tr(
                                                  'experience_company_placeholder')),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Text(
                                            "${tr('employment_type')}*",
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors
                                                    .headingPrimaryColor),
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Radio(
                                                  visualDensity:
                                                      const VisualDensity(
                                                    horizontal: VisualDensity
                                                        .minimumDensity,
                                                  ),
                                                  materialTapTargetSize:
                                                      MaterialTapTargetSize
                                                          .shrinkWrap,
                                                  value: "full_time",
                                                  groupValue: employmentType,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      employmentType =
                                                          value.toString();
                                                    });
                                                  }),
                                              SizedBox(
                                                width: width(context) * 0.02,
                                              ),
                                              Text('full_time').tr(),
                                              Spacer(),
                                              Radio(
                                                  value: "part_time",
                                                  groupValue: employmentType,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      employmentType =
                                                          value.toString();
                                                    });
                                                  }),
                                              Text('part_time').tr(),
                                              Spacer(),
                                              Radio(
                                                  value: "internship",
                                                  groupValue: employmentType,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      employmentType =
                                                          value.toString();
                                                    });
                                                  }),
                                              Text('internship').tr(),
                                            ],
                                          ),
                                          Text(
                                            "${tr('start_date')}*",
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors
                                                    .headingPrimaryColor),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          InkWell(
                                            onTap: () {
                                              try {
                                                selectDate(context, startDate!);
                                              } catch (e) {
                                                startDate =
                                                    TextEditingController();
                                                selectDate(context, startDate!);
                                              }
                                            },
                                            child: Container(
                                              width: width(context),
                                              height: height(context) * 0.07,
                                              decoration: BoxDecoration(
                                                color:
                                                    context.appColors.surface,
                                                border: Border.all(
                                                    width: 1.0,
                                                    color: const Color.fromARGB(
                                                        255, 142, 142, 142)),
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(10.0)),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: Text(
                                                      startDate!
                                                                  .value.text !=
                                                              ''
                                                          ? startDate!
                                                              .value.text
                                                          : tr('select_date'),
                                                      style: TextStyle(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          color: startDate!
                                                                      .value
                                                                      .text !=
                                                                  ''
                                                              ? context
                                                                  .appColors
                                                                  .textBlack
                                                              : Color(
                                                                  0xff929BA3)),
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 8.0,
                                                            left: 8.0),

                                                    child: SvgPicture.asset(
                                                        'assets/images/selected_calender.svg'),
                                                    // ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Row(
                                            children: [
                                              Checkbox(
                                                  visualDensity:
                                                      const VisualDensity(
                                                    horizontal: VisualDensity
                                                        .minimumDensity,
                                                  ),
                                                  materialTapTargetSize:
                                                      MaterialTapTargetSize
                                                          .shrinkWrap,
                                                  value: isclicked,
                                                  onChanged: (bool) {
                                                    setState(() {
                                                      isclicked = bool;
                                                    });
                                                  }),
                                              Text(
                                                  "  ${tr('currently_work_here')}",
                                                  style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: context
                                                          .appColors.grey3))
                                            ],
                                          ),
                                          SizedBox(
                                            height: 6,
                                          ),
                                          Text(
                                            'end_date',
                                            style: Styles.regular(
                                                size: 14,
                                                color: context.appColors
                                                    .headingPrimaryColor),
                                          ).tr(),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          InkWell(
                                            onTap: () {
                                              if (isclicked == true) return;
                                              if (startDate!.value.text == '') {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                              'Plz_start_date_first')
                                                          .tr()),
                                                );
                                              }
                                              try {
                                                selectDate(context, endDate!,
                                                    startDate: selectedDate);
                                              } catch (e) {
                                                endDate =
                                                    TextEditingController();
                                                selectDate(context, endDate!,
                                                    startDate: selectedDate);
                                              }
                                            },
                                            child: Container(
                                              width: width(context),
                                              height: height(context) * 0.07,
                                              decoration: BoxDecoration(
                                                color:
                                                    context.appColors.surface,
                                                border: Border.all(
                                                    width: 1.0,
                                                    color: const Color.fromARGB(
                                                        255, 142, 142, 142)),
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(10.0)),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: Text(
                                                      endDate!.value.text !=
                                                                  '' &&
                                                              isclicked == false
                                                          ? endDate!.value.text
                                                          : tr('select_date'),
                                                      style: TextStyle(
                                                          fontSize: 14,
                                                          fontWeight: FontWeight
                                                              .w400,
                                                          color: endDate!.value
                                                                      .text !=
                                                                  ''
                                                              ? context
                                                                  .appColors
                                                                  .textBlack
                                                              : Color(
                                                                  0xff929BA3)),
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 8.0,
                                                            left: 8.0),

                                                    child: SvgPicture.asset(
                                                        'assets/images/selected_calender.svg'),
                                                    // ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Text(
                                              "${tr('description')}*",
                                              style: Styles.regular(
                                                  size: 14,
                                                  color: context.appColors
                                                      .headingPrimaryColor),
                                            ),
                                          ),
                                          CustomTextField(
                                            controller: descController,
                                            maxLine: 6,
                                            validate: true,
                                            maxChar: 500,
                                            validationString:
                                                tr('write_your_post'),
                                            hintText: tr(
                                                'activity_description_placeholder'),
                                          ),
                                          SizedBox(
                                            height: 20,
                                          ),
                                          Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8),
                                              child: Text(
                                                  '${tr('featured_image')}*',
                                                  style: Styles.regular(
                                                      size: 14,
                                                      color: context.appColors
                                                          .headingPrimaryColor))),
                                          Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: InkWell(
                                                  onTap: () async {
                                                    FilePickerResult? result;

                                                    if (Platform.isIOS) {
                                                      result = await FilePicker
                                                          .platform
                                                          .pickFiles(
                                                        allowMultiple: false,
                                                        type: FileType.image,
                                                      );
                                                      if (result != null) {
                                                        setState(() {
                                                          uploadCerti = File(
                                                              result!.files
                                                                  .first.path!);
                                                        });
                                                      }
                                                    } else {
                                                      final pickedFileC =
                                                          await ImagePicker()
                                                              .pickImage(
                                                        source:
                                                            ImageSource.gallery,
                                                        imageQuality: 100,
                                                      );
                                                      if (pickedFileC != null) {
                                                        setState(() {
                                                          uploadCerti = File(
                                                              pickedFileC.path);
                                                        });
                                                      }
                                                    }
                                                    // final picker = ImagePicker();
                                                    // final pickedFileC =
                                                    //     await ImagePicker().pickImage(
                                                    //   source: ImageSource.gallery,
                                                    //   imageQuality: 100,
                                                    // );
                                                    // if (pickedFileC != null) {
                                                    //   setState(() {

                                                    //     uploadCerti = File(pickedFileC.path);
                                                    //   });
                                                    // } else if (Platform.isAndroid) {
                                                    //   final LostData response =
                                                    //       await picker.getLostData();
                                                    // }
                                                  },
                                                  child: Row(children: [
                                                    ShaderMask(
                                                        blendMode:
                                                            BlendMode.srcIn,
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                                context
                                                                    .appColors
                                                                    .gradientLeft,
                                                                context
                                                                    .appColors
                                                                    .gradientRight
                                                              ]).createShader(
                                                              bounds);
                                                        },
                                                        child: Row(
                                                          children: [
                                                            SvgPicture.asset(
                                                                'assets/images/upload_icon.svg'),
                                                            Text(
                                                              "${tr('upload_image')}  ",
                                                              style: Styles
                                                                  .getBoldThemeStyle(
                                                                      context,
                                                                      size: 12),
                                                            ),
                                                          ],
                                                        )),
                                                    Text(
                                                      uploadCerti != null
                                                          ? ' ${uploadCerti?.path.split('/').last}'
                                                          : widget.experience
                                                                  ?.imageName ??
                                                              " ${tr('upload_only_image')}",
                                                      style: Styles.regular(
                                                          size: 10,
                                                          color: context
                                                              .appColors
                                                              .headingText),
                                                    ),
                                                  ]))),
                                          SizedBox(
                                            width: 4,
                                          ),
                                          PortfolioCustomButton(
                                            clickAction: () async {
                                              if (!_formKey.currentState!
                                                  .validate()) {
                                                return;
                                              }

                                              if (employmentType == "") {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(tr(
                                                          'plz_choose_emp_type'))),
                                                );
                                                return;
                                              } else if (startDate
                                                      ?.value.text ==
                                                  '') {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(tr(
                                                          'please_choose_start_date'))),
                                                );
                                                return;
                                              }

                                              if (isclicked == false) {
                                                try {
                                                  DateTime startD = DateFormat(
                                                          "yyyy-MM-dd")
                                                      .parse(
                                                          '${startDate?.value.text}');
                                                  DateTime endD = DateFormat(
                                                          "yyyy-MM-dd")
                                                      .parse(
                                                          '${endDate?.value.text}');

                                                  if (startD.isAfter(endD) ||
                                                      startD == endD) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(SnackBar(
                                                            content: Text(
                                                                    'end_date_must_lg_then_start')
                                                                .tr()));
                                                    return;
                                                  }
                                                } catch (e) {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(SnackBar(
                                                          content: Text(
                                                                  'please_choose_end_date')
                                                              .tr()));
                                                  return;
                                                }
                                              }
                                              if (_formKey.currentState!
                                                  .validate()) {
                                                Map<String, dynamic> data = {};

                                                setState(() {
                                                  isAddExperienceLoading = true;
                                                });

                                                try {
                                                  if (widget.isEditMode ==
                                                      true) {
                                                    if (uploadCerti?.path !=
                                                        null) {
                                                      String? fileName =
                                                          uploadCerti?.path
                                                              .split('/')
                                                              .last;

                                                      data['certificate'] =
                                                          await MultipartFile
                                                              .fromFile(
                                                                  '${uploadCerti?.path}',
                                                                  filename:
                                                                      fileName);
                                                      await Utility()
                                                          .s3UploadFile(
                                                              '${uploadCerti?.path}')
                                                          .then((value) => data[
                                                              'certificate_cdn']);
                                                    }
                                                  } else {
                                                    String? fileName =
                                                        uploadCerti?.path
                                                            .split('/')
                                                            .last;

                                                    data['certificate'] =
                                                        await MultipartFile
                                                            .fromFile(
                                                                '${uploadCerti?.path}',
                                                                filename:
                                                                    fileName);
                                                    await Utility()
                                                        .s3UploadFile(
                                                            '${uploadCerti?.path}')
                                                        .then((value) => data[
                                                            'certificate_cdn']);
                                                  }
                                                  data["activity_type"] =
                                                      'Experience';
                                                  data["title"] =
                                                      titleController
                                                          .value.text;
                                                  data["description"] =
                                                      descController.value.text;
                                                  data["start_date"] =
                                                      startDate?.value.text;
                                                  data["end_date"] =
                                                      endDate?.value.text;
                                                  data["institute"] =
                                                      nameController.value.text;
                                                  data[
                                                      "professional_key"] = widget
                                                              .isEditMode ==
                                                          true
                                                      ? "experience_${widget.experience?.id}"
                                                      : 'new_professional';
                                                  data["edit_url_professional"] =
                                                      widget.isEditMode == true
                                                          ? '${widget.experience?.imageName}'
                                                          : '';
                                                  data['employment_type'] =
                                                      employmentType;
                                                  data['currently_work_here'] =
                                                      isclicked == true
                                                          ? 'on'
                                                          : null;

                                                  addExperience(data);
                                                } catch (e) {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                        content: Text(
                                                                'plz_add_feature_img')
                                                            .tr()),
                                                  );
                                                  setState(() {
                                                    isAddExperienceLoading =
                                                        false;
                                                  });

                                                  return;
                                                }
                                              }
                                            },
                                          )
                                        ]),
                                  ))
                            ]),
                          )),
                        ))))));
  }

  void addExperience(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddActivitiesEvent(data: data));
  }

  void handleAddExperience(AddActivitiesState state) {
    var addExperienceState = state;
    setState(() {
      switch (addExperienceState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add Experience....................");
          isAddExperienceLoading = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add Experience....................");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(widget.isEditMode == true
                        ? 'experience_updated_successfully'
                        : 'experience_added_successfully')
                    .tr()),
          );
          isAddExperienceLoading = false;

          Navigator.pop(context);
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Experience....................");
          isAddExperienceLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'add_education', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> selectDate(
      BuildContext context, TextEditingController controller,
      {DateTime? startDate}) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate, // Refer step 1
      firstDate: startDate ?? DateTime(1900),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        controller.text =
            Utility.convertDateFormat(selectedDate, format: 'yyyy-MM-dd');
      });
    }
  }
}
