import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_skill.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:page_transition/page_transition.dart';

class SkillList extends StatefulWidget {
  final List<CommonProfession>? skills;

  final String baseUrl;
  const SkillList({super.key, this.skills, required this.baseUrl});

  @override
  State<SkillList> createState() => _SkillListState();
}

class _SkillListState extends State<SkillList> {
  List<CommonProfession>? skills;
  bool deleteSkills = false;

  @override
  void initState() {
    skills = widget.skills;
    debugPrint('skill length is ${skills?.length}');
    // education?.sort((a, b) => b.endDate.compareTo(a.endDate));
    super.initState();
  }

  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(
              title:
                  Text("skills", style: Styles.getBoldThemeStyle(context)).tr(),
              elevation: 0,
              backgroundColor: context.appColors.surface,
              leading: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: context.appColors.headingText,
                  )),
              actions: [
                IconButton(
                    onPressed: () async {
                      await Navigator.push(
                              context,
                              PageTransition(
                                  duration: Duration(milliseconds: 350),
                                  reverseDuration: Duration(milliseconds: 350),
                                  type: PageTransitionType.bottomToTop,
                                  child: AddSkill()))
                          .then((value) => updatePortfolioList());
                    },
                    icon: Icon(
                      Icons.add,
                      color: context.appColors.headingText,
                    )),
              ],
            ),
            body: ScreenWithLoader(
                isLoading: deleteSkills,
                body: BlocListener<HomeBloc, HomeState>(
                    listener: (context, state) async {
                      if (state is SingularisDeletePortfolioState) {
                        handleDeletePortfolio(state);
                      }
                      if (state is PortfolioState) {
                        handlePortfolioState(state);
                      }
                    },
                    child: SizedBox(
                        height: height(context) * 1,
                        width: width(context),
                        child: ListView.builder(
                            itemCount: skills?.length,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20)),
                                      context: context,
                                      enableDrag: true,
                                      isScrollControlled: true,
                                      builder: (context) {
                                        return FractionallySizedBox(
                                          heightFactor: 0.9,
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                "${widget.baseUrl}${skills?[index].imageName}",
                                            width: double.infinity,
                                            height: double.infinity,
                                            errorWidget: (context, url, data) =>
                                                Image.asset(
                                              "assets/images/certificate_dummy.png",
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        );
                                      });
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 14, horizontal: 10),
                                  child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            child: SizedBox(
                                                width: width(context),
                                                height: width(context) * 0.45,
                                                child: CachedNetworkImage(
                                                  width: width(context) * 0.7,
                                                  height: width(context) * 0.45,
                                                  imageUrl:
                                                      "${widget.baseUrl}${skills?[index].imageName}",
                                                  fit: BoxFit.cover,
                                                  errorWidget:
                                                      (context, url, data) =>
                                                          Image.asset(
                                                    "assets/images/certificate_dummy.png",
                                                    fit: BoxFit.cover,
                                                  ),
                                                ))),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              width: width(context) * 0.75,
                                              child: Text(
                                                "${skills?[index].title}",
                                                maxLines: 2,
                                                style: Styles.getBoldThemeStyle(
                                                    context),
                                              ),
                                            ),
                                            Spacer(),
                                            InkWell(
                                                onTap: () async {
                                                  await Navigator.push(
                                                          context,
                                                          PageTransition(
                                                              duration: Duration(
                                                                  milliseconds:
                                                                      350),
                                                              reverseDuration:
                                                                  Duration(
                                                                      milliseconds:
                                                                          350),
                                                              type: PageTransitionType
                                                                  .bottomToTop,
                                                              child:
                                                                  AddSkill()))
                                                      .then((value) =>
                                                          updatePortfolioList());
                                                },
                                                child: SizedBox(
                                                  height: 20,
                                                  width: 20,
                                                  child: SvgPicture.asset(
                                                      'assets/images/edit_portfolio.svg',
                                                      colorFilter: context
                                                              .isDarkMode
                                                          ? ColorFilter.mode(
                                                              context.appColors
                                                                  .primaryForeground,
                                                              BlendMode.srcIn)
                                                          : null),
                                                )),
                                            SizedBox(
                                              width: 22,
                                            ),
                                            InkWell(
                                                onTap: () {
                                                  AlertsWidget.showCustomDialog(
                                                      context: context,
                                                      title: '',
                                                      text: tr(
                                                          'confirm_deletion_textone'),
                                                      icon:
                                                          'assets/images/circle_alert_fill.svg',
                                                      onOkClick: () async {
                                                        deleteResume(
                                                            skills![index].id);
                                                      });
                                                },
                                                child: SvgPicture.asset(
                                                  'assets/images/delete.svg',
                                                  colorFilter: context
                                                          .isDarkMode
                                                      ? ColorFilter.mode(
                                                          context.appColors
                                                              .primaryForeground,
                                                          BlendMode.srcIn)
                                                      : null,
                                                )),
                                            SizedBox(
                                              width: 10,
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 6),
                                        Container(
                                          height: 6,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              1,
                                          decoration: BoxDecoration(
                                              color: context.appColors.grey,
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Stack(
                                            children: [
                                              Container(
                                                height: 10,
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    1 *
                                                    (int.parse(
                                                            '${skills?[index].percent}') /
                                                        100),
                                                decoration: BoxDecoration(
                                                    color:
                                                        context.appColors.green,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ]),
                                ),
                              );
                            })))));
      },
    );
  }

  void deleteResume(int id) {
    BlocProvider.of<HomeBloc>(context)
        .add(SingularisDeletePortfolioEvent(portfolioId: id));
  }

  void handleDeletePortfolio(SingularisDeletePortfolioState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Skills....................");
          deleteSkills = true;

          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add  Skills....................");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('skills_deleted_successfully').tr()),
          );
          updatePortfolioList();
          deleteSkills = false;
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Skills....................");
          deleteSkills = false;
          FirebaseAnalytics.instance.logEvent(name: 'my_skill', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void updatePortfolioList() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading....................");
          deleteSkills = true;
          setState(() {});

          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PortfolioState Success....................${portfolioState.response?.data.skill?.length}");
          skills = portfolioState.response?.data.skill;

          deleteSkills = false;

          setState(() {});
          break;

        case ApiStatus.ERROR:
          deleteSkills = false;
          setState(() {});

          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'my_skill', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
