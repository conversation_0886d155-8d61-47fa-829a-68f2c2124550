import 'package:flutter/material.dart';

class CircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    // final path = Path();
    // path.lineTo(0, size.height); // Start at the bottom-left corner
    // path.quadraticBezierTo(
    //     size.width , size.height, size.width, size.height); // Create a quadratic bezier curve
    // path.lineTo(size.width, 0); // Close the path to the top-right corner
    // return path;
    Path path_0 = Path();

    path_0.moveTo(size.width * 0.9710882, size.height * 0.6679645);
    path_0.cubicTo(size.width * 0.9898066, size.height * 0.6154684, size.width,
        size.height * 0.5589250, size.width, size.height * 0.5000000);
    path_0.cubicTo(size.width, size.height * 0.2238579, size.width * 0.7761421,
        0, size.width * 0.5000000, 0);
    path_0.cubicTo(size.width * 0.2238579, 0, 0, size.height * 0.2238579, 0,
        size.height * 0.5000000);
    path_0.cubicTo(0, size.height * 0.7761421, size.width * 0.2238579,
        size.height, size.width * 0.5000000, size.height);
    path_0.cubicTo(
        size.width * 0.5361355,
        size.height,
        size.width * 0.5713763,
        size.height * 0.9961671,
        size.width * 0.6053368,
        size.height * 0.9888842);
    path_0.cubicTo(
        size.width * 0.5644908,
        size.height * 0.9453461,
        size.width * 0.5394737,
        size.height * 0.8867789,
        size.width * 0.5394737,
        size.height * 0.8223684);
    path_0.cubicTo(
        size.width * 0.5394737,
        size.height * 0.6879303,
        size.width * 0.6484566,
        size.height * 0.5789474,
        size.width * 0.7828947,
        size.height * 0.5789474);
    path_0.cubicTo(
        size.width * 0.8587184,
        size.height * 0.5789474,
        size.width * 0.9264447,
        size.height * 0.6136158,
        size.width * 0.9710882,
        size.height * 0.6679645);
    path_0.close();

    return path_0;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

// import 'dart:ui' as ui;

// //Copy this CustomPainter code to the Bottom of the File
// class CircleClipper extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     Path path_0 = Path();
//     path_0.moveTo(size.width * 0.9975592, size.height * 0.5497355);
//     path_0.cubicTo(size.width * 0.9991711, size.height * 0.5333750, size.width,
//         size.height * 0.5167842, size.width, size.height * 0.5000000);
//     path_0.cubicTo(size.width, size.height * 0.2238579, size.width * 0.7761421,
//         0, size.width * 0.5000000, 0);
//     path_0.cubicTo(size.width * 0.2238579, 0, 0, size.height * 0.2238579, 0,
//         size.height * 0.5000000);
//     path_0.cubicTo(0, size.height * 0.7761421, size.width * 0.2238579,
//         size.height, size.width * 0.5000000, size.height);
//     path_0.cubicTo(
//         size.width * 0.5591013,
//         size.height,
//         size.width * 0.6158066,
//         size.height * 0.9897447,
//         size.width * 0.6684382,
//         size.height * 0.9709184);
//     path_0.cubicTo(
//         size.width * 0.6211961,
//         size.height * 0.9223342,
//         size.width * 0.5921053,
//         size.height * 0.8560118,
//         size.width * 0.5921053,
//         size.height * 0.7828947);
//     path_0.cubicTo(
//         size.width * 0.5921053,
//         size.height * 0.6339237,
//         size.width * 0.7128711,
//         size.height * 0.5131579,
//         size.width * 0.8618421,
//         size.height * 0.5131579);
//     path_0.cubicTo(
//         size.width * 0.9113250,
//         size.height * 0.5131579,
//         size.width * 0.9576934,
//         size.height * 0.5264816,
//         size.width * 0.9975592,
//         size.height * 0.5497355);
//     path_0.close();

//     Paint paint_0_fill = Paint()..style = PaintingStyle.fill;
//     paint_0_fill.color = Color(0xffD9D9D9).withValues(alpha:1.0);
//     canvas.drawPath(path_0, paint_0_fill);
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true;
//   }
// }
