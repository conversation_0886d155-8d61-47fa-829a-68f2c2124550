import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/assign_learner_response.dart';
import 'package:masterg/data/providers/training_content_provider.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';

import 'course_curriculum_module.dart';
// import 'dart:html';

double opacityLevel = 1.0;

bool showLoading = false;

class CourseCurriculum extends StatefulWidget {
  final int? programId;
  const CourseCurriculum({super.key, this.programId});

  @override
  State<CourseCurriculum> createState() => _CourseCurriculumState();
}

class _CourseCurriculumState extends State<CourseCurriculum> {
  late BuildContext mContext;

  TrainingContentProvier? currentTrainingProvider;
  AssignLearnerResponse? assignLearner;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    mContext = context;

    return OrientationBuilder(
        builder: (BuildContext context, Orientation orientation) {
      if (kIsWeb) orientation = Orientation.portrait;
      return Consumer<TrainingDetailProvider>(
          builder: (context, traininDetailProvider, child) {
        return traininDetailProvider.apiStatus == ApiStatus.LOADING
            ? Center(
                child: CircularProgressIndicator(),
              )
            : traininDetailProvider.program != null
                ? Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Container(
                          decoration: BoxDecoration(
                              color: context.appColors.surface,
                              borderRadius: BorderRadius.circular(0),
                              border: Border.all(
                                  color: context.appColors.grey, width: 2)),
                          child: CourseCurriculumModules(
                            module: traininDetailProvider.modules ?? [],
                            programId: widget.programId ?? 0,
                          ),
                        ),
                      ),
                    ],
                  )
                : Center(child: const Text('no_module_found').tr());
      });
    });
  }
}
