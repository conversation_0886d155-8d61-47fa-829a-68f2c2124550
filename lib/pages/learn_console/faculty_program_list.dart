import 'package:cached_network_image/cached_network_image.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/faculty_module_list_resp.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/program_completion_resp.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/learn_console/faculty_console_program_details.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

class FacultyProgramList extends StatefulWidget {
  final ProgramCompletionResponse? programCompletion;
  final AttendancePercentageResponse? attendancePercentage;
  final Function onExpension;
  final int index;
  const FacultyProgramList(
      {super.key,
      this.programCompletion,
      this.attendancePercentage,
      required this.onExpension,
      required this.index});

  @override
  State<FacultyProgramList> createState() => _FacultyProgramListState();
}

class _FacultyProgramListState extends State<FacultyProgramList> {
  FacultyModuleListResponse? facultyProgramList;
  ProgramCompletionResponse? programCompletion;
  AttendancePercentageResponse? attendancePercentage;
  bool isModuleExpanded = false;
  bool isLoading = false;
  int? selectedProgram;
  int? expandedIndex;
  int? selectedIndexFaculty;
  bool? isFacultyLead = false;
  var formatter = NumberFormat("#0.00");

  @override
  void initState() {
    getfacultyProgramList();
    super.initState();
  }

  // void _getFacultyCourseDetails({int? programId}) {
  //   BlocProvider.of<HomeBloc>(context)
  //       .add(FacultyCourseDetailsEvent(programId: programId));
  // }

  void _getProgramCompletion({dynamic programCompId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(ProgramCompletionEvent(programCompletionId: programCompId));
  }

  void _getAttendancePercentageEvent({int? programCompletionId}) {
    BlocProvider.of<HomeBloc>(context).add(
        AttendancePercentageEvent(programCompletionId: programCompletionId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is FacultyModuleListState) {
                    _handleFacultyModuleListState(state);
                  }
                  if (state is ProgramCompletionState) {
                    _handleProgramCompletionState(state);
                  }
                  if (state is AttendancePercentageState) {
                    _handleAttendancePercentageState(state);
                  }
                },
                child: Scaffold(
                  appBar: AppBar(
                    elevation: 0,
                    backgroundColor: context.appColors.surface,
                    leading: BackButton(color: context.appColors.textBlack),
                    title: Text('faculty_console',
                            style: Styles.getBoldThemeStyle(context, size: 16))
                        .tr(),
                  ),
                  body: ScreenWithLoader(
                    isLoading: isLoading,
                    body: Container(
                        margin: EdgeInsets.symmetric(horizontal: 8.0),
                        height: height(context),
                        width: width(context),
                        child: facultyProgramList != null
                            ? SingleChildScrollView(
                                child: Column(
                                  children: [
                                    Container(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.05,
                                      // width: MediaQuery.of(context).size.height *
                                      //     0.2,
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 8),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4, vertical: 8),
                                      decoration: ShapeDecoration(
                                        color: context.appColors.surface,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        shadows: const [],
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton2<int>(
                                          isExpanded: true,
                                          hint: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  'Select Course',
                                                  style: Styles
                                                      .getRegularThemeStyle(
                                                          context,
                                                          size: 14),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                          items: facultyProgramList?.data
                                                          ?.facultyCourses ==
                                                      null &&
                                                  facultyProgramList
                                                          ?.data
                                                          ?.facultyCourses
                                                          ?.isEmpty ==
                                                      true
                                              ? []
                                              : facultyProgramList
                                                      ?.data?.facultyCourses
                                                      ?.map((value) {
                                                    return DropdownMenuItem<
                                                        int>(
                                                      value: value.courseId,
                                                      child: SizedBox(
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.7,
                                                        child: Text(
                                                            value.courseName!,
                                                            maxLines: 2,
                                                            style: TextStyle(
                                                                color: context
                                                                    .appColors
                                                                    .textBlack,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500)),
                                                      ),
                                                    );
                                                  }).toList() ??
                                                  [],
                                          value: selectedProgram,
                                          onChanged: (int? value) {
                                            setState(() {
                                              if (value != null) {
                                                getfacultyProgramList(
                                                    courseId: value);
                                                selectedProgram = value;
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: facultyProgramList
                                              ?.data?.programs?.length ??
                                          0,
                                      physics: BouncingScrollPhysics(),
                                      scrollDirection: Axis.vertical,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        // bool isModuleExpanded =
                                        //     expandedIndex == index;

                                        return Stack(children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(
                                                      FacultyProgramDetailsPage(
                                                        image:
                                                            '${facultyProgramList?.data?.programs?[index].image}',
                                                        programName:
                                                            facultyProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .name,
                                                        description:
                                                            facultyProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .description,
                                                        startDate:
                                                            '${facultyProgramList?.data?.programs?[index].startDate}',
                                                        endDate:
                                                            '${facultyProgramList?.data?.programs?[index].endDate}',
                                                        // facultyNames:
                                                        //     '${facultyProgramList?.data?.programs?[index].facultyNames}',
                                                        totLearners:
                                                            '${facultyProgramList?.data?.programs?[index].totLearners}',
                                                        programId:
                                                            facultyProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .id,
                                                        status:
                                                            '${facultyProgramList?.data?.programs?[index].status}',
                                                        category:
                                                            '${facultyProgramList?.data?.programs?[index].createdBy}',
                                                      ),
                                                      isMaintainState: true));
                                            },
                                            child: Container(
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height *
                                                  0.3,
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 10, vertical: 10),
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.95,
                                              decoration: BoxDecoration(
                                                  color:
                                                      context.appColors.surface,
                                                  borderRadius:
                                                      BorderRadius.circular(6)),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            8.0),
                                                    child: Row(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        SizedBox(
                                                          width: 85,
                                                          height: 90,
                                                          child: ClipRRect(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                            child:
                                                                CachedNetworkImage(
                                                              imageUrl: facultyProgramList
                                                                      ?.data
                                                                      ?.programs?[
                                                                          index]
                                                                      .image ??
                                                                  '',
                                                              width: 100,
                                                              height: 120,
                                                              errorWidget: (context,
                                                                      url,
                                                                      error) =>
                                                                  SvgPicture
                                                                      .asset(
                                                                'assets/images/gscore_postnow_bg.svg',
                                                              ),
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        ),
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Container(
                                                                    width: width(
                                                                            context) *
                                                                        0.34,
                                                                    margin: EdgeInsets.only(
                                                                        left: 9,
                                                                        top: 3),
                                                                    child: Text(
                                                                        facultyProgramList?.data?.programs?[index].name ??
                                                                            '',
                                                                        maxLines:
                                                                            2,
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        softWrap:
                                                                            false,
                                                                        style: Styles.semibold(
                                                                            size:
                                                                                16))),
                                                                // SizedBox(width: 80),
                                                                facultyProgramList
                                                                            ?.data
                                                                            ?.programs?[
                                                                                index]
                                                                            .batchName !=
                                                                        null
                                                                    ? Container(
                                                                        width: width(context) *
                                                                            0.25,
                                                                        decoration: BoxDecoration(
                                                                            color: Color(
                                                                                0xffF0F1FA),
                                                                            borderRadius: BorderRadius.circular(
                                                                                10),
                                                                            border: Border.all(
                                                                                color: Color(
                                                                                    0xffF0F1FA),
                                                                                width:
                                                                                    2)),
                                                                        margin: EdgeInsets.only(
                                                                            left:
                                                                                9,
                                                                            top:
                                                                                3),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsets
                                                                              .all(
                                                                              4.0),
                                                                          child:
                                                                              Center(
                                                                            child:
                                                                                ShaderMask(
                                                                              blendMode: BlendMode.srcIn,
                                                                              shaderCallback: (Rect bounds) {
                                                                                return LinearGradient(begin: Alignment.centerLeft, end: Alignment.centerRight, colors: <Color>[
                                                                                  context.appColors.gradientLeft,
                                                                                  context.appColors.gradientRight
                                                                                ]).createShader(bounds);
                                                                              },
                                                                              child: Text(facultyProgramList?.data?.programs?[index].batchName ?? '',
                                                                                  maxLines: 1,
                                                                                  // overflow:
                                                                                  //     TextOverflow
                                                                                  //         .ellipsis,
                                                                                  softWrap: false,
                                                                                  style: Styles.semibold(size: 12, color: Color(0xff4F5AED))),
                                                                            ),
                                                                          ),
                                                                        ))
                                                                    : SizedBox(),
                                                              ],
                                                            ),
                                                            Container(
                                                                width: width(
                                                                        context) *
                                                                    0.5,
                                                                margin: EdgeInsets
                                                                    .only(
                                                                        left: 9,
                                                                        top: 3),
                                                                child: Text(
                                                                    facultyProgramList
                                                                            ?.data
                                                                            ?.programs?[
                                                                                index]
                                                                            .description ??
                                                                        '',
                                                                    maxLines: 2,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                    softWrap:
                                                                        false,
                                                                    style: Styles
                                                                        .regular(
                                                                            size:
                                                                                12))),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Container(
                                                      width:
                                                          width(context) * 0.6,
                                                      margin: EdgeInsets.only(
                                                          left: 9, top: 3),
                                                      child: Row(
                                                        children: [
                                                          Icon(
                                                              Icons
                                                                  .calendar_month_outlined,
                                                              size: 20),
                                                          SizedBox(width: 10),
                                                          SizedBox(
                                                            child: Text(
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              softWrap: false,
                                                              '${Utility.convertDateFromMillis(int.parse('${facultyProgramList?.data?.programs?[index].startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} To  ${Utility.convertDateFromMillis(int.parse('${facultyProgramList?.data?.programs?[index].endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color: context
                                                                      .appColors
                                                                      .headingText),
                                                              textDirection: ui
                                                                  .TextDirection
                                                                  .ltr,
                                                            ),
                                                          ),
                                                        ],
                                                      )),
                                                  Container(
                                                    // width: width(context),
                                                    margin: EdgeInsets.only(
                                                        left: 9,
                                                        top: 3,
                                                        right: 9),
                                                    child: Row(
                                                      children: [
                                                        if (facultyProgramList
                                                                ?.data
                                                                ?.programs?[
                                                                    index]
                                                                .organizedBy !=
                                                            null) ...[
                                                          SvgPicture.asset(
                                                            'assets/images/person.svg',
                                                            height: 20.0,
                                                            width: 20.0,
                                                            allowDrawingOutsideViewBox:
                                                                true,
                                                          ),
                                                          SizedBox(width: 10),
                                                          Text(
                                                              '${facultyProgramList?.data?.programs?[index].organizedBy ?? ''}',
                                                              maxLines: 2,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              softWrap: false,
                                                              style: Styles
                                                                  .regular(
                                                                      size:
                                                                          12)),
                                                        ],
                                                        Spacer(),
                                                        Row(
                                                          children: [
                                                            SvgPicture.asset(
                                                              'assets/images/local_library.svg',
                                                              height: 20.0,
                                                              width: 20.0,
                                                              allowDrawingOutsideViewBox:
                                                                  true,
                                                            ),
                                                            SizedBox(width: 10),
                                                            Text(
                                                                '${facultyProgramList?.data?.programs?[index].totLearners ?? ''}',
                                                                maxLines: 2,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                softWrap: false,
                                                                style: Styles
                                                                    .regular(
                                                                        size:
                                                                            12)),
                                                          ],
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                                height: selectedIndexFaculty ==
                                                            index &&
                                                        isFacultyLead == true
                                                    ? 130
                                                    : 60, //isModuleExpanded == true
                                                //height: 130,
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 8),
                                                decoration: BoxDecoration(
                                                    color: context
                                                        .appColors.lightBlueBg,
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                10)),
                                                    border: Border.all(
                                                        color: context.appColors
                                                            .lightBlueBg)),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        attendancePercentage
                                                            ?.attendanceCompletion = 0;
                                                        if (selectedIndexFaculty !=
                                                            index) {
                                                          _getProgramCompletion(
                                                              programCompId:
                                                                  facultyProgramList
                                                                      ?.data
                                                                      ?.programs?[
                                                                          index]
                                                                      .id);
                                                          _getAttendancePercentageEvent(
                                                              programCompletionId:
                                                                  facultyProgramList
                                                                      ?.data
                                                                      ?.programs?[
                                                                          index]
                                                                      .id);

                                                          setState(() {
                                                            selectedIndexFaculty =
                                                                index;
                                                            isFacultyLead =
                                                                true;
                                                          });
                                                        } else {
                                                          setState(() {
                                                            selectedIndexFaculty =
                                                                null;
                                                            isFacultyLead =
                                                                false;
                                                          });
                                                        }
                                                      },
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                top: 15.0),
                                                        child: Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      left:
                                                                          16.0,
                                                                      top: 0.0,
                                                                      bottom:
                                                                          0.0),
                                                              child: Text(
                                                                'module_completion',
                                                                style: Styles
                                                                    .getBoldThemeStyle(
                                                                        context),
                                                              ).tr(),
                                                            ),
                                                            Spacer(),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      right:
                                                                          10.0),
                                                              child: selectedIndexFaculty ==
                                                                          index &&
                                                                      isFacultyLead ==
                                                                          true
                                                                  ? Icon(
                                                                      Icons
                                                                          .keyboard_arrow_up,
                                                                      size: 20,
                                                                    )
                                                                  : Icon(
                                                                      Icons
                                                                          .keyboard_arrow_down,
                                                                      size: 20,
                                                                    ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                    selectedIndexFaculty ==
                                                                index &&
                                                            isFacultyLead ==
                                                                true
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        16.0),
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  '${tr('program_completion')} ${formatter.format(programCompletion?.programCompletion ?? 0.0 ?? 0)}%',
                                                                  style: Styles
                                                                      .getRegularThemeStyle(
                                                                          context),
                                                                ).tr(),
                                                                SizedBox(
                                                                    height: 5),
                                                                ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius.all(
                                                                          Radius.circular(
                                                                              30)),
                                                                  child:
                                                                      LinearProgressIndicator(
                                                                    minHeight:
                                                                        6,
                                                                    value: (programCompletion?.programCompletion ??
                                                                            0) /
                                                                        100,
                                                                    backgroundColor:
                                                                        Colors.grey[
                                                                            300],
                                                                    valueColor:
                                                                        AlwaysStoppedAnimation<
                                                                            Color>(
                                                                      context
                                                                          .appColors
                                                                          .gradientLeft,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )
                                                        : SizedBox(),
                                                    selectedIndexFaculty ==
                                                                index &&
                                                            isFacultyLead ==
                                                                true
                                                        ? SizedBox(height: 10)
                                                        : SizedBox(),
                                                    selectedIndexFaculty ==
                                                                index &&
                                                            isFacultyLead ==
                                                                true
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        16.0),
                                                            child: Column(
                                                              children: [
                                                                Row(
                                                                  children: [
                                                                    Text(
                                                                      '${tr('attendance_percentage')} ${formatter.format(attendancePercentage?.attendanceCompletion ?? 0.0)}%',
                                                                      style: Styles
                                                                          .getRegularThemeStyle(
                                                                              context),
                                                                    ).tr()
                                                                  ],
                                                                ),
                                                                SizedBox(
                                                                    height: 5),
                                                                ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius.all(
                                                                          Radius.circular(
                                                                              30)),
                                                                  child:
                                                                      LinearProgressIndicator(
                                                                    minHeight:
                                                                        6,
                                                                    value: (attendancePercentage?.attendanceCompletion ??
                                                                            0) /
                                                                        100,
                                                                    backgroundColor:
                                                                        Colors.grey[
                                                                            300],
                                                                    valueColor:
                                                                        AlwaysStoppedAnimation<
                                                                            Color>(
                                                                      context
                                                                          .appColors
                                                                          .gradientLeft,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )
                                                        : SizedBox(),
                                                  ],
                                                )
                                                //
                                                ),
                                          ),

                                          /*Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                              height: isModuleExpanded == true
                                                  ? 160
                                                  : 62,
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              decoration: BoxDecoration(
                                                  color: context.appColors.lightBlueBg,
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(10)),
                                                  border: Border.all(
                                                      color:
                                                          context.appColors.lightBlueBg)),
                                              child: AnimatedContainer(
                                                  duration: Duration(
                                                      milliseconds: 300),
                                                  // height: isModuleExpanded == true ? 100 : 60,
                                                  margin: EdgeInsets.symmetric(
                                                      horizontal: 8),
                                                  decoration: BoxDecoration(
                                                    color: context.appColors.lightBlueBg,
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                10)),
                                                    border: Border.all(
                                                        color:
                                                            context.appColors.lightBlueBg),
                                                  ),
                                                  child: ExpansionTile(
                                                    maintainState: false,
                                                    key: Key(index.toString()),
                                                    collapsedTextColor:
                                                        context.appColors.textBlack,
                                                    initiallyExpanded: false,
                                                    title: Text(
                                                      'module_completion',
                                                      style: Styles.getBoldThemeStyle(context),
                                                    ).tr(),
                                                    children: [
                                                      SingleChildScrollView(
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                      .symmetric(
                                                                  horizontal:
                                                                      16.0),
                                                          child: Column(
                                                            children: [
                                                              Row(
                                                                children: [
                                                                  Text(
                                                                    '${tr('program_completion')}-${programCompletion?.programCompletion.toStringAsFixed(2) ?? 0.0}%',
                                                                    // 'program_completion - ${programCompletion?.programCompletion ?? 0}%',
                                                                    style: Styles
                                                                       .getRegularThemeStyle(context),
                                                                  )
                                                                ],
                                                              ),
                                                              SizedBox(
                                                                  height: 5),
                                                              ClipRRect(
                                                                borderRadius: BorderRadius
                                                                    .all(Radius
                                                                        .circular(
                                                                            30)),
                                                                child:
                                                                    LinearProgressIndicator(
                                                                  minHeight: 6,
                                                                  value: (programCompletion?.programCompletion ?? 0) / 100,
                                                                  backgroundColor:
                                                                      Colors.grey[
                                                                          300],
                                                                  valueColor:
                                                                      AlwaysStoppedAnimation<
                                                                          Color>(
                                                                    context.appColors
                                                                        .gradientLeft,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(height: 10),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                    .symmetric(
                                                                horizontal:
                                                                    16.0),
                                                        child: Column(
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Text(
                                                                  '${tr('attendance_percentage')}-${attendancePercentage?.attendanceCompletion?.toStringAsFixed(2) ?? 0.0}%',
                                                                  style: Styles
                                                                     .getRegularThemeStyle(context),
                                                                ).tr()
                                                              ],
                                                            ),
                                                            SizedBox(height: 5),
                                                            ClipRRect(
                                                              borderRadius: BorderRadius
                                                                  .all(Radius
                                                                      .circular(
                                                                          30)),
                                                              child:
                                                                  LinearProgressIndicator(
                                                                minHeight: 6,
                                                                value: (attendancePercentage?.attendanceCompletion ?? 0) / 100,
                                                                backgroundColor:
                                                                    Colors.grey[
                                                                        300],
                                                                valueColor:
                                                                    AlwaysStoppedAnimation<
                                                                        Color>(
                                                                  context.appColors
                                                                      .gradientLeft,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                    // onExpansionChanged:
                                                    //     ((newState) {
                                                    //   if (isModuleExpanded ==
                                                    //       false) {
                                                    //     widget.onExpension();
                                                    //     setState(() {
                                                    //       isModuleExpanded =
                                                    //           true;
                                                    //     });
                                                    //   } else {
                                                    //     setState(() {
                                                    //       isModuleExpanded =
                                                    //           false;
                                                    //     });
                                                    //   }
                                                    // })),
                                                    onExpansionChanged: (bool expanded) {
                                                      setState(() {
                                                        if (expanded) {
                                                          expandedIndex = index;
                                                          attendancePercentage = null;
                                                          programCompletion = null;
                                                          _getProgramCompletion(programCompId: facultyProgramList?.data?.programs?[index].id);
                                                          _getAttendancePercentageEvent(programCompletionId: facultyProgramList?.data?.programs?[index].id);

                                                        } else if (expandedIndex == index) {
                                                          expandedIndex = null;
                                                        }
                                                      });
                                                    },
                                                  )),
                                            ),
                                          ),*/
                                        ]);
                                      },
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox()),
                  ),
                ))));
  }

  void _handleFacultyModuleListState(FacultyModuleListState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................FacultyModuleListState");
            isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................FacultyModuleListState");
            facultyProgramList = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................FacultyModuleListState");
            isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleProgramCompletionState(ProgramCompletionState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ProgramCompletionState");
            // isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ProgramCompletionState");
            programCompletion = state.response;

            // isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................ProgramCompletionState");
            // isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleAttendancePercentageState(AttendancePercentageState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................AttendancePercentageState");
            // isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................AttendancePercentageState");
            attendancePercentage = state.response;

            isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error..........................AttendancePercentageState");
            // isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        isLoading = false;
      });
    }
  }

  void getfacultyProgramList({int? courseId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(FacultyModuleListEvent(courseId: courseId));
  }
}
