import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/explore_job/explore_job_proivder.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';

import '../../../utils/styles.dart';

enum SelectedOption { none, country, experience, matching_level }

const selectionMap = {
  SelectedOption.country: 'country',
  SelectedOption.experience: 'experience',
  // SelectedOption.matching_level: 'matching_level',
};

class FilterPop extends StatefulWidget {
  const FilterPop({super.key});

  @override
  State<FilterPop> createState() => _FilterPopState();
}

class _FilterPopState extends State<FilterPop> {
  SelectedOption currSelection = SelectedOption.none;

  @override
  Widget build(BuildContext context) {
    ExploreJobProvider jobProvider = Provider.of<ExploreJobProvider>(context);

    return Container(
        padding: const EdgeInsets.all(10),
        child: () {
          switch (currSelection) {
            case SelectedOption.none:
              return ListView(
                shrinkWrap: true,
                // crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text('filter_by').tr(),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          jobProvider.resetFilter();
                        },
                        child: Text(
                          'reset_button',
                          style: Styles.regular(
                            color: context.appColors.primary,
                          ),
                        ).tr(),
                      ),
                    ],
                  ),
                  Center(
                    child: Divider(),
                  ),
                  Options(
                    onClick: () {
                      setState(() {
                        currSelection = SelectedOption.country;
                      });
                    },
                    text: '${selectionMap[SelectedOption.country]}',
                    selectedString:
                        jobProvider.countryFilterListApplied.isNotEmpty
                            ? jobProvider.countryFilterListApplied
                                .toString()
                                .replaceAll('[', '')
                                .replaceAll(']', '')
                            : 'any',
                  ),
                  Options(
                    onClick: () {
                      setState(() {
                        currSelection = SelectedOption.experience;
                      });
                    },
                    text: '${selectionMap[SelectedOption.experience]}',
                    selectedString:
                        jobProvider.experienceFilterListApplied.isNotEmpty
                            ? jobProvider.experienceFilterListApplied
                                .toString()
                                .replaceAll('[', '')
                                .replaceAll(']', '')
                            : 'any',
                  ),
                  // Options(
                  //   onClick: () {
                  //     setState(() {
                  //       currSelection = SelectedOption.matching_level;
                  //     });
                  //   },
                  //   text: '${selectionMap[SelectedOption.matching_level]}',
                  //   selectedString:
                  //       jobProvider.matchingFilterListApplied.length != 0
                  //           ? jobProvider.matchingFilterListApplied
                  //               .toString()
                  //               .replaceAll('[', '')
                  //               .replaceAll(']', '')
                  //           : 'any',
                  // ),
                  // Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(vertical: 22),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                            color: context.appColors.primary,
                            borderRadius: BorderRadius.circular(8)),
                        child: Center(
                          child: Text(
                            'show_results',
                            style: Styles.boldWhite(),
                          ).tr(),
                        )),
                  )
                ],
              );

            default:
              return FilterLayout(
                goBack: () {
                  setState(() {
                    currSelection = SelectedOption.none;
                  });
                },
                selectedType: currSelection,
              );
          }
        }());
  }
}

class Options extends StatelessWidget {
  final String text;
  final String? selectedString;
  final Function onClick;

  const Options(
      {super.key,
      required this.text,
      this.selectedString,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: () {
          onClick();
        },
        child: Row(
          children: [
            Text(
              text,
              style: Styles.getSemiboldThemeStyle(context, size: 16),
            ).tr(),
            Spacer(),
            SizedBox(
              width: width(context) * 0.6,
              child: Text(
                '$selectedString',
                style: Styles.regular(
                  size: 14,
                  color: selectedString == 'any'
                      ? context.appColors.grey1
                      : context.appColors.primary,
                ),
                textAlign: TextAlign.right,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ).tr(),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
            )
          ],
        ),
      ),
    );
  }
}

class FilterLayout extends StatelessWidget {
  final SelectedOption selectedType;
  final Function goBack;

  const FilterLayout(
      {super.key, required this.selectedType, required this.goBack});

  @override
  Widget build(BuildContext context) {
    ExploreJobProvider jobProvider = Provider.of<ExploreJobProvider>(context);
    return Column(
      children: [
        Row(
          children: [
            InkWell(
                onTap: () {
                  goBack();
                },
                child: Icon(Icons.arrow_back_ios)),
            Text('${selectionMap[selectedType]}').tr(),
            Spacer(),
            InkWell(
              onTap: () {
                switch (selectedType) {
                  case SelectedOption.country:
                    jobProvider.resetAppliedCountryFilter();
                    break;
                  case SelectedOption.experience:
                    jobProvider.resetAppliedExperienceFilter();
                    break;
                  case SelectedOption.matching_level:
                    jobProvider.resetAppliedMatchingLevelFilter();
                    break;
                  default:
                }
              },
              child: Text(
                'reset_button',
                style: Styles.regular(
                  color: context.appColors.primary,
                ),
              ).tr(),
            ),
          ],
        ),
        SizedBox(height: 10),
        SizedBox(
            width: double.infinity,
            child: Column(
              children: [
                if (selectedType == SelectedOption.country) ...[
                  ConstrainedBox(
                    constraints:
                        BoxConstraints(maxHeight: height(context) * 0.6),
                    child: SingleChildScrollView(
                      child: Wrap(
                        direction: Axis.horizontal,
                        children: () {
                          return List.generate(
                              jobProvider.countryFilterList.length, (index) {
                            bool isSelected = jobProvider
                                .countryFilterListApplied
                                .contains(jobProvider.countryFilterList[index]);
                            return Container(
                              margin: const EdgeInsets.all(2.0),
                              child: InputChip(
                                pressElevation: 0.0,
                                shape: isSelected
                                    ? RoundedRectangleBorder(
                                        side: BorderSide(
                                            color: context.appColors.primary,
                                            width: 1.0),
                                        borderRadius: BorderRadius.circular(
                                            40), // Adjust the border radius as needed
                                      )
                                    : null,
                                backgroundColor: isSelected
                                    ? context.appColors.lightBlueBg
                                    : Colors.transparent,
                                label: Text(
                                    '${jobProvider.countryFilterList[index]}'),
                                deleteIcon: isSelected
                                    ? Icon(
                                        Icons.close,
                                        color: context.appColors.textBlack,
                                        size: 16,
                                      )
                                    : SizedBox(),
                                onSelected: (bool _) {
                                  if (isSelected) {
                                    jobProvider.removeAppliedCountryFilter(
                                        country: jobProvider
                                            .countryFilterList[index]);
                                  } else {
                                    jobProvider.addAppliedCountryFilter(
                                        country: jobProvider
                                            .countryFilterList[index]);
                                  }
                                },
                                onDeleted: () {
                                  jobProvider.removeAppliedCountryFilter(
                                      country:
                                          jobProvider.countryFilterList[index]);
                                },
                              ),
                            );
                          });
                        }(),
                      ),
                    ),
                  ),
                ] else if (selectedType == SelectedOption.experience) ...[
                  ConstrainedBox(
                    constraints:
                        BoxConstraints(maxHeight: height(context) * 0.6),
                    child: SingleChildScrollView(
                      child: Wrap(
                        direction: Axis.horizontal,
                        children: () {
                          return List.generate(
                              jobProvider.experienceFilterList.length, (index) {
                            bool isSelected = jobProvider
                                .experienceFilterListApplied
                                .contains(
                                    jobProvider.experienceFilterList[index]);
                            return Container(
                              margin: const EdgeInsets.all(2.0),
                              child: InputChip(
                                pressElevation: 0.0,
                                shape: isSelected
                                    ? RoundedRectangleBorder(
                                        side: BorderSide(
                                            color: context.appColors.primary,
                                            width: 1.0),
                                        borderRadius: BorderRadius.circular(
                                            40), // Adjust the border radius as needed
                                      )
                                    : null,
                                backgroundColor: isSelected
                                    ? context.appColors.lightBlueBg
                                    : Colors.transparent,
                                label: Text(
                                    '${jobProvider.experienceFilterList[index]}'),
                                deleteIcon: isSelected
                                    ? Icon(
                                        Icons.close,
                                        color: context.appColors.textBlack,
                                        size: 16,
                                      )
                                    : SizedBox(),
                                onSelected: (bool _) {
                                  if (isSelected) {
                                    jobProvider.removeAppliedExperienceFilter(
                                        experience: jobProvider
                                            .experienceFilterList[index]);
                                  } else {
                                    jobProvider.addAppliedExperienceFilter(
                                        experience: jobProvider
                                            .experienceFilterList[index]);
                                  }
                                },
                                onDeleted: () {
                                  jobProvider.removeAppliedExperienceFilter(
                                      experience: jobProvider
                                          .experienceFilterList[index]);
                                },
                              ),
                            );
                          });
                        }(),
                      ),
                    ),
                  ),
                ] else if (selectedType == SelectedOption.matching_level) ...[
                  ConstrainedBox(
                    constraints:
                        BoxConstraints(maxHeight: height(context) * 0.6),
                    child: SingleChildScrollView(
                      child: Wrap(
                        direction: Axis.horizontal,
                        children: () {
                          return List.generate(
                              jobProvider.matchingLevelFilterList.length,
                              (index) {
                            bool isSelected =
                                jobProvider.matchingFilterListApplied.contains(
                                    jobProvider.matchingLevelFilterList[index]);
                            return Container(
                              margin: const EdgeInsets.all(2.0),
                              child: InputChip(
                                pressElevation: 0.0,
                                shape: isSelected
                                    ? RoundedRectangleBorder(
                                        side: BorderSide(
                                            color: context.appColors.primary,
                                            width: 1.0),
                                        borderRadius: BorderRadius.circular(
                                            40), // Adjust the border radius as needed
                                      )
                                    : null,
                                backgroundColor: isSelected
                                    ? context.appColors.lightBlueBg
                                    : Colors.transparent,
                                label: Text(
                                    '${jobProvider.matchingLevelFilterList[index]}'),
                                deleteIcon: isSelected
                                    ? Icon(
                                        Icons.close,
                                        color: context.appColors.textBlack,
                                        size: 16,
                                      )
                                    : SizedBox(),
                                onSelected: (bool _) {
                                  if (isSelected) {
                                    jobProvider
                                        .removeAppliedMatchingLevelFilter(
                                            level: jobProvider
                                                    .matchingLevelFilterList[
                                                index]);
                                  } else {
                                    jobProvider.addAppliedMatchingLevelFilter(
                                        level: jobProvider
                                            .matchingLevelFilterList[index]);
                                  }
                                },
                                onDeleted: () {
                                  jobProvider.removeAppliedMatchingLevelFilter(
                                      level: jobProvider
                                          .matchingLevelFilterList[index]);
                                },
                              ),
                            );
                          });
                        }(),
                      ),
                    ),
                  ),
                ],
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(vertical: 22),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                          color: context.appColors.primary,
                          borderRadius: BorderRadius.circular(8)),
                      child: Center(
                        child: Text(
                          'show_results',
                          style: Styles.boldWhite(),
                        ).tr(),
                      )),
                )
              ],
            ))
      ],
    );
  }
}
