import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:masterg/utils/utility.dart';

import '../../data/models/response/home_response/explore_job_list_response.dart';
import '../../data/models/response/home_response/new_portfolio_response.dart';

class ExploreJobProvider extends ChangeNotifier {
  List<dynamic> countryFilterList = [];
  List<dynamic> experienceFilterList = [];
  List<int> matchingLevelFilterList = [];
  Resume? resumeDetail;
  List<ExploreJob>? allJobs = [];
  List<ExploreJob>? filterJobs = [];
  double popHeight = 0.0;
  int pageIndex = 0;
  bool fromVideoResume = false;

  //applied filter list
  List<dynamic> countryFilterListApplied = [];
  List<dynamic> experienceFilterListApplied = [];
  List<int> matchingFilterListApplied = [];

  // List<dynamic> countryFilterFinal = [];
  // List<dynamic> experienceFilterFinal = [];
  // List<int> matchingFilterFinal = [];

  bool isVerified = false;

  bool loading = false;
  void updateFromVideoResume({required bool fromVideoResume}) {
    this.fromVideoResume = fromVideoResume;
    notifyListeners();
  }

  void updateResumeDetail({required Resume? data}) {
    if (data == null) return;
    resumeDetail = data;
    notifyListeners();
  }

  void resetResume() {
    resumeDetail = null;
    notifyListeners();
  }

  void updateSkillMatch({required String jobID, required int skillID}) {
    allJobs
        ?.firstWhere((element) => element.jobId == jobID)
        .skills
        ?.firstWhere((element) => element.id == skillID)
        .userMatch = 1;

    filterJobs
        ?.firstWhere((element) => element.jobId == jobID)
        .skills
        ?.firstWhere((element) => element.id == skillID)
        .userMatch = 1;

    notifyListeners();
  }

  void reset() {
    countryFilterListApplied = [];
    experienceFilterListApplied = [];
    matchingFilterListApplied = [];
    allJobs = [];
    filterJobs = [];
    pageIndex = 0;
    notifyListeners();
  }

  void incrpageIndex() {
    pageIndex++;
    notifyListeners();
  }

  void resetFilter() {
    countryFilterListApplied = [];
    experienceFilterListApplied = [];
    matchingFilterListApplied = [];
    getFilteredList();
    notifyListeners();
  }

  void updatePopHeight({required double popHeight}) {
    this.popHeight = popHeight;
    notifyListeners();
  }

  void addAppliedCountryFilter({required String country}) {
    countryFilterListApplied.add(country);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedCountryFilter() {
    countryFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedCountryFilter({required String country}) {
    countryFilterListApplied.remove(country);
    notifyListeners();
    getFilteredList();
  }

  void addAppliedExperienceFilter({required experience}) {
    experienceFilterListApplied.add(experience);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedExperienceFilter() {
    experienceFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedExperienceFilter({required String experience}) {
    experienceFilterListApplied.remove(experience);
    notifyListeners();
    getFilteredList();
  }

  void addAppliedMatchingLevelFilter({required int level}) {
    matchingFilterListApplied.add(level);
    notifyListeners();
    getFilteredList();
  }

  void resetAppliedMatchingLevelFilter() {
    matchingFilterListApplied = [];
    notifyListeners();
    getFilteredList();
  }

  void removeAppliedMatchingLevelFilter({required int level}) {
    matchingFilterListApplied.remove(level);
    notifyListeners();
    getFilteredList();
  }

  // void applyFilter() {
  //   this.countryFilterFinal = this.countryFilterListApplied;
  //   this.experienceFilterFinal = this.experienceFilterListApplied;
  //   this.matchingFilterFinal = this.matchingFilterListApplied;
  //   getFilteredList();
  // }

  void updateFilter() async {
    Set<String> country = {};
    Set<String> experience = {};
    Set<int> matchingLevel = {};
    Utility.tryCatch(fn: () {
      for (var jobs in allJobs!) {
        if (jobs.location != null && jobs.location != 'N/A') {
          country.add(jobs.location!);
        }

        if (jobs.experience != null &&
            jobs.experience != 'N/A' &&
            jobs.experience?.indexOf('-') != 0) {
          experience.add(jobs.experience!);
        }

        if (jobs.resumeRating != null) {
          matchingLevel.add(int.parse('${jobs.resumeRating}'));
          log("the value is  update ${jobs.resumeRating}");
        }
      }
      countryFilterList.clear();
      experienceFilterList.clear();
      matchingLevelFilterList.clear();

      countryFilterList.addAll(country);
      experienceFilterList.addAll(experience);
      matchingLevelFilterList.addAll(matchingLevel);
    });

    notifyListeners();
  }

  void setLoading({required bool isLoading}) {
    loading = isLoading;
    notifyListeners();
  }

  void setCountryFilterList(List<String> countries) {
    countryFilterList = countries;
    notifyListeners();
  }

  void addJobs(List<ExploreJob> jobs) {
    allJobs = [];
    allJobs?.addAll(jobs);

    getFilteredList();
    // if (this.filterJobs?.length == 0) {
    //   this.filterJobs?.addAll(jobs);
    // }
    notifyListeners();
  }

  void updateIsVerified({required bool isVerified}) {
    this.isVerified = isVerified;

    // notifyListeners();
    getFilteredList();
  }

  void getFilteredList() {
    filterJobs = [];
    for (var jobs in allJobs!) {
      if (countryFilterListApplied.isNotEmpty) {
        if (!countryFilterListApplied.contains(jobs.location)) continue;
      }
      if (experienceFilterListApplied.isNotEmpty) {
        if (!experienceFilterListApplied.contains(jobs.experience)) continue;
      }

      if (matchingFilterListApplied.isNotEmpty) {
        if (!matchingFilterListApplied
            .contains(int.tryParse('${jobs.resumeRating}'))) {
          continue;
        }
      }

      if (isVerified == true) {
        if (jobs.isVerified == 1) {
          filterJobs?.add(jobs);
        }
      } else {
        filterJobs?.add(jobs);
      }
    }

    notifyListeners();
  }
}
