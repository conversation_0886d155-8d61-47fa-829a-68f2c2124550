import 'package:flutter/material.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/get_widget_size.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';

import 'explore_job_proivder.dart';

class ExploreJobBottomSheet {
  static Future<bool> showBottomSheet(
      {required bool isDismissible,
      required Widget widget,
      required BuildContext context}) async {
    return await showModalBottomSheet(
      isDismissible: isDismissible,
      enableDrag: isDismissible,
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      backgroundColor: context.appColors.surface,
      builder: (BuildContext context) {
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
          ExploreJobProvider jobProvider =
              Provider.of<ExploreJobProvider>(context);

          return SizedBox(
            height: jobProvider.popHeight,
            child: PopScope(
              canPop: isDismissible,
              child: ListView(
                physics: NeverScrollableScrollPhysics(),
                children: [
                  MeasureSize(
                    onChange: (size) {
                      // jobProvider.updatePopHeight(popHeight: size.height);
                      jobProvider.updatePopHeight(
                          popHeight: size.height > height(context) * 0.8
                              ? height(context) * 0.8
                              : size.height);
                    },
                    child: widget,
                  ),
                ],
              ),
            ),
          );
        });
      },
    ).then((value) => true);
  }
}
