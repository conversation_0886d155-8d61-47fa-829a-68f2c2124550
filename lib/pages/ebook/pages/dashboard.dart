import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/pages/ebook/colors/colors.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/course_category_list_id_response.dart';
import '../../../utils/Log.dart';
import 'widgets/export.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  List<MProgram>? meriPustake;
  List<MProgram>? recommendedBooks;
  List<MProgram>? popularBooks;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<HomeBloc>(context)
        .add(CourseCategoryListIDEvent(categoryId: 0));
    BlocProvider.of<HomeBloc>(context).add(FilteredPopularCoursesEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: (context, state) {
        if (state is CourseCategoryListIDState) {
          _handlemeriPustakeResponse(state);
        }
        if (state is FilteredPopularCoursesState) {
          _handlePopularFilteredCourses(state);
        }
      },
      child: Scaffold(
        backgroundColor: ColorUtils.screenBg(context),
        appBar: AppBar(
            elevation: 0,
            backgroundColor: ColorUtils.appBarBg(context),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Image.asset('assets/images/ebook/appbar_logo.png'),
                Icon(
                  Icons.search,
                  color: ColorUtils.darkIcon(context),
                )
              ],
            )),
        body: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: ListView(children: [
            if (!(recommendedBooks != null && recommendedBooks?.length == 0))
              RecommendedBooks(
                recommendedBooks: recommendedBooks,
              ),
            meriPustake == null
                ? Text('loading').tr()
                : MeriPustak(
                    meriPustake: meriPustake,
                  ),
            // AudioBook(),
            if (!(popularBooks != null && popularBooks?.length == 0))
              RecommendedBooks(
                recommendedBooks: popularBooks,
              ),
          ]),
        ),
      ),
    );
  }

  void _handlePopularFilteredCourses(FilteredPopularCoursesState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          // isJoyCategoryLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("LiveClassState Done ....................");
          Log.v(state.response!.data);
          recommendedBooks = state.response?.data?.recommended
              ?.map((e) =>
                  MProgram.fromJson(Map<String, dynamic>.from(e.toJson())))
              .cast<MProgram>()
              .toList();

          popularBooks = state.response?.data?.mostViewed
              ?.map((e) =>
                  MProgram.fromJson(Map<String, dynamic>.from(e.toJson())))
              .cast<MProgram>()
              .toList();
          // isJoyCategoryLoading = false;
          break;
        case ApiStatus.ERROR:
          // isJoyCategoryLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'popular_filter_course', parameters: {
            "ERROR": loginState.error ?? "",
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handlemeriPustakeResponse(CourseCategoryListIDState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          // ismeriPustakeLoading = true;

          break;
        case ApiStatus.SUCCESS:
          Log.v("CourseCategoryState....................");

          meriPustake = state.response!.data!.programs;
          // ismeriPustakeLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'ebookmycourse', parameters: {
            "ERROR": '${loginState.error}',
          });

          // ismeriPustakeLoading = false;

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
