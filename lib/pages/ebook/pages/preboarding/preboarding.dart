import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/ebook/colors/colors.dart';
import 'package:masterg/utils/constant.dart';

import '../../../../utils/styles.dart';
import '../../../auth_pages/new_language_screen.dart';
import '../../../custom_pages/custom_widgets/next_page_routing.dart';

class PreboardingPage extends StatelessWidget {
  const PreboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        height: height(context),
        child: Stack(children: [
          Image.asset(
            'assets/images/ebook/preboarding_bg.png',
            width: width(context),
            fit: BoxFit.cover,
          ),
          Positioned(
              top: height(context) * 0.1,
              left: 0,
              right: 0,
              child: Image.asset('assets/images/ebook/preboarding_phone.png')),
          Positioned(
            top: height(context) * 0.5,
            left: 0,
            right: 0,
            child: CarouselSlider(
              options: CarouselOptions(
                  disableCenter: true,
                  viewportFraction: 0.8,
                  enlargeCenterPage: true,
                  reverse: false,
                  height: 400.0),
              items: [1, 2, 3].map((i) {
                return Builder(
                  builder: (BuildContext context) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/ebook/preboarding_$i.png',
                        ),
                        Text(
                          'Digital Repository of Non-academic Resources',
                          textAlign: TextAlign.center,
                          style: Styles.getSemiboldThemeStyle(context),
                        ),
                      ],
                    );
                  },
                );
              }).toList(),
            ),
          ),
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Column(
              children: [
                SizedBox(
                  height: 4,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 10,
                        width: width(context) * 0.1,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        color: 0 == 0
                            ? ColorUtils.primary
                            : ColorUtils.secondaryDark(context),
                      ),
                      Container(
                        height: 10,
                        width: width(context) * 0.1,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        color: 0 == 0
                            ? ColorUtils.primary
                            : ColorUtils.secondaryDark(context),
                      ),
                      Container(
                        height: 10,
                        width: width(context) * 0.1,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        color: 0 == 0
                            ? ColorUtils.primary
                            : ColorUtils.secondaryDark(context),
                      )
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Column(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.pushAndRemoveUntil(
                            context,
                            NextPageRoute(SelectLanguage(
                              showEdulystLogo: true,
                            )),
                            (route) => false);
                      },
                      child: Container(
                        width: width(context) * 0.9,
                        height: 50,
                        color: ColorUtils.primary,
                        child: Center(
                          child: Text(
                            'Get Started',
                            style: Styles.boldWhite(),
                          ),
                        ),
                      ),
                    )
                  ],
                )
              ],
            ),
          )
        ]),
      ),
    );
  }
}
