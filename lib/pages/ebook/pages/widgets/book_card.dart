import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:masterg/pages/ebook/colors/colors.dart';
import 'package:provider/provider.dart';

import '../../../../data/api/api_service.dart';
import '../../../../data/models/response/home_response/course_category_list_id_response.dart';
import '../../../../data/providers/training_detail_provider.dart';
import '../../../custom_pages/custom_widgets/next_page_routing.dart';
import '../../../training_pages/program_content/training_detail_page.dart';
import '../../../training_pages/training_service.dart';

class BookCard extends StatelessWidget {
  final MProgram? bookDetail;
  const BookCard({super.key, this.bookDetail});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            NextPageRoute(
                ChangeNotifierProvider<TrainingDetailProvider>(
                    create: (context) => TrainingDetailProvider(
                        TrainingService(ApiService()), bookDetail),
                    child: TrainingDetailPage()),
                isMaintainState: true));
      },
      child: Container(
        width: (199 * 0.67),
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Column(
          children: [
            SizedBox(
              height: 199,
              width: (199 * 0.67),
              child: AspectRatio(
                aspectRatio: 0.67,

                child: CachedNetworkImage(
                  imageUrl: "${bookDetail?.image}",
                  imageBuilder: (context, imageProvider) => Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                              context.appColors.error, BlendMode.colorBurn)),
                    ),
                  ),
                  placeholder: (context, url) => CircularProgressIndicator(),
                  errorWidget: (context, url, error) => Icon(Icons.error),
                ),
                // child: Image.network(
                //   '${bookDetail?.image}',
                //   fit: BoxFit.cover,
                // ),
              ),
            ),
            Text(
              bookDetail?.name ?? '',
              style: GoogleFonts.poppins(
                  fontSize: 12, fontWeight: FontWeight.w500),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
            Text(
              bookDetail?.description ?? '',
              style: GoogleFonts.poppins(
                  color: ColorUtils.secondaryDark(context),
                  fontSize: 10,
                  fontWeight: FontWeight.w400),
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
