import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:masterg/pages/ebook/colors/colors.dart';

class AudioBookCard extends StatelessWidget {
  const AudioBookCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      decoration: BoxDecoration(color: ColorUtils.containerLight(context)),
      margin: const EdgeInsets.symmetric(vertical: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Transform.translate(
            offset: Offset(25, -25),
            child: Transform.scale(
              scale: 1.3,
              child: Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withValues(alpha: 0.5),
                      blurRadius: 10.9,
                      offset: Offset(0, 10),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                      'https://source.unsplash.com/user/c_v_r/131x200'),
                ),
              ),
            ),
          ),
          SizedBox(width: 50),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Guru Nanakdev',
                  style: GoogleFonts.playfairDisplay(
                      fontWeight: FontWeight.w400, fontSize: 22),
                ),
                Text(
                  'Adrian Tchaikovsky',
                  style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w400, fontSize: 12),
                ),
                Text(
                  'Get Book',
                  style: GoogleFonts.poppins(
                      color: ColorUtils.link,
                      fontWeight: FontWeight.w400,
                      fontSize: 12),
                ),
              ],
            ),
          ),
          Spacer(),
          SvgPicture.asset('assets/images/ebook/play_audio.svg'),
          Spacer(),
        ],
      ),
    );
  }
}
