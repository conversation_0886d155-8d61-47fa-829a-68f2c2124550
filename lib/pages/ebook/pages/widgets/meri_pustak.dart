import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../data/models/response/home_response/course_category_list_id_response.dart';
import 'book_card.dart';

class MeriPustak extends StatelessWidget {
  final List<MProgram>? meriPustake;
  const MeriPustak({super.key, this.meriPustake});

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: ColorUtils.containerLight,
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Meri Pustake',
            style: GoogleFonts.playfairDisplay(
                fontSize: 18, fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 270,
            child: CarouselSlider(
              options: CarouselOptions(
                  disableCenter: true,
                  viewportFraction: 0.4,
                  aspectRatio: 0.65,
                  enlargeCenterPage: true,
                  reverse: false,
                  height: 400.0),
              items: meriPustake?.map((i) {
                return Builder(
                  builder: (BuildContext context) {
                    return BookCard(
                      bookDetail: i,
                    );
                  },
                );
              }).toList(),
            ),
          )
        ],
      ),
    );
  }
}
