import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';

import '../../../../data/models/response/home_response/popular_courses_response.dart';
import 'book_card.dart';

class PopularBooks extends StatelessWidget {
  final List<OtherLearners>? recommendedBooks;
  const PopularBooks({super.key, this.recommendedBooks});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Popular Books',
            style: GoogleFonts.playfairDisplay(
                fontSize: 18, fontWeight: FontWeight.w600),
          ),
          Si<PERSON><PERSON><PERSON>(
            height: 280,
            child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: recommendedBooks?.length,
                itemBuilder: ((context, index) {
                  return BookCard(
                    bookDetail:
                        MProgram.from<PERSON>son(recommendedBooks![index].toJson()),
                  );
                })),
          )
        ],
      ),
    );
  }
}
