import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class ColorUtils {
  static const Color primary = Color(0xff0A84FF);
  static const Color link = Color(0xff1A76FF);

  // Theme-aware colors
  static Color appBarBg(BuildContext context) =>
      context.isDarkMode ? const Color(0xFF1E1E1E) : const Color(0xffffffff);

  static Color screenBg(BuildContext context) =>
      context.isDarkMode ? const Color(0xFF121212) : const Color(0xffF4F4F4);

  static Color darkIcon(BuildContext context) =>
      context.isDarkMode ? const Color(0xFFE0E0E0) : const Color(0xff222631);

  static Color secondaryDark(BuildContext context) =>
      context.isDarkMode ? const Color(0xFFB0B0B0) : const Color(0xff767676);

  static Color containerLight(BuildContext context) =>
      context.isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xffffffff);
}

// Legacy constants for backward compatibility
class LegacyColorUtils {
  static const Color primary = Color(0xff0A84FF);
  static const Color link = Color(0xff1A76FF);
  static const Color appBarBg = Color(0xffffffff);
  static const Color screenBg = Color(0xffF4F4F4);
  static const Color darkIcon = Color(0xff222631);
  static const Color secondaryDark = Color(0xff767676);
  static const Color containerLight = Color(0xffffffff);
}
