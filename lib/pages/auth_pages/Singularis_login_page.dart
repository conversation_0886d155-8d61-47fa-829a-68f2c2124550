import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/swayam_login_request.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_view_resp.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/auth_pages/forget_password.dart';
import 'package:masterg/pages/auth_pages/mec/register_screen_v1.dart';
import 'package:masterg/pages/auth_pages/register_with_email.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/validation.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../../utils/notification_helper.dart';
import '../../utils/utility.dart';
import '../custom_pages/custom_widgets/common_web_view.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  TextEditingController _emailController = TextEditingController();
  TextEditingController _passController = TextEditingController();
  int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
  var _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  final bool _autoValidation = true;
  var _isObscure = true;
  List<Menu>? menuList;
  String? deviceId;
  NotificationHelper? _notificationHelper;
  bool visibleNotExist = false;
  String? mecURL;
  String? appVersion;
  bool rememberMe = false;
  DashboardViewResponse? dashboardViewResponse;
  WhoAmI? iAmAItemsList;

  void initHive() async {
    await getApplicationDocumentsDirectory().then((value) {
      Hive.init(value.path);
      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
    });
  }

  @override
  void initState() {
    super.initState();
    initHive();
    _notificationHelper = NotificationHelper.getInstance(context);
    _notificationHelper?.setFcm();
    _notificationHelper?.getFcmToken();
    _getId();
    getAppVersion();
    getDashboardIsVisible();

    //set email and password when remember me is on
    rememberMe = Preference.getBool(Preference.rememberMe) ?? false;
    log("chekc if remeber me $rememberMe");
    if (rememberMe == true) {
      String email = Preference.getString(Preference.LOGIN_ID) ?? '';
      String password = Preference.getString(Preference.LOGIN_PASS) ?? '';
      _emailController = TextEditingController(text: email);
      _passController = TextEditingController(text: password);
    }
  }

  Future<Null> _getId() async {
    var deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      var iosDeviceInfo = await deviceInfo.iosInfo;
      deviceId = iosDeviceInfo.identifierForVendor;
    } else if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;

      deviceId = androidDeviceInfo.id;
    }
  }

  void getBottomNavigationBar() {
    getPortfolio();
    getPiDetail();
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context)
        .add(PortfolioEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void getPiDetail() {
    if (Preference.getInt(Preference.ENABLE_PI) == 1) {
      BlocProvider.of<HomeBloc>(context)
          .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
    }
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading................... hksdf");
          break;
        case ApiStatus.SUCCESS:
          try {
            Log.v("PortfolioStatedone ...................");

            if ('${portfolioState.response?.data.name}' != '') {
              Preference.setString(Preference.FIRST_NAME,
                  '${portfolioState.response?.data.name}');
            }

            Preference.setInt(Preference.PROFILE_PERCENT,
                portfolioState.response!.data.profileCompletion);
            debugPrint(
                'PROFILE_PERCENT ${portfolioState.response!.data.profileCompletion}');
            if (portfolioState.response?.data.image.contains(
                    '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
                true) {
              Preference.setString(Preference.PROFILE_IMAGE,
                  '${portfolioState.response?.data.image}');
            }

            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data.portfolioSocial.first['email']}');
            Preference.setString(Preference.PHONE,
                '${portfolioState.response?.data.portfolioSocial.first['mob_num']}');

            if ('${portfolioState.response?.data.image}' != '') {
              Preference.setString(Preference.PROFILE_IMAGE,
                  '${portfolioState.response?.data.image}');
            }

            Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
                portfolioState.response!.data.resumeParserDataCount!);

            Preference.setString(Preference.PROFILE_VIDEO,
                '${portfolioState.response?.data.profileVideo}');

            if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
                true) {
              Preference.setString(Preference.ABOUT_ME,
                  '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

              Preference.setString(Preference.USER_HEADLINE,
                  '${portfolioState.response?.data.portfolioProfile.first.headline}');
              Preference.setString(Preference.LOCATION,
                  '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
            }
            Log.v("PortfolioState Success....................");
          } catch (e) {}

          BlocProvider.of<HomeBloc>(context)
              .add((GetBottomNavigationBarEvent()));

          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'login_page', parameters: {
            "login_failed": "true",
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> getDashboardIsVisible() async {
    BlocProvider.of<HomeBloc>(context).add(DashboardIsVisibleEvent());
  }

  void handleDashboardIsVisible(DashboardIsVisibleState state) {
    var dashboardIsVisibleState = state;
    setState(() {
      switch (dashboardIsVisibleState.apiState) {
        case ApiStatus.LOADING:
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          setState(() {
            _isLoading = false;
          });
          dashboardViewResponse = state.response;
          iAmAItemsList = dashboardViewResponse?.data?.whoAmI;
          Log.v('iAmAItemsList............>$iAmAItemsList');

          break;

        case ApiStatus.ERROR:
          setState(() {
            _isLoading = false;
          });
          break;

        case ApiStatus.INITIAL:
          setState(() {
            _isLoading = false;
          });
          break;
      }
    });
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success.................... and name is ${portfolioState.response?.data?.name}");

          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null) {
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          }
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null) {
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');
          }

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null) {
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');
          }
          setState(() {});

          break;

        case ApiStatus.ERROR:
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'new_login_page', parameters: {
            "Error": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getCountry() async {
    // String?   value =
    await Utility.getCurrentLocale();
    setState(() {});
  }

  void _swayamLoginResponse(SwayamLoginState state) async {
    var loginState = state;
    setState(() async {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "Success.................... -- ${loginState.response?.toJson()}");

          if (state.response?.status == 2) {
            setState(() {
              visibleNotExist = true;
              mecURL = state.response?.data?.url;
            });
          } else {
            setState(() {
              visibleNotExist = false;
            });
            if (state.response?.error?.length != 0) {
              AlertsWidget.showCustomDialog(
                  context: context,
                  title: loginState.response?.error?.first,
                  text: "",
                  icon: 'assets/images/circle_alert_fill.svg',
                  showCancel: false,
                  oKText: tr('ok'),
                  onOkClick: () async {
                    setState(() {
                      _isLoading = false;
                    });
                  });
              setState(() {
                _isLoading = false;
              });

              break;
            }

            UserSession.userToken = state.response?.data?.token;
            UserSession.email = state.response?.data?.user?.email;
            UserSession.userName = state.response?.data?.user?.name;
            UserSession.userImageUrl = state.response?.data?.user?.profileImage;
            UserSession.socialEmail = state.response?.data?.user?.email;
            Preference.setString(
                Preference.USER_TOKEN, '${state.response?.data?.token}');
            Preference.setString(
                Preference.FIRST_NAME, '${state.response?.data?.user?.name}');
            Preference.setString(
                Preference.USER_EMAIL, '${state.response?.data?.user?.email}');
            Preference.setString(
                Preference.PHONE, '${state.response?.data?.user?.mobileNo}');
            Preference.setString(Preference.PROFILE_IMAGE,
                '${state.response?.data?.user?.profileImage}');
            Preference.setInt(Preference.USER_ID,
                int.parse('${state.response?.data?.user?.id}'));
            Preference.setString('interestCategory',
                '${state.response!.data!.user!.categoryIds}');
            Preference.setString(Preference.DEFAULT_VIDEO_URL_CATEGORY,
                '${state.response!.data!.user!.defaultVideoUrlOnCategory}');
            Preference.setString(
                Preference.ORG_URL, '${state.response!.data!.user!.orgLogo}');
            Preference.setString(Preference.MEC_REGD_ID,
                '${state.response!.data!.user!.mecRegdId}');
            Preference.setString(
                Preference.SSOTOKEN, '${state.response!.data!.user!.ssoToken}');
            Preference.setString(
                Preference.ROLE, '${state.response!.data!.user!.role}');

            //Set Language after login
            if (state.response!.data!.user!.role != 'Learner') {
              Preference.setString(
                  Preference.LANGUAGE, '${state.response!.data!.user!.locale}');
              Preference.setInt(Preference.APP_LANGUAGE,
                  int.parse('${state.response!.data!.user!.languageId}'));
              Preference.setInt(
                  Preference.IS_PRIMARY_LANGUAGE,
                  int.parse(
                      '${state.response!.data!.user!.isPrimaryLanguage}'));
              Preference.setString(Preference.APP_ENGLISH_NAME,
                  '${state.response!.data!.user!.englishName}'.toLowerCase());
              context
                  .setLocale(Locale('${state.response!.data!.user!.locale}'));
            }

            getBottomNavigationBar();
          }

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................${loginState.error}");
          setState(() {
            _isLoading = false;
          });
          AlertsWidget.alertWithOkBtn(
              context: context,
              text: loginState.response?.error?.first,
              onOkClick: () {
                setState(() {
                  _isLoading = false;
                });
              });
          FirebaseAnalytics.instance
              .logEvent(name: 'explore_job_apply', parameters: {
            "ERROR": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          menuList = state.response!.data!.menu;
          menuList = menuList!.where((element) {
            bool containRole = element.role.toString().toLowerCase().contains(
                '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
            return containRole;
          }).toList();

          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            //menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => int.parse('${a.inAppOrder}')
                .compareTo(int.parse('${b.inAppOrder}')));
            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    HomePage(
                      bottomMenu: menuList,
                    ),
                    isMaintainState: true),
                (route) => false);
          }
          _isLoading = false;

          break;

        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................${getBottomBarState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'bottom_navigatorBar', parameters: {
            "ERROR": '${getBottomBarState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      appVersion = packageInfo.version;
    });
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return MultiBlocListener(
          listeners: [
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is SwayamLoginState) _swayamLoginResponse(state);
              },
            ),
            BlocListener<HomeBloc, HomeState>(
              listener: (BuildContext context, state) {
                if (state is GetBottomBarState) {
                  _handelBottomNavigationBar(state);
                }
                if (state is PiDetailState) {
                  handlePiDetail(state);
                }
                if (state is PortfolioState) {
                  handlePortfolioState(state);
                }
                if (state is DashboardIsVisibleState) {
                  handleDashboardIsVisible(state);
                }
              },
            ),
          ],
          child: ScreenWithLoader(
              isLoading: _isLoading,
              isContainerHeight: false,
              body: _content()),
        );
      },
    );
  }

  Widget _content() {
    // String appBarImagePath =
    //  'assets/images/${APK_DETAILS['theme_image_url']}';
    return SafeArea(
      child: Form(
        key: _formKey,
        autovalidateMode: _autoValidation
            ? AutovalidateMode.always
            : AutovalidateMode.disabled,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    _size(height: 10),
                    _textField(
                        isEmail: true,
                        controller: _emailController,
                        hintText: APK_DETAILS["package_name"] ==
                                "com.singulariswow.mec"
                            ? tr('enter_mec_id')
                            : tr('enter_email_address'),
                        prefixImage: 'assets/images/email_icon.png',
                        validation: (value) {
                          return null;
                        }),
                    _size(height: 25),
                    _textField(
                        controller: _passController,
                        hintText: tr('password'),
                        prefixImage: 'assets/images/lock.png',
                        obscureText: _isObscure,
                        validation: validatePassword1,
                        onEyePress: () {
                          setState(() {
                            _isObscure = !_isObscure;
                          });
                        }),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      children: [
                        SizedBox(
                          width: 32,
                          height: 32,
                          child: Checkbox(
                            value: rememberMe,
                            onChanged: (bool? value) {
                              setState(() {
                                rememberMe = value ?? false;
                              });
                            },
                          ),
                        ),
                        Text(
                          'remember_me',
                          style: Styles.getRegularThemeStyle(context, size: 15),
                        ).tr(),
                        Spacer(),
                        InkWell(
                            onTap: () {
                              if (APK_DETAILS["package_name"] ==
                                  "com.singulariswow.mec") {
                                Navigator.push(
                                    context,
                                    NextPageRoute(CommonWebView(
                                      url: APK_DETAILS["forgot_cange_pass"],
                                    )));
                              } else {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ForgotPassword()));
                              }
                            },
                            child: Text(
                              "forgot_password",
                              style: Styles.regular(
                                  color: context.appColors.grey3),
                            ).tr())
                      ],
                    ),

                    //signup for MEC
                    _size(height: 30),
                    APK_DETAILS["package_name"] == "com.singulariswow.mec"
                        ? InkWell(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  NextPageRoute(CommonWebView(
                                    url: mecURL,
                                  )));
                            },
                            child: visibleNotExist == true
                                ? Text(
                                    "user_not_exist",
                                    style: TextStyle(
                                        color: context.errorColor,
                                        decoration: TextDecoration.underline,
                                        fontWeight: FontWeight.bold),
                                  ).tr()
                                : Text(''))
                        : SizedBox(),

                    _size(height: 10),
                    Column(
                      children: [
                        InkWell(
                          onTap: () {
                            // log('chekc if remeber m ${rememberMe}');
                            Preference.setBool(
                                Preference.rememberMe, rememberMe);
                            Preference.setBool(
                                Preference.LOGGEDIN_WITH_EMAIL, true);
                            String? value = validateEmail(
                                _emailController.text.toString().trim());
                            String? pass = validatePassword(
                                _passController.text.toString().trim());
                            if (value != null) {
                              AlertsWidget.showCustomDialog(
                                  context: context,
                                  title: value,
                                  text: "",
                                  icon: 'assets/images/circle_alert_fill.svg',
                                  showCancel: false,
                                  oKText: tr('ok'),
                                  onOkClick: () async {});
                            } else if (pass != null) {
                              AlertsWidget.showCustomDialog(
                                  context: context,
                                  title: pass,
                                  text: "",
                                  icon: 'assets/images/circle_alert_fill.svg',
                                  showCancel: false,
                                  oKText: tr('ok'),
                                  onOkClick: () async {});
                            } else
                              BlocProvider.of<AuthBloc>(context).add(
                                  PvmSwayamLogin(
                                      request: SwayamLoginRequest(
                                          deviceToken: UserSession
                                              .firebaseToken,
                                          device_id: deviceId,
                                          locale: Preference
                                                  .getString(Preference
                                                      .APP_ENGLISH_NAME)
                                              .toString(),
                                          deviceType: Utility
                                                  .getDeviceType()
                                              .toString(),
                                          userName: _emailController
                                              .text
                                              .toString()
                                              .trim(),
                                          password: _passController.text
                                              .toString()
                                              .trim())));

                            Preference.setString(Preference.LOGIN_ID,
                                _emailController.text.toString());
                            Preference.setString(Preference.LOGIN_PASS,
                                _passController.text.toString().trim());
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(vertical: 12),
                            width: width(context),
                            height: MediaQuery.of(context).size.height * 0.06,
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                              gradient: LinearGradient(colors: [
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight,
                              ]),
                              boxShadow: [
                                BoxShadow(
                                    color: context.appColors.gradientRight
                                        .withValues(alpha: 0.3),
                                    offset: Offset(2.5, 10),
                                    blurRadius: 20)
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'login',
                                style: Styles.bold(
                                  size: 16,
                                  color: context.primaryForegroundColor,
                                ),
                              ).tr(),
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                        APK_DETAILS['register_now'] == '0'
                            ? SizedBox()
                            : Column(children: [
                                Text(
                                  'not_registered',
                                  style: Styles.regular(
                                      size: 14,
                                      color: context.headingTextColor),
                                ).tr(),
                                SizedBox(height: 10),
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: InkWell(
                                      onTap: () {
                                        if (APK_DETAILS["register_in_app"] ==
                                            "0") {
                                          Navigator.push(
                                              context,
                                              NextPageRoute(CommonWebView(
                                                url: "https://mec.edu.om/"
                                                    "${Preference.getString(Preference.LANGUAGE)}",
                                              )));
                                        } else {
                                          if (APK_DETAILS["package_name"] ==
                                              "com.singulariswow.mec") {
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        // RegisterScreenV1()));
                                                        RegisterScreenV1(
                                                            iAmAItemsList:
                                                                iAmAItemsList
                                                                        ?.toJson()[
                                                                    '${Preference.getString(Preference.LANGUAGE)}'])));
                                          } else {
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        RegisterScreen()));
                                          }
                                        }
                                      },
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text('register_now',
                                              style: Styles.getBoldThemeStyle(
                                                context,
                                                size: 14,
                                              )).tr(),
                                          Icon(
                                            Icons.arrow_forward_ios_outlined,
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ))
                              ]),
                        Row(
                          children: [
                            Text('${tr('version')}: $appVersion',
                                style: Styles.getRegularThemeStyle(context,
                                    size: 12)),
                            Spacer(),
                            Text(''),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  SizedBox _size({double height = 5}) {
    return SizedBox(
      height: height,
    );
  }

  Widget _textField({
    bool isEmail = false,
    TextEditingController? controller,
    String? hintText,
    required String prefixImage,
    bool obscureText = false,
    required Function(String) validation,
    Function()? onEyePress,
  }) {
    return TextFormField(
      cursorColor: context.headingTextColor,
      style: Styles.regular(color: context.headingTextColor),
      controller: controller,
      obscureText: obscureText,
      decoration: InputDecoration(
        hintText: hintText,
        isDense: true,
        prefixIcon: Padding(
          padding: const EdgeInsets.all(5),
          child: isEmail == true
              ? Icon(Icons.email_outlined, color: context.appColors.grey3)
              : Image.asset(prefixImage,
                  height: 32, width: 32, color: context.appColors.grey3),
        ),
        suffixIcon: Visibility(
          visible: onEyePress != null,
          child: TapWidget(
            onTap: () {
              onEyePress!();
            },
            child: Padding(
                padding: const EdgeInsets.only(right: 5),
                child: !obscureText
                    ? Icon(
                        Icons.remove_red_eye_outlined,
                        color: context.headingTextColor,
                      )
                    : Icon(
                        Icons.visibility_off,
                        color: context.appColors.grey3,
                      )),
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
        hintStyle: Styles.regular(size: 18, color: context.appColors.grey3),
        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 13),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
        disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            borderSide: BorderSide(color: context.appColors.grey3, width: 1)),
      ),
    );
  }
}
