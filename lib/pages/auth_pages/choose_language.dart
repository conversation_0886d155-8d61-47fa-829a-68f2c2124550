import 'dart:developer';
import 'dart:core';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/data/models/response/home_response/master_language_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/widget_size.dart';
import 'singularis_login_username_pass.dart';

class ChooseLanguage extends StatefulWidget {
  final bool showEdulystLogo;
  const ChooseLanguage({super.key, required this.showEdulystLogo});

  @override
  State<ChooseLanguage> createState() => _ChooseLanguageState();
}

class _ChooseLanguageState extends State<ChooseLanguage> {
  var selected = 0;

  bool _isLoading = false;
  List<ListLanguage>? myList;
  var localeCodes = {
    'english': "en",
    'hindi': "hi",
    'kannada': "kn",
    'marathi': "mrth",
    'tamil': "tml",
    'telugu': "te",
    'bengali': "bn",
    'malyalam': 'ml',
    'arabic': 'ar',
    'odia': 'odi'
  };

  @override
  void initState() {
    super.initState();
    _getLanguage();
    setCurrentLanguage();
  }

  void setCurrentLanguage() async {
    int? currentLanId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;
    for (int i = 0; i < myList!.length; i++) {
      if (currentLanId == myList?[i].languageId) {
        selected = i;
        Preference.setInt(Preference.APP_LANGUAGE, myList![i].languageId!);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, myList![i].isPrimaryLanguage!);
        Preference.setString(
            Preference.APP_ENGLISH_NAME, myList![i].englishName.toString());
        Preference.setString(
            Preference.LANGUAGE, '${myList?[i].languageCode?.toLowerCase()}');
        context.setLocale(Locale('${myList?[i].languageCode}'));
        break;
      } else {
        selected = 0;
      }
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is MasterLanguageState) _handleResponse(state);
        },
        child: Builder(builder: (context) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, state) {
              return Scaffold(
                backgroundColor: context.backgroundColor,
                body: ScreenWithLoader(
                  isLoading: _isLoading,
                  body: SafeArea(
                      child: SizedBox(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      children: [
                        SizedBox(height: 20),
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: height(context) * 0.065,
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 30.0),
                                  child: Text(
                                    'welcome_msg',
                                    style: TextStyle(fontSize: 14),
                                  ).tr(),
                                ),
                              ),
                              if (widget.showEdulystLogo == true)
                                Transform.scale(
                                    scale: 1.2,
                                    child:
                                        appBarImagePath.split('.').last == 'svg'
                                            ? SvgPicture.asset(
                                                appBarImagePath,
                                                fit: BoxFit.cover,
                                              )
                                            : Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 80.0,
                                                    top: 2.0,
                                                    right: 80.0,
                                                    bottom: 20.0),
                                                child: Image.asset(
                                                  appBarImagePath,
                                                  //height: 150,
                                                  //width: 150,
                                                ),
                                              )),
                              SizedBox(height: 10),
                              if (APK_DETAILS['theme_image_url2'] != "")
                                APK_DETAILS['theme_image_url2']
                                            ?.split('.')
                                            .last ==
                                        'svg'
                                    ? SvgPicture.asset(
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.25,
                                        'assets/images/${APK_DETAILS['theme_image_url2']}',
                                        fit: BoxFit.cover,
                                      )
                                    : Image.asset(
                                        'assets/images/${APK_DETAILS['theme_image_url2']}',
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.25,
                                        // width: 150,
                                      ),
                              SizedBox(height: 30),
                              SizedBox(
                                height: 30,
                                child: Center(
                                  child: Text(
                                    'app_language',
                                    style: Styles.getBoldThemeStyle(context,
                                        size: 18),
                                    textAlign: TextAlign.center,
                                  ).tr(),
                                ),
                              ),
                              SizedBox(height: 10),
                              Container(
                                  height: 180,
                                  margin: EdgeInsets.symmetric(horizontal: 10),
                                  child: GridView.builder(
                                    gridDelegate:
                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                      mainAxisSpacing: 0,
                                      crossAxisSpacing: 0,
                                      childAspectRatio: 10 / 4.4,
                                      crossAxisCount: 2,
                                    ),
                                    itemCount: myList?.length ?? 0,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return languageCard(
                                          myList![index], index);
                                    },
                                  )),
                            ],
                          ),
                        ),
                        if (!widget.showEdulystLogo)
                          SizedBox(
                              height: MediaQuery.of(context).size.height * 0.2),
                        // Spacer(),
                        InkWell(
                            onTap: () {
                              if (widget.showEdulystLogo) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            SingularisLogin()));
                              } else {
                                Navigator.pop(context);
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: context.appColors.primary,
                              ),
                              margin:
                                  EdgeInsets.only(left: 25, right: 25, top: 40),
                              width: width(context),
                              height: MediaQuery.of(context).size.height *
                                  WidgetSize.AUTH_BUTTON_SIZE,
                              // decoration: BoxDecoration(
                              //     borderRadius: BorderRadius.circular(10)),
                              child: Center(
                                  child: Text(
                                'Continue_button',
                                style: Styles.regular(
                                  color: context.primaryForegroundColor,
                                ),
                              ).tr()),
                            )),
                      ],
                    ),
                  )),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  void _handleResponse(MasterLanguageState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          _isLoading = false;
          myList = state.response!.data!.listData;
          setCurrentLanguage();
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${loginState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getLanguage() {
    BlocProvider.of<HomeBloc>(context).add(MasterLanguageEvent());
  }

  Widget languageCard(langauge, index) {
    return InkWell(
      onTap: () {
        Preference.setInt(Preference.APP_LANGUAGE, langauge.languageId);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, langauge.isPrimaryLanguage);
        setState(() {
          log('${langauge.languageCode}');
          selected = index;
          // var locale = Locale('${langauge.languageCode}',
          //     '${langauge.englishName}'.substring(0, 2));

          try {
            // context.setLocale(Locale('${langauge.languageCode}'));
            context.setLocale(Locale('hi'));
            Preference.setString(
                Preference.LANGUAGE, langauge.languageCode.toLowerCase());
            Preference.setString(Preference.APP_ENGLISH_NAME,
                langauge.englishName.toLowerCase());
            // MyApp.setLocale(context,
            //     Locale('${localeCodes[langauge.englishName.toLowerCase()]}'));
          } catch (e, stack) {
            log('$stack');
          }
        });
      },
      child: Container(
          // width: double.infinity,
          width: MediaQuery.of(context).size.width * 0.4,
          height: MediaQuery.of(context).size.width * 0.2,
          // padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  width: 1,
                  color: index == selected
                      ? context.appColors.success
                      : context.appColors.darkGrey)),
          child: Stack(
            children: [
              Positioned(
                  left: 5,
                  top: 5,
                  child: SvgPicture.asset(
                    height: 16,
                    index == selected
                        ? 'assets/images/selected_lang.svg'
                        : 'assets/images/unselected_lang.svg',
                    fit: BoxFit.cover,
                  )),
              Center(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${langauge.title ?? ''}',
                    style: Styles.bold(
                        size: 18,
                        color: index == selected
                            ? context.appColors.green
                            : context.appColors.headingText),
                  ),
                  SizedBox(height: 2),
                  Text(langauge.name ?? '',
                      style: Styles.regular(
                        size: 14,
                        color: context.appColors.headingText,
                      )),
                ],
              )),
            ],
          )),
    );
  }
}
