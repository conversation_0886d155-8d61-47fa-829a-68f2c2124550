import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive/hive.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/swayam_login_request.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/auth_pages/forget_password.dart';
import 'package:masterg/pages/auth_pages/register_with_email.dart';
import 'package:masterg/pages/auth_pages/sign_up_screen.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/validation.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/notification_helper.dart';
import '../../utils/utility.dart';

class SingularisLogin extends StatefulWidget {
  const SingularisLogin({super.key});

  @override
  State<SingularisLogin> createState() => _SingularisLoginState();
}

class _SingularisLoginState extends State<SingularisLogin> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passController = TextEditingController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
  var _isLoading = false;

  final _formKey = GlobalKey<FormState>();
  final bool _autoValidation = true;
  var _isObscure = true;
  List<Menu>? menuList;
  String? deviceId;
  NotificationHelper? _notificationHelper;

  void initHive() async {
    await getApplicationDocumentsDirectory().then((value) {
      Hive.init(value.path);
      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
    });
  }

  @override
  void initState() {
    super.initState();
    initHive();
    _notificationHelper = NotificationHelper.getInstance(context);
    _notificationHelper?.setFcm();
    _notificationHelper?.getFcmToken();
    _getId();
  }

  Future<Null> _getId() async {
    var deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      var iosDeviceInfo = await deviceInfo.iosInfo;
      deviceId = iosDeviceInfo.identifierForVendor;
    } else if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;

      deviceId = androidDeviceInfo.id;
    }
  }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
    getPortfolio();
    getPiDetail();
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading...................");
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PortfolioStatedone ................... ${portfolioState.response?.toJson()}");

          portfolioState.response = portfolioState.response;

          if ('${portfolioState.response?.data.name}' != '') {
            Preference.setString(
                Preference.FIRST_NAME, '${portfolioState.response?.data.name}');
          }
          if (portfolioState.response?.data.image.contains(
                  '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
              true) {
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');
          }

          Preference.setString(Preference.USER_EMAIL,
              '${portfolioState.response?.data.portfolioSocial.first['email']}');
          Preference.setString(Preference.PHONE,
              '${portfolioState.response?.data.portfolioSocial.first['mob_num']}');

          if ('${portfolioState.response?.data.image}' != '') {
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');
          }

          Preference.setString(Preference.PROFILE_VIDEO,
              '${portfolioState.response?.data.profileVideo}');
          Preference.setInt(Preference.PROFILE_PERCENT,
              portfolioState.response!.data.profileCompletion);
          Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
              portfolioState.response!.data.resumeParserDataCount!);

          if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
              true) {
            Preference.setString(Preference.ABOUT_ME,
                '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

            Preference.setString(Preference.USER_HEADLINE,
                '${portfolioState.response?.data.portfolioProfile.first.headline}');
            Preference.setString(Preference.LOCATION,
                '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
          }
          Log.v("PortfolioState Success....................");
          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'singularis_login_name_pass', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success.................... and name is ${portfolioState.response?.data?.name}");

          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null) {
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          }
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null) {
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');
          }

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null) {
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');
          }
          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v("PI Detail Error..........................");
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'singularis_login_name_pass', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getCountry() async {
    await Utility.getCurrentLocale();
    setState(() {});
  }

  void _swayamLoginResponse(SwayamLoginState state) async {
    var loginState = state;
    setState(() async {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "Success.................... -- ${loginState.response?.toJson()}");
          if (state.response?.error?.length != 0) {
            AlertsWidget.showCustomDialog(
                context: context,
                title: loginState.response?.error?.first,
                text: "",
                icon: 'assets/images/circle_alert_fill.svg',
                showCancel: false,
                oKText: tr('ok'),
                onOkClick: () async {
                  setState(() {
                    _isLoading = false;
                  });
                });

            break;
          }

          UserSession.userToken = state.response?.data?.token;
          UserSession.email = state.response?.data?.user?.email;
          UserSession.userName = state.response?.data?.user?.name;
          UserSession.userImageUrl = state.response?.data?.user?.profileImage;
          UserSession.socialEmail = state.response?.data?.user?.email;
          Preference.setString(
              Preference.USER_TOKEN, '${state.response?.data?.token}');
          Preference.setString(
              Preference.FIRST_NAME, '${state.response?.data?.user?.name}');
          Preference.setString(
              Preference.USER_EMAIL, '${state.response?.data?.user?.email}');
          Preference.setString(
              Preference.PHONE, '${state.response?.data?.user?.mobileNo}');
          Preference.setString(Preference.PROFILE_IMAGE,
              '${state.response?.data?.user?.profileImage}');
          Preference.setInt(Preference.USER_ID,
              int.parse('${state.response?.data?.user?.id}'));

          Preference.setString(
              'interestCategory', '${state.response!.data!.user!.categoryIds}');
          Preference.setString(Preference.DEFAULT_VIDEO_URL_CATEGORY,
              '${state.response!.data!.user!.defaultVideoUrlOnCategory}');
          Preference.setString(
              Preference.ORG_URL, '${state.response!.data!.user!.orgLogo}');
          Preference.setString(Preference.ORGANIZATION_ID,
              '${state.response!.data!.user!.orgId}');
          debugPrint('seeting url to ${state.response!.data!.user!.orgLogo}');
          getBottomNavigationBar();

          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          _isLoading = false;

          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");

          AlertsWidget.alertWithOkBtn(
              context: context,
              text: loginState.response?.error?.first,
              onOkClick: () {
                setState(() {
                  _isLoading = false;
                });
              });
          FirebaseAnalytics.instance
              .logEvent(name: 'singularis_login_name_pass', parameters: {
            "ERROR": '${loginState.response?.error?.first}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          menuList = state.response!.data!.menu;
          menuList = menuList!.where((element) {
            bool containRole = element.role.toString().toLowerCase().contains(
                '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
            return containRole;
          }).toList();

          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => (int.tryParse('${a.inAppOrder}') ?? 0)
                .compareTo(int.tryParse('${b.inAppOrder}') ?? 0));

            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    HomePage(
                      bottomMenu: menuList,
                    ),
                    isMaintainState: true),
                (route) => false);
          }
          _isLoading = false;
          break;

        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${getBottomBarState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return MultiBlocListener(
          listeners: [
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is SwayamLoginState) _swayamLoginResponse(state);
              },
            ),
            BlocListener<HomeBloc, HomeState>(
              listener: (BuildContext context, state) {
                if (state is GetBottomBarState) {
                  _handelBottomNavigationBar(state);
                }
                if (state is PiDetailState) {
                  handlePiDetail(state);
                }
                if (state is PortfolioState) {
                  handlePortfolioState(state);
                }
              },
            ),
          ],
          child: Scaffold(
            backgroundColor: context.backgroundColor,
            resizeToAvoidBottomInset: true,
            key: _scaffoldKey,
            body: Builder(builder: (context) {
              return ScreenWithLoader(
                body: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: _content(),
                ),
                isLoading: _isLoading,
              );
            }),
          ),
        );
      },
    );
  }

  Widget _content() {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';
    return Form(
      key: _formKey,
      autovalidateMode:
          _autoValidation ? AutovalidateMode.always : AutovalidateMode.disabled,
      child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: 30,
              ),
              Text("welcome_msg",
                      style: Styles.regular(
                          size: 18, color: context.appColors.grey3))
                  .tr(),
              Transform.scale(
                scale: 1.2,
                child: appBarImagePath.split('.').last == 'svg'
                    ? SvgPicture.asset(
                        appBarImagePath,
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        appBarImagePath,
                        height: 40,
                        width: 150,
                      ),
              ),
              SizedBox(height: 10),
              if (APK_DETAILS['theme_image_url2'] != "")
                APK_DETAILS['theme_image_url2']?.split('.').last == 'svg'
                    ? SvgPicture.asset(
                        height: MediaQuery.of(context).size.height * 0.2,
                        'assets/images/${APK_DETAILS['theme_image_url2']}',
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        'assets/images/${APK_DETAILS['theme_image_url2']}',
                        height: MediaQuery.of(context).size.height * 0.2,
                        // width: 150,
                      ),
              _size(height: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20)),
                  gradient: LinearGradient(colors: context.gradientColors),
                ),
                height: height(context) * 0.61,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text('${tr('login')}/${tr('register')}',
                          style: Styles.bold(
                              size: 18, color: context.appColors.textWhite)),
                      _size(),
                      Text('personalised_path',
                              style: Styles.regular(
                                  size: 16, color: context.appColors.textWhite))
                          .tr(),
                      _size(height: 20),
                      _textField(
                        isEmail: true,
                        controller: _emailController,
                        hintText: tr('enter_email_address'),
                        prefixImage: 'assets/images/email_icon.png',
                        validation: validateEmail,
                      ),
                      _size(height: 13),
                      _textField(
                          controller: _passController,
                          hintText: tr('password'),
                          prefixImage: 'assets/images/lock.png',
                          obscureText: _isObscure,
                          validation: validatePassword1,
                          onEyePress: () {
                            setState(() {
                              _isObscure = !_isObscure;
                            });
                          }),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Spacer(),
                          InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ForgotPassword()));
                              },
                              child: Text(
                                "forgot_password",
                                style: Styles.regularWhite(),
                              ).tr())
                        ],
                      ),
                      _size(height: 20),
                      Column(
                        children: [
                          InkWell(
                            onTap: () {
                              Preference.setBool(
                                  Preference.LOGGEDIN_WITH_EMAIL, true);

                              String? value = validateEmail(
                                  _emailController.text.toString().trim());
                              String? pass = validatePassword(
                                  _passController.text.toString().trim());
                              if (value != null) {
                                AlertsWidget.showCustomDialog(
                                    context: context,
                                    title: value,
                                    text: "",
                                    icon: 'assets/images/circle_alert_fill.svg',
                                    showCancel: false,
                                    oKText: tr('ok'),
                                    onOkClick: () async {});
                              } else if (pass != null) {
                                AlertsWidget.showCustomDialog(
                                    context: context,
                                    title: pass,
                                    text: "",
                                    icon: 'assets/images/circle_alert_fill.svg',
                                    showCancel: false,
                                    oKText: tr('ok'),
                                    onOkClick: () async {});
                              } else
                                BlocProvider.of<AuthBloc>(context).add(
                                    PvmSwayamLogin(
                                        request: SwayamLoginRequest(
                                            deviceToken:
                                                UserSession.firebaseToken,
                                            device_id: deviceId,
                                            locale: Preference.getString(
                                                    Preference.APP_ENGLISH_NAME)
                                                .toString(),
                                            deviceType: Utility.getDeviceType()
                                                .toString(),
                                            userName: _emailController.text
                                                .toString()
                                                .trim(),
                                            password: _passController.text
                                                .toString()
                                                .trim())));
                            },
                            child: Container(
                                margin: EdgeInsets.symmetric(vertical: 12),
                                width: double.infinity,
                                height:
                                    MediaQuery.of(context).size.height * 0.06,
                                decoration: BoxDecoration(
                                    color: context.surfaceColor,
                                    borderRadius: BorderRadius.circular(10)),
                                child: ShaderMask(
                                  blendMode: BlendMode.srcIn,
                                  shaderCallback: (Rect bounds) {
                                    return LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: <Color>[
                                          context.appColors.gradientLeft,
                                          context.appColors.gradientRight
                                        ]).createShader(bounds);
                                  },
                                  child: Center(
                                    child: Text(
                                      'login',
                                      style: Styles.regular(
                                        size: 16,
                                        color: context.appColors.textWhite,
                                      ),
                                    ).tr(),
                                  ),
                                )),
                          ),
                          Row(children: [
                            Icon(
                              Utility().isRTL(context)
                                  ? Icons.arrow_back_ios_sharp
                                  : Icons.arrow_back_ios_new,
                              color: context.primaryForegroundColor,
                              size: 15,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pushReplacement(
                                    context, NextPageRoute(SignUpScreen()));
                              },
                              child: Text(
                                'login_with_mobile',
                                style: Styles.regular(
                                    size: 12,
                                    color: context.appColors.textWhite),
                              ).tr(),
                            ),
                            Expanded(
                              child: SizedBox(
                                width: 20,
                              ),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            RegisterScreen()));
                              },
                              child: RichText(
                                text: TextSpan(
                                  text: tr('not_registered'),
                                  style: Styles.regular(
                                      size: 14,
                                      color: context.appColors.textWhite),
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: tr('register_now'),
                                        style: Styles.bold(
                                            size: 14,
                                            color:
                                                context.appColors.textWhite)),
                                  ],
                                ),
                              ),
                            ),
                          ]),
                          SizedBox(
                            height: height(context) * 0.06,
                          ),
                          SizedBox(
                            height: height(context) * 0.05,
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text('clicking_continue',
                                          style: Styles.regular(
                                              size: 12,
                                              color: context
                                                  .primaryForegroundColor))
                                      .tr(),
                                  SizedBox(width: 10),
                                  InkWell(
                                    onTap: () {
                                      Navigator.push(
                                          context,
                                          NextPageRoute(
                                              TermsAndCondition(
                                                  url: APK_DETAILS[
                                                      'policy_url']),
                                              isMaintainState: false));
                                    },
                                    child: Text(
                                      'terms_conditions',
                                      style: Styles.regular(
                                          size: 12,
                                          color: context.appColors.textWhite),
                                    ).tr(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  SizedBox _size({double height = 5}) {
    return SizedBox(
      height: height,
    );
  }

  Widget _textField({
    bool isEmail = false,
    TextEditingController? controller,
    String? hintText,
    required String prefixImage,
    bool obscureText = false,
    required Function(String) validation,
    Function()? onEyePress,
  }) {
    return Container(
      color: context.appColors.surface.withValues(alpha: 0.2),
      child: TextFormField(
        cursorColor: context.appColors.textWhite,
        style: Styles.regularWhite(),
        controller: controller,
        obscureText: obscureText,
        decoration: InputDecoration(
          hintText: hintText,
          isDense: true,
          errorStyle: TextStyle(
            color: context.primaryForegroundColor,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.all(5),
            child: isEmail == true
                ? Icon(
                    Icons.email_outlined,
                    color: context.appColors.textWhite,
                  )
                : Image.asset(
                    prefixImage,
                    height: 32,
                    width: 32,
                  ),
          ),
          suffixIcon: Visibility(
            visible: onEyePress != null,
            child: TapWidget(
              onTap: () {
                onEyePress!();
              },
              child: Padding(
                  padding: const EdgeInsets.only(right: 5),
                  child: !obscureText
                      ? Icon(
                          Icons.remove_red_eye_outlined,
                          color: context.appColors.textWhite,
                        )
                      : Icon(
                          Icons.visibility_off,
                          color: context.appColors.textWhite,
                        )),
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide:
                  BorderSide(color: context.appColors.textWhite, width: 1)),
          hintStyle: Styles.regular(
              size: 18,
              color: context.appColors.textWhite.withValues(alpha: 0.7)),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 0, vertical: 13),
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide:
                  BorderSide(color: context.appColors.textWhite, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide:
                  BorderSide(color: context.appColors.textWhite, width: 1)),
          disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide:
                  BorderSide(color: context.appColors.textWhite, width: 1)),
          errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              borderSide:
                  BorderSide(color: context.appColors.textWhite, width: 1)),
        ),
      ),
    );
  }
}
