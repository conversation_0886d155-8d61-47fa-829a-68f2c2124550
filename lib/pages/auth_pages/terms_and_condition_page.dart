import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class TermsAndCondition extends StatefulWidget {
  final String? url;
  final String? title;
  final bool? appBarEnable;
  const TermsAndCondition({
    super.key,
    this.url,
    this.title,
    this.appBarEnable = true,
  });

  @override
  State<TermsAndCondition> createState() => _TermsAndConditionState();
}

class _TermsAndConditionState extends State<TermsAndCondition> {
  bool isLoading = true;
  final _key = UniqueKey();
  late WebViewController _controller;

  @override
  void initState() {
    super.initState();
    // No need to set WebView.platform for latest webview_flutter, handled internally.
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(context.appColors.surface)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                isLoading = false;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.contains('.png') ||
                request.url.contains('.pdf') ||
                request.url.contains('.doc') ||
                request.url.contains('.xls')) {
              _shareQrCode(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url ?? ''));
  }

  Future<void> _shareQrCode(String qrCodeUrl) async {
    try {
      // Download the image
      final response = await http.get(Uri.parse(qrCodeUrl));
      if (response.statusCode == 200) {
        final documentDirectory = await getApplicationDocumentsDirectory();
        final file = File('${documentDirectory.path}/qr_code.png');
        file.writeAsBytesSync(response.bodyBytes);

        // Share the file using Share.shareXFiles()
        final xFile = XFile(file.path);
        await SharePlus.instance.share(ShareParams(files: [xFile]));
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.surface,
      appBar: widget.appBarEnable == true
          ? AppBar(
              elevation: 0.5,
              leading: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.arrow_back,
                    color: context.appColors.textBlack,
                  )),
              title: Text(
                '${widget.title}'
                    .replaceAll('Singularis', '${APK_DETAILS['app_name']}'),
                style: Styles.semibold(
                  color: context.appColors.textBlack,
                ),
              ),
              backgroundColor: context.appColors.surface)
          : null,
      body: Stack(
        children: [
          WebViewWidget(
            key: _key,
            controller: _controller,
          ),
          if (isLoading) ScreenWithLoader(isLoading: isLoading),
        ],
      ),
    );
  }
}
