import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:lottie/lottie.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:material_dialogs/dialogs.dart';
import 'package:material_dialogs/widgets/buttons/icon_button.dart';

import '../../../blocs/auth_bloc.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/request/auth_request/signup_request.dart';
import '../../../data/models/response/auth_response/user_session.dart';
import '../../../local/pref/Preference.dart';
import '../../../utils/log.dart';
import '../../../utils/config.dart';
import '../../../utils/utility.dart';
import '../../custom_pages/screen_with_loader.dart';
import '../../custom_pages/alert_widgets/alerts_widget.dart';
import '../../custom_pages/custom_widgets/next_page_routing.dart';
import '../../onboarding_pages/onboarding_select_intreset.dart';
import '../terms_and_condition_page.dart';

class RegisterScreenV1 extends StatefulWidget {
  final List<String>? iAmAItemsList;
  const RegisterScreenV1({super.key, this.iAmAItemsList});

  @override
  State<RegisterScreenV1> createState() => _RegisterScreenV1State();
}

class _RegisterScreenV1State extends State<RegisterScreenV1> {
  bool isFill = false;
  TextEditingController emailController = TextEditingController();
  TextEditingController otpController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confPassController = TextEditingController();
  final fullNameController = TextEditingController();
  final phoneController = TextEditingController();
  final loginTypeController = TextEditingController(
      text: '${Preference.getString(Preference.USER_LOGIN_TYPE)}');

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool isCodeSent = false;
  bool codeVerified = false;
  bool _isLoading = false;
  // bool _emailTrue = false;
  // bool _codeVerifiedTrue = false; //change for show default //old --- false
  int endTime = 0;
  bool verifiedAutoFocus = true;
  bool? checkedValue = false;
  String? countryCodeReq;

  String? selectedValue;
  // var _isObscure = true;
  // var _isObscure1 = true;

  @override
  void initState() {
    super.initState();
  }

  void fieldValidation() {
    if (!_formKey.currentState!.validate()) return;
    if (checkedValue == true) {
      /*if(emailController.text.toString().contains('mec.edu.om')){
              getParticipate(
                  name: fullNameController.text.toString(),
                  email: emailController.text.toString(),
                  mobileNo: '',
                  programId: 0,
                  isMobile: 1);

            }else{
              saveChanges();
            }*/
      saveChanges();
    } else {
      AlertsWidget.showCustomDialog(
          context: context,
          title: tr('agree_tnc'),
          text: "",
          icon: 'assets/images/circle_alert_fill.svg',
          showCancel: false,
          oKText: tr('ok'),
          onOkClick: () async {});
    }
    /* if (newPassController.text.isEmpty || confPassController.text.isEmpty) {
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('password_confirm_password'));
    } else {
      if (newPassController.text.toString().length > 7) {
        if (newPassController.text == confPassController.text) {
          if(checkedValue == true){
            saveChanges();

          }else{
            AlertsWidget.showCustomDialog(
                context: context,
                title: tr('agree_tnc'),
                text: "",
                icon: 'assets/images/circle_alert_fill.svg',
                showCancel: false,
                oKText: tr('ok'),
                onOkClick: () async {});
          }
        } else {
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('password_notmatched'));
        }
      } else {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('security_password_length'));
      }
    }*/
  }

  // void _obscuredConformPass() {
  //   setState(() {
  //     _isObscure = !_isObscure;
  //   });
  // }

  // void _obscuredPass() {
  //   setState(() {
  //     _isObscure1 = !_isObscure1;
  //   });
  // }

  void sendEmailVerificationCode(String email) {
    BlocProvider.of<HomeBloc>(context)
        .add(EmailCodeSendEvent(email: email, isSignup: 1));
  }

  void verifyOtp(String email, String otp) {
    BlocProvider.of<HomeBloc>(context)
        .add(VerifyEmailCodeEvent(email: email, code: otp));
  }

  Future<void> saveChanges() async {
    if (!_formKey.currentState!.validate()) return;
    if (checkedValue == true) {
      var req = SignUpRequest(
          profilePic: '',
          firstName: fullNameController.text.toString(),
          mobileNo: phoneController.text.toString(),
          alternateMobileNo: phoneController.text.toString(),
          emailAddress: emailController.text.toString(),
          username: emailController.text.toString(),
          firmName: '',
          lastName: '',
          gender: '',
          dateOfBirth: '',
          dbCode: '0',
          whoiam: Preference.getString(Preference.REGISTER_TYPE),
          password: confPassController.text.toString(),
          locale: Preference.getString(Preference.APP_ENGLISH_NAME).toString(),
          countryCode: countryCodeReq ?? '+968');
      BlocProvider.of<AuthBloc>(context).add(SignUpEvent(request: req));
    } else {
      AlertsWidget.showCustomDialog(
          context: context,
          title: tr('agree_tnc'),
          text: "",
          icon: 'assets/images/circle_alert_fill.svg',
          showCancel: false,
          oKText: tr('ok'),
          onOkClick: () async {});
    }
  }

  void _handleResponse(SignUpState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          _isLoading = false;

          try {
            Preference.setString(
                Preference.USER_TOKEN, '${state.response!.data!.token}');

            Preference.setString(Preference.PROFILE_IMAGE,
                '${state.response!.data!.user!.profileImage}');
            Preference.setInt(
                Preference.USER_ID, loginState.response!.data!.user!.id!);
            Preference.setString(Preference.DESIGNATION,
                '${loginState.response!.data!.user!.designation}');

            Preference.setString(Preference.PROFILE_IMAGE,
                '${loginState.response!.data!.user!.profileImage}');
            Preference.setString(Preference.DESIGNATION,
                '${loginState.response!.data!.user!.designation}');
            Preference.setString(Preference.DEFAULT_VIDEO_URL_CATEGORY,
                '${state.response!.data!.user!.defaultVideoUrlOnCategory}');
            Preference.setString(
                Preference.ORG_URL, '${state.response!.data!.user!.orgLogo}');
            Preference.setString(Preference.ORGANIZATION_ID,
                '${state.response!.data!.user!.orgId}');

            Preference.setString(
                Preference.ROLE, '${state.response!.data!.user!.role}');
            Preference.setString('interestCategory',
                '${state.response!.data!.user!.categoryIds}');
            Preference.setString(
                Preference.USERNAME, '${state.response!.data!.user!.name}');
            Preference.setString(
                Preference.FIRST_NAME, '${state.response!.data!.user!.name}');

            UserSession.userToken = state.response!.data!.token;
            UserSession.email = state.response!.data!.user!.email;
            UserSession.userImageUrl = state.response!.data!.user!.profileImage;
            UserSession.socialEmail = state.response!.data!.user!.email;
            Preference.setString(
                Preference.USER_EMAIL, '${state.response!.data!.user!.email}');
          } catch (e, stack) {
            debugPrint("stack: $stack");
          } finally {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => OnboardingSelecteInterestPage(
                          backEnable: false,
                          moveToHome: true,
                        )));
          }
          // doLogin();
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          _isLoading = false;

          if (loginState.error.toString().contains('Congratulations')) {
            Dialogs.bottomMaterialDialog(
              msg: 'you_registered_mec'.tr(),
              msgStyle: TextStyle(),
              title: 'congratulations'.tr(),
              color: context.appColors.surface,
              lottieBuilder: Lottie.asset(
                'assets/cong_example.json',
                fit: BoxFit.contain,
              ),
              context: context,
              actions: [
                Container(
                  height: height(context) * 0.06,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    gradient: checkedValue == true
                        ? LinearGradient(colors: [
                            context.appColors.gradientLeft,
                            context.appColors.gradientRight,
                          ])
                        : LinearGradient(colors: [
                            context.appColors.unselectedButton,
                            context.appColors.unselectedButton,
                          ]),
                  ),
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                    child: Center(
                      child: Text(
                        'continue',
                        style: Styles.regular(
                          size: 16,
                          color: context.appColors.textWhite,
                        ),
                      ).tr(),
                    ),
                  ),
                ),
              ],
            );
          } else {
            AlertsWidget.showCustomDialog(
                context: context,
                title: loginState.error,
                text: "",
                icon: 'assets/images/circle_alert_fill.svg',
                showCancel: false,
                oKText: tr('ok'),
                onOkClick: () async {});
          }
          FirebaseAnalytics.instance
              .logEvent(name: 'self_details_page', parameters: {
            "ERROR": '${loginState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleEmailCodeSendResponse(EmailCodeSendState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("EmailCodeSend Suuuuuuuus....................");
          isCodeSent = true;
          _isLoading = false;
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('verify_code_on_email'));
          endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error emailCodeSendState ..........................${emailCodeSendState.error}");
          Utility.showSnackBar(
              scaffoldContext: context,
              message: emailCodeSendState.error ?? 'something_went_wrong'.tr());
          _isLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'register_page', parameters: {
            "ERROR": '${emailCodeSendState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleVerifyOtp(VerifyEmailCodeState state) {
    var emailCodeSendState = state;
    setState(() {
      switch (emailCodeSendState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          codeVerified = false;
          break;
        case ApiStatus.SUCCESS:
          Log.v("VerifyOtp Suuuuuuuus....................");
          codeVerified = true;
          // _codeVerifiedTrue = true;
          _isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error handleVerifyOtp ..........................${emailCodeSendState.error}");
          _isLoading = false;
          codeVerified = false;
          Utility.showSnackBar(
              scaffoldContext: context, message: tr('invalid_code'));
          FirebaseAnalytics.instance
              .logEvent(name: 'register_page', parameters: {
            "ERROR": '${emailCodeSendState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getParticipate(
      {String? name,
      String? email,
      String? mobileNo,
      int? isMobile,
      int? programId,
      int? countryCode}) {
    BlocProvider.of<HomeBloc>(context).add(ParticipateEvent(
        name: name,
        email: email,
        mobileNo: mobileNo,
        programId: programId,
        isMobile: isMobile,
        countryCode: countryCode));
  }

  void _handleParticipateState(ParticipateState state) {
    try {
      var loginState = state;
      setState(() {
        switch (loginState.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading...................ParticipateState.");
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            Log.v("Success....................ParticipateState");
            //registerResponse = state.response;
            Dialogs.bottomMaterialDialog(
              msg: 'you_registered_mec'.tr(),
              title: 'congratulations'.tr(),
              color: context.appColors.surface,
              lottieBuilder: Lottie.asset(
                'assets/cong_example.json',
                fit: BoxFit.contain,
              ),
              context: context,
              actions: [
                IconsButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  },
                  text: 'Continue_button'.tr(),
                  iconData: Icons.done,
                  color: Colors.blue,
                  textStyle:
                      TextStyle(color: context.appColors.primaryForeground),
                  iconColor: context.appColors.primaryForeground,
                ),
              ],
            );

            _isLoading = false;
            break;
          case ApiStatus.ERROR:
            Log.v("Error...................ParticipateState");
            //Utility.showSnackBar(scaffoldContext: context, message: state.error);
            Dialogs.bottomMaterialDialog(
              msg: 'you_registered_mec'.tr(),
              title: 'congratulations'.tr(),
              color: context.appColors.surface,
              lottieBuilder: Lottie.asset(
                'assets/cong_example.json',
                fit: BoxFit.contain,
              ),
              context: context,
              actionsBuilder: (context) => IconsButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                text: 'Continue',
                iconData: Icons.done,
                color: Colors.blue,
                textStyle:
                    TextStyle(color: context.appColors.primaryForeground),
                iconColor: context.appColors.primaryForeground,
              ),
            );
            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e, stacktrace) {
      Log.v("$stacktrace: $e");

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is EmailCodeSendState) {
                _handleEmailCodeSendResponse(state);
              }
              if (state is VerifyEmailCodeState) {
                handleVerifyOtp(state);
              }
              if (state is ParticipateState) {
                _handleParticipateState(state);
              }
            },
            child: BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is SignUpState) _handleResponse(state);
              },
              child: Scaffold(
                appBar: AppBar(
                  centerTitle: true,
                  backgroundColor: Colors.transparent,
                  title: Text(
                    "register_your_self",
                    style: Styles.semibold(
                        color: context.appColors.textBlack, size: 16),
                  ).tr(),
                  leading: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Utility().isRTL(context)
                          ? Icons.arrow_back_ios_sharp
                          : Icons.arrow_back_ios_new_outlined,
                      color: context.appColors.textBlack,
                    ),
                  ),
                  elevation: 0.0,
                ),
                body: SafeArea(
                    child: ScreenWithLoader(
                  isLoading: _isLoading,
                  body: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(
                            height: height(context) * 0.04,
                            child: Text('welcome_msg',
                                    style: Styles.regular(
                                        size: 18,
                                        color:
                                            context.appColors.subHeadingTitle))
                                .tr(),
                          ),

                          APK_DETAILS['package_name'] == 'com.singularis.mesc'
                              ? SizedBox(
                                  height: 20,
                                )
                              : SizedBox(),
                          SvgPicture.asset(
                            appBarImagePath,
                            fit: BoxFit.cover,
                            width: width(context) * 0.5,
                          ),
                          SizedBox(height: 30),

                          if (APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec" &&
                                  Preference.getString(
                                          Preference.USER_LOGIN_TYPE_LOCALE) ==
                                      'Mec Student' ||
                              Preference.getString(
                                      Preference.USER_LOGIN_TYPE_LOCALE) ==
                                  'Mec Faculty/Staff' ||
                              Preference.getString(
                                      Preference.USER_LOGIN_TYPE_LOCALE) ==
                                  'Mec Alumni') ...[
                            const SizedBox(height: 30),
                            Padding(
                              padding: Utility().isRTL(context)
                                  ? EdgeInsets.only(right: 20.0)
                                  : EdgeInsets.only(left: 20.0),
                              child: Row(
                                children: [
                                  ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.person_add_sharp,
                                      size: 20,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Text(
                                    'iam_a',
                                    style: Styles.getTextRegularStyle(context),
                                  ).tr(),
                                ],
                              ),
                            ),
                            Padding(
                              padding: Utility().isRTL(context)
                                  ? EdgeInsets.only(
                                      top: 8.0, right: 16, left: 16)
                                  : EdgeInsets.only(
                                      top: 8.0, left: 16, right: 16),
                              child: SizedBox(
                                height: height(context) * 0.1,
                                child: DropdownButtonFormField2<String>(
                                  isExpanded: true,
                                  decoration: InputDecoration(
                                    // Add Horizontal padding using menuItemStyleData.padding so it matches
                                    // the menu padding when button's width is not specified.
                                    contentPadding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    // Add more decoration..
                                  ),
                                  hint: const Text(
                                    'select',
                                    style: TextStyle(fontSize: 14),
                                  ).tr(),
                                  items: widget.iAmAItemsList
                                      ?.map((item) => DropdownMenuItem<String>(
                                            value: item,
                                            child: Text(
                                              item,
                                              style: const TextStyle(
                                                fontSize: 14,
                                              ),
                                            ),
                                          ))
                                      .toList(),
                                  validator: (value) {
                                    if (value == null) {
                                      return 'please_select_im'.tr();
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    //Do something when selected item is changed.
                                    setState(() {
                                      selectedValue = value.toString();
                                    });
                                    debugPrint(
                                        'selectedValue====$selectedValue');
                                  },
                                  onSaved: (value) {
                                    selectedValue = value.toString();
                                  },
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(right: 8),
                                  ),
                                  iconStyleData: const IconStyleData(
                                    icon: Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.black45,
                                    ),
                                    iconSize: 24,
                                  ),
                                  dropdownStyleData: DropdownStyleData(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                  menuItemStyleData: const MenuItemStyleData(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                  ),
                                ),
                              ),
                            ),
                          ] else ...[
                            Padding(
                              padding: Utility().isRTL(context)
                                  ? EdgeInsets.only(right: 20.0)
                                  : EdgeInsets.only(left: 20.0),
                              child: Row(
                                children: [
                                  ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.person_add_sharp,
                                      size: 20,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Text(
                                    'iam_a',
                                    style: Styles.getTextRegularStyle(context),
                                  ).tr(),
                                ],
                              ),
                            ),
                            Padding(
                              padding: Utility().isRTL(context)
                                  ? EdgeInsets.only(
                                      top: 8.0, right: 16, left: 16)
                                  : EdgeInsets.only(
                                      top: 8.0, left: 16, right: 16),
                              child: SizedBox(
                                height: height(context) * 0.1,
                                child: TextFormField(
                                  controller: loginTypeController,
                                  autofocus: true,
                                  style: Styles.regular(
                                    color:
                                        context.appColors.headingPrimaryColor,
                                    size: 14,
                                  ),
                                  decoration: InputDecoration(
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10.0),
                                      borderSide: BorderSide(
                                        color: Color(0xffE5E5E5),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10.0),
                                      borderSide: BorderSide(
                                        color: Color(0xffE5E5E5),
                                        width: 1.5,
                                      ),
                                    ),
                                    hintText: tr('enter_full_name'),
                                    hintStyle: TextStyle(
                                      color: Color(0xffE5E5E5),
                                    ),
                                    isDense: true,
                                    prefixIconConstraints: BoxConstraints(
                                        minWidth: 0, minHeight: 0),
                                    border: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            width: 1,
                                            color: context.appColors.textWhite),
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    helperStyle: Styles.regular(
                                        size: 14,
                                        color: context.appColors.grey3
                                            .withValues(alpha: 0.1)),
                                    counterText: "",
                                  ),
                                  enabled: false,
                                  onChanged: (value) {
                                    setState(() {});
                                  },
                                  validator: (value) {
                                    if (value!.isEmpty) {
                                      return tr('enter_full_name');
                                    }

                                    return null;
                                  },
                                ),
                              ),
                            ),
                          ],
                          // SizedBox(height: 30),

                          //Full Name
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0)
                                : EdgeInsets.only(left: 20.0),
                            child: Row(
                              children: [
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.perm_identity,
                                      size: 20,
                                    )),
                                SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  "${tr('full_name')} ",
                                  style: Styles.getTextRegularStyle(context),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(top: 8.0, right: 16, left: 16)
                                : EdgeInsets.only(
                                    top: 8.0, left: 16, right: 16),
                            child: SizedBox(
                              height: height(context) * 0.1,
                              child: TextFormField(
                                controller: fullNameController,
                                autofocus: true,
                                style: Styles.regular(
                                  color: context.appColors.headingPrimaryColor,
                                  size: 14,
                                ),
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                      width: 1.5,
                                    ),
                                  ),
                                  hintText: tr('enter_full_name'),
                                  hintStyle: TextStyle(
                                    color: Color(0xffE5E5E5),
                                  ),
                                  isDense: true,
                                  prefixIconConstraints:
                                      BoxConstraints(minWidth: 0, minHeight: 0),
                                  border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1,
                                          color: context.appColors.textWhite),
                                      borderRadius: BorderRadius.circular(10)),
                                  helperStyle: Styles.regular(
                                      size: 14,
                                      color: context.appColors.grey3
                                          .withValues(alpha: 0.1)),
                                  counterText: "",
                                ),
                                onChanged: (value) {
                                  setState(() {});
                                },
                                validator: (value) {
                                  if (value!.isEmpty) {
                                    return tr('enter_full_name');
                                  }

                                  return null;
                                },
                              ),
                            ),
                          ),

                          ///Email
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0, top: 0.0)
                                : EdgeInsets.only(left: 20.0, top: 0.0),
                            child: Row(
                              children: [
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.email_outlined,
                                      size: 20,
                                    )),
                                SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  "${tr('email')} ",
                                  style: Styles.getTextRegularStyle(context),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(top: 8.0, right: 16, left: 16)
                                : EdgeInsets.only(
                                    top: 8.0, left: 16, right: 16),
                            child: SizedBox(
                              height: height(context) * 0.1,
                              child: TextFormField(
                                cursorColor: context.appColors.gradientRight,
                                controller: emailController,
                                style: Styles.regular(
                                  color: context.appColors.headingPrimaryColor,
                                  size: 14,
                                ),
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: Color(0xffE5E5E5),
                                      width: 1.5,
                                    ),
                                  ),
                                  suffix: isCodeSent
                                      ? Container(
                                          padding: EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: context.appColors.green,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.done,
                                            size: 12,
                                            color: context
                                                .appColors.primaryForeground,
                                          ),
                                        )
                                      : SizedBox() /*GestureDetector(
                                    onTap: () {
                                      codeVerified = false;
                                      // _codeVerifiedTrue = false;
                                      otpController.clear();
                                      newPassController.clear();
                                      confPassController.clear();
                                      if (emailController
                                          .value.text.isEmpty) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                          content:
                                          Text('plz_enter_email').tr(),
                                        ));
                                      // } else if (_emailTrue == false) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                          content: Text('enter_valid_email')
                                              .tr(),
                                        ));
                                      } else {
                                        sendEmailVerificationCode(emailController.value.text);
                                      }
                                    },
                                    child: GradientText(
                                      tr('verify_code'),
                                      style: Styles.getRegularThemeStyle(context,size: 14),
                                      // colors: _emailTrue == true
                                          ? [
                                        context.appColors
                                            .gradientLeft,
                                        context.appColors
                                            .gradientRight
                                      ]
                                          : [
                                        context.appColors
                                            .unselectedButton,
                                        context.appColors
                                            .unselectedButton
                                      ],
                                    ),
                                  )*/
                                  ,
                                  hintText: '<EMAIL>',
                                  hintStyle: TextStyle(
                                    color: Color(0xffE5E5E5),
                                  ),
                                  isDense: true,
                                  prefixIconConstraints:
                                      BoxConstraints(minWidth: 0, minHeight: 0),
                                  border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1,
                                          color: context.appColors.textWhite),
                                      borderRadius: BorderRadius.circular(10)),
                                  helperStyle: Styles.regular(
                                      size: 14,
                                      color: context.appColors.grey3
                                          .withValues(alpha: 0.1)),
                                  counterText: "",
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    if (!RegExp(
                                            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                        .hasMatch(value)) {
                                      // _emailTrue = false;
                                      isCodeSent = false;
                                      otpController.clear();
                                      newPassController.clear();
                                      confPassController.clear();
                                    } else {
                                      // _emailTrue = true;
                                      otpController.clear();
                                      newPassController.clear();
                                      confPassController.clear();
                                    }
                                  });
                                },
                                validator: (value) {
                                  if (value == '') {
                                    return tr('email_required');
                                  }
                                  int index = value?.length as int;

                                  if (value![index - 1] == '.') {
                                    return tr('email_address_error');
                                  }

                                  if (!RegExp(
                                          r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                      .hasMatch(value)) {
                                    return tr('email_address_error');
                                  }

                                  isCodeSent = true;
                                  return null;
                                },
                              ),
                            ),
                          ),

                          ///Re-Send Code
                          /*isCodeSent == true
                              ? Row(
                            children: [
                              Expanded(
                                child: SizedBox(),
                              ),
                              CountdownTimer(
                                endTime: endTime,
                                widgetBuilder:
                                    (_, CurrentRemainingTime? time) {
                                  return Padding(
                                    padding: Utility().isRTL(context)
                                        ? EdgeInsets.only(
                                        left: 20.0, bottom: 15.0)
                                        : EdgeInsets.only(
                                        right: 20.0, bottom: 15.0),
                                    child: RichText(
                                      text: TextSpan(
                                          text: '',
                                          style: TextStyle(
                                            fontSize: 3,
                                          ),
                                          children: <TextSpan>[
                                            time == null
                                                ? TextSpan(
                                                text: tr('resend_code'),
                                                recognizer:
                                                TapGestureRecognizer()
                                                  ..onTap = () {
                                                    // _emailTrue =
                                                    false;
                                                    isCodeSent =
                                                    false;
                                                    codeVerified =
                                                    false;
                                                    // _codeVerifiedTrue =
                                                    false;
                                                    otpController
                                                        .clear();
                                                    newPassController
                                                        .clear();
                                                    confPassController
                                                        .clear();
                  
                                                    sendEmailVerificationCode(
                                                        emailController
                                                            .value
                                                            .text);
                                                  },
                                                style: Styles.regular(
                                                    size: 12,
                                                    color:
                                                    context.appColors
                                                      .textBlack))
                                                : TextSpan(
                                                text:
                                                tr('resend_app_secs') +
                                                    ' ${time.sec} ' +
                                                    tr('secs'),
                                                style: Styles.regular(
                                                    size: 12,
                                                    color:
                                                    context.appColors
                                                      .textBlack)),
                                          ]),
                                    ),
                                  );
                                },
                              ),
                            ],
                          )
                              : SizedBox(),*/

                          ///enter email Code field--
                          /*isCodeSent == true
                              ? Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0)
                                : EdgeInsets.only(left: 20.0),
                            child: Row(
                              children: [
                                Text(
                                  tr('code_received_mail'),
                                  style: Styles.getTextRegularStyle(context),
                                ),
                              ],
                            ),
                          )
                              : SizedBox(),
                          isCodeSent == true
                              ? Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(
                                top: 8.0, right: 16, left: 16)
                                : EdgeInsets.only(
                                top: 8.0, left: 16, right: 16),
                            child: Container(
                              height: height(context) * 0.01,
                              child: Stack(
                                children: [
                                  TextFormField(
                                    obscureText: false,
                                    keyboardType: TextInputType.number,
                                    cursorColor: codeVerified == false
                                        ? context.appColors.gradientRight
                                        : context.appColors.primaryForeground,
                                    controller: otpController,
                                    style: Styles.otp(
                                        color: Colors.black,
                                        size: 14,
                                        letterSpacing: 40),
                                    maxLength: 4,
                                    onChanged: (value) {
                                      setState(() {
                                        if (value.length == 4) {
                                          codeVerified = true;
                                        } else {
                                          codeVerified = false;
                                        }
                                      });
                                    },
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                        BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                        BorderRadius.circular(10.0),
                                        borderSide: BorderSide(
                                          color: Color(0xffE5E5E5),
                                          width: 1.5,
                                        ),
                                      ),
                                      fillColor: Color(0xffE5E5E5),
                                      hintText: '••••',
                                      hintStyle: TextStyle(
                                        color: Colors.black,
                                      ),
                                      isDense: true,
                                      prefixIconConstraints: BoxConstraints(
                                          minWidth: 0, minHeight: 0),
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              width: 1,
                                              color: context.appColors.white),
                                          borderRadius:
                                          BorderRadius.circular(10)),
                                      helperStyle: Styles.regular(
                                          size: 14,
                                          color: context.appColors.grey3
                                              .withValues(alpha:0.1)),
                                    ),
                                  ),
                                  Positioned(
                                    right: Utility().isRTL(context)
                                        ? null
                                        : 10,
                                    left: Utility().isRTL(context)
                                        ? 10
                                        : null,
                                    top: 14,
                                    // child: _codeVerifiedTrue
                                        ? Container(
                                      padding: EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: context.appColors.green,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.done,
                                        size: 12,
                                        color: context.appColors.primaryForeground,
                                      ),
                                    )
                                        : GestureDetector(
                                      onTap: () {
                                        if (otpController
                                            .value.text.isEmpty) {
                                          ScaffoldMessenger.of(
                                              context)
                                              .showSnackBar(SnackBar(
                                            content: Text(
                                                'enter_verification_code')
                                                .tr(),
                                          ));
                                        } else if (codeVerified ==
                                            false) {
                                          ScaffoldMessenger.of(
                                              context)
                                              .showSnackBar(SnackBar(
                                            content: Text(
                                                'valid_four_digit_code')
                                                .tr(),
                                          ));
                                        } else {
                                          verifyOtp(
                                              emailController
                                                  .value.text,
                                              otpController
                                                  .value.text);
                                        }
                                      },
                                      child: GradientText(
                                        tr('verify_otp'),
                                        style:
                                        Styles.getRegularThemeStyle(context,size: 14),
                                        colors: codeVerified == true
                                            ? [
                                          context.appColors
                                              .gradientLeft,
                                          context.appColors
                                              .gradientRight,
                                        ]
                                            : [
                                          context.appColors
                                              .unselectedButton,
                                          context.appColors
                                              .unselectedButton,
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          )
                              : SizedBox(),*/

                          ///Mobile Number

                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0, top: 5.0)
                                : EdgeInsets.only(left: 20.0, top: 5.0),
                            child: Row(
                              children: [
                                ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (Rect bounds) {
                                      return LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight
                                          ]).createShader(bounds);
                                    },
                                    child: Icon(
                                      Icons.phone_android_rounded,
                                      size: 20,
                                    )),
                                SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  "${tr('mobile')} ",
                                  style: Styles.getTextRegularStyle(context),
                                ),
                              ],
                            ),
                          ),

                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: 18,
                              ),
                              child: IntlPhoneField(
                                onCountryChanged: (value) {
                                  // Preference.setString(Preference.COUNTRY_CODE, value.dialCode);
                                  setState(() {
                                    countryCodeReq = value.dialCode;
                                    debugPrint(
                                        'country code is ${value.dialCode} ');
                                  });
                                },
                                initialCountryCode: 'OM',

                                // maxLength:
                                // APK_DETAILS["package_name"] ==
                                //     "com.singulariswow.mec"
                                //     ? 8
                                //     : 10,
                                cursorColor: context.appColors.textBlack,
                                autofocus: false,
                                controller: phoneController,
                                keyboardType: TextInputType.number,
                                style: Styles.bold(
                                  color: context.appColors.headingPrimaryColor,
                                  size: 14,
                                ),
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.deny(
                                      RegExp(r'^0+')), // Deny input of zero
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'[0-9]')),
                                ],
                                // inputFormatters: <TextInputFormatter>[
                                //   FilteringTextInputFormatter.allow(
                                //       RegExp(r'[0-9]')),
                                // ],
                                // maxLength: 10,
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: context.appColors.grey3,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                    borderSide: BorderSide(
                                      color: context.appColors.grey3,
                                      width: 0.7,
                                    ),
                                  ),
                                  fillColor: context.appColors.grey3,
                                  hintText: tr('mobile_number'),
                                  hintStyle: Styles.regular(
                                    color: context.appColors.grey3,
                                    size: 14,
                                  ),
                                  isDense: true,
                                  prefixIconConstraints:
                                      BoxConstraints(minWidth: 0, minHeight: 0),
                                  prefixIcon: SizedBox(
                                    width: 70,
                                    child: InkWell(
                                      onTap: () {
                                        /*if (APK_DETAILS[
                                          "package_name"] !=
                                              "com.singulariswow.mec") {
                                            Utility.showSnackBar(
                                                scaffoldContext:
                                                context,
                                                message: tr(
                                                    'mobile_indian_users'));
                                          }*/
                                      },
                                      child: Padding(
                                        padding: Utility().isRTL(context)
                                            ? EdgeInsets.only(right: 10.0)
                                            : EdgeInsets.only(left: 10.0),
                                        child: Row(
                                          children: [
                                            Text(
                                              APK_DETAILS["package_name"] ==
                                                      "com.singulariswow.mec"
                                                  ? "+968 "
                                                  : "+91 ",
                                              style: Styles.regular(
                                                color: context.appColors.grey3,
                                                size: 14,
                                              ),
                                            ),
                                            Image.asset(
                                              APK_DETAILS["package_name"] ==
                                                      "com.singulariswow.mec"
                                                  ? 'assets/images/oman_flag.png'
                                                  : 'assets/images/in_flag.png',
                                              width: 20,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 0.7,
                                          color: context.appColors.grey3),
                                      borderRadius: BorderRadius.circular(10)),
                                  helperStyle: Styles.regular(
                                      size: 14,
                                      color: context.appColors.grey3
                                          .withValues(alpha: 0.1)),
                                  counterText: "",
                                ),
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                            ),
                          ),

                          ///Password Field
                          //_codeVerifiedTrue == true
                          /*Column(
                              children: [
                                Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(right: 20.0)
                                      : EdgeInsets.only(left: 20.0),
                                  child: Row(
                                    children: [
                                      ShaderMask(
                                        blendMode: BlendMode.srcIn,
                                        shaderCallback: (Rect bounds) {
                                          return LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: <Color>[
                                                context.appColors
                                                    .gradientLeft,
                                                context.appColors
                                                    .gradientRight
                                              ]).createShader(bounds);
                                        },
                                        child: Icon(
                                          Icons.lock_outline,
                                          size: 20,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Text(
                                        "enter_pwd",
                                        style: Styles.getTextRegularStyle(context),
                                      ).tr(),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(
                                      top: 8.0, right: 16, left: 16)
                                      : EdgeInsets.only(
                                      top: 8.0, left: 16, right: 16),
                                  child: Container(
                                    height: height(context) * 0.1,
                                    child: TextFormField(
                                      //obscureText: true,
                                      obscureText: _isObscure1,
                                      cursorColor:
                                      context.appColors.gradientRight,
                                      autofocus: false,
                                      controller: newPassController,
                                      style: Styles.bold(
                                        color: Colors.black,
                                        size: 14,
                                      ),
                                      maxLength: 20,
                                      decoration: InputDecoration(
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(10.0),
                                          borderSide: BorderSide(
                                            color: Color(0xffE5E5E5),
                                          ),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(10.0),
                                          borderSide: BorderSide(
                                            color: Color(0xffE5E5E5),
                                            width: 1.5,
                                          ),
                                        ),
                                        fillColor: Color(0xffE5E5E5),
                                        hintText:
                                        'please_enter_password'.tr(),
                                        hintStyle: TextStyle(
                                          color: Color(0xffE5E5E5),
                                        ),
                                        isDense: true,
                                        prefixIconConstraints: BoxConstraints(
                                            minWidth: 0, minHeight: 0),
                                        border: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                width: 1,
                                                color: context.appColors.white),
                                            borderRadius:
                                            BorderRadius.circular(10)),
                                        helperStyle: Styles.regular(
                                            size: 14,
                                            color: context.appColors.grey3
                                                .withValues(alpha:0.1)),
                                        counterText: "",
                                        suffixIcon: Visibility(
                                          visible: _isObscure1 != null,
                                          child: TapWidget(
                                            onTap: () {
                                              _obscuredPass();
                                            },
                                            child: Padding(
                                                padding: const EdgeInsets.only(right: 5),
                                                child: !_isObscure1
                                                    ? Icon(
                                                  Icons.remove_red_eye_outlined,
                                                  color: context.appColors.headingPrimaryColor,
                                                ): Icon(
                                                  Icons.visibility_off,
                                                  color: context.appColors.grey3,
                                                )),
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {});
                                      },
                                      validator: (value) {
                                        if (value!.length == 0) return tr('please_enter_password');
                  
                                        return null;
                                      },
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(right: 20.0)
                                      : EdgeInsets.only(left: 20.0),
                                  child: Row(
                                    children: [
                                      ShaderMask(
                                        blendMode: BlendMode.srcIn,
                                        shaderCallback: (Rect bounds) {
                                          return LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: <Color>[
                                                context.appColors
                                                    .gradientLeft,
                                                context.appColors
                                                    .gradientRight
                                              ]).createShader(bounds);
                                        },
                                        child: Icon(
                                          Icons.lock_outline,
                                          size: 20,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Text(
                                        'confirm_password',
                                        style: Styles.getTextRegularStyle(context),
                                      ).tr(),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: Utility().isRTL(context)
                                      ? EdgeInsets.only(
                                      top: 8.0, right: 16, left: 16)
                                      : EdgeInsets.only(
                                      top: 8.0, left: 16, right: 16),
                                  child: Container(
                                    child: TextFormField(
                                      //obscureText: true,
                                      obscureText: _isObscure,
                                      cursorColor: context.appColors.gradientRight,
                                      autofocus: false,
                                      controller: confPassController,
                                      style: Styles.bold(
                                        color: Colors.black,
                                        size: 14,
                                      ),
                                      maxLength: 20,
                                      decoration: InputDecoration(
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(10.0),
                                          borderSide: BorderSide(
                                            color: Color(0xffE5E5E5),
                                          ),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                          BorderRadius.circular(10.0),
                                          borderSide: BorderSide(
                                            color: Color(0xffE5E5E5),
                                            width: 1.5,
                                          ),
                                        ),
                                        fillColor: Color(0xffE5E5E5),
                                        hintText:
                                        'please_enter_confirm_password'
                                            .tr(),
                                        hintStyle: TextStyle(
                                          color: Color(0xffE5E5E5),
                                        ),
                                        isDense: true,
                                        prefixIconConstraints: BoxConstraints(
                                            minWidth: 0, minHeight: 0),
                                        border: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                width: 1,
                                                color: context.appColors.white),
                                            borderRadius:
                                            BorderRadius.circular(10)),
                                        helperStyle: Styles.regular(
                                            size: 14,
                                            color: context.appColors.grey3
                                                .withValues(alpha:0.1)),
                                        counterText: "",
                                        suffixIcon: Visibility(
                                          visible: _isObscure != null,
                                          child: TapWidget(
                                            onTap: () {
                                              _obscuredConformPass();
                                            },
                                            child: Padding(
                                                padding: const EdgeInsets.only(right: 5),
                                                child: !_isObscure
                                                    ? Icon(
                                                  Icons.remove_red_eye_outlined,
                                                  color: context.appColors.headingPrimaryColor,
                                                ): Icon(
                                                  Icons.visibility_off,
                                                  color: context.appColors.grey3,
                                                )),
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {});
                                      },
                                      validator: (value) {
                                        if (value!.length == 0) return tr('please_enter_confirm_password');
                                        return null;
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),*/

                          ///Show Dropdown button

                          ///terms conditions checkbox
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(right: 20.0, top: 0.0)
                                : EdgeInsets.only(left: 20.0, top: 0.0),
                            child: Transform.translate(
                              offset: Offset(
                                  Utility().isRTL(context) ? 24 : -28, 0.0),
                              child: CheckboxListTile(
                                title: InkWell(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        NextPageRoute(
                                            TermsAndCondition(
                                              url: Preference.getString(
                                                  Preference.TERMS_AND_CON_URL),
                                              title: tr('t_c'),
                                            ),
                                            isMaintainState: false));
                                  },
                                  child: Transform.translate(
                                    offset: Offset(
                                        Utility().isRTL(context) ? 40 : -10, 0),
                                    child: Text.rich(
                                        TextSpan(
                                          children: [
                                            TextSpan(
                                                text: tr('clicking_continue'),
                                                style:
                                                    Styles.getRegularThemeStyle(
                                                        context,
                                                        size: 10)),
                                            TextSpan(
                                              text: tr('terms_conditions'),
                                              style: Styles.bold(
                                                  size: 10,
                                                  color:
                                                      context.appColors.grey2),
                                            ),
                                          ],
                                        ),
                                        textAlign: TextAlign.left),
                                  ),
                                ),
                                value: checkedValue,
                                onChanged: (newValue) {
                                  setState(() {
                                    checkedValue = newValue;
                                  });
                                },
                                controlAffinity: ListTileControlAffinity
                                    .leading, //  <-- leading Checkbox
                              ),
                            ),
                          ),

                          //_codeVerifiedTrue == true
                          Padding(
                            padding: Utility().isRTL(context)
                                ? EdgeInsets.only(
                                    right: 18.0,
                                    left: 18.0,
                                    bottom: 18.0,
                                  )
                                : EdgeInsets.only(
                                    left: 18.0,
                                    right: 18.0,
                                    bottom: 18.0,
                                  ),
                            child: SizedBox(
                              height: height(context) * 0.11,
                              child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                      height: height(context) * 0.06,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        gradient: checkedValue == true
                                            ? LinearGradient(colors: [
                                                context.appColors.gradientLeft,
                                                context.appColors.gradientRight,
                                              ])
                                            : LinearGradient(colors: [
                                                context
                                                    .appColors.unselectedButton,
                                                context
                                                    .appColors.unselectedButton,
                                              ]),
                                      ),
                                      child: InkWell(
                                        onTap: () {
                                          setState(() {});
                                          fieldValidation();
                                        },
                                        child: Center(
                                          child: Text(
                                            'next',
                                            style: Styles.regular(
                                              size: 16,
                                              color:
                                                  context.appColors.textWhite,
                                            ),
                                          ).tr(),
                                        ),
                                      ),
                                    ),
                                  ]),
                            ),
                          )
                          //: SizedBox(),
                        ],
                      ),
                    ),
                  ),
                )),
              ),
            )));
  }
}
