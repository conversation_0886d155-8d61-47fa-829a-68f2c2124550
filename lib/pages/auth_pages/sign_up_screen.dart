import 'dart:convert';
import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/home_response/user_info_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/singularis_login_username_pass.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/pages/auth_pages/verify_otp.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:path_provider/path_provider.dart';

// ignore: must_be_immutable
class SignUpScreen extends StatefulWidget {
  bool isFromProfile;

  SignUpScreen({super.key, this.isFromProfile = false});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final phoneController = TextEditingController();
  FocusNode phoneFocus = FocusNode();
  bool isFocused = false;

  DateTime selectedDate = DateTime.now();
  final picker = ImagePicker();
  late String selectedImage;
  bool _isLoading = false;
  final _formKey = GlobalKey<FormState>();
  late String gender = "male";
  List<String> blockedPhone = ["1", "2", "3", "4", "0"];

  UserData userData = UserData();

  void initHive() async {
    await getApplicationDocumentsDirectory().then((value) {
      Hive.init(value.path);
      Hive.openBox(DB.CONTENT);
      Hive.openBox(DB.ANALYTICS);
      Hive.openBox(DB.TRAININGS);
      Hive.openBox('theme');
    });
  }

  @override
  void initState() {
    super.initState();
    initHive();
    phoneFocus.addListener(_onFocusChange);
    if (widget.isFromProfile) {
      userData = UserData.fromJson(json.decode(UserSession.userData!));
      phoneController.text = UserSession.phone!;
      gender = UserSession.gender!;
      UserSession.userImageUrl = userData.profileImage;
      Log.v(userData.branch);
    }
  }

  void _onFocusChange() {
    setState(() {
      isFocused = phoneFocus.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return BlocManager(
            initState: (BuildContext context) {},
            child: BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is LoginState) _handleLoginResponse(state);
              },
              child: Scaffold(
                backgroundColor: context.backgroundColor,
                appBar: AppBar(
                  toolbarHeight: 22,
                  backgroundColor: context.backgroundColor,
                  elevation: 0,
                ),
                body: ScreenWithLoader(
                  isLoading: _isLoading,
                  body: _makeBody(context),
                ),
              ),
            ));
      },
    );
  }

  Widget _makeBody(BuildContext context) {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';
    return Form(
      key: _formKey,
      child: ListView(
        children: [
          SizedBox(
            height: height(context) * 0.9,
            child: Column(
              children: [
                Text('welcome_msg',
                        style: Styles.regular(
                            size: 18, color: context.appColors.subHeadingTitle))
                    .tr(),
                Transform.scale(
                  scale: 1.2,
                  child: appBarImagePath.split('.').last == 'svg'
                      ? SvgPicture.asset(
                          appBarImagePath,
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          appBarImagePath,
                          height: 60,
                          width: 180,
                        ),
                ),
                if (APK_DETAILS['theme_image_url2'] != "")
                  APK_DETAILS['theme_image_url2']?.split('.').last == 'svg'
                      ? SvgPicture.asset(
                          height: MediaQuery.of(context).size.height * 0.25,
                          'assets/images/${APK_DETAILS['theme_image_url2']}',
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          'assets/images/${APK_DETAILS['theme_image_url2']}',
                          height: MediaQuery.of(context).size.height * 0.25,
                          // width: 150,
                        ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              gradient: LinearGradient(colors: context.gradientColors),
            ),
            height: height(context) * 0.6,
            child: Column(
              children: [
                SizedBox(height: 20),
                Text('${tr('login')}/${tr('register')}',
                        style: Styles.bold(
                            size: 18, color: context.appColors.textWhite))
                    .tr(),
                SizedBox(
                  height: 2,
                ),
                Text('create_account_to_continue',
                        style: Styles.regular(
                            size: 16, color: context.appColors.textWhite))
                    .tr(),

                SizedBox(
                  height: 40,
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Stack(
                    children: [
                      Container(
                        width: width(context) * 0.72,
                        height: height(context) * 0.06,
                        decoration: BoxDecoration(
                            color: context.appColors.surface
                                .withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      TextFormField(
                        maxLength: 10,
                        cursorColor: context.appColors.textWhite,
                        autofocus: false,
                        focusNode: phoneFocus,
                        controller: phoneController,
                        keyboardType: TextInputType.number,
                        style: Styles.bold(
                          color: context.appColors.textWhite,
                          size: 14,
                        ),
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                        ],
                        // maxLength: 10,
                        decoration: InputDecoration(
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                            borderSide: BorderSide(
                              color: context.appColors.textWhite,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                            borderSide: BorderSide(
                              color: context.appColors.textWhite,
                              width: 0.7,
                            ),
                          ),
                          fillColor: context.appColors.textWhite,
                          hintText: tr('mobile_number'),
                          hintStyle: Styles.regular(
                            color: context.appColors.textWhite,
                            size: 14,
                          ),
                          isDense: true,
                          prefixIconConstraints:
                              BoxConstraints(minWidth: 0, minHeight: 0),
                          prefixIcon: SizedBox(
                            width: 70,
                            child: InkWell(
                              onTap: () {
                                if (APK_DETAILS["package_name"] !=
                                    "com.singulariswow.mec") {
                                  Utility.showSnackBar(
                                      scaffoldContext: context,
                                      message: tr('mobile_indian_users'));
                                }
                              },
                              child: Padding(
                                padding: Utility().isRTL(context)
                                    ? EdgeInsets.only(right: 10.0)
                                    : EdgeInsets.only(left: 10.0),
                                child: Row(
                                  children: [
                                    Text(
                                      APK_DETAILS["package_name"] ==
                                              "com.singulariswow.mec"
                                          ? "+968 "
                                          : "+91 ",
                                      style: Styles.regular(
                                        color: context.appColors.grey3,
                                        size: 14,
                                      ),
                                    ),
                                    Image.asset(
                                      APK_DETAILS["package_name"] ==
                                              "com.singulariswow.mec"
                                          ? 'assets/images/oman_flag.png'
                                          : 'assets/images/in_flag.png',
                                      width: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(
                                  width: 0.7,
                                  color: context.appColors.textWhite),
                              borderRadius: BorderRadius.circular(10)),
                          helperStyle: Styles.regular(
                              size: 14,
                              color: context.appColors.grey3
                                  .withValues(alpha: 0.1)),
                          counterText: "",
                        ),
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                      Positioned(
                          right: Utility().isRTL(context) ? null : 0,
                          left: Utility().isRTL(context) ? 0 : null,
                          bottom: Utility().isRTL(context) ? 0.5 : null,
                          child: InkWell(
                            onTap: () {
                              Preference.setBool(
                                  Preference.LOGGEDIN_WITH_EMAIL, false);
                              if (phoneController.text
                                  .toString()
                                  .trim()
                                  .isNotEmpty) {
                                if (APK_DETAILS["package_name"] ==
                                    "com.singulariswow.mec") {
                                  if (phoneController.text.toString().length ==
                                      8) {
                                    if (blockedPhone.contains(phoneController
                                        .text
                                        .toString()
                                        .split('')
                                        .first)) {
                                      Utility.showSnackBar(
                                          scaffoldContext: context,
                                          message: tr('valid_phn_number'));
                                    } else {
                                      doLogin();
                                    }
                                  } else {
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                  }
                                } else {
                                  if (phoneController.text.toString().length ==
                                      10) {
                                    if (blockedPhone.contains(phoneController
                                        .text
                                        .toString()
                                        .split('')
                                        .first)) {
                                      Utility.showSnackBar(
                                          scaffoldContext: context,
                                          message: tr('valid_phn_number'));
                                    } else {
                                      doLogin();
                                    }
                                  } else {
                                    Utility.showSnackBar(
                                        scaffoldContext: context,
                                        message: tr('valid_phn_number'));
                                  }
                                }
                              } else {
                                Utility.showSnackBar(
                                    scaffoldContext: context,
                                    message: tr('enter_phone_number'));
                              }
                            },
                            child: Container(
                              //height: height(context) * 0.07,
                              height: Utility().isRTL(context) ? 55 : 50,
                              width: width(context) * 0.26,
                              decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10))),

                              child: ShaderMask(
                                blendMode: BlendMode.srcIn,
                                shaderCallback: (Rect bounds) {
                                  return LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: <Color>[
                                        context.appColors.gradientLeft,
                                        context.appColors.gradientRight
                                      ]).createShader(bounds);
                                },
                                child: Center(
                                  child: Text(
                                    "get_otp",
                                    style: Styles.getRegularThemeStyle(
                                      context,
                                      size: 14,
                                    ),
                                  ).tr(),
                                ),
                              ),
                            ),
                          ))
                    ],
                  ),
                ),
                // SizedBox(height: 20.0),
                InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => SingularisLogin()));
                  },
                  child: SizedBox(
                    // margin: EdgeInsets.symmetric(vertical: 12),
                    width: double.infinity,
                    height: MediaQuery.of(context).size.height *
                        WidgetSize.AUTH_BUTTON_SIZE,

                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/email.svg',
                          color: context.appColors.textWhite,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Text(
                          'continue_with_email',
                          style: Styles.semibold(
                            size: 14,
                            color: context.appColors.textWhite,
                          ),
                        ).tr(),
                      ],
                    ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.only(top: 110.0),
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          NextPageRoute(
                              TermsAndCondition(
                                  url: Preference.getString(
                                      Preference.TERMS_AND_CON_URL)),
                              isMaintainState: false));
                    },
                    child: Column(
                      children: [
                        Text('clicking_continue',
                                style: Styles.regular(
                                    size: 10,
                                    color: context.appColors.textWhite))
                            .tr(),
                        Text(
                          'terms_conditions',
                          style: Styles.regular(
                              size: 12, color: context.appColors.textWhite),
                        ).tr(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleLoginResponse(LoginState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          Navigator.push(
              context,
              NextPageRoute(VerifyOtp(
                phoneController.text.toString(),
              )));
          _isLoading = false;
          break;

        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v(
              "Error..........................${loginState.response?.error?[0]}");
          AlertsWidget.alertWithOkBtn(
              context: context,
              text: loginState.response?.error?[0],
              onOkClick: () {
                FocusScope.of(context).unfocus();
              });
          FirebaseAnalytics.instance.logEvent(name: 'signUp_page', parameters: {
            "ERROR": '${loginState.response?.error?[0]}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void doLogin() {
    setState(() {
      _isLoading = true;
    });
    if (!_formKey.currentState!.validate()) return;

    BlocProvider.of<AuthBloc>(context).add(LoginUser(
        request: LoginRequest(
            mobileNo: phoneController.text.toString().trim(),
            mobile_exist_skip: '1')));
  }

  void showFileChoosePopup() {
    //  Utility.hideKeyboard();
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black45,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        // return object of type Dialog
        return SimpleDialog(
          backgroundColor: context.appColors.primary,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          children: <Widget>[
            Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(18)),
                color: context.appColors.primary,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                      padding: EdgeInsets.all(10),
                      child: Text(
                        'photo_alert',
                        style: Styles.regularWhite(size: 18),
                      ).tr()),
                  InkWell(
                      child: Padding(
                        padding: EdgeInsets.all(10),
                        child: Text('take_a_picture',
                                style: Styles.regularWhite(size: 16))
                            .tr(),
                      ),
                      onTap: () {
                        _getImages(ImageSource.camera).then((value) {
                          Navigator.pop(context);
                          if (value.isEmpty) return;
                          setState(() {
                            UserSession.userImageUrl = null;
                            selectedImage = value;
                          });
                        });
                      }),
                  InkWell(
                      child: Padding(
                          padding: EdgeInsets.all(10),
                          child: Text('pick_from_gallery',
                                  style: Styles.regularWhite(size: 16))
                              .tr()),
                      onTap: () async {
                        _getImages(ImageSource.gallery).then((value) {
                          Navigator.pop(context);
                          if (value.isEmpty) return;
                          setState(() {
                            UserSession.userImageUrl = null;
                            selectedImage = value;
                          });
                        });
                      }),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Future<String> _getImages(ImageSource source) async {
    final picker = ImagePicker();
    final XFile? pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null) {
      return pickedFile.path;
    } else if (Platform.isAndroid) {
      final LostDataResponse response = await picker.retrieveLostData();
      if (response.file != null) {
        return response.file!.path;
      }
    }
    return "";
  }
}
