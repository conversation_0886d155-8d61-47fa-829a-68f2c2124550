import 'dart:async';

// import 'package:dots_indicator/dots_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/select_language_bottomsheet.dart.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class FullScreenPreboarding extends StatefulWidget {
  const FullScreenPreboarding({super.key});

  @override
  State<FullScreenPreboarding> createState() => _FullScreenPreboardingState();
}

class _FullScreenPreboardingState extends State<FullScreenPreboarding>
    with SingleTickerProviderStateMixin {
  late final PageController _pageController = PageController();
  // late final AnimationController _controller = AnimationController(
  //   duration: const Duration(seconds: 2),
  //   vsync: this,
  // )..repeat(reverse: true);
  // late final Animation<Offset> _offsetAnimation = Tween<Offset>(
  //   begin: Offset.zero,
  //   end: const Offset(1.5, 0.0),
  // ).animate(CurvedAnimation(
  //   parent: _controller,
  //   curve: Curves.elasticIn,
  // ));

  int preboardingLen = 3;
  int dotIndex = 0;
  late Timer _timer;

  @override
  void dispose() {
    _pageController.dispose(); // Dispose the PageController
    _timer.cancel(); // Cancel the timer
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
    _timer = Timer.periodic(Duration(seconds: 5), (Timer timer) {
      if (_pageController.page! < preboardingLen - 1) {
        _pageController.nextPage(
            duration: Duration(milliseconds: 500), curve: Curves.ease);
      } else {
        _pageController.animateToPage(0,
            duration: Duration(milliseconds: 500), curve: Curves.ease);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PageView.builder(
            pageSnapping: true,
            controller: _pageController,
            itemCount: preboardingLen,
            itemBuilder: (context, index) {
              String imagePath = '${APK_DETAILS['preboarding${index + 1}']}';
              String title =
                  tr('${APK_DETAILS['preboarding_title${index + 1}']}');
              String desc =
                  tr('${APK_DETAILS['preboarding_desc${index + 1}']}');

              Future.delayed(Duration(milliseconds: 800)).then((value) {
                if (index != dotIndex) {
                  setState(() {
                    dotIndex = index;
                  });
                }
              });

              return Stack(
                children: [
                  Positioned.fill(
                      left: 0,
                      right: 0,
                      child: imagePath.split('.').last == 'svg'
                          ? SvgPicture.asset(
                              imagePath,
                              fit: BoxFit.cover,
                            )
                          : Image.asset(
                              imagePath,
                              fit: BoxFit.cover,
                            )),
                  Positioned(
                      top: desc != ''
                          ? height(context) * 0.6
                          : height(context) * 0.7,
                      left: Utility().isRTL(context) ? 0 : 15,
                      right: Utility().isRTL(context) ? 15 : null,
                      child: SizedBox(
                        width: width(context) * 0.9,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              title,
                              maxLines: 4,
                              textAlign: TextAlign.center,
                              style: Styles.textBold(
                                  lineHeight: 1.3,
                                  color: context.appColors.textWhite,
                                  size: APK_DETAILS['package_name'] !=
                                          'com.singularis.mesc'
                                      ? 33.37
                                      : 33.37),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                desc,
                                textAlign: TextAlign.center,
                                maxLines: 4,
                                style: Styles.regular(
                                    lineHeight: 1.4,
                                    color: context.appColors.textWhite,
                                    size: 16),
                              ),
                            )
                          ],
                        ),
                      )),
                ],
              );
            }),
        Positioned(
          bottom: 60,
          left: 15,
          right: 15,
          child: Column(
            children: [
              // _dots(dotIndex),
              SizedBox(height: 10),
              TapWidget(
                onTap: () async {
                  _getBottomSheet();
                  // Navigator.pushAndRemoveUntil(
                  //     context,
                  //     NextPageRoute(SelectLanguage(
                  //       showEdulystLogo: true,
                  //     )),
                  //     (route) => false);

                  // Navigator.push(
                  //     context, NextPageRoute(ParticiaptionRegister()));
                },
                child: Container(
                  height: height(context) * 0.06,
                  width: width(context),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: context.appColors.darkButton
                      // gradient: LinearGradient(colors: [
                      //   context.appColors.gradientLeft,
                      //   context.appColors.gradientRight,
                      // ]),
                      ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'get_started',
                        style: TextStyle(
                            color: context.appColors.primaryForeground,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ).tr(),
                      SizedBox(width: 5),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: context.appColors.textWhite,
                        size: 18,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  _getBottomSheet() {
    return Get.bottomSheet(
      Container(
        height: MediaQuery.of(context).size.height * 0.45,
        decoration: BoxDecoration(
          color: context.appColors.darkBackground,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Text('app_language',
                        style: Styles.dMSansregular(
                            color: context.appColors.textWhite, size: 16))
                    .tr(),
                SizedBox(height: 20),
                SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    child: SelectLanguageBottomSheet(
                      showEdulystLogo: true,
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Row _dots(int index) {
  //   return Row(
  //     mainAxisSize: MainAxisSize.min,
  //     mainAxisAlignment: MainAxisAlignment.center,
  //     children: [
  //       DotsIndicator(
  //         dotsCount: 3,
  //         position: index.toDouble(),
  //         decorator: DotsDecorator(
  //           size: const Size.square(8.0),
  //           color: Color(0xffCCCACA),
  //           activeColor: context.appColors.white,
  //           activeSize: const Size(30.0, 8.0),
  //           activeShape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(5.0)),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}
