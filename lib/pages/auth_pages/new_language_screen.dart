import 'dart:developer';
import 'dart:core';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/pages/auth_pages/bottomSheet_login_flow/bottom_sheet_login_page.dart';
import 'package:masterg/pages/auth_pages/new_Signup_screen.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/data/models/response/home_response/master_language_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class SelectLanguage extends StatefulWidget {
  final bool showEdulystLogo;
  const SelectLanguage({super.key, required this.showEdulystLogo});

  @override
  State<SelectLanguage> createState() => _SelectLanguageState();
}

class _SelectLanguageState extends State<SelectLanguage> {
  var selected = 0;

  bool _isLoading = false;
  List<ListLanguage>? myList;
  var localeCodes = {
    'english': "en",
    'hindi': "hi",
    'kannada': "kn",
    'marathi': "mr",
    'tamil': "ta",
    'telugu': "te",
    'bengali': "bn",
    'malyalam': 'ml',
    'arabic': 'ar',
  };

  @override
  void initState() {
    super.initState();
    _getLanguage();
    setCurrentLanguage();
  }

  void setCurrentLanguage() async {
    int? currentLanId = Preference.getInt(Preference.APP_LANGUAGE) ?? 1;

    for (int i = 0; i < myList!.length; i++) {
      if (currentLanId == myList?[i].languageId) {
        selected = i;
        Preference.setInt(Preference.APP_LANGUAGE, myList![i].languageId!);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, myList![i].isPrimaryLanguage!);
        Preference.setString(
            Preference.APP_ENGLISH_NAME, myList![i].englishName.toString());
        Preference.setString(
            Preference.LANGUAGE, '${myList?[i].languageCode?.toLowerCase()}');
        context.setLocale(Locale('${myList?[i].languageCode}'));
        break;
      } else {
        selected = 0;
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';
    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is MasterLanguageState) _handleResponse(state);
        },
        child: Builder(builder: (context) {
          return Scaffold(
            backgroundColor: context.appColors.background,
            body: ScreenWithLoader(
              isLoading: _isLoading,
              body: SafeArea(
                  child: SingleChildScrollView(
                child: SizedBox(
                  height: height(context) * 0.9,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: height(context) * 0.12,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 70.0),
                          child: Text(
                            'welcome_msg',
                            style: TextStyle(
                                fontSize: 18,
                                color: context.appColors.subHeadingTitle,
                                fontWeight: FontWeight.w400),
                          ).tr(),
                        ),
                      ),
                      APK_DETAILS['package_name'] == 'com.singularis.mesc'
                          ? SizedBox(
                              height: 20,
                            )
                          : SizedBox(),
                      if (widget.showEdulystLogo == true)
                        appBarImagePath.split('.').last == 'svg'
                            ? SvgPicture.asset(
                                appBarImagePath,
                                fit: BoxFit.cover,
                                width: width(context) * 0.5,
                              )
                            : Padding(
                                padding: const EdgeInsets.only(
                                    left: 80.0,
                                    top: 2.0,
                                    right: 80.0,
                                    bottom: 20.0),
                                child: Image.asset(
                                  appBarImagePath,
                                  width: width(context) * 0.5,
                                  //height: 150,
                                  //width: 150,
                                ),
                              ),
                      SizedBox(
                          height:
                              APK_DETAILS['package_name'] == 'com.at.masterg'
                                  ? 60
                                  : 10),
                      SizedBox(
                        height: 60,
                        child: Center(
                          child: Text(
                            'app_language',
                            style: Styles.dMSansregular(
                              color: context.appColors.headingPrimaryColor,
                            ),
                            textAlign: TextAlign.center,
                          ).tr(),
                        ),
                      ),
                      SizedBox(height: 10),
                      Container(
                          width: width(context),
                          margin: EdgeInsets.symmetric(
                              horizontal: width(context) > 1000
                                  ? width(context) * 0.1
                                  : 10),
                          child: GridView.builder(
                            shrinkWrap: true,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              mainAxisSpacing: 0,
                              crossAxisSpacing: 0,
                              childAspectRatio:
                                  myList?.length == 1 ? 12 / 4.4 : 10 / 4.4,
                              crossAxisCount: myList?.length == 1 ? 1 : 2,
                            ),
                            itemCount: myList?.length ?? 0,
                            itemBuilder: (BuildContext context, int index) {
                              return languageCard(myList![index], index);
                            },
                          )),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          // ignore: unrelated_type_equality_checks
                          if (APK_DETAILS["bottom_sheet_login"] == "1") {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        BottomSheetLoginScreen()));
                          } else if (widget.showEdulystLogo)
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => NewSignUpScreen(
                                        showEdulystLogo: true)));
                          else
                            Navigator.pop(context);
                        },
                        child: Container(
                          height: height(context) * 0.06,
                          width: width(context),
                          margin: EdgeInsets.only(
                              left: 15, right: 15, top: 40, bottom: 20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            gradient: LinearGradient(colors: [
                              context.appColors.gradientLeft,
                              context.appColors.gradientRight,
                            ]),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Continue_button',
                                style: TextStyle(
                                    color: context.appColors.primaryForeground,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold),
                              ).tr(),
                            ],
                          ),
                        ),
                        //
                      ),
                    ],
                  ),
                ),
              )),
            ),
          );
        }),
      ),
    );
  }

  void _handleResponse(MasterLanguageState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          _isLoading = false;
          myList = state.response!.data!.listData;
          setCurrentLanguage();
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'language_page', parameters: {
            "Error": '${state.response?.error?.first}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getLanguage() {
    BlocProvider.of<HomeBloc>(context).add(MasterLanguageEvent());
  }

  Widget languageCard(langauge, index) {
    return InkWell(
      onTap: () {
        Preference.setInt(Preference.APP_LANGUAGE, langauge.languageId);
        Preference.setInt(
            Preference.IS_PRIMARY_LANGUAGE, langauge.isPrimaryLanguage);
        setState(() {
          selected = index;
          context.setLocale(Locale('${langauge.languageCode}'));
          try {
            Preference.setString(
                Preference.LANGUAGE, langauge.languageCode.toLowerCase());
            Preference.setString(Preference.APP_ENGLISH_NAME,
                langauge.englishName.toLowerCase());
          } catch (e, stack) {
            log('$stack');
          }
        });
      },
      child: Container(
          width: MediaQuery.of(context).size.width * 0.4,
          height: MediaQuery.of(context).size.width * 0.2,
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                  width: 1,
                  color: index == selected
                      ? context.appColors.green
                      : context.appColors.darkGrey)),
          child: Stack(
            children: [
              Positioned(
                  left: 5,
                  top: 5,
                  child: SvgPicture.asset(
                    height: 16,
                    index == selected
                        ? 'assets/images/selected_lang.svg'
                        : 'assets/images/unselected_lang.svg',
                    fit: BoxFit.cover,
                  )),
              Center(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${langauge.title ?? ''}',
                    style: Styles.bold(
                        size: 18,
                        color: index == selected
                            ? context.appColors.green
                            : context.appColors.textBlack),
                  ),
                  SizedBox(height: 2),
                  Text(langauge.name ?? '',
                      style: Styles.regular(
                        size: 14,
                        color: context.appColors.textBlack,
                      )),
                ],
              )),
            ],
          )),
    );
  }
}
