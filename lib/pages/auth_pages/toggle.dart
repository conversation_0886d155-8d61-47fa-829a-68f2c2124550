import 'package:flutter/material.dart';
import 'package:masterg/utils/constant.dart';

class ToggleButton extends StatefulWidget {
  final double width;
  final double height;

  final String leftDescription;
  final String rightDescription;

  final Color toggleColor;
  final Color toggleBackgroundColor;
  final Color toggleBorderColor;

  final Color inactiveTextColor;
  final Color activeTextColor;

  final double _leftToggleAlign = -1;
  final double _rightToggleAlign = 1;

  final VoidCallback onLeftToggleActive;
  final VoidCallback onRightToggleActive;

  const ToggleButton(
      {super.key,
      required this.width,
      required this.height,
      required this.toggleBackgroundColor,
      required this.toggleBorderColor,
      required this.toggleColor,
      required this.activeTextColor,
      required this.inactiveTextColor,
      required this.leftDescription,
      required this.rightDescription,
      required this.onLeftToggleActive,
      required this.onRightToggleActive});

  @override
  State<ToggleButton> createState() => _ToggleButtonState();
}

class _ToggleButtonState extends State<ToggleButton> {
  double _toggleXAlign = -1;

  late Color _leftDescriptionColor;
  late Color _rightDescriptionColor;

  @override
  void initState() {
    super.initState();

    _leftDescriptionColor = widget.activeTextColor;
    _rightDescriptionColor = widget.inactiveTextColor;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height(context) * 0.055,
      child: GestureDetector(
        onTap: () {
          setState(
            () {
              _toggleXAlign = widget._leftToggleAlign;

              _rightDescriptionColor = widget.inactiveTextColor;
              _leftDescriptionColor = widget.activeTextColor;
            },
          );

          widget.onRightToggleActive();
        },
        child: Container(
          margin: EdgeInsets.symmetric(
            horizontal: 1,
          ),
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.toggleBackgroundColor,
            borderRadius: BorderRadius.all(
              Radius.circular(10.0),
            ),
            border: Border.all(color: widget.toggleBorderColor),
          ),
          child: Stack(
            children: [
              AnimatedAlign(
                alignment: Alignment(_toggleXAlign, 0),
                duration: Duration(milliseconds: 300),
                child: Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: 2,
                  ),
                  width: widget.width * 0.9,
                  //height: widget.height,
                  decoration: BoxDecoration(
                    color: widget.toggleColor,
                    borderRadius: BorderRadius.all(
                      Radius.circular(6.0),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  // setState(
                  //   () {
                  //     _toggleXAlign = widget._leftToggleAlign;

                  //     _rightDescriptionColor = widget.inactiveTextColor;
                  //     _leftDescriptionColor = widget.activeTextColor;
                  //   },
                  // );

                  // widget.onRightToggleActive();
                },
                child: Align(
                  alignment: Alignment(-1, 0),
                  child: SizedBox(
                    width: widget.width * 0.9,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Icon(Icons.email_outlined,
                              color: _leftDescriptionColor),
                        ),
                        SizedBox(width: 10),
                        Text(
                          widget.leftDescription,
                          style: TextStyle(
                              color: _leftDescriptionColor,
                              fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(
                    () {
                      _toggleXAlign = widget._rightToggleAlign;

                      _rightDescriptionColor = widget.activeTextColor;
                      _leftDescriptionColor = widget.inactiveTextColor;
                    },
                  );

                  widget.onLeftToggleActive();
                },
                child: Align(
                  alignment: Alignment(1, 0),
                  child: Container(
                    width: widget.width * 0.7,
                    color: Colors.transparent,
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 40.0),
                      child: Row(
                        children: [
                          Icon(Icons.phone_android_outlined,
                              color: _rightDescriptionColor),
                          SizedBox(width: 10),
                          Text(
                            widget.rightDescription,
                            style: TextStyle(
                                color: _rightDescriptionColor,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
