import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/utils/config.dart';

class EventAdminConsolePage extends StatefulWidget {
  const EventAdminConsolePage({super.key});

  @override
  State<EventAdminConsolePage> createState() => _EventAdminConsolePageState();
}

class _EventAdminConsolePageState extends State<EventAdminConsolePage> {
  @override
  Widget build(BuildContext context) {
    debugPrint(
        'webview url is${APK_DETAILS['event_admin_console']}${Preference.getInt(Preference.USER_ID)}');
    return TermsAndCondition(
      url:
          '${APK_DETAILS['event_admin_console']}${Preference.getInt(Preference.USER_ID)}',
      title: tr('event_admin_console'),
      appBarEnable: true,
    );
  }
}
