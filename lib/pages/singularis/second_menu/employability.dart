import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/terms_and_condition_page.dart';
import 'package:masterg/utils/config.dart';

class EmployabilityMenuPage extends StatefulWidget {
  const EmployabilityMenuPage({super.key});

  @override
  State<EmployabilityMenuPage> createState() => _EmployabilityMenuPageState();
}

class _EmployabilityMenuPageState extends State<EmployabilityMenuPage> {
  @override
  Widget build(BuildContext context) {
    debugPrint(
        'webview url is${APK_DETAILS['emploability_coordinators']}${Preference.getInt(Preference.USER_ID)}');
    return TermsAndCondition(
      url:
          '${APK_DETAILS['emploability_coordinators']}${Preference.getInt(Preference.USER_ID)}',
      title: tr('employability'),
      appBarEnable: true,
    );
  }
}
