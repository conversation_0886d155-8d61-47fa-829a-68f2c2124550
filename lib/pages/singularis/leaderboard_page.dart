import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/leaderboard_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/singularis/point_history.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class LeaderboardPage extends StatefulWidget {
  final bool fromDashboard;
  final competitionId;
  const LeaderboardPage(
      {super.key, this.fromDashboard = false, this.competitionId});

  @override
  State<LeaderboardPage> createState() => _LeaderboardPageState();
}

class _LeaderboardPageState extends State<LeaderboardPage> {
  bool? isLeaderboardLoading = true;
  LeaderboardResponse? leaderboardResponse;

  @override
  void initState() {
    // getLeaderboard();
    getLeaderboardList();
    super.initState();
  }

  void getLeaderboardList() {
    BlocProvider.of<HomeBloc>(context).add(LeaderboardEvent(
        id: widget.competitionId,
        type: 'competition',
        skipotherUser: 0,
        skipcurrentUser: 0));
  }

  // void getLeaderboard() {
  //   BlocProvider.of<HomeBloc>(context).add(LeaderboardEvent(
  //       id: 2166, type: 'competition', skipotherUser: 0, skipcurrentUser: 0));
  // }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) async {
            if (state is LeaderboardState) {
              handleLeaderboardResponse(state);
            }
          },
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: context.appColors.bgGrey,
              elevation: 0.0,
              flexibleSpace: Container(
                color: context.appColors.surface,
              ),
              centerTitle: true,
              leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: context.appColors.textBlack,
                  )),
              title: Text(
                'leaderboard',
                style: Styles.getBoldThemeStyle(context),
              ).tr(),
            ),
            body: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: context.appColors.surface,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                            color: context.appColors.shadow,
                            offset: Offset(0, 4.0),
                            blurRadius: 11)
                      ],
                    ),
                    height: height(context) * 0.35,
                    width: width(context),
                    child: isLeaderboardLoading == true ||
                            leaderboardResponse!.data.isEmpty
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              leadercard(null, '', 0, 0, 2),
                              leadercard(null, '', 0, 0, 1),
                              leadercard(null, '', 0, 0, 3),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              leaderboardResponse!.data.length > 1
                                  ? leadercard(
                                      '${leaderboardResponse?.data[1].profileImage}',
                                      '${leaderboardResponse?.data[1].name}',
                                      int.parse(
                                          '${leaderboardResponse?.data[1].totalActivities}'),
                                      leaderboardResponse?.data[1].gScore,
                                      2)
                                  : leadercard(null, '', 0, 0, 2),
                              leadercard(
                                  '${leaderboardResponse?.data[0].profileImage}',
                                  '${leaderboardResponse?.data[0].name}',
                                  int.parse(
                                      '${leaderboardResponse?.data[0].totalActivities}'),
                                  leaderboardResponse?.data[0].gScore,
                                  1),
                              leaderboardResponse!.data.length > 2
                                  ? leadercard(
                                      '${leaderboardResponse?.data[2].profileImage}',
                                      '${leaderboardResponse?.data[2].name}',
                                      int.parse(
                                          '${leaderboardResponse?.data[2].totalActivities}'),
                                      leaderboardResponse?.data[2].gScore,
                                      3)
                                  : leadercard(null, '', 0, 0, 3),
                            ],
                          ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Text(
                          'your_rank',
                          style: TextStyle(
                              fontSize: 15, fontWeight: FontWeight.w500),
                        ).tr(),
                      ],
                    ),
                  ),
                  if (isLeaderboardLoading == false)
                    Container(
                        margin: EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: context.appColors.surface,
                          border: Border.all(
                              color: context.appColors.gradientRight),
                          boxShadow: [
                            BoxShadow(
                                blurRadius: 13,
                                offset: Offset(0, 4),
                                color: context.appColors.textBlack
                                    .withValues(alpha: 0.2)),
                          ],
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: 10),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(2),
                            visualDensity:
                                VisualDensity(horizontal: -4, vertical: -4),
                            leading: SizedBox(
                              width: width(context) * 0.2,
                              child: Row(
                                children: [
                                  leaderboardResponse?.data.indexWhere((e) =>
                                              e.id ==
                                              Preference.getInt(
                                                  Preference.USER_ID)) ==
                                          -1
                                      ? Padding(
                                          padding:
                                              const EdgeInsets.only(left: 8.0),
                                          child: Text("--"),
                                        )
                                      : Padding(
                                          padding:
                                              const EdgeInsets.only(left: 8.0),
                                          child: Text(
                                              "${(leaderboardResponse?.data.indexWhere((e) => e.id == Preference.getInt(Preference.USER_ID)))! + 1}. "),
                                        ),
                                  SizedBox(width: 5),
                                  CircleAvatar(
                                      backgroundImage: NetworkImage(
                                          "${Preference.getString(Preference.PROFILE_IMAGE)}")),
                                ],
                              ),
                            ),
                            title: Text(leaderboardResponse?.data
                                        .where((e) =>
                                            e.id ==
                                            Preference.getInt(
                                                Preference.USER_ID))
                                        .isEmpty ==
                                    true
                                ? Utility().decrypted128(
                                    '${Preference.getString(Preference.FIRST_NAME)}')
                                : Utility().decrypted128(
                                    '${leaderboardResponse?.data.where((e) => e.id == Preference.getInt(Preference.USER_ID)).first.name}')),
                            subtitle: Text(
                                "${leaderboardResponse?.data.where((e) => e.id == Preference.getInt(Preference.USER_ID)).isEmpty == true ? '0' : leaderboardResponse?.data.where((e) => e.id == Preference.getInt(Preference.USER_ID)).first.totalActivities} ${tr('activities')}"),
                            trailing: SizedBox(
                              width: width(context) * 0.18,
                              child: Row(
                                children: [
                                  if (leaderboardResponse?.data.indexWhere(
                                          (e) =>
                                              e.id ==
                                              Preference.getInt(
                                                  Preference.USER_ID)) !=
                                      -1) ...[
                                    SvgPicture.asset(
                                      'assets/images/coin.svg',
                                      width: width(context) * 0.07,
                                    ),
                                    const SizedBox(
                                      width: 4,
                                    ),
                                  ],
                                  Text(
                                      "${leaderboardResponse?.data.where((e) => e.id == Preference.getInt(Preference.USER_ID)).isEmpty == true ? '--' : leaderboardResponse?.data.where((e) => e.id == Preference.getInt(Preference.USER_ID)).first.gScore.toInt() ?? 0}"),
                                ],
                              ),
                            ),
                          ),
                        )),
                  isLeaderboardLoading == false
                      ? leaderboardResponse!.data.length > 3
                          ? Column(
                              children: [
                                Container(
                                  color: context.appColors.surface,
                                  margin: EdgeInsets.symmetric(vertical: 16),
                                  child: ListView.builder(
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: max(0,
                                          leaderboardResponse!.data.length - 3),
                                      shrinkWrap: true,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        index = index + 3;
                                        return userCard(
                                            name: leaderboardResponse
                                                ?.data[index].name,
                                            profileImg: leaderboardResponse
                                                ?.data[index].profileImage,
                                            index: index + 1,
                                            coin: leaderboardResponse
                                                ?.data[index].gScore,
                                            totalAct: leaderboardResponse
                                                ?.data[index].totalActivities);
                                      }),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                              ],
                            )
                          : SizedBox()
                      : Container(
                          margin: EdgeInsets.only(top: height(context) * 0.4),
                          child: Text(
                            'loading',
                            style: Styles.getRegularThemeStyle(context),
                          ).tr()),
                ],
              ),
            ),
          ),
        ));
  }

  Widget userCard(
      {String? name,
      String? profileImg,
      int? index,
      dynamic coin,
      int? totalAct}) {
    return Container(
      color: context.appColors.surface,
      margin: EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          ListTile(
              contentPadding: const EdgeInsets.all(2),
              visualDensity: VisualDensity(horizontal: -4, vertical: -4),
              leading: SizedBox(
                width: width(context) * 0.2,
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        "$index.",
                        style: Styles.getSemiboldThemeStyle(context, size: 14),
                      ),
                    ),
                    SizedBox(width: 8),
                    CircleAvatar(backgroundImage: NetworkImage("$profileImg")),
                  ],
                ),
              ),
              title: Text(
                Utility().decrypted128('$name'),
                style: Styles.getSemiboldThemeStyle(context, size: 14),
              ),
              subtitle: Text(
                "${totalAct ?? 0} ${tr('activities')}",
                style: Styles.regular(
                    size: 10, color: context.appColors.subHeadingTitle),
              ),
              trailing: SizedBox(
                width: width(context) * 0.25,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/coin.svg',
                      width: width(context) * 0.07,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        right: 8.0,
                      ),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => PointHistory()));
                        },
                        child: Text(
                          "${coin.toInt() ?? 0}",
                          style:
                              Styles.getSemiboldThemeStyle(context, size: 12),
                        ),
                      ),
                    )
                  ],
                ),
              )),
          Divider()
        ],
      ),
    );
  }

  void handleLeaderboardResponse(LeaderboardState state) {
    var leaderBoardState = state;
    setState(() {
      switch (leaderBoardState.apiState) {
        case ApiStatus.LOADING:
          Log.v("LeaderboardLoading....................");
          isLeaderboardLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("LraderboardState....................");

          leaderboardResponse = leaderBoardState.response;

          isLeaderboardLoading = false;
          break;
        case ApiStatus.ERROR:
          isLeaderboardLoading = false;
          Log.v("Error..........................");
          Log.v(
              "ErrorHome......................${leaderBoardState.response?.error}");
          FirebaseAnalytics.instance.logEvent(name: 'leaderboard', parameters: {
            "ERROR": '${leaderBoardState.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget leadercard(String? image, String title, dynamic activityCount,
      double coinCount, int rank) {
    String? url;
    switch (rank) {
      case 1:
        url = 'assets/images/leader_first.svg';
        break;
      case 2:
        url = 'assets/images/leader_second.svg';
        break;
      case 3:
        url = 'assets/images/leader_third.svg';
        break;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SvgPicture.asset('$url', width: rank == 1 ? 50 : 30),
        const SizedBox(
          height: 10,
        ),
        Container(
          width: rank == 1 ? 100 : 70,
          decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                  width: 3,
                  color: rank == 1
                      ? Color(0xffF2A91E)
                      : rank == 2
                          ? Color(0xffCACACA)
                          : Color(0xffE0997A))),
          child: image != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(200),
                  child: SizedBox(
                    width: rank == 1 ? 100 : 70,
                    child: Image.network(image),
                  ),
                )
              : ClipRRect(
                  borderRadius: BorderRadius.circular(200),
                  child: SizedBox(
                      width: rank == 1 ? 100 : 70,
                      height: rank == 1 ? 100 : 70,
                      child:
                          SvgPicture.asset('assets/images/default_user.svg'))),
        ),
        const SizedBox(
          height: 5,
        ),
        SizedBox(
          width: width(context) * 0.3,
          child: Text(Utility().decrypted128(title),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Styles.semibold(
                size: 12,
                color: context.appColors.dashboardApplyColor,
              )),
        ),
        Text("${tr('rank')} $rank",
            style: Styles.semibold(
              size: 12,
              color: context.appColors.gradientRight,
            )),
        Text(
          "$activityCount ${tr('activities')}",
          style: Styles.regular(
              size: 10, color: context.appColors.subHeadingTitle),
        ),
        Row(
          children: [
            SvgPicture.asset(
              'assets/images/coin.svg',
              width: width(context) * 0.05,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              "${coinCount.toInt()}",
              style: Styles.getRegularThemeStyle(context, size: 12),
            ),
          ],
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
