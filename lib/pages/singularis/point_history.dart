import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/utils/constant.dart';

class PointHistory extends StatefulWidget {
  const PointHistory({super.key});

  @override
  State<PointHistory> createState() => _PointHistoryState();
}

class _PointHistoryState extends State<PointHistory> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0.0,
          backgroundColor: context.appColors.surface,
          title: Text("Point History",
              style: TextStyle(
                  color: context.appColors.headingTitle,
                  fontWeight: FontWeight.bold)),
          leading: Icon(
            Icons.arrow_back_ios_new_outlined,
            color: context.appColors.headingTitle,
          ),
        ),
        body: SizedBox(
          height: height(context) * 0.5,
          width: width(context),
          child: ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                return SingleChildScrollView(
                    child: ListTile(
                        leading: SvgPicture.asset(
                          'assets/images/coin.svg',
                          width: 30,
                        ),
                        title: Text.rich(TextSpan(
                            text: '20',
                            style: TextStyle(
                                fontSize: 16,
                                color: context.appColors.headingTitle,
                                fontWeight: FontWeight.bold),
                            children: [
                              TextSpan(
                                text: '  earned from Activity',
                                style: TextStyle(
                                    fontSize: 16,
                                    color: context.appColors.headingText,
                                    fontWeight: FontWeight.w300),
                              )
                            ]))));
              }),
        ));
  }
}
