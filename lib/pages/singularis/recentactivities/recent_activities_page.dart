import 'package:buttons_tabbar/buttons_tabbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/pages/gcarvaan/post/gcarvaan_post_page.dart';
import 'package:masterg/pages/singularis/recentactivities/recent_activities_reels_page.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';

import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/providers/video_player_provider.dart';
import '../../../utils/styles.dart';

class RecentActivitiesPage extends StatefulWidget {
  final int animateToIndex;
  const RecentActivitiesPage({super.key, this.animateToIndex = 0});

  @override
  State<RecentActivitiesPage> createState() => _RecentActivitiesPageState();
}

class _RecentActivitiesPageState extends State<RecentActivitiesPage> {
  bool selectedFlag = false;

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<VideoPlayerProvider>(
          create: (context) => VideoPlayerProvider(true),
        ),
      ],
      child: Consumer<VideoPlayerProvider>(
        builder: (context, menuProvider, child) => BlocManager(
          initState: (context) {},
          child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {},
            child: Scaffold(
              backgroundColor: context.appColors.background,
              appBar: AppBar(
                  backgroundColor: context.appColors.surface,
                  elevation: 0,
                  leading: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: Color.fromARGB(255, 189, 193, 211),
                      )),
                  title: Text(
                    'recent_activities',
                    style: Styles.getSemiboldThemeStyle(context),
                  ).tr()),
              body: DefaultTabController(
                length: 2,
                initialIndex: widget.animateToIndex,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 10.0, bottom: 30.0),
                      child: ButtonsTabBar(
                        backgroundColor: context.appColors.surface,
                        borderColor: context.appColors.gradientRight,
                        unselectedBackgroundColor: context.appColors.textWhite,
                        labelStyle: TextStyle(
                            color: context.appColors.tabSelected,
                            fontWeight: FontWeight.bold),
                        unselectedLabelStyle: TextStyle(
                            color: context.appColors.textBlack,
                            fontWeight: FontWeight.bold),
                        borderWidth: 1,
                        unselectedBorderColor: context.appColors.grey3,
                        radius: 100,
                        tabs: [
                          Tab(
                            //icon: Icon(Icons.directions_car),
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: 20.0, right: 20.0),
                              child: Text('community').tr(),
                            ),
                          ),
                          Tab(
                            //icon: Icon(Icons.directions_transit),
                            //text: "Reels",
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: 20.0, right: 20.0),
                              child: Text('reels').tr(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        physics: NeverScrollableScrollPhysics(),
                        children: [
                          GCarvaanPostPage(
                            fileToUpload: null,
                            desc: null,
                            filesPath: null,
                            formCreatePost: false,
                            recentActivities: true,
                            fromUserActivity: true,
                          ),
                          RecentActivitiesReelsPage(),
                          //ReelsDashboardPage(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    /*return Scaffold(
      backgroundColor: context.appColors.white,
      appBar: AppBar(
          backgroundColor: context.appColors.white,
          elevation: 0,
          leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                color: context.appColors.headingPrimaryColor,
              )),
          title: Text(
            'Recent Activities',
            style: Styles.getSemiboldThemeStyle(context),
          )),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(right: 150, top: 10.0, bottom: 30.0),
              child: ButtonsTabBar(
                backgroundColor: context.appColors.primaryForeground,
                borderColor: context.appColors.gradientRight,
                unselectedBackgroundColor: context.appColors.primaryForeground,
                labelStyle: TextStyle(color: context.appColors.selected, fontWeight: FontWeight.bold),
                unselectedLabelStyle: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                borderWidth: 1,
                unselectedBorderColor: context.appColors.grey3,
                radius: 100,
                tabs: [
                  Tab(
                    //icon: Icon(Icons.directions_car),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Text('Community'),
                    ),
                  ),
                  Tab(
                    //icon: Icon(Icons.directions_transit),
                    //text: "Reels",
                    child: Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Text('Reels'),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                physics: NeverScrollableScrollPhysics(),
                children: [
                  GCarvaanPostPage(
                    fileToUpload: null,
                    desc: null,
                    filesPath: null,
                    formCreatePost: false,
                    recentActivities: true),
                  ReelsDashboardPage(),
                ],
              ),
            ),
          ],
        ),
      )
    );*/
  }
}
