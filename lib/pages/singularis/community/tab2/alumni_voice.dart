import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:path_provider/path_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:masterg/pages/auth_pages/select_interest.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/custom_pages/card_loader.dart';
import 'package:masterg/utils/custom_outline_button.dart';
import 'package:masterg/utils/utility.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../../../blocs/bloc_manager.dart';
import '../../../../blocs/home_bloc.dart';
import '../../../../data/api/api_service.dart';
import '../../../../data/models/response/home_response/joy_category_response.dart';
import '../../../../data/models/response/home_response/joy_content_list_response.dart';
import '../../../../data/providers/video_player_provider.dart';
import '../../../../local/pref/Preference.dart';
import '../../../../utils/Log.dart';

// import '../../../../utils/Strings.dart';
import '../../../../utils/styles.dart';
import '../../../../utils/config.dart';
import '../../../../utils/constant.dart';
import '../../../../utils/offline_data/url_model.dart';
import '../../../custom_pages/custom_widgets/next_page_routing.dart';
import '../../../ghome/widget/view_widget_details_page.dart';

late FlickManager customVideoController;
YoutubePlayerController ytController = YoutubePlayerController(
    flags: YoutubePlayerFlags(
      autoPlay: true,
    ),
    initialVideoId: '');

class AlumniVoice extends StatefulWidget {
  final int? postId;

  const AlumniVoice({super.key, this.postId});

  @override
  State<AlumniVoice> createState() => _AlumniVoiceState();
}

class _AlumniVoiceState extends State<AlumniVoice> with WidgetsBindingObserver {
  List<JoyContentListElement>? joyContentListResponse;
  List<JoyContentListElement>? joyContentListView;

  List<JoyContentListElement>? joyContentByPostIdResponse;

  List<String>? isAppend;

  Box? box;
  late VideoPlayerProvider videoPlayerProvider;
  // bool _isJoyCategoryLoading = true;
  List<ListElement>? joyCategoryList = [];
  int? selectedJoyContentCategoryId = 1;
  bool isLoading = false;
  bool? handleAppLink = true;
  Dio? dio;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    if (widget.postId != null) {
      handleAppLink = true;
      _getJoyContentByPostId(postId: widget.postId);
    } else {
      handleAppLink = false;

      getFilters();
      _getJoyContentList();
    }
  }

  void getFilters() {
    BlocProvider.of<HomeBloc>(context).add(JoyCategoryEvent());
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      //pause video
      // _videoController.play();

      ytController
        ..mute()
        ..play();
      videoPlayerProvider.mute();

      setState(() {
        customVideoController.flickControlManager?.pause();
        customVideoController.flickControlManager?.mute();
      });

      // ytController.mute();
    }
  }

  void _getJoyContentList() {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(JoyContentListEvent());
  }

  //TODO: Get Alumni Content
  void _handleJoyContentListResponse(
      JoyContentListState state, VideoPlayerProvider value) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("JoyContentListState....................");
          log('calll again 1');
          Log.v(state.response!.data!.list.toString());
          joyContentListResponse = state.response!.data!.list;
          joyContentListView = joyContentListResponse;
          log('>>>>>>>${joyContentListView?.length} and ${joyContentByPostIdResponse?.length}');

          if (widget.postId != null && handleAppLink == true) {
            joyContentListView?.insert(0, joyContentByPostIdResponse!.first);
            handleAppLink = false;

            FirebaseAnalytics.instance
                .logEvent(name: 'wow_studio_alumni_view', parameters: {
              "alumni_name": joyContentListView![0].title ?? "",
            });
            value.enableProviderControl();
            value.mute();

            value.pause().then((value) async {
              await showModalBottomSheet(
                  context: context,
                  backgroundColor: context.appColors.surface,
                  isScrollControlled: true,
                  builder: (context) {
                    return FractionallySizedBox(
                        heightFactor: 1.0,
                        child: ViewWidgetDetailsPage(
                          joyContentList: joyContentListView,
                          currentIndex: 0,
                        ));
                  });
              log('calll again 2');
              //     .then((value) {
              //   // getFilters();
              //   // _getJoyContentList();
              // });
            });
          }
          log('calll again 3');

          isLoading = false;
          setState(() {});

          break;
        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getJoyContentByPostId({int? postId}) {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context)
        .add(JoyContentByPostIdEvent(postId: postId));
  }

  void _handleJoyContentByPostIdResponse(JoyContentByPostIdState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("JoyContentByPostIdState....................");
          Log.v(state.response!.data!.list.toString());
          joyContentByPostIdResponse = state.response!.data!.list;

          getFilters();
          _getJoyContentList();

          break;
        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  //TODO: Offline Download video
  /*Future<void> _downloadAndSaveVideo(
      {String? videoURL,
      String? videoThumbnail,
      String? view,
      String? des,
      int? post_id}) async {
    //String url = videoURL!;
    log('videoURL====${videoURL!}');
    String url = '';
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String savePath = appDocDir.path + '/video.mp4';

      */ /*await dio?.download(
        videoURL,
        savePath,
        onReceiveProgress: (received, total) {
         log('progress===${total}');
          if (total != -1) {
           log('progress1===${total}');
            double progress = (received / total * 100);
            // Update UI with download progress
           log('progress2===${progress}');
            setState(() {
              _progress = progress;
            });
          }
        },
      );*/ /*

      await dio?.download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            double progress = (received / total * 100);
            // Update UI with download progress
            setState(() {
              _progress = progress;
            });
          }
        },
      );

     log('File downloaded to: $savePath');

      // Save the file path to Hive
      final box = await Hive.openBox<URLModel>('videos_offline');

      bool isDuplicate = box.values.any((element) => element.postid == post_id);
      if (isDuplicate) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Video already exists!'),
          ),
        );
      } else {
        box.add(URLModel(savePath, videoThumbnail!, view!, des!, post_id!));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Video added successfully'),
          ),
        );
      }
    } catch (e) {
      log('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error downloading and saving video'),
        ),
      );
    }
  }*/

  Future<void> _downloadAndSaveVideo({
    required String videoURL,
    required String videoThumbnail,
    required String view,
    required String des,
    required int post_id,
  }) async {
    try {
      log('videoURL====$videoURL');

      Directory appDocDir = await getApplicationDocumentsDirectory();
      String savePath = '${appDocDir.path}/video.mp4';

      await dio?.download(
        videoURL,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            double progress = (received / total * 100);
            setState(() {
              _progress = progress;
            });
          }
        },
      );

      log('File downloaded to: $savePath');

      // Save the file path to Hive
      final box = await Hive.openBox<URLModel>('videos_offline');

      bool isDuplicate = box.values.any((element) => element.postid == post_id);
      if (isDuplicate) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Video already exists!'),
          ),
        );
      } else {
        box.add(URLModel(savePath, videoThumbnail, view, des, post_id));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Video added successfully'),
          ),
        );
      }
    } catch (e) {
      log('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error downloading and saving video'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenWithLoader(
      isLoading: isLoading,
      body: MultiProvider(
          providers: [
            ChangeNotifierProvider<VideoPlayerProvider>(
              create: (context) => VideoPlayerProvider(true),
            ),
          ],
          child: Consumer<VideoPlayerProvider>(
            builder: (context, value, child) => BlocManager(
              initState: (context) {
                videoPlayerProvider = value;
              },
              child: BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is JoyContentListState) {
                    _handleJoyContentListResponse(state, value);
                  }
                  if (state is JoyContentByPostIdState) {
                    _handleJoyContentByPostIdResponse(state);
                  }
                },
                child: SingleChildScrollView(
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 15.0, top: 2.0, right: 15.0, bottom: 20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        box!.get("joy_category") != null
                            ? __getJoyCategoryWidget(context, value)
                            : CardLoader(),
                        ValueListenableBuilder(
                            valueListenable: box!.listenable(),
                            builder: (bc, Box box, child) {
                              if (box.get("joyContentListResponse") == null) {
                                return Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        0.07,
                                    margin: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 20),
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                        borderRadius: BorderRadius.circular(6)),
                                  ),
                                );
                              } else if (box
                                  .get("joyContentListResponse")
                                  .isEmpty) {
                                return SizedBox(
                                  height: height(context) * 0.5,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/wow_studio_empty.svg',
                                        height: height(context) * 0.15,
                                      ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Text(
                                        'wow_studio_blank_state',
                                        textAlign: TextAlign.center,
                                        style: Styles.regular(
                                            size: 14,
                                            color:
                                                context.appColors.headingTitle),
                                      ).tr()
                                    ],
                                  ),
                                );
                              }
                              // return  Text('${joyContentListView?[0].toJson()}\n${joyContentListView?[1].toJson()}');

                              if (widget.postId == null) {
                                joyContentListResponse = box
                                    .get("joyContentListResponse")
                                    .map((e) => JoyContentListElement.fromJson(
                                        Map<String, dynamic>.from(e)))
                                    .cast<JoyContentListElement>()
                                    .toList();

                                joyContentListView = joyContentListResponse;
                              }

                              if (widget.postId != null &&
                                  (joyContentListView?.length == 0 ||
                                      joyContentListView?.length == null)) {
                                return SizedBox();
                              }

                              if (selectedJoyContentCategoryId != 1) {
                                joyContentListView = joyContentListView
                                    ?.where((element) =>
                                        element.categoryId ==
                                        selectedJoyContentCategoryId)
                                    .toList();
                              }

                              if (joyContentListView?.length == 0) {
                                return SizedBox(
                                  height: height(context) * 0.5,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/images/wow_studio_empty.svg',
                                        height: height(context) * 0.15,
                                      ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Text(
                                        'wow_studio_blank_state',
                                        textAlign: TextAlign.center,
                                        style: Styles.regular(
                                            size: 14,
                                            color:
                                                context.appColors.headingTitle),
                                      ).tr()
                                    ],
                                  ),
                                );
                              }

                              return Padding(
                                //padding: const EdgeInsets.symmetric(vertical: 10),
                                padding: const EdgeInsets.only(
                                    top: 10.0, bottom: 140.0),
                                child: Visibility(
                                  visible: joyContentListView!.isNotEmpty,
                                  child: GridView.builder(
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: joyContentListView!.length,
                                    shrinkWrap: true,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                            mainAxisSpacing: 0,
                                            crossAxisSpacing: 20,
                                            childAspectRatio: 2 / 3,
                                            mainAxisExtent:
                                                MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    (Utility().isRTL(context)
                                                        ? 0.36
                                                        : 0.35),
                                            crossAxisCount: 2),
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return InkWell(
                                        onTap: () async {
                                          FirebaseAnalytics.instance.logEvent(
                                              name: 'wow_studio_alumni_view',
                                              parameters: {
                                                "alumni_name":
                                                    joyContentListView![index]
                                                            .title ??
                                                        "",
                                              });
                                          value.enableProviderControl();
                                          value.mute();

                                          value.pause().then((value) async {
                                            await showModalBottomSheet(
                                                context: context,
                                                backgroundColor:
                                                    context.appColors.surface,
                                                isScrollControlled: true,
                                                builder: (context) {
                                                  return FractionallySizedBox(
                                                      heightFactor: 1.0,
                                                      child:
                                                          ViewWidgetDetailsPage(
                                                        joyContentList:
                                                            joyContentListView,
                                                        currentIndex: index,
                                                      ));
                                                }).then((value) {
                                              if (handleAppLink == false) {
                                                getFilters();
                                                _getJoyContentList();
                                              }
                                            });
                                          });
                                        },
                                        child: Column(
                                          children: [
                                            Container(
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                child: Stack(
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      child: Container(
                                                          height: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .height *
                                                              0.25,
                                                          width: MediaQuery.of(
                                                                  context)
                                                              .size
                                                              .width,
                                                          decoration: BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          10)),
                                                          foregroundDecoration:
                                                              BoxDecoration(
                                                                  gradient:
                                                                      LinearGradient(
                                                            end:
                                                                const Alignment(
                                                                    0.0, -1),
                                                            begin:
                                                                const Alignment(
                                                                    0.0, 0.8),
                                                            colors: [
                                                              const Color(
                                                                      0x8A000000)
                                                                  .withValues(
                                                                      alpha:
                                                                          0.4),
                                                              Colors.black12
                                                                  .withValues(
                                                                      alpha:
                                                                          0.0)
                                                            ],
                                                          )),
                                                          child:
                                                              CachedNetworkImage(
                                                            imageUrl:
                                                                '${joyContentListView![index].thumbnailUrl}',
                                                            imageBuilder: (context,
                                                                    imageProvider) =>
                                                                Container(
                                                              decoration:
                                                                  BoxDecoration(
                                                                      image:
                                                                          DecorationImage(
                                                                image:
                                                                    imageProvider,
                                                                fit: BoxFit
                                                                    .fitHeight,
                                                              )),
                                                            ),
                                                            placeholder:
                                                                (context,
                                                                        url) =>
                                                                    Image.asset(
                                                              'assets/images/placeholder.png',
                                                              fit: BoxFit.fill,
                                                            ),
                                                            errorWidget:
                                                                (context, url,
                                                                        error) =>
                                                                    Image.asset(
                                                              'assets/images/placeholder.png',
                                                              fit: BoxFit.fill,
                                                            ),
                                                          )
                                                          // child: Image.network(
                                                          //   '${joyContentListView![index].thumbnailUrl}',
                                                          //   fit: BoxFit.fill,
                                                          // ),
                                                          ),
                                                    ),
                                                    if (joyContentListView![
                                                                index]
                                                            .resourceType
                                                            ?.toLowerCase() ==
                                                        'video')
                                                      Positioned.fill(
                                                        child: Align(
                                                          alignment:
                                                              Alignment.center,
                                                          child:
                                                              SvgPicture.asset(
                                                            'assets/images/play_video_icon.svg',
                                                            height: 30.0,
                                                            width: 30.0,
                                                            allowDrawingOutsideViewBox:
                                                                true,
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                )),
                                            Container(
                                              margin: EdgeInsets.only(top: 4),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  if (joyContentListView![index]
                                                          .resourceType
                                                          ?.toLowerCase() ==
                                                      'video')
                                                    APK_DETAILS["offline_video_download"] ==
                                                            "1"
                                                        ? InkWell(
                                                            onTap: () {
                                                              //Singh
                                                              /*Navigator.push(context,
                                                          MaterialPageRoute(builder: (context) =>
                                                              OfflineDownloadFile(
                                                                videoURL: joyContentListView![index].multiFileUploads![0],
                                                                videoThumbnail: joyContentListView![index].thumbnailUrl,
                                                                view: joyContentListView![index].viewCount.toString(),
                                                                des: joyContentListView![index].title,
                                                              )));*/
                                                              _downloadAndSaveVideo(
                                                                videoURL: joyContentListView![
                                                                        index]
                                                                    .multiFileUploads![0],
                                                                videoThumbnail:
                                                                    joyContentListView![
                                                                            index]
                                                                        .thumbnailUrl!,
                                                                view: joyContentListView![
                                                                        index]
                                                                    .viewCount
                                                                    .toString(),
                                                                des: joyContentListView![
                                                                        index]
                                                                    .title!,
                                                                post_id:
                                                                    joyContentListView![
                                                                            index]
                                                                        .id!,
                                                              );
                                                              //Hive.deleteBoxFromDisk('videos_offline');
                                                            },
                                                            //child: Icon(Icons.download_outlined,  color: Colors.blue,),
                                                            child:
                                                                CircularPercentIndicator(
                                                              radius: 9.0,
                                                              lineWidth: 1.8,
                                                              //animation: true,
                                                              percent:
                                                                  _progress,
                                                              center: Icon(
                                                                Icons.download,
                                                                color:
                                                                    Colors.blue,
                                                                size: 12,
                                                              ),
                                                              backgroundColor:
                                                                  Colors.grey
                                                                      .shade300,
                                                              circularStrokeCap:
                                                                  CircularStrokeCap
                                                                      .round,
                                                              progressColor:
                                                                  Colors
                                                                      .blueAccent,
                                                            ),
                                                          )
                                                        : SizedBox(),

                                                  joyContentListView![index]
                                                              .viewCount !=
                                                          null
                                                      ? Row(
                                                          children: [
                                                            Text(
                                                                '${joyContentListView![index].viewCount}  ${tr('views')}',
                                                                style: Styles.regular(
                                                                    size: 10,
                                                                    color: context
                                                                        .appColors
                                                                        .grey3)),
                                                            // if (joyContentListView![
                                                            //             index]
                                                            //         .viewCount! >
                                                            //     1)
                                                            //   Text(
                                                            //       Preference.getInt(Preference
                                                            //                   .APP_LANGUAGE) ==
                                                            //               1
                                                            //           ? 's'
                                                            //           : '',
                                                            //       style: Styles.regular(
                                                            //           size: 10,
                                                            //           color: context.appColors
                                                            //               .grey3)),
                                                          ],
                                                        )
                                                      : Text(
                                                          '${0}  ${tr('views')}',
                                                          style: Styles.regular(
                                                              size: 10,
                                                              color: context
                                                                  .appColors
                                                                  .grey3)),
                                                  // SizedBox(
                                                  //   width: 10,
                                                  //   height: 4,
                                                  // ),
                                                  SizedBox(
                                                    //width: 170,
                                                    child: Text(
                                                        joyContentListView![
                                                                    index]
                                                                .title ??
                                                            '',
                                                        maxLines: 2,
                                                        softWrap: true,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: Styles.regular(
                                                            size: 13,
                                                            color: context
                                                                .appColors
                                                                .grey1)),
                                                  ),
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            }),
                        SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          )),
    );
  }

  Widget __getJoyCategoryWidget(
      BuildContext context, VideoPlayerProvider videoController) {
    return box != null
        ? ValueListenableBuilder(
            valueListenable: box!.listenable(),
            builder: (bc, Box box, child) {
              if (box.get("joy_category") == null) {
                return Shimmer.fromColors(
                  baseColor: context.appColors.shimmerBase,
                  highlightColor: context.appColors.shimmerHighlight,
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.07,
                    margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        color: context.appColors.surface,
                        borderRadius: BorderRadius.circular(6)),
                  ),
                );
              } else if (box.get("joy_category").isEmpty) {
                return SizedBox(
                  height: MediaQuery.of(context).size.height * 0.07,
                  width: MediaQuery.of(context).size.width,
                  // child: Center(
                  //   child: Text(
                  //     "${Strings.of(context)?.CategoryNotFound}",
                  //     style: Styles.getRegularThemeStyle(context),
                  //   ),
                  // ),
                );
              }

              joyCategoryList = box
                  .get("joy_category")
                  .map(
                      (e) => ListElement.fromJson(Map<String, dynamic>.from(e)))
                  .cast<ListElement>()
                  .toList();
              joyCategoryList!.insert(
                  0,
                  ListElement(
                    id: 1,
                    title: tr('for_you'),
                    description: tr('for_you'),
                    createdAt: 1647343211,
                    updatedAt: 1647343211,
                    createdBy: 0,
                    updatedBy: 0,
                    status: tr('active'),
                    sectionType: 3,
                    isSelected: 1,
                    parentId: 1,
                    video: Preference.getString(
                        Preference.DEFAULT_VIDEO_URL_CATEGORY),
                    image:
                        "https://qa.learningoxygen.com/joy_content/do-100-erase-or-removal-your-photo-or-imagage-bacground-627f.jpeg",
                  ));

              List<ListElement> temp = [];

              for (int i = 0; i < joyCategoryList!.length; i++) {
                if (joyCategoryList![i].isSelected == 1) {
                  temp.add(joyCategoryList![i]);
                }
              }
              joyCategoryList = temp;
              int? isParentLanguage =
                  Preference.getInt(Preference.IS_PRIMARY_LANGUAGE) ?? 1;

              return Column(children: [
                Row(
                  children: [
                    Container(
                      ///height: MediaQuery.of(context).size.height * 0.09,
                      width: MediaQuery.of(context).size.width * 0.8,
                      //width: MediaQuery.of(context).size.width,
                      margin: EdgeInsets.only(right: 17.0, top: 0.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.width * 0.10,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemBuilder: (context, index) {
                                bool isSelected = false;

                                if (isParentLanguage == 1) {
                                  isSelected = joyCategoryList![index].id ==
                                      selectedJoyContentCategoryId;
                                } else {
                                  isSelected =
                                      joyCategoryList![index].parentId ==
                                          selectedJoyContentCategoryId;
                                }

                                return Transform.scale(
                                  scale: 0.9,
                                  child: CustomOutlineButton(
                                    strokeWidth: isSelected ? 1.5 : 1,
                                    radius: 100,
                                    gradient: LinearGradient(
                                      colors: [
                                        isSelected
                                            ? context.appColors.gradientLeft
                                            : context.appColors.textBlack
                                                .withValues(alpha: 0.3),
                                        isSelected
                                            ? context.appColors.gradientRight
                                            : context.appColors.textBlack
                                                .withValues(alpha: 0.3)
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.topRight,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: GradientText(
                                        '${joyCategoryList![index].title}',
                                        style: Styles.getRegularThemeStyle(
                                            context,
                                            size: 12),
                                        colors: [
                                          isSelected
                                              ? context.appColors.gradientLeft
                                              : context.appColors.textBlack,
                                          isSelected
                                              ? context.appColors.gradientRight
                                              : context.appColors.textBlack,
                                        ],
                                      ),
                                    ),
                                    onPressed: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name: 'wow_studio_category',
                                          parameters: {
                                            "category":
                                                joyCategoryList![index].title ??
                                                    "",
                                          });

                                      setState(() {
                                        if (isParentLanguage == 1) {
                                          selectedJoyContentCategoryId =
                                              joyCategoryList![index].id;
                                        } else {
                                          selectedJoyContentCategoryId =
                                              joyCategoryList![index].parentId;
                                        }

                                        if (selectedJoyContentCategoryId == 1) {
                                          joyContentListView =
                                              joyContentListResponse;
                                        } else {
                                          if (isParentLanguage != 1) {
                                            joyContentListView =
                                                joyContentListResponse!
                                                    .where((element) =>
                                                        element.categoryId ==
                                                        joyCategoryList![index]
                                                            .parentId)
                                                    .toList();
                                          } else {
                                            joyContentListView =
                                                joyContentListResponse!
                                                    .where((element) =>
                                                        element.categoryId ==
                                                        joyCategoryList![index]
                                                            .id)
                                                    .toList();
                                          }
                                        }

                                        //   ytController = YoutubePlayerController(
                                        //       flags: YoutubePlayerFlags(
                                        //         mute: videoController.isMute,
                                        //         autoPlay: true,
                                        //         loop: true,
                                        //       ),
                                        //       initialVideoId:
                                        //           '${YoutubePlayer.convertUrlToId('${joyCategoryList![index].video}')}');
                                      });
                                    },
                                  ),
                                );
                              },
                              itemCount: joyCategoryList!.length,
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        //TODO:used this page then change in home_provider => mapInterest
                        /*Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    OnboardingSelecteInterestPage()));*/
                        Navigator.push(
                            context,
                            NextPageRoute(InterestPage(
                              backEnable: true,
                            ))).then((value) {
                          getFilters();
                          _getJoyContentList();
                        });
                      },
                      child: Container(
                        //color: context.appColors.error,
                        height: MediaQuery.of(context).size.height * 0.07,
                        margin: EdgeInsets.only(left: 0.0),
                        child: Center(
                          child: Icon(
                            Icons.add,
                            size: 20,
                            color: context.appColors.textBlack,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ]);
            },
          )
        : Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.07,
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(6)),
            ),
          );
  }
}


  // void _handleJoyCategoryResponse(JoyCategoryState state) {
  //   var loginState = state;
  //   setState(() {
  //     switch (loginState.apiState) {
  //       case ApiStatus.LOADING:
  //         Log.v("Loading....................");
  //         _isJoyCategoryLoading = true;
  //         isLoading = true;
  //         break;
  //       case ApiStatus.SUCCESS:
  //         Log.v("JoyCategoryState....................");
  //         Log.v(state.response!.data!.list.toString());

  //         for (int i = 0; i < state.response!.data!.list!.length; i++) {
  //           if (state.response!.data!.list![i].isSelected == 1) {
  //             joyCategoryList!.add(state.response!.data!.list![i]);
  //           }
  //         }

  //         //joyCategoryList = state.response.data.list;

  //         // box.put('joy_category',
  //         //     state.response.data.list.map((e) => e.toJson()).toList());
  //         Log.v("JoyCategoryState Done ....................");

  //         _isJoyCategoryLoading = false;
  //         isLoading = false;
  //         setState(() {});
  //         break;
  //       case ApiStatus.ERROR:
  //         _isJoyCategoryLoading = false;
  //         isLoading = false;
  //         Log.v("Error..........................");
  //         Log.v("ErrorHome..........................${loginState.error}");
  //         break;
  //       case ApiStatus.INITIAL:
  //         break;
  //     }
  //   });
  // }

