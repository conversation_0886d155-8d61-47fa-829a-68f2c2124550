import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:video_player/video_player.dart';

class CompetitionVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final int id;
  const CompetitionVideoPlayer(
      {super.key, required this.videoUrl, required this.id});

  @override
  State<CompetitionVideoPlayer> createState() => _CompetitionVideoPlayerState();
}

class _CompetitionVideoPlayerState extends State<CompetitionVideoPlayer> {
  late VideoPlayerController _videoPlayerController;
  int currentMin = 0, prevMin = 0;
  late FlickManager flickManager;

  void listenVideoChanges() {
    _videoPlayerController.addListener(() {
      currentMin = _videoPlayerController.value.position.inMinutes;
      if (currentMin != 0 && prevMin != currentMin) {
        prevMin = currentMin;
        _updateCourseCompletion(currentMin);
      }
    });
  }

  @override
  void initState() {
    _updateCourseCompletion(0);
    _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

    flickManager = FlickManager(
      videoPlayerController: _videoPlayerController,
    );
    listenVideoChanges();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);

    super.initState();
  }

  @override
  void dispose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    _videoPlayerController.dispose();
    super.dispose();
  }

  void _updateCourseCompletion(bookmark) async {
    //change bookmark with 25
    BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
        bookmark: bookmark, contentId: widget.id, completionPercent: 0));
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Transform.scale(
          scale: 0.7,
          child: Container(
            decoration: BoxDecoration(
              color: context.appColors.textBlack.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_back,
                size: 30,
                color: context.appColors.textWhite,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        ),
      ),
      body: FlickVideoPlayer(
        flickManager: flickManager,
        preferredDeviceOrientation: [
          DeviceOrientation.landscapeLeft,
        ],
        flickVideoWithControls: FlickVideoWithControls(
          videoFit: BoxFit.contain,
          controls: FlickPortraitControls(
            iconSize: 25,
            progressBarSettings: FlickProgressBarSettings(
              playedColor: context.appColors.textBlack.withValues(alpha: 0.1),
              handleRadius: 8,
              backgroundColor: context.appColors.grey,
              height: 4,
            ),
          ),
        ),
      ),
    );
  }
}
