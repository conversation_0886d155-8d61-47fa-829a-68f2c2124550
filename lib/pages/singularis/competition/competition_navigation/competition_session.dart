import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:url_launcher/url_launcher.dart';

class CompetitionSession extends StatefulWidget {
  final CompetitionContent? data;
  const CompetitionSession({super.key, this.data});

  @override
  State<CompetitionSession> createState() => _CompetitionSessionState();
}

class _CompetitionSessionState extends State<CompetitionSession> {
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    // List<String> listOfMonths = const [
    //   "January",
    //   "February",
    //   "March",
    //   "April",
    //   "May",
    //   "June",
    //   "July",
    //   "August",
    //   "September",
    //   "October",
    //   "November",
    //   "December"
    // ];

    String startDateString = "${widget.data?.startDate}";

    DateTime startDate =
        DateFormat("yyyy-MM-dd H:mm:ss").parse(startDateString);

    late DateTime now;
    now = currentIndiaTime!;
    //get india time alway because server gives indian time for checking start and end of sesssion

    return Scaffold(
        appBar: AppBar(
            elevation: 0.3,
            backgroundColor: context.appColors.surface,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: context.appColors.textBlack,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            title: Text(
              'interview',
              style: Styles.bold(
                color: context.appColors.textBlack,
              ),
            ).tr()),
        body: ScreenWithLoader(
          isLoading: isLoading,
          body: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              now = currentIndiaTime!;

              final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
              final String formatted = formatter.format(now);
              now = DateFormat("yyyy-MM-dd HH:mm:ss").parse(formatted);
              if (state is ZoomOpenUrlState) handleOpenUrlState(state);
            },
            child: Container(
                color: context.appColors.surface,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              widget.data?.contentTypeLabel ?? '',
                              style:
                                  Styles.getBoldThemeStyle(context, size: 14),
                            ),
                            Text(
                              widget.data?.isJoined ?? '',
                              style:
                                  Styles.getBoldThemeStyle(context, size: 14),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                        ),
                        child: Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                                color: context.appColors.gradientRight
                                    .withValues(alpha: 0.08),
                                borderRadius: BorderRadius.circular(8)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 40,
                                      backgroundImage: NetworkImage(
                                        '${widget.data?.baseFileUrl}${widget.data?.presenterImage}',
                                      ),
                                    ),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            Utility().decrypted128(
                                                '${widget.data?.presenter}'),
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: context
                                                    .appColors.textBlack)),
                                        SizedBox(height: 6),
                                        Text('${widget.data?.title}',
                                            style: Styles.regular(
                                                size: 12,
                                                color: context.appColors.grey3))
                                      ],
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      '${tr('start_date')}: ',
                                      style: Styles.regular(
                                          size: 14,
                                          color: context
                                              .appColors.subHeadingTitle),
                                    ),
                                    StrToTime(
                                      time: widget.data!.startDate!,
                                      dateFormat: 'yyyy-MMMM-dd, hh:mm a',
                                      appendString: '',
                                      textStyle: Styles.regular(
                                          size: 14,
                                          color: context
                                              .appColors.headingPrimaryColor),
                                    )
                                    // Text(
                                    //   '${Utility.ordinal(startDate.day)} ${listOfMonths[startDate.month - 1]} | $pmTime',
                                    //   style: Styles.regular(
                                    //       size: 14, color: context.appColors.headingPrimaryColor),
                                    // )
                                  ],
                                ),
                                SizedBox(
                                  height: 4,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      '${tr('duration')}:',
                                      style: Styles.regular(
                                          size: 14,
                                          color: context
                                              .appColors.subHeadingTitle),
                                    ),
                                    Text(
                                      ' ${widget.data?.duration} ${tr('minute')}',
                                      style: Styles.regular(
                                          size: 14,
                                          color: context
                                              .appColors.headingPrimaryColor),
                                    )
                                  ],
                                )
                              ],
                            )),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 20.0, top: 12, right: 20.0),
                        child: Text(
                          '${widget.data?.description}',
                          style: TextStyle(
                              fontWeight: FontWeight.w400,
                              color: context.appColors.subHeadingTitle),
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Spacer(),
                      // Text('$startDate and ${now.isAfter(startDate.subtract(Duration(
                      //             minutes: 15)))}'),
                      // Text(
                      //     '$now and ${startDate.add(Duration(minutes: int.parse('${widget.data?.duration}')))}'),
                      Center(
                        child: GestureDetector(
                            onTap: () {
                              debugPrint(
                                  'the current session time is${widget.data?.zoomUrl} $now and api time is $startDate');

                              //open session
                              if (now.isAfter(startDate.add(Duration(
                                  minutes: int.parse(
                                      '${widget.data?.duration}'))))) {
                                return;
                              } else if (now.isAfter(
                                  startDate.subtract(Duration(minutes: 15)))) {
                                if (widget.data?.zoomUrl != null) {
                                  BlocProvider.of<HomeBloc>(context)
                                      .add(ZoomOpenUrlEvent(
                                    contentId: widget.data?.id,
                                  ));
                                  launchUrl(
                                      Uri.parse('${widget.data?.zoomUrl}'),
                                      mode: LaunchMode.externalApplication);
                                } else {
                                  //make api call for url
                                  BlocProvider.of<HomeBloc>(context).add(
                                      ZoomOpenUrlEvent(
                                          contentId: widget.data?.id));
                                }
                              }
                            },
                            child: Container(
                              height: MediaQuery.of(context).size.height * 0.06,
                              width: MediaQuery.of(context).size.width,
                              padding: const EdgeInsets.all(10.0),
                              margin: const EdgeInsets.all(20.0),
                              decoration: BoxDecoration(
                                  color: now.isAfter(startDate.add(Duration(
                                              minutes: int.parse(
                                                  '${widget.data?.duration}')))) ||
                                          now.isAfter(startDate.subtract(
                                                  Duration(minutes: 15))) ==
                                              false
                                      ? context.appColors.headingPrimaryColor
                                          .withValues(alpha: 0.3)
                                      : context.appColors.headingPrimaryColor,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(21))),
                              child: Center(
                                child: Text(
                                  now.isAfter(startDate.add(Duration(
                                              minutes: int.parse(
                                                  '${widget.data?.duration}')))) ==
                                          true
                                      ? 'concluded'
                                      : 'join_now',
                                  style: Styles.semibold(
                                      size: 14,
                                      color: context.appColors.textWhite),
                                  textAlign: TextAlign.center,
                                ).tr(),
                              ),
                            )),
                      )
                    ])),
          ),
        ));
  }

  void handleOpenUrlState(ZoomOpenUrlState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        Log.v("Zoom Open Url Loading....................");
        isLoading = true;
        setState(() {});
        break;
      case ApiStatus.SUCCESS:
        Log.v("Zoom Open Url Success....................");
        isLoading = false;
        setState(() {});
        if (widget.data?.zoomUrl != null) return;

        if (state.response?.status == 0) {
          if (widget.data?.openUrl != null) {
            launchUrl(Uri.parse('${widget.data?.openUrl}'),
                mode: LaunchMode.externalApplication);
          } else if (widget.data?.zoomUrl != null)
            launchUrl(Uri.parse('${widget.data?.zoomUrl}'),
                mode: LaunchMode.externalApplication);
          else
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('${state.response?.error?.first}'),
            ));
        } else if (state.response?.data?.list?.joinUrl != null)
          launchUrl(Uri.parse('${state.response?.data?.list?.joinUrl}'),
              mode: LaunchMode.externalApplication);
        else if (widget.data?.openUrl != null)
          launchUrl(Uri.parse('${widget.data?.openUrl}'),
              mode: LaunchMode.externalApplication);
        else
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${widget.data?.contentTypeLabel} not started yet!'),
          ));
        break;

      case ApiStatus.ERROR:
        isLoading = false;
        setState(() {});
        Log.v("Zoom open url Error..........................");
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }
}
