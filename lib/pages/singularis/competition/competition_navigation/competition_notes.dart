import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/config.dart';
import '../../../../utils/utility.dart';
import '../../../training_pages/program_content/widgets/common_webview_page.dart';

class CompetitionNotes extends StatefulWidget {
  final int? id;
  final notesUrl;
  final String? title;
  const CompetitionNotes({super.key, this.notesUrl, this.id, this.title});

  @override
  State<CompetitionNotes> createState() => _CompetitionNotesState();
}

class _CompetitionNotesState extends State<CompetitionNotes> {
  Dio? dio;

  void _updateCourseCompletion(bookmark, int completionPercent) async {
    //change bookmark with 25
    try {
      BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
          bookmark: bookmark,
          contentId: widget.id,
          completionPercent: completionPercent));
    } catch (e) {
      Log.v('exception is $e');
    }
    setState(() {});
  }

  bool isLoading = false;
  double progress = 0.0;

  @override
  void initState() {
    super.initState();
  }

  Future<void> downloadFileOLD(
    String url,
    int postid,
  ) async {
    try {
      setState(() {
        isLoading = true;
        progress = 0.0;
      });

      // Get a temporary directory
      final dir = await getTemporaryDirectory();
      //final filePath = "${dir.path}/project_manager_resume.pdf";
      final filePath = "${dir.path}/learn$postid.pdf";

      // Download the file
      await Dio().download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              progress = received / total;
            });
          }
        },
      );

      log('FilePath:--------$filePath');
      // Open the file
      //await OpenFilex.open(filePath);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error downloading file: $e")),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  //TODO: Offline pdf Download Function
  // Future<void> _downloadAndSaveVideo({
  //   required String PdfUrl,
  //   required String videoThumbnail,
  //   required String view,
  //   required String des,
  //   required int postid,
  // }) async {
  //   try {
  //     log('videoURL====$PdfUrl');
  //     // String appDocDir;
  //     //Directory appDocDir = await getApplicationDocumentsDirectory();
  //     final dir = await getTemporaryDirectory();
  //     //final filePath = "${dir.path}/project_manager_resume.pdf";
  //     //final filePath = "${dir.path}/learn$postid.pdf"; //it is working

  //     final filePath = "${dir.path}/${PdfUrl.split('/').last}";
  //     await Dio().download(
  //       PdfUrl,
  //       filePath,
  //       onReceiveProgress: (received, total) {
  //         if (total != -1) {
  //           setState(() {
  //             progress = received / total;
  //           });
  //         }
  //       },
  //     );

  //     // Save the file path to Hive
  //     final box = await Hive.openBox<URLModel>('pdf_offline');
  //     bool isDuplicate = box.values.any((element) => element.postid == postid);
  //     if (isDuplicate) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(
  //           content: Text('file_already_exists').tr(),
  //         ),
  //       );
  //     } else {
  //       box.add(URLModel(filePath, videoThumbnail, view, des, postid));
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(
  //           content: Text('file_downloaded_successfully').tr(),
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(
  //         content: Text('download_failed').tr(),
  //       ),
  //     );
  //   }
  // }

  Future<void> downloadFile({
    required BuildContext context,
    required String? url,
    required String? filename,
  }) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      // final status =
      await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = '/sdcard/download/';
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      //final file = File("$localPath/${url!.split('/').last}");
      final fileName = Uri.decodeComponent(Uri.parse(url!).pathSegments.last);
      final file = File(path.join(localPath, fileName));
      print(file);
      if (await file.exists()) {
        Utility.showSnackBar(
            scaffoldContext: context, message: '${tr('file_already_exists')}');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('downloading_start').tr(),
        ));

        // final id =
        await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          saveInPublicStorage: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('file_downloaded_successfully').tr(),
            ),
          );
        });
      }
    } else {
      launchUrl(Uri.parse(url!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: Text(widget.title ?? '',
                style: TextStyle(color: context.appColors.textBlack)),
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.pop(
                context,
              ),
              icon: Icon(
                Icons.arrow_back_ios,
                color: context.appColors.textBlack,
              ),
            ),
            actions: APK_DETAILS["offline_pdf_download_learn"] == "1"
                ? <Widget>[
                    IconButton(
                      onPressed: () {
                        downloadFile(
                            context: context,
                            url: widget.notesUrl,
                            filename: '');

                        // _downloadAndSaveVideo(
                        //   PdfUrl: widget.notesUrl,
                        //   videoThumbnail: '',
                        //   view: 'pdf',
                        //   des: widget.title??'',
                        //   postid: widget.id!,
                        // );
                        //downloadFile(widget.notesUrl, widget.id!);
                      },
                      icon: Icon(
                        Icons.cloud_download_rounded,
                        color: context.appColors.textBlack,
                      ),
                    )
                  ]
                : null,
            backgroundColor: context.appColors.surface),
        body: SizedBox(
          width: width(context),
          height: height(context),
          child: widget.notesUrl.toString().toLowerCase().contains('pdf')
              ? PDF(
                  onViewCreated: ((controller) {
                    controller.getPageCount().then((value) {
                      _updateCourseCompletion(
                          1, double.parse('${(1 / value!) * 100}').toInt());
                    });
                  }),
                  onPageChanged: ((page, total) {
                    int pageno = page! + 1;

                    _updateCourseCompletion(pageno,
                        double.parse('${(pageno / total!) * 100}').toInt());
                  }),
                  enableSwipe: true,
                  gestureRecognizers: [
                    Factory(() => PanGestureRecognizer()),
                    Factory(() => VerticalDragGestureRecognizer())
                  ].toSet(),
                ).cachedFromUrl(
                  widget.notesUrl,
                  placeholder: (progress) => Center(child: Text('$progress %')),
                  errorWidget: (error) => Center(child: Text(error.toString())),
                )
              : widget.notesUrl.toString().toLowerCase().contains('jpg') ||
                      widget.notesUrl
                          .toString()
                          .toLowerCase()
                          .contains('jpeg') ||
                      widget.notesUrl.toString().toLowerCase().contains('png')
                  ? CommonWebviewPage(
                      strUrl: widget.notesUrl,
                      enableAppBar: false,
                    )
                  : widget.notesUrl.toString().toLowerCase().contains('zip') ||
                          widget.notesUrl
                              .toString()
                              .toLowerCase()
                              .contains('rar')
                      ? Center(
                          child: Container(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                (widget.notesUrl
                                            ?.toString()
                                            .toLowerCase()
                                            .endsWith('.rar') ??
                                        false)
                                    ? Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/rar.png',
                                            height: 130,
                                            width: 130,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(12.0),
                                            child: Text(
                                              'file_type_massage',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  color: Colors.black54,
                                                  fontSize: 13),
                                            ).tr(),
                                          ),
                                        ],
                                      )
                                    : (widget.notesUrl
                                                ?.toString()
                                                .toLowerCase()
                                                .endsWith('.zip') ??
                                            false)
                                        ? Column(
                                            children: [
                                              Image.asset(
                                                'assets/images/zip.png',
                                                height: 130,
                                                width: 130,
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(12.0),
                                                child: Text('file_type_massage',
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black54,
                                                            fontSize: 13))
                                                    .tr(),
                                              ),
                                            ],
                                          )
                                        : CommonWebviewPage(
                                            strUrl: widget.notesUrl,
                                            enableAppBar: false),
                              ],
                            ),
                          ),
                        )
                      : Center(
                          child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/no_view.png',
                              height: 100,
                              width: 100,
                            ),
                            Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: Text(
                                'file_type_massage',
                                textAlign: TextAlign.center,
                              ).tr(),
                            ),
                          ],
                        )),
        ));
  }
}
