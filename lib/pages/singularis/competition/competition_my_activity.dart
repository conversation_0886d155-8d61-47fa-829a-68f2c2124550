import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/singularis/competition/competition_detail.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class CompetitionMyAcitivityCard extends StatefulWidget {
  final String? image;
  final String? title;
  final int? totalAct;
  final int? doneAct;
  final int? id;
  final dynamic score;
  final int? gscore;
  final String? desc;
  final String? date;
  final String? difficulty;
  final String? conductedBy;
  final String? activityStatus;
  final int? rank;
  final bool? verticalView;
  final int? activityStatusNumeric;
  final String? progressStatus;
  final bool? enableProgressStatus;
  final Function? onClose;

  const CompetitionMyAcitivityCard(
      {super.key,
      this.image,
      this.title,
      this.totalAct,
      this.doneAct,
      this.id,
      this.score,
      this.desc,
      this.date,
      this.difficulty,
      this.conductedBy,
      this.activityStatus,
      this.rank,
      this.verticalView = false,
      this.onClose,
      this.activityStatusNumeric,
      this.progressStatus,
      required this.gscore,
      this.enableProgressStatus = false});

  @override
  State<CompetitionMyAcitivityCard> createState() =>
      _CompetitionMyAcitivityCardState();
}

class _CompetitionMyAcitivityCardState extends State<CompetitionMyAcitivityCard>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    );

    if (kDebugMode) {
      debugPrint('widget.doneAct:--- ${widget.doneAct}');
      debugPrint('widget.totalAct:--- ${widget.totalAct}');
      debugPrint('widget.verticalView:--- ${widget.verticalView}');
    }
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // double percent = widget.doneAct/ widget.totalAct ;
    double percent = (double.parse('${widget.doneAct}') /
            double.parse('${widget.totalAct}')) *
        100;
    double progressBarWidth = widget.verticalView == true ? 0.65 : 0.45;
    if (kDebugMode) {
      debugPrint('percent==:- $percent');
      debugPrint('progressBarWidth==: $progressBarWidth');
    }

    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            NextPageRoute(CompetitionDetail(
              competitionId: widget.id,
              isEvent: widget.enableProgressStatus,
              // competition: Competition(
              //     image: widget.image,
              //     id: widget.id,
              //     competitionLevel: widget.difficulty,
              //     description: widget.desc,
              //     gScore: widget.score ?? 0,
              //     startDate: widget.date,
              //     name: widget.title),
            )));
        // TO DO : comment for getting hidden every time we attempt an activity in that competition.
        //Also when we view any other competition. When we switch tabs and return to the competitions list, it is displayed again,

        // .then((value) => widget.onClose!());
      },
      child: Container(
        width: width(context) * 0.8,
        height: height(context) * 0.15,
        margin: EdgeInsets.symmetric(vertical: 12, horizontal: 6),
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: context.appColors.grey, width: 1),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: width(context) * 0.2,
              height: width(context) * 0.2,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: '${widget.image}',
                  // width: 100,
                  // height: 120,
                  errorWidget: (context, url, error) => Image.asset(
                    'assets/images/comp_emp.png',
                    fit: BoxFit.cover,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(
              width: 10,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    '${widget.title}',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Styles.getBoldThemeStyle(context, size: 16),
                  ),
                  SizedBox(height: 4),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text: '${widget.doneAct}',
                            style: Styles.bold(
                                size: 13, color: context.appColors.bodyText)),
                        TextSpan(
                            text: '/${widget.totalAct} ${tr('activity_done')}',
                            style: Styles.regular(
                                size: 13, color: context.appColors.bodyText)),
                      ],
                    ),
                  ),

                  //Text('${widget.progressStatus}'),
                  widget.enableProgressStatus == true
                      ? widget.progressStatus != null
                          ? Padding(
                              padding:
                                  const EdgeInsets.only(top: 6.0, bottom: 6.0),
                              /*child: Text(
                      '${widget.progressStatus?? ''}',
                      style: Styles.regular(
                          color: context.appColors.error, size: 15),
                    ),*/
                              child: widget.progressStatus?.toLowerCase() ==
                                      'live'
                                  ? Row(
                                      children: [
                                        SvgPicture.asset(
                                          'assets/images/live_icon.svg',
                                          fit: BoxFit.fitHeight,
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 5.0),
                                          child: FadeTransition(
                                            opacity: _animation,
                                            child: Text(
                                              'live',
                                              style: Styles.bold(
                                                  color:
                                                      context.appColors.error,
                                                  size: 16),
                                            ).tr(),
                                          ),
                                        ),
                                      ],
                                    )
                                  : SizedBox())
                          : SizedBox()
                      : SizedBox(),
                  Spacer(),
                  Container(
                    height: 6,
                    width: MediaQuery.of(context).size.width * progressBarWidth,
                    decoration: BoxDecoration(
                        color: context.appColors.grey,
                        borderRadius: BorderRadius.circular(10)),
                    child: Stack(
                      children: [
                        Container(
                          height: 10,
                          width: percent.isNaN
                              ? MediaQuery.of(context).size.width *
                                  progressBarWidth *
                                  (0 / 100)
                              : MediaQuery.of(context).size.width *
                                  progressBarWidth *
                                  (percent / 100),
                          decoration: BoxDecoration(
                              color: context.appColors.green,
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      ],
                    ),
                  ),
                  if (widget.activityStatus != null &&
                      widget.activityStatus != '')
                    widget.enableProgressStatus == false
                        ? Text(
                            '${widget.activityStatus}',
                            style: Styles.regular(
                                size: 11,
                                color: widget.activityStatusNumeric == 0
                                    ? context.appColors.error
                                    : context.appColors.green),
                          )
                        : SizedBox(),
                  SizedBox(
                    height: 6,
                  ),
                  if (widget.activityStatus == null)
                    Row(
                      children: [
                        Text('${tr('rank')}: ${widget.rank}',
                            style:
                                Styles.getRegularThemeStyle(context, size: 12)),
                        SizedBox(
                          width: 4,
                        ),
                        SizedBox(
                            height: 15,
                            child: Image.asset('assets/images/coin.png')),
                        SizedBox(
                          width: 4,
                        ),
                        Text('${widget.gscore ?? 0} ${tr('points')}',
                            style:
                                Styles.getRegularThemeStyle(context, size: 12)),
                      ],
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
