import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/Log.dart';
import 'package:shimmer/shimmer.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../utils/styles.dart';
import '../../custom_pages/custom_widgets/next_page_routing.dart';
import 'job_details_page.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class MyJobAllViewListPage extends StatefulWidget {
  final CompetitionResponse? myJobResponse;
  const MyJobAllViewListPage({
    super.key,
    this.myJobResponse,
  });

  @override
  State<MyJobAllViewListPage> createState() => _MyJobAllViewListPageState();
}

class _MyJobAllViewListPageState extends State<MyJobAllViewListPage> {
  bool? myJobLoading = true;
  CompetitionResponse? myJobResponse;
  bool myJobRecall = false;

  @override
  void initState() {
    super.initState();
    getMyJobList(true);
  }

  void getMyJobList(bool jobType) {
    BlocProvider.of<HomeBloc>(context).add(JobCompListEvent(
        isPopular: false,
        isFilter: false,
        isJob: 1,
        myJob: 1,
        jobTypeMyJob: jobType));
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is JobCompListState) {
              _handleCompetitionListResponse(state);
            }
          },
          child: Scaffold(
            appBar: AppBar(
              iconTheme: IconThemeData(
                color: context.appColors.textBlack,
              ),
              elevation: 0.0,
              backgroundColor: context.appColors.surface,
              title: Text('my_job',
                      style: TextStyle(color: context.appColors.textBlack))
                  .tr(),
            ),
            backgroundColor: context.appColors.jobBackground,
            body: ScreenWithLoader(
              isLoading: myJobLoading,
              body: myJobResponse?.data != null
                  ? _myJobSectionCard()
                  : myJobLoading == true
                      ? MyJobsBlankPage()
                      : SizedBox(),
            ),
          ),
        ));
  }

  void _handleCompetitionListResponse(JobCompListState state) {
    var jobCompState = state;
    setState(() {
      switch (jobCompState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          myJobLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState....................");
          if (myJobRecall == false) {
            myJobResponse = state.myJobListResponse;
          } else {
            myJobResponse = state.myJobListResponse;
          }

          myJobLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState .....................${jobCompState.error}");
          myJobLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'job_dashboard', parameters: {
            "ERROR": '${jobCompState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget _myJobSectionCard() {
    return SingleChildScrollView(
      physics: ScrollPhysics(),
      child: Container(
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.all(10),
          child: myJobResponse?.data == null &&
                      myJobResponse?.data?.length == 0 ||
                  myJobResponse?.data?.isEmpty == true
              ? Padding(
                  padding: const EdgeInsets.only(top: 350.0),
                  child: Center(
                      child: Text('no_jobs_found',
                              style:
                                  Styles.getBoldThemeStyle(context, size: 16))
                          .tr()),
                )
              : ListView.builder(
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: myJobResponse?.data?.length ?? 0,
                  itemBuilder: (BuildContext context, int index) {
                    return InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            NextPageRoute(JobDetailsPage(
                              title: myJobResponse?.data![index]!.name,
                              description:
                                  myJobResponse?.data![index]!.description,
                              location: myJobResponse?.data![index]!.location,
                              skillNames:
                                  myJobResponse?.data![index]!.skillNames,
                              companyName:
                                  myJobResponse?.data![index]!.organizedBy,
                              domain: myJobResponse?.data![index]!.domainName,
                              companyThumbnail:
                                  myJobResponse?.data![index]!.image,
                              experience:
                                  myJobResponse?.data![index]!.experience,
                              //jobListDetails: jobList,
                              id: myJobResponse?.data![index]!.id,
                              jobStatus: myJobResponse?.data![index]!.jobStatus,
                              jobStatusNumeric: int.parse(
                                  '${myJobResponse?.data![index]!.jobStatusNumeric ?? 0}'),
                              vacancy: myJobResponse?.data![index]!.vacancy,
                              minExperience:
                                  myJobResponse?.data![index]!.minExperience,
                              maxExperience:
                                  myJobResponse?.data![index]!.maxExperience,
                              isMyJob: true,
                              landingPageUrl:
                                  myJobResponse?.data![index]!.landingPageUrl,
                            )));
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 136,
                            width: MediaQuery.of(context).size.width,
                            margin: EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border:
                                  Border.all(color: context.appColors.surface),
                              color: context.appColors.surface,
                            ),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 7.0,
                                      top: 15.0,
                                      right: 7.0,
                                      bottom: 15.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Container(
                                          padding: EdgeInsets.only(
                                              right: 10.0, left: 10.0),
                                          child: myJobResponse
                                                      ?.data![index]!.image !=
                                                  null
                                              ? Image.network(
                                                  '${myJobResponse?.data![index]!.image}',
                                                  height: 60,
                                                  width: 80,
                                                )
                                              : Image.asset(
                                                  'assets/images/pb_2.png')),
                                      Container(
                                        padding:
                                            const EdgeInsets.only(left: 10.0),
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.58,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '${myJobResponse?.data![index]!.name}',
                                              style:
                                                  Styles.getRegularThemeStyle(
                                                      context,
                                                      size: 14),
                                              maxLines: 2,
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 4.0),
                                              child: Text(
                                                '${myJobResponse?.data![index]!.organizedBy}',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: Styles.regular(
                                                    color:
                                                        context.appColors.grey3,
                                                    size: 12),
                                              ),
                                            ),
                                            myJobResponse?.data![index]!
                                                        .jobStatus !=
                                                    null
                                                ? Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 16.0),
                                                    child: Text(
                                                      myJobResponse
                                                              ?.data![index]!
                                                              .jobStatus ??
                                                          '',
                                                      style: Styles.regular(
                                                          color: myJobResponse
                                                                      ?.data![
                                                                          index]!
                                                                      .jobStatusNumeric ==
                                                                  0
                                                              ? context
                                                                  .appColors
                                                                  .viewAll
                                                              : context
                                                                  .appColors
                                                                  .green,
                                                          size: 12),
                                                    ),
                                                  )
                                                : Text(''),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                )),
    );
  }
}

class BlankWidgetPage extends StatelessWidget {
  const BlankWidgetPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 70.0),
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: context.appColors.shimmerBase,
                          highlightColor: context.appColors.shimmerHighlight,
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: context.appColors.surface,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: context.appColors.shimmerBase,
                                highlightColor:
                                    context.appColors.shimmerHighlight,
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: context.appColors.surface,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: context.appColors.grey3,
        ),
        SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: context.appColors.shimmerBase,
                          highlightColor: context.appColors.shimmerHighlight,
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: context.appColors.surface,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: context.appColors.shimmerBase,
                                highlightColor:
                                    context.appColors.shimmerHighlight,
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: context.appColors.surface,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: context.appColors.grey3,
        ),
        SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: context.appColors.shimmerBase,
                          highlightColor: context.appColors.shimmerHighlight,
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: context.appColors.surface,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: context.appColors.shimmerBase,
                                highlightColor:
                                    context.appColors.shimmerHighlight,
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: context.appColors.surface,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: context.appColors.shimmerBase,
                                  highlightColor:
                                      context.appColors.shimmerHighlight,
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: context.appColors.surface,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class MyJobsBlankPage extends StatelessWidget {
  const MyJobsBlankPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListView.builder(
          itemCount: 6,
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                height: 90,
                width: double.infinity,
                padding: EdgeInsets.all(8),
                // margin: EdgeInsets.only(bottom: 6.0, top: 20.0),
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: const Offset(5, 5),
                    ),
                  ],
                ),
                child: Row(children: [
                  Shimmer.fromColors(
                    baseColor: context.appColors.shimmerBase,
                    highlightColor: context.appColors.shimmerHighlight,
                    child: Container(
                        height: 80,
                        margin: EdgeInsets.only(left: 2),
                        width: 80,
                        decoration: BoxDecoration(
                          color: context.appColors.surface,
                        )),
                  ),
                  SizedBox(width: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                            height: 12,
                            margin: EdgeInsets.only(left: 2),
                            width: 150,
                            decoration: BoxDecoration(
                              color: context.appColors.surface,
                            )),
                      ),
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 100,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                          Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 100,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 60,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                          SizedBox(
                            width: 7,
                          ),
                          SizedBox(
                            width: 7,
                          ),
                          Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 70,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 70,
                                decoration: BoxDecoration(
                                  color: context.appColors.surface,
                                )),
                          ),
                        ],
                      )
                    ],
                  ),
                ]),
              ),
            );
          },
        ),
      ],
    );
  }
}
