import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/common_web_view.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

import '../../../utils/config.dart';

class IndustryDomainPage extends StatelessWidget {
  final List<JobDomain>? jobDomain;
  const IndustryDomainPage({super.key, this.jobDomain});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: jobDomain != null && jobDomain?.length != null,
      child: Container(
        decoration: BoxDecoration(color: context.appColors.surface),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 15.0)
                      : EdgeInsets.only(left: 15.0),
                  child: Icon(
                    Icons.domain,
                    color: context.appColors.error,
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 10,
                    ),
                    child: Text(
                      'industry_domain',
                      style: Styles.bold(
                          color: context.appColors.headingPrimaryColor),
                    ).tr()),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SizedBox(
                height: height(context) * 0.14,
                child: ListView.builder(
                    itemCount: jobDomain?.length,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (BuildContext context, int index) {
                      return InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => CommonWebView(
                                        url: '${APK_DETAILS['domain_url']}'
                                            'jobs/job-details?domain=${Uri.encodeComponent('${jobDomain?[index].sectorIndustryDomain}')}',
                                      )));
                        },
                        child: Container(
                          width: min(width(context), 480) * 0.4,
                          decoration: BoxDecoration(
                            color: context.appColors.gradientRight
                                .withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: 8.0,
                              right: 8.0,
                            ),
                            child: SizedBox(
                              width: min(width(context), 480) * 0.42,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${jobDomain?[index].sectorIndustryDomain}',
                                    textAlign: TextAlign.center,
                                    style: Styles.bold(
                                        color: context
                                            .appColors.headingPrimaryColor,
                                        size: 13),
                                    softWrap: true,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                  Text(
                                    '${tr('companies')}: ${jobDomain?[index].totCompanies}',
                                    style: Styles.regular(
                                        color: context.appColors.grey3,
                                        size: 11),
                                    softWrap: true,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                  Text(
                                    '${tr('job_roles')}: ${jobDomain?[index].totJobRoles}',
                                    style: Styles.regular(
                                        color: context.appColors.grey3,
                                        size: 11),
                                    softWrap: true,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                  Text(
                                    '${tr('vacancies')}: ${jobDomain?[index].totVacancies}',
                                    style: Styles.regular(
                                        color: context.appColors.green,
                                        size: 11),
                                    softWrap: true,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
              ),
            ),
            SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }
}
