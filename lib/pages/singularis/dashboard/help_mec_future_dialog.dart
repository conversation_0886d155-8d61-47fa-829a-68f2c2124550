import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class HelpMecFutureDialog extends StatelessWidget {
  final String? title;
  final String? content;
  final List<Widget> actions;

  const HelpMecFutureDialog({
    super.key,
    this.title,
    this.content,
    this.actions = const [],
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: context.appColors.surface,
      title: Text(
        title!,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      actions: actions,
      content: Text(
        '$content',
        style: Theme.of(context).textTheme.bodyLarge,
      ),
    );
  }
}
