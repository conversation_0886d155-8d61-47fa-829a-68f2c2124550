import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../utils/styles.dart';
import '../../../../utils/utility.dart';
import 'model/gain_skill_response.dart';

class MentorshipViewDetailsPage extends StatefulWidget {
  final List<Mentorship>? gainSkillMentorship;
  final int? index;
  const MentorshipViewDetailsPage(
      {this.gainSkillMentorship, this.index, super.key});

  @override
  State<MentorshipViewDetailsPage> createState() =>
      _MentorshipViewDetailsPageState();
}

class _MentorshipViewDetailsPageState extends State<MentorshipViewDetailsPage> {
  int? selectedDate = DateTime.now().day;
  int? selectedMonth = DateTime.now().month;
  int? selectedYear = DateTime.now().year;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    void showMonthPicker(BuildContext context) async {
      try {
        DateTime? picked = await showDatePicker(
          context: context,
          firstDate: DateTime(2010),
          lastDate: DateTime(2050),
          initialDate: DateTime(
              selectedYear ?? DateTime.now().year,
              selectedMonth ?? DateTime.now().month,
              selectedDate ?? DateTime.now().day),
          builder: (BuildContext context, Widget? child) {
            return Theme(
              data: ThemeData.light().copyWith(
                primaryColor: context.appColors.gradientRight,
                buttonTheme:
                    ButtonThemeData(textTheme: ButtonTextTheme.primary),
                colorScheme:
                    ColorScheme.light(primary: context.appColors.gradientRight)
                        .copyWith(secondary: context.appColors.gradientRight),
              ),
              child: child!,
            );
          },
        );

        if (picked != null &&
            picked != DateTime(selectedYear!, selectedMonth!, selectedDate!)) {
          setState(() {
            selectedDate = picked.day;
            selectedMonth = picked.month;
            selectedYear = picked.year;
          });

          String formattedDate = DateFormat('yyyy-MM-dd').format(picked);
          debugPrint('Selected date is: $formattedDate');

          //getFacultyClass(selectedDate: formattedDate);
          //filterAssessmentsFromCalender();
        }
      } catch (e, stacktrace) {
        debugPrint('Error: $e');
        debugPrint('Stack trace: $stacktrace');
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'mentorship',
          style: TextStyle(fontSize: 16, color: context.appColors.textBlack),
        ).tr(),
        backgroundColor: context.appColors.surface,
        elevation: 0,
        iconTheme: IconThemeData(
          color: context.appColors.textBlack, //change your color here
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: 15,
            ),
            Container(
              width: MediaQuery.of(context).size.width,
              margin: EdgeInsets.only(right: 10, left: 10),
              decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(5)),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      //height: MediaQuery.of(context).size.width * 0.5,
                      //width: MediaQuery.of(context).size.width,
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(100)),
                        child: Image.network(
                          '${widget.gainSkillMentorship![widget.index!].profileImage}',
                          height: 100,
                          width: 100,
                          errorBuilder: (context, error, stacktrace) {
                            return SvgPicture.asset(
                              'assets/images/gscore_postnow_bg.svg',
                            );
                          },
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8, right: 8),
                      child: Text(
                          '${widget.gainSkillMentorship![widget.index!].name}',
                          overflow: TextOverflow.ellipsis,
                          style: Styles.bold(
                              size: 16, color: context.appColors.headingTitle)),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8, right: 8),
                      child: Text(
                          '${Utility().decrypted128('${widget.gainSkillMentorship![widget.index!].experienceYear}')} ${tr('year_exp')} ',
                          style: Styles.regular(
                              size: 12, color: context.appColors.bodyText)),
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 8, right: 8, top: 20),
                      child: Text(
                          '${widget.gainSkillMentorship![widget.index!].educationQualification}',
                          overflow: TextOverflow.clip,
                          maxLines: 1,
                          style: Styles.regular(
                              size: 12, color: context.appColors.bodyText)),
                    ),
                    Divider(),
                    Padding(
                      padding: const EdgeInsets.only(left: 8, right: 8, top: 2),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            'specialties',
                            style:
                                Styles.getRegularThemeStyle(context, size: 10),
                          ).tr(),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8, right: 8, top: 6),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            '${widget.gainSkillMentorship![widget.index!].functionalArea}',
                            style: Styles.regular(
                                size: 12, color: context.appColors.textBlack),
                          ),
                        ],
                      ),
                    ),
                  ]),
            ),

            //TODO: Schedule this Tutor
            SizedBox(
              height: 15,
            ),
            Container(
              margin: EdgeInsets.only(right: 10, left: 10),
              padding: EdgeInsets.only(right: 10, left: 10, bottom: 20),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(5)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Schedule this Tutor',
                      overflow: TextOverflow.ellipsis,
                      style: Styles.bold(
                          size: 16, color: context.appColors.headingTitle)),
                  SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      Container(
                        height: 60,
                        width: 140,
                        decoration: BoxDecoration(
                            color: context.appColors.grey10,
                            borderRadius: BorderRadius.circular(5)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              onTap: () {
                                showMonthPicker(context);
                              },
                              child: Container(
                                margin: EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(Icons.calendar_month,
                                            size: 15,
                                            color: context.appColors.grey2),
                                        Text("date_str",
                                                style: Styles.regular(
                                                    color:
                                                        context.appColors.grey2,
                                                    size: 13))
                                            .tr(),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 3.0, left: 2.0),
                                      child: Text("22-Des-2024",
                                          style: Styles.bold(
                                              color: context.appColors.grey2,
                                              size: 14)),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Container(
                          height: 60,
                          width: 140,
                          decoration: BoxDecoration(
                              color: context.appColors.grey10,
                              borderRadius: BorderRadius.circular(5)),
                          child: Column(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  showMonthPicker(context);
                                },
                                child: Container(
                                  margin: EdgeInsets.all(8.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.calendar_month,
                                              size: 15,
                                              color: context.appColors.grey2),
                                          Text("time_str",
                                                  style: Styles.regular(
                                                      color: context
                                                          .appColors.grey2,
                                                      size: 13))
                                              .tr(),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 3.0, left: 2.0),
                                        child: Text("select_time",
                                                style: Styles.bold(
                                                    color:
                                                        context.appColors.grey2,
                                                    size: 14))
                                            .tr(),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
