import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

import 'gain_skill_page.dart';

class SkillChildCard extends StatelessWidget {
  final String skill;
  final String? level;
  final bool showActionText;
  final int? skillId;
  final bool? rootCall;
  final int? weightagePerNo;

  const SkillChildCard({
    super.key,
    required this.skill,
    required this.level,
    this.showActionText = true,
    this.skillId,
    this.rootCall = true,
    this.weightagePerNo,
  });

  @override
  Widget build(BuildContext context) {
    Color containerColor =
        context.isDarkMode ? Color(0xff2A2A3A) : Color(0xffF4F7FD);

    Color barColor1 = Color(0xffd9e4fa);
    Color barColor2 = Color(0xffd9e4fa);
    Color barColor3 = Color(0xffd9e4fa);
    Color barColor4 = Color(0xffd9e4fa);
    Color barColor5 = Color(0xffd9e4fa);

    String actionText = 'Gain Skill';
    switch (weightagePerNo) {
      case 0:
        barColor1 = Color(0xffe5e9f1);
        barColor2 = Color(0xffe5e9f1);
        barColor3 = Color(0xffe5e9f1);
        barColor4 = Color(0xffe5e9f1);
        barColor5 = Color(0xffe5e9f1);
        actionText = 'Gain Skill';

        break;
      case 1:
        containerColor =
            context.isDarkMode ? Color(0xff3A2A2A) : Color(0xfffadcdc);
        barColor1 = Color(0xffF47272);
        actionText = 'Level-up';

        break;
      case 2:
        containerColor =
            context.isDarkMode ? Color(0xff3A2F2A) : Color(0xfff6e5d4);
        barColor1 = Color(0xffF8A757);
        barColor2 = Color(0xffF8A757);
        actionText = 'Level-up';

        break;
      case 3:
        containerColor =
            context.isDarkMode ? Color(0xff2A3A2A) : Color(0xffDBF3DD);
        barColor1 = Color(0xff7ADC77);
        barColor2 = Color(0xff7ADC77);
        barColor3 = Color(0xff7ADC77);
        actionText = 'Level-up';

        break;
      case 4:
        containerColor =
            context.isDarkMode ? Color(0xff2A3A2A) : Color(0xffDBF3DD);
        barColor1 = Color(0xffA0C1FF);
        barColor2 = Color(0xffA0C1FF);
        barColor3 = Color(0xffA0C1FF);
        barColor4 = Color(0xffA0C1FF);
        actionText = 'Level-up';

        break;
      case 5:
        containerColor =
            context.isDarkMode ? Color(0xff2A2A3A) : Color(0xffE5F1FC);
        barColor1 = Color(0xff9C9FFF);
        barColor2 = Color(0xff9C9FFF);
        barColor3 = Color(0xff9C9FFF);
        barColor4 = Color(0xff9C9FFF);
        barColor5 = Color(0xff9C9FFF);
        actionText = 'Level-up';

        break;
      default:
        containerColor =
            context.isDarkMode ? Color(0xff2A2A3A) : Color(0xffE5F1FC);
        barColor1 = Color(0xff9C9FFF);
        barColor2 = Color(0xff9C9FFF);
        barColor3 = Color(0xff9C9FFF);
        barColor4 = Color(0xff9C9FFF);
        barColor5 = Color(0xff9C9FFF);
        actionText = 'Level-up';
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      decoration: BoxDecoration(
        color: rootCall == true ? containerColor : context.appColors.surface,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          rootCall == true
              ? InkWell(
                  onTap: () {
                    //TODO: ON Skill Click
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => GainSkillPage(
                                  skillId: skillId,
                                  skill: skill,
                                  level: level,
                                  weightagePerNo: weightagePerNo,
                                )));
                  },
                  child: Row(
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 140,
                        child: Text(
                          skill,
                          style: Styles.bold(
                              color: context.appColors.textBlack, size: 14),
                          maxLines: 1,
                        ),
                      ),
                      Spacer(),
                      if (showActionText) ...[
                        Text(
                          actionText,
                          style: Styles.semibold(
                              size: 12,
                              lineHeight: 1,
                              color: context.appColors.primaryDark),
                        ),
                        SizedBox(width: 10),
                        Container(
                            decoration: BoxDecoration(
                                color: context.appColors.primaryDark,
                                shape: BoxShape.circle),
                            child: Icon(
                              Icons.chevron_right,
                              size: 18,
                              color: context.appColors.primaryForeground,
                            )),
                      ],
                    ],
                  ),
                )
              : SizedBox(),
          RichText(
              text: TextSpan(
            style: Theme.of(context).textTheme.bodyMedium,
            children: <TextSpan>[
              TextSpan(
                  text: '${tr('level')}: ',
                  style: Styles.getRegularThemeStyle(
                    context,
                    size: 12,
                  )),
              TextSpan(
                  text: level != null && level != ''
                      ? '$level'.capital()
                      : ' ${tr('not_assessed')}',
                  style: level != null
                      ? Styles.getSemiboldThemeStyle(context,
                          size: 12, lineHeight: 1)
                      : Styles.getRegularThemeStyle(context,
                          size: 12, lineHeight: 1)),
            ],
          )),
          SizedBox(
            height: 4,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Container(
                  height: 10,
                  // width: width(context) * 0.18,
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: barColor1,
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  height: 10,
                  // width: width(context) * 0.18,
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: barColor2,
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  height: 10,
                  // width: width(context) * 0.18,
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: barColor3,
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  height: 10,
                  // width: width(context) * 0.18,
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: barColor4,
                  ),
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  height: 10,
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: barColor5,
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}

extension on String {
  String capital() {
    try {
      return this[0].toUpperCase() + substring(1);
    } catch (e) {
      return '';
    }
  }
}
