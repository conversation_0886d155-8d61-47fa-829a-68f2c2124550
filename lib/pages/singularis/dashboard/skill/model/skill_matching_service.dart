import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../../../../../data/api/api_constants.dart';
import 'gain_skill_response.dart';

class SkillMatchingService {
  Future<GainSkillMatchingResponse?> fetchSkillMatchingData(
      int? skillId, int? weightagePerNo) async {
    final String apiUrl =
        "${ApiConstants().PRODUCTION_BASE_URL()}/api/g-gainskill?skill_id=$skillId&user_weightage_per_no=$weightagePerNo";

    try {
      final response = await http.get(Uri.parse(apiUrl));

      if (response.statusCode == 201) {
        final jsonData = jsonDecode(response.body);

        // Check if the response data is as expected
        if (jsonData != null && jsonData['status'] == 1) {
          return GainSkillMatchingResponse.fromJson(jsonData);
        } else {
          return null;
        }
      } else {
        throw Exception(
            'Failed to load skill matching data. Status code: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error occurred: $e');
      return null;
    }
  }
}
