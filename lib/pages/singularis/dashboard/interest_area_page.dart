import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/common_web_view.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:shimmer/shimmer.dart';

import '../../../data/models/response/auth_response/oraganization_program_resp.dart';
import '../../../utils/config.dart';

class InterestAreaPage extends StatelessWidget {
  final List<OragnizationProgram>? interestArea;
  const InterestAreaPage({super.key, this.interestArea});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: interestArea != null && interestArea?.length != null,
      child: Container(
        decoration: BoxDecoration(color: context.appColors.surface),
        // height: Utility().isRTL(context)
        //     ? MediaQuery.of(context).size.height * 0.265
        //     : MediaQuery.of(context).size.height * 0.26,
        child: Column(
          children: [
            // SizedBox(
            //   height: height(context) * 0.085,
            // ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 15.0)
                      : EdgeInsets.only(left: 15.0),
                  child: Icon(
                    Icons.interests,
                    color: Color(0xffFF2252),
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 10,
                    ),
                    child: Text(
                      'interest_area',
                      style: Styles.bold(
                          color: context.appColors.headingPrimaryColor),
                    ).tr()),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SizedBox(
                height: height(context) * 0.15,
                child: ListView.builder(
                    itemCount: interestArea?.length,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (BuildContext context, int index) {
                      return InkWell(
                        onTap: () {
                          try {
                            FirebaseAnalytics.instance.logEvent(
                                name:
                                    'user_interest_${Preference.getInt(Preference.USER_ID).toString()}',
                                parameters: {
                                  "interest_area":
                                      "${interestArea?[index].interestarea}",
                                });
                          } catch (e, stacktrace) {
                            debugPrint('$stacktrace');
                            return;
                          }

                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => CommonWebView(
                                        url: '${APK_DETAILS['domain_url']}'
                                            'jobs/job-role-details-web?interestarea=${Uri.encodeComponent('${interestArea?[index].interestarea}')}&user_id=${Preference.getInt(Preference.USER_ID)}',
                                      )));
                        },
                        child: Container(
                          width: min(width(context), 480) * 0.4,
                          decoration: BoxDecoration(
                            color: context.appColors.gradientRight
                                .withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: 8.0,
                              right: 8.0,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CachedNetworkImage(
                                  imageUrl: "${interestArea?[index].iconUrl}",
                                  height: height(context) * 0.07,
                                  progressIndicatorBuilder:
                                      (context, url, downloadProgress) =>
                                          Shimmer.fromColors(
                                    baseColor: context.appColors.shimmerBase,
                                    highlightColor:
                                        context.appColors.shimmerHighlight,
                                    child: Container(
                                      margin:
                                          EdgeInsets.symmetric(vertical: 10),
                                      width: MediaQuery.of(context).size.width,
                                      decoration: BoxDecoration(
                                          color: context.appColors.surface,
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      SizedBox(
                                          height: 120,
                                          child: Image.asset(
                                              'assets/images/blank.png')),
                                ),
                                SizedBox(
                                  height: 5,
                                ),
                                SizedBox(
                                  width: width(context) * 0.4,
                                  child: Center(
                                    child: Text(
                                      '${interestArea?[index].interestarea}',
                                      style: Styles.bold(
                                          color: context
                                              .appColors.headingPrimaryColor,
                                          size: 13),
                                      softWrap: true,
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
              ),
            ),
            SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }
}
