import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/pages/training_pages/new_screen/courses_details_page.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:flutter_svg/flutter_svg.dart';

class GetCourseTemplate extends StatelessWidget {
  const GetCourseTemplate({
    super.key,
    required this.context,
    required this.recommendedcourses,
    required this.yourCourses,
    required this.index,
    required this.tag,
    required this.size,
  });

  final dynamic context;
  final dynamic recommendedcourses;
  final dynamic yourCourses;
  final int index;
  final String tag;
  final dynamic size;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => CoursesDetailsPage(
                  imgUrl: recommendedcourses![index].image,
                  indexc: index,
                  tagName: 'TagReco',
                  name: recommendedcourses![index].name,
                  description: recommendedcourses![index].description ?? '',
                  regularPrice:
                      recommendedcourses![index].regularPrice?.toInt(),
                  salePrice: recommendedcourses![index].salePrice?.toInt(),
                  trainer: recommendedcourses![index].trainer,
                  enrolmentCount: recommendedcourses![index].enrolmentCount,
                  type: recommendedcourses![index].subscriptionType,
                  id: recommendedcourses![index].id,
                  shortCode: recommendedcourses![index].shortCode,
                  isSubscribed: recommendedcourses![index].isSubscribed)),
        ).then((isSuccess) {
          // if (isSuccess == true) {
          //   Navigator.push(
          //       context, MaterialPageRoute(builder: (context) => MyCourses()));
          // }
        });
      },
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          return Container(
            width: MediaQuery.of(context).size.width * 0.7,
            margin: EdgeInsets.only(
                right: Utility().isRTL(context) ? 0 : 15,
                left: Utility().isRTL(context) ? 15 : 10),
            decoration: BoxDecoration(
                border: Border.all(
                  color: context.appColors.grey,
                  width: 1,
                  style: BorderStyle.solid,
                ),
                color: context.appColors.surface,
                borderRadius: BorderRadius.circular(10)),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    // height: size * 0.5,
                    height: MediaQuery.of(context).size.width * 0.5,
                    width: MediaQuery.of(context).size.width,
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10)),
                      child: Image.network(
                        '${yourCourses.image}',
                        errorBuilder: (context, error, stacktrace) {
                          return SvgPicture.asset(
                            'assets/images/gscore_postnow_bg.svg',
                          );
                        },
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 8, right: 8),
                    child: Text('${yourCourses.name}',
                        overflow: TextOverflow.ellipsis,
                        style: Styles.bold(
                            size: 16, color: context.appColors.headingTitle)),
                  ),
                  SizedBox(
                    height: 4,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 8, right: 8),
                    child: Text(
                        '${tr('by')} ${Utility().decrypted128(yourCourses.trainer ?? '')}',
                        style: Styles.regular(
                            size: 12, color: context.appColors.bodyText)),
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 8, right: 8),
                    child: Row(
                      children: [
                        Icon(CupertinoIcons.alarm,
                            size: 16, color: context.appColors.bodyText),
                        SizedBox(
                          width: 4,
                        ),
                        Text('${yourCourses.duration}',
                            overflow: TextOverflow.clip,
                            maxLines: 1,
                            style: Styles.regular(
                                size: 12, color: context.appColors.bodyText)),
                      ],
                    ),
                  )
                ]),
          );
        },
      ),
    );
  }
}
