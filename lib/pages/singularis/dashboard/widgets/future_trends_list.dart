import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/pages/singularis/graph.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';

class FutureTrendsList extends StatelessWidget {
  final List<FutureTrends>? domainList;

  const FutureTrendsList({Key? key, this.domainList}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (domainList == null || domainList!.isEmpty) return SizedBox();
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: context.appColors.surface,
            // border: Border.all(
            //   color: context.appColors.grey,
            //   width: 1,
            //   style: BorderStyle.solid,
            // ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                      padding: Utility().isRTL(context)
                          ? EdgeInsets.only(right: 15.0)
                          : EdgeInsets.only(left: 15.0),
                      child: Icon(Icons.trending_up_outlined, size: 18)),
                  Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 10,
                      ),
                      child: Text(
                        'future_trend',
                        style: Styles.bold(
                            size: 14, color: context.appColors.headingTitle),
                      ).tr()),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Container(
                  height: 85,
                  child: ListView.builder(
                      itemCount: domainList!.length,
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () {
                            FirebaseAnalytics.instance
                                .logEvent(name: 'future_trend', parameters: {
                              "trend": domainList![index].name ?? '',
                              "organization_id":
                                  domainList![index].organizationId ?? ''
                            });
                            _futureTrendsButtonSheet(
                                context,
                                domainList![index].name ?? '',
                                domainList![index].jobCount.toString(),
                                domainList![index].growthType ?? '',
                                domainList![index].growth ?? '0',
                                domainList![index].id);
                          },
                          child: Container(
                            width: min(width(context), 480) * 0.4,
                            decoration: BoxDecoration(
                              color: context.appColors.primaryBlue
                                  .withValues(alpha: 0.08),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            margin: EdgeInsets.all(8),
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: 8.0, right: 8.0, top: 8.0, bottom: 8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: width(context) * 0.4,
                                    child: Center(
                                      child: Text(
                                        '${domainList![index].name}',
                                        style: Styles.bold(
                                            color:
                                                context.appColors.headingText,
                                            size: 13),
                                        softWrap: true,
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 5,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        '${domainList![index].jobCount} ${tr('job_roles')} ',
                                        style: Styles.regular(
                                            color: context.appColors.grey3,
                                            size: 11),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(
                                            left: !Utility().isRTL(context)
                                                ? 0
                                                : 8.0),
                                        child: Text(
                                          domainList![index].growthType == 'up'
                                              ? ' + ${domainList![index].growth ?? ' 0'}%'
                                              : domainList![index].growth !=
                                                      null
                                                  ? ' - ${domainList![index].growth ?? ' 0'}%'
                                                  : '${domainList![index].growth ?? ' 0'}%',
                                          style: Styles.regular(
                                              color: domainList![index]
                                                          .growthType ==
                                                      'up'
                                                  ? context.appColors.green
                                                  : context.appColors.error,
                                              size: 11),
                                        ),
                                      ),
                                      Transform.translate(
                                          offset: Offset(
                                              Utility().isRTL(context)
                                                  ? 10.0
                                                  : 0,
                                              0),
                                          child: domainList![index]
                                                      .growthType ==
                                                  'up'
                                              ? Icon(
                                                  Icons.arrow_drop_up_outlined,
                                                  color: Colors.green,
                                                  size: 20,
                                                )
                                              : Icon(
                                                  Icons.arrow_drop_down,
                                                  color: Colors.red,
                                                  size: 20,
                                                )),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }),
                ),
              ),
              SizedBox(
                height: 10,
              ),
            ],
          ),
        );
      },
    );
  }

  _futureTrendsButtonSheet(
    BuildContext context,
    String title,
    String jobsCount,
    String growthType,
    String growth,
    int domainId,
  ) {
    return showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30))),
        backgroundColor: context.appColors.surface,
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return Stack(
            children: [
              Positioned(
                right: 10,
                child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(Icons.close)),
              ),
              Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width * 0.6,
                      decoration: BoxDecoration(
                          color: context.appColors.primaryBlue
                              .withValues(alpha: 0.08),
                          border:
                              Border.all(color: context.appColors.listColor)),
                      margin: EdgeInsets.all(8),
                      // color: Colors.red,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                        child: Column(
                          children: [
                            Center(
                              child: Text(
                                '$title',
                                style: Styles.bold(
                                    color: context.appColors.headingText,
                                    size: 13),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '$jobsCount ${tr('job_roles')} ',
                                  style: Styles.regular(
                                      color: context.appColors.grey3, size: 11),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left:
                                          !Utility().isRTL(context) ? 0 : 8.0),
                                  child: Text(
                                    growthType == 'up'
                                        ? ' + $growth%'
                                        : growth == '0'
                                            ? ' $growth%'
                                            : ' - $growth%',
                                    style: Styles.regular(
                                        color: growthType == 'up'
                                            ? context.appColors.green
                                            : context.appColors.error,
                                        size: 11),
                                  ),
                                ),
                                Transform.translate(
                                    offset: Offset(
                                        Utility().isRTL(context) ? 10.0 : 0, 0),
                                    child: growthType == 'up'
                                        ? Icon(
                                            Icons.arrow_drop_up_outlined,
                                            color: Colors.green,
                                            size: 20,
                                          )
                                        : Icon(
                                            Icons.arrow_drop_down,
                                            color: Colors.red,
                                            size: 20,
                                          )),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                        child: LineChartWidget(
                      domainid: domainId,
                    )),
                  ],
                ),
              ),
            ],
          );
        });
  }
}
