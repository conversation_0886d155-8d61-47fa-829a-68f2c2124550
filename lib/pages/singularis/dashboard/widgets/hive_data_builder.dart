import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/utils/constant.dart';

class HiveDataBuilder<T> extends StatelessWidget {
  final String boxName;
  final String hiveKey;
  final Widget Function(BuildContext context, List<T> data) builder;
  final Widget? loadingWidget;
  final T Function(Map<String, dynamic>) fromJson;

  const HiveDataBuilder({
    Key? key,
    required this.hiveKey,
    required this.builder,
    this.loadingWidget,
    required this.fromJson,
    this.boxName = DB.CONTENT,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: Hive.box(boxName).listenable(),
      builder: (context, Box box, child) {
        final dynamic rawData = box.get(hiveKey);
        if (rawData == null || (rawData is List && rawData.isEmpty)) {
          return loadingWidget ?? const SizedBox.shrink();
        }

        final dataList = (rawData as List)
            .map((e) => fromJson(Map<String, dynamic>.from(e)))
            .toList();

        return builder(context, dataList);
      },
    );
  }
}
