import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/ghome/my_assessments.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/custom_outline_button.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class AssessInterestFitment extends StatelessWidget {
  const AssessInterestFitment({
    super.key,
    required this.context,
  });

  final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 6.0),
      child: SizedBox(
        height: 35,
        child: CustomOutlineButton(
          strokeWidth: 1,
          radius: 50,
          gradient: LinearGradient(
            colors: [
              context.appColors.gradientLeft,
              context.appColors.gradientRight
            ],
            begin: Alignment.topLeft,
            end: Alignment.topRight,
          ),
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0, right: 10.0),
            child: GradientText(
              tr('assess_interest_fitment'),
              style: Styles.getRegularThemeStyle(context, size: 13),
              colors: [
                context.appColors.gradientLeft,
                context.appColors.gradientRight,
              ],
            ),
          ),
          onPressed: () {
            FirebaseAnalytics.instance
                .logScreenView(screenName: 'Dashboard_Screen');
            FirebaseAnalytics.instance.logEvent(
                name: 'user_id-${Preference.getInt(Preference.USER_ID)}',
                parameters: {
                  "set_goal_event": 'Evaluate_Suitability_Interest',
                });

            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyAssessmentPage(
                          interestAreaID: '0',
                          jobRoleID: int.parse(
                              '${Preference.getString(Preference.SETUP_GOAL_INTEREST_ID) ?? 173}'),
                          skillID: 0,
                        )));
          },
        ),
      ),
    );
  }
}
