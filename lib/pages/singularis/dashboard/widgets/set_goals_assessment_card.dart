import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/local/pref/Preference.dart';

import 'package:masterg/pages/singularis/dashboard/skill/set_goal_intreset_area_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/assess_interest_fitment.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class SetGoalsAssessmentCard extends StatelessWidget {
  final Function onGoalUpdated;

  const SetGoalsAssessmentCard({Key? key, required this.onGoalUpdated})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(
          top: 100, left: 10.0, right: 10.0, bottom: 10.0),
      height: 110,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(6)),
        color: context.appColors.accentColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                const SizedBox(
                  height: 20,
                  width: 20,
                  child: Icon(Icons.bar_chart),
                ),
                const SizedBox(width: 10),
                Text(
                  'career_fitment_evalution',
                  style: Styles.bold(color: context.appColors.headingText),
                ).tr(),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: Row(
              children: [
                Preference.getString(Preference.SETUP_GOAL) == '' ||
                        Preference.getString(Preference.SETUP_GOAL) == null
                    ? _buildSetGoalButton(context)
                    : _buildUpdateGoalWidget(context),
                const Spacer(),
                if (Preference.getString(Preference.SETUP_GOAL)?.isNotEmpty ??
                    false) ...[
                  AssessInterestFitment(
                    context: context,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSetGoalButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10.0),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(context.appColors.primary),
          shape: WidgetStateProperty.all(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(30))),
        ),
        child: SizedBox(
          width: 120,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('set_your_goal').tr(),
              const Padding(
                padding: EdgeInsets.only(left: 2.0),
                child: Icon(Icons.arrow_forward_ios_rounded, size: 18),
              ),
            ],
          ),
        ),
        onPressed: () => _navigateToSetGoal(context),
      ),
    );
  }

  Widget _buildUpdateGoalWidget(BuildContext context) {
    return GestureDetector(
      onTap: () => _navigateToSetGoal(context),
      child: Padding(
        padding: const EdgeInsets.only(left: 10.0),
        child: Row(
          children: [
            Text(
              '${Preference.getString(Preference.SETUP_GOAL) ?? 'set_your_goal'}',
              style: Styles.regularUnderline(
                  lineHeight: 1, size: 14, color: context.appColors.textBlack),
            ).tr(),
            const SizedBox(width: 5),
            const Icon(Icons.edit, size: 16),
          ],
        ),
      ),
    );
  }

  void _navigateToSetGoal(BuildContext context) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => SetGoalIntresetAreaPage(
                  singleSelection: false,
                  returnValue: true,
                  fetchGoalList: 1,
                ))).then((value) {
      if (value != null) {
        onGoalUpdated();
      }
    });
  }
}
