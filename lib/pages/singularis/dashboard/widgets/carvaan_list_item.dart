import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class CarvaanListItem extends StatelessWidget {
  final String description;

  const CarvaanListItem({super.key, required this.description});

  List<TextSpan> _getTextSpans(BuildContext context, String text) {
    List<TextSpan> spans = [];
    RegExp exp = RegExp(r'(#\w+)|([^#]+)');
    Iterable<RegExpMatch> matches = exp.allMatches(text);

    for (var match in matches) {
      String matchText = match[0] ?? '';
      spans.add(TextSpan(
        text: matchText,
        style: TextStyle(
          color: matchText.startsWith('#')
              ? Colors.blue
              : context.appColors.textBlack,
        ),
      ));
    }
    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: _getTextSpans(context, description),
        style: TextStyle(
          fontSize: 14,
          height: 1.2, // equivalent to lineHeight
        ),
      ),
    );
  }
}
