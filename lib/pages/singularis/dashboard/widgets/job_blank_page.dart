import 'package:flutter/material.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class JobBlankPage extends StatelessWidget {
  const JobBlankPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Column(
          children: [
            Container(
              height: 160,
              width: double.infinity,
              padding: EdgeInsets.all(8),
              margin: EdgeInsets.only(bottom: 6.0, top: 10.0),
              decoration: BoxDecoration(
                color: context.surfaceColor,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: const Offset(5, 5),
                  ),
                ],
              ),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: context.appColors.shimmerBase,
                      highlightColor: context.isDarkMode
                          ? Colors.grey[700]!
                          : context.appColors.shimmerHighlight,
                      child: Container(
                          height: 100,
                          margin: EdgeInsets.only(left: 2),
                          width: 200,
                          decoration: BoxDecoration(
                            color: context.surfaceColor,
                          )),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BlocBuilder<ThemeBloc, ThemeState>(
                          builder: (context, themeState) => Shimmer.fromColors(
                            baseColor: context.appColors.shimmerBase,
                            highlightColor: context.appColors.shimmerHighlight,
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2, top: 10),
                                width: 250,
                                decoration: BoxDecoration(
                                  color: context.surfaceColor,
                                )),
                          ),
                        ),
                        SizedBox(
                          height: 7,
                        ),
                        Row(
                          children: [
                            BlocBuilder<ThemeBloc, ThemeState>(
                              builder: (context, themeState) =>
                                  Shimmer.fromColors(
                                baseColor: context.appColors.shimmerBase,
                                highlightColor:
                                    context.appColors.shimmerHighlight,
                                child: Container(
                                    height: 12,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 280,
                                    decoration: BoxDecoration(
                                      color: context.surfaceColor,
                                    )),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ]),
            ),
          ],
        );
      },
    );
  }
}
