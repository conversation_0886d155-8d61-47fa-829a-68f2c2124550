import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class CreateThumnail extends StatelessWidget {
  final String? path;
  final bool? playIcon;

  const CreateThumnail({super.key, this.path, this.playIcon = true});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
        future: getFile(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image.memory(
                    snapshot.data!,
                    fit: BoxFit.cover,
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                  ),
                ),
                if (playIcon == true)
                  Center(
                    child: SvgPicture.asset(
                      'assets/images/play.svg',
                      height: 40.0,
                      width: 40.0,
                      allowDrawingOutsideViewBox: true,
                    ),
                  ),
              ],
            );
          }
          return Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.2,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: context.appColors.surface,
                  borderRadius: BorderRadius.circular(6)),
            ),
          );
        });
  }

  Future<Uint8List?> getFile() async {
    final uint8list = await VideoThumbnail.thumbnailData(
      video: path!,
      imageFormat: ImageFormat.JPEG,
      timeMs: Duration(seconds: 1).inMilliseconds,
    );

    return uint8list;
  }
}
