import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:shimmer/shimmer.dart';

class FutureTrendBlankPage extends StatelessWidget {
  const FutureTrendBlankPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Column(
          children: [
            Container(
              height: 90,
              width: double.infinity,
              padding: EdgeInsets.all(8),
              margin: EdgeInsets.only(bottom: 6.0, top: 20.0),
              decoration: BoxDecoration(
                color: context.surfaceColor,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: context.appColors.shadow,
                    blurRadius: 10,
                    offset: const Offset(5, 5),
                  ),
                ],
              ),
              child: Row(children: [
                Shimmer.fromColors(
                  baseColor: context.shimmerBaseColor,
                  highlightColor: context.shimmerHighlightColor,
                  child: Container(
                      height: 80,
                      margin: EdgeInsets.only(left: 2),
                      width: 80,
                      decoration: BoxDecoration(
                        color: context.surfaceColor,
                      )),
                ),
                SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: context.shimmerBaseColor,
                      highlightColor: context.shimmerHighlightColor,
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 150,
                          decoration: BoxDecoration(
                            color: context.surfaceColor,
                          )),
                    ),
                    SizedBox(
                      height: 7,
                    ),
                    Row(
                      children: [
                        Shimmer.fromColors(
                          baseColor: context.shimmerBaseColor,
                          highlightColor: context.shimmerHighlightColor,
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 100,
                              decoration: BoxDecoration(
                                color: context.surfaceColor,
                              )),
                        ),
                        Shimmer.fromColors(
                          baseColor: context.shimmerBaseColor,
                          highlightColor: context.shimmerHighlightColor,
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 100,
                              decoration: BoxDecoration(
                                color: context.surfaceColor,
                              )),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 7,
                    ),
                    Row(
                      children: [
                        Shimmer.fromColors(
                          baseColor: context.shimmerBaseColor,
                          highlightColor: context.shimmerHighlightColor,
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 60,
                              decoration: BoxDecoration(
                                color: context.surfaceColor,
                              )),
                        ),
                        SizedBox(
                          width: 7,
                        ),
                        SizedBox(
                          width: 7,
                        ),
                        Shimmer.fromColors(
                          baseColor: context.shimmerBaseColor,
                          highlightColor: context.shimmerHighlightColor,
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 70,
                              decoration: BoxDecoration(
                                color: context.surfaceColor,
                              )),
                        ),
                        SizedBox(
                          width: 3,
                        ),
                        SizedBox(
                          width: 3,
                        ),
                        Shimmer.fromColors(
                          baseColor: context.shimmerBaseColor,
                          highlightColor: context.shimmerHighlightColor,
                          child: Container(
                              height: 12,
                              margin: EdgeInsets.only(left: 2),
                              width: 70,
                              decoration: BoxDecoration(
                                color: context.surfaceColor,
                              )),
                        ),
                      ],
                    )
                  ],
                ),
              ]),
            ),
          ],
        );
      },
    );
  }
}
