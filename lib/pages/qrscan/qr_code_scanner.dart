// import 'dart:developer';

// import 'package:flutter/material.dart';
// import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
// import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
// import 'package:masterg/pages/ghome/my_assessments.dart';
// import 'package:masterg/pages/ghome/my_assignments.dart';
// import 'package:masterg/pages/ghome/my_classes.dart';
// import 'package:masterg/pages/reels/reel_screen.dart';
// import 'package:masterg/pages/singularis/competition/competition_detail.dart';
// import 'package:masterg/pages/singularis/job/job_details_page.dart';
// import 'package:masterg/pages/singularis/wow_studio.dart';
// import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
// import 'package:mobile_scanner/mobile_scanner.dart';
// // import 'package:qr_code_scanner/qr_code_scanner.dart';  // Temporarily disabled

// class QRCodeScanner extends StatefulWidget {
//   final MenuListProvider? menuProvider;

//   const QRCodeScanner({super.key, this.menuProvider});

//   @override
//   State<QRCodeScanner> createState() => _QRCodeScannerState();
// }

// class _QRCodeScannerState extends State<QRCodeScanner> {
//   // Barcode? result;
//   // QRViewController? controller;
//   final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
//   String strResult = '';
//   MenuListProvider? menuProvider;
//   late MobileScannerController scannerController;

//   @override
//   void initState() {
//     scannerController = MobileScannerController(
//       detectionSpeed: DetectionSpeed.noDuplicates,
//       facing: CameraFacing.back,
//       torchEnabled: false,
//       formats: [BarcodeFormat.qrCode],
//     );
//     super.initState();
//   }

//   @override
//   void dispose() {
//     scannerController.dispose();
//     super.dispose();
//   }

//   // @override
//   // void reassemble() {
//   //   super.reassemble();
//   // if (Platform.isAndroid) {
//   //   controller!.pauseCamera();
//   // }
//   // controller!.resumeCamera();
//   // }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Column(
//         children: <Widget>[
//           Expanded(flex: 4, child: _buildQrView(context)),
//           // Expanded(
//           //   flex: 1,
//           //   child: FittedBox(
//           //     fit: BoxFit.contain,
//           //     child: Column(
//           //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//           //       children: <Widget>[
//           //         Row(
//           //           mainAxisAlignment: MainAxisAlignment.center,
//           //           crossAxisAlignment: CrossAxisAlignment.center,
//           //           children: <Widget>[
//           //             Container(
//           //               width: MediaQuery.of(context).size.width,
//           //               padding: EdgeInsets.only(top: 20),
//           //               margin: const EdgeInsets.all(20),
//           //               child: ElevatedButton(
//           //                 style: ButtonStyle(
//           //                   // backgroundColor: result != null
//           //                   //     ? WidgetStateProperty.all(Colors.lightBlue)
//           //                   //     : WidgetStateProperty.all(Colors.grey),
//           //                 ),
//           //                 onPressed: () {
//           //                 //   debugPrint('aaaaa${result!.code}');
//           //                 //   String jsonString = result!.code;
//           //                 //   String strText = result!.code.split('::').first;
//           //                 //   String strAssID = result!.code.split('::').last;

//           //                 //  debugPrint('strText-----$strText');
//           //                 //  debugPrint('strAssID-----$strAssID');

//           //                   /*Map<String, dynamic> jsonMap = json.decode(jsonString);
//           //                   debugPrint('route----${jsonMap['route']}');
//           //                   if (result!.code != null) {
//           //                     widget.menuProvider
//           //                         ?.updateCurrentIndex(jsonMap['route']);
//           //                     Navigator.pop(context);
//           //                   }*/

//           //                   //switch (jsonMap['route']) {
//           //                   switch (jsonString) {
//           //                     case '/portfolio':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(NewPortfolioPage()));

//           //                       break;
//           //                     case '/my_classes':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(MyClasses()));
//           //                       break;

//           //                     case '/my_quizzes':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(MyAssessmentPage()));
//           //                       break;
//           //                     case '/my_assignments':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(MyAssignmentPage()));
//           //                       break;
//           //                     case '/job_details':
//           //                       Navigator.push(
//           //                           context,
//           //                           NextPageRoute(JobDetailsPage(
//           //                             jobStatusNumeric: 1,
//           //                           )));
//           //                       break;
//           //                     case '/reels':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(ReelScreen()));
//           //                       break;
//           //                     case '/wow_studio':
//           //                       Navigator.push(
//           //                           context, NextPageRoute(WowStudio()));

//           //                       break;
//           //                     case '/competition_detail':
//           //                       Navigator.push(
//           //                           context,
//           //                           MaterialPageRoute(
//           //                               builder: (context) => CompetitionDetail(
//           //                                     competitionId: int.parse(''),
//           //                                   )));
//           //                       break;
//           //                   }

//           //                   //OnClick
//           //                   // if(result != null){
//           //                   //  debugPrint('dd${result!.code}');
//           //                   //   if(result!.code.toString().contains('http')) {
//           //                   //     Navigator.push(
//           //                   //       context,
//           //                   //       MaterialPageRoute(
//           //                   //         builder: (context) {
//           //                   //           return CommonWebView(
//           //                   //               url: result!.code);
//           //                   //         },
//           //                   //       ),
//           //                   //     );
//           //                   //   }
//           //                   // }
//           //                 },
//           //                 child: Padding(
//           //                   padding: const EdgeInsets.all(20.0),
//           //                   child: const Text('Continue_button',
//           //                           style: TextStyle(fontSize: 12))
//           //                       .tr(),
//           //                 ),
//           //               ),
//           //             )
//           //             //TODO:toggle Flash
//           //             /*Container(
//           //               margin: const EdgeInsets.all(8),
//           //               child: ElevatedButton(
//           //                   onPressed: () async {
//           //                     await controller?.toggleFlash();
//           //                     setState(() {});
//           //                   },
//           //                   child: FutureBuilder(
//           //                     future: controller?.getFlashStatus(),
//           //                     builder: (context, snapshot) {
//           //                       return Text('Flash: ${snapshot.data}');
//           //                     },
//           //                   )),
//           //             ),*/
//           //             //TODO:flipCamera
//           //             /*Container(
//           //               margin: const EdgeInsets.all(8),
//           //               child: ElevatedButton(
//           //                   onPressed: () async {
//           //                     await controller?.flipCamera();
//           //                     setState(() {});
//           //                   },
//           //                   child: FutureBuilder(
//           //                     future: controller?.getCameraInfo(),
//           //                     builder: (context, snapshot) {
//           //                       if (snapshot.data != null) {
//           //                         return Text(
//           //                             'Camera facing ${describeEnum(snapshot.data!)}');
//           //                       } else {
//           //                         return const Text('loading');
//           //                       }
//           //                     },
//           //                   )),
//           //             ),*/
//           //           ],
//           //         ),
//           //         Row(
//           //           mainAxisAlignment: MainAxisAlignment.center,
//           //           crossAxisAlignment: CrossAxisAlignment.center,
//           //           children: <Widget>[
//           //             /*Container(
//           //               margin: const EdgeInsets.all(8),
//           //               child: ElevatedButton(
//           //                 onPressed: () async {
//           //                   await controller?.pauseCamera();
//           //                 },
//           //                 child: const Text('pause',
//           //                     style: TextStyle(fontSize: 20)),
//           //               ),
//           //             ),*/

//           //             //TODO:Resume
//           //             /*Container(
//           //               margin: const EdgeInsets.all(8),
//           //               child: ElevatedButton(
//           //                 onPressed: () async {
//           //                   await controller?.resumeCamera();
//           //                 },
//           //                 child: const Text('resume',
//           //                     style: TextStyle(fontSize: 20)),
//           //               ),
//           //             )*/
//           //           ],
//           //         ),
//           //       ],
//           //     ),
//           //   ),
//           // )
//         ],
//       ),
//     );
//   }

//   Widget _buildQrView(BuildContext context) {
//     // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.
//     var scanArea = (MediaQuery.of(context).size.width < 400 ||
//             MediaQuery.of(context).size.height < 400)
//         ? 250.0
//         : 300.0;
//     // To ensure the Scanner view is properly sizes after rotation
//     // we need to listen for Flutter SizeChanged notification and update controller
//     return MobileScanner(
//       controller: scannerController,
//       onDetect: (capture) {
//         log('barcode found! ${capture.barcodes.first.rawValue}',
//             name: 'QRCodeScanner');
//         final List<Barcode> barcodes = capture.barcodes;
//         // final Uint8List? image = capture.image;
//         if (barcodes.isEmpty) {
//           return;
//         }
//         final Barcode barcode = barcodes.first;
//         setState(() {
//           strResult = barcode.rawValue ?? '';
//         });
//       },
//       onDetectError: (error, stackTrace) {
//         log('barcode error! $error $stackTrace', name: 'QRCodeScanner');
//       },
//     );

//     // return QRView(
//     //   key: qrKey,
//     //   onQRViewCreated: _onQRViewCreated,
//     //   overlay: QrScannerOverlayShape(
//     //       borderColor: Colors.lightBlue,
//     //       borderRadius: 10,
//     //       borderLength: 100,
//     //       borderWidth: 5,
//     //       cutOutSize: scanArea),
//     //   onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
//     // );
//   }

//   // void _onQRViewCreated(QRViewController controller) {
//   //   setState(() {
//   //     this.controller = controller;
//   //     controller.resumeCamera();
//   //   });
//   //   controller.scannedDataStream.listen((scanData) {
//   //     setState(() {
//   //       result = scanData;
//   //     });
//   //   });
//   // }

//   // void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
//   //   log('${DateTime.now().toIso8601String()}_onPermissionSet $p');
//   //   if (!p) {
//   //     ScaffoldMessenger.of(context).showSnackBar(
//   //       const SnackBar(content: Text('no Permission')),
//   //     );
//   //   }
//   // }
// }
