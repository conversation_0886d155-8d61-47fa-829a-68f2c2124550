import 'dart:developer';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/pages/ghome/video_player_screen.dart';
import 'package:masterg/pages/video_resume/preview_sample_resume_page.dart';
import 'package:masterg/utils/extensions/snackbar_extenstion.dart';
import 'package:masterg/utils/log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
// import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

class FullContentPage extends StatefulWidget {
  final String? resourcePath, contentType, title;
  final int? updatedAt;
  final bool isLandscape;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  FullContentPage(
      {super.key,
      this.resourcePath,
      this.contentType,
      this.isLandscape = false,
      this.updatedAt,
      this.title});

  @override
  State<FullContentPage> createState() => _FullContentPageState();
}

class _FullContentPageState extends State<FullContentPage> {
  VideoPlayerController? _controller;
  bool _isLoading = false;
  String? localPath;

  @override
  void initState() {
    log('content type is${widget.contentType}');
    if (widget.contentType == 'mp4') {
      _controller =
          VideoPlayerController.networkUrl(Uri.parse('${widget.resourcePath}'));
      _controller!.addListener(() {
        setState(() {});
      });
      _controller!.setLooping(true);
      _controller!.initialize().then((_) => setState(() {}));
      _controller!.play();
    }

    _isLoading = widget.contentType == '1' || widget.contentType == '13';
    if (_isLoading) {
      _download();
    } else if (widget.contentType == 'docx' ||
        widget.contentType == 'doc' ||
        widget.contentType == 'xls' ||
        widget.contentType == 'xlsx') {
      _download();
    }
    if (widget.isLandscape) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeRight,
        DeviceOrientation.landscapeLeft,
      ]);
    }
    super.initState();
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);
    if (_controller != null) _controller!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        key: widget._scaffoldKey,
        body: Builder(builder: (context) {
          return SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: Stack(
                children: [
                  _getAnnouncmentChild(),
                  // _getTitleWidget(context),
                ],
              ));
        }),
      ),
    );
  }

  // Widget _getTitleWidget(BuildContext context) {
  //   return BlocBuilder<ThemeBloc, ThemeState>(
  //     builder: (context, state) {
  //       return Container(
  //         width: MediaQuery.of(context).size.width,
  //         height: 80,
  //         padding: EdgeInsets.only(left: 12, bottom: 20),
  //         child: Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             InkWell(
  //                 onTap: () {
  //                   Navigator.pop(context);
  //                 },
  //                 child: Container(
  //                   padding: EdgeInsets.all(10),
  //                   decoration: BoxDecoration(
  //                       color: context.appColors.cardBackground,
  //                       // color: context.appColors.white,
  //                       borderRadius: BorderRadius.all(Radius.circular(10))),
  //                   child: Icon(
  //                     Icons.arrow_back_ios_rounded,
  //                     color: context.appColors.darkBlue,
  //                   ),
  //                 )),
  //             Text(
  //               "",
  //               style: Styles.boldWhite(size: 20),
  //             ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }

  dynamic _getAnnouncmentChild() {
    Log.v(widget.resourcePath);
    if (widget.contentType == 'mp4') {
      return _controller!.value.isInitialized
          ? Stack(
              alignment: Alignment.bottomCenter,
              children: <Widget>[
                CustomVideoPlayer(
                  maintainAspectRatio: true,
                  url: widget.resourcePath,
                  showPlayButton: false,
                  wowStudioIcon: true,
                  autoPlay: true,
                ),
                // VideoPlayer(_controller!),
                ControlsOverlay(controller: _controller),
                VideoProgressIndicator(_controller!, allowScrubbing: true),
              ],
            )
          : Container(
              alignment: Alignment.center,
              child: CircularProgressIndicator(),
            );
    } else if (widget.contentType == '1')
      return _getWebview();
    else if (widget.contentType == '13')
      return _getWebview();
    else
      return SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: widget.resourcePath != null
              ? PreviewSampleResume(
                  previewUrl: widget.resourcePath,
                  title: tr('submission_preview'),
                  msg: tr('file_type_msg'),
                )
              : SizedBox()
          //  PhotoView(
          //   imageProvider: NetworkImage(widget.resourcePath!),
          // ),
          );
  }

  Center _getWebview() {
    return Center(
        child: _isLoading
            ? Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                    color: context.appColors.pureBlack,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      'loading',
                      style: Styles.boldWhite(size: 16),
                    ).tr(),
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                          context.appColors.primaryForeground),
                    ),
                  ],
                ),
              )
            : Container());
  }

  Future<bool> _requestPermissions() async {
    var permission = await Permission.storage.request().isGranted;
    return permission;
  }

  final Dio _dio = Dio();

  Future<void> _download() async {
    try {
      var dir = await getApplicationDocumentsDirectory();

      final isPermissionStatusGranted = await _requestPermissions();
      DeviceInfoPlugin plugin = DeviceInfoPlugin();

      late AndroidDeviceInfo android;
      try {
        android = await plugin.androidInfo;
      } catch (e) {
        Log.v("exception file download $e");
      }
      if (Platform.isIOS ||
          isPermissionStatusGranted ||
          android.version.sdkInt >= 33) {
        String ext = widget.resourcePath!.split(".").last;
        final savePath = Platform.isIOS
            ? path.join(dir.path,
                "${widget.resourcePath?.split("/").last.split(".").first ?? "File"} - ${widget.updatedAt ?? ""}.$ext")
            : path.join('/storage/emulated/0/Download/Singularis/',
                "${widget.resourcePath?.split("/").last.split(".").first ?? "File"} - ${widget.updatedAt ?? ""}.$ext");
        if (await File(savePath).exists()) {
          if (mounted) {
            "file_downloaded".tr().showSnackbar(context);
          }

          // if (Platform.isAndroid) {
          Navigator.pop(context);
          //await OpenFile.open(savePath);
          await OpenFilex.open(savePath);
        } else {
          debugPrint('savePath:----$savePath');
          await _startDownload(savePath);
        }
      }
    } catch (e) {
      debugPrint('Exception is $e');
    }
  }

  Future<void> _startDownload(String savePath) async {
    Map<String, dynamic> result = {
      'isSuccess': false,
      'filePath': null,
      'error': null,
    };

    if (widget.resourcePath != null) {
      try {
        final response = await _dio.download(widget.resourcePath!, savePath,
            onReceiveProgress: _onReceiveProgress);
        result['isSuccess'] = response.statusCode == 200;
        result['filePath'] = savePath;

        if (response.statusCode == 200) {
          // if (Platform.isAndroid) {
          Future.delayed(Duration(seconds: 1), () {
            Navigator.pop(context);
          });
          //await OpenFile.open(savePath);
          setState(() {
            _isLoading = false;
          });
          if (mounted) {
            "file_downloaded".tr().showSnackbar(context);
          }
          await OpenFilex.open(savePath);
        }
        Log.v(savePath);
      } catch (ex) {
        setState(() {
          _isLoading = false;
        });
        Log.v(ex);
        result['error'] = ex.toString();
      }
    }
  }

  void _onReceiveProgress(int received, int total) {
    if (total != -1) {}
  }
}

class ControlsOverlay extends StatelessWidget {
  const ControlsOverlay({super.key, this.controller});

  final VideoPlayerController? controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: Duration(milliseconds: 50),
          reverseDuration: Duration(milliseconds: 200),
          child: controller!.value.isPlaying
              ? SizedBox.shrink()
              : Container(
                  color: context.appColors.pureBlack,
                  child: Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: context.appColors.primaryForeground,
                      size: 100.0,
                    ),
                  ),
                ),
        ),
        GestureDetector(
          onTap: () {
            controller!.value.isPlaying
                ? controller!.pause()
                : controller!.play();
          },
        ),
      ],
    );
  }
}
