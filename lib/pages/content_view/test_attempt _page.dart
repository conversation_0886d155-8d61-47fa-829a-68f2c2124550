// import 'dart:async';
// import 'dart:io';
// import 'dart:isolate';
// import 'dart:ui';
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_downloader/flutter_downloader.dart';
// import 'package:html_unescape/html_unescape.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:pentagon/blocs/auth_bloc.dart';
// import 'package:pentagon/blocs/bloc_manager.dart';
// import 'package:pentagon/data/api/api_constants.dart';
// import 'package:pentagon/data/api/api_service.dart';
// import 'package:pentagon/data/models/request/save_answer_request.dart';
// import 'package:pentagon/data/models/request/submit_answer_reqest.dart';
// import 'package:pentagon/data/models/response/attemp_test_response.dart';
// import 'package:pentagon/local/pref/Preference.dart';
// import 'package:pentagon/main.dart';
// import 'package:pentagon/pages/auth_pages/language/l10n/locale_keys.g.dart';
// import 'package:pentagon/pages/custom_widgets/alerts_widget.dart';
// import 'package:pentagon/pages/custom_widgets/next_page_route.dart';
// import 'package:pentagon/pages/custom_widgets/no_data_found.dart';
// import 'package:pentagon/pages/custom_widgets/screen_with_loader.dart';
// import 'package:pentagon/pages/dashboard_pages/dashboard_page.dart';
// import 'package:pentagon/utils/Strings.dart';
// import 'package:pentagon/utils/Styles.dart';
// import 'package:pentagon/utils/resource/colors.dart';
// import 'package:pentagon/utils/tap_widget.dart';
// import 'package:pentagon/utils/utility.dart';
// import 'package:stop_watch_timer/stop_watch_timer.dart';
// import 'package:collection/collection.dart';
// import 'package:easy_localization/easy_localization.dart';

// class TestAttemptPage extends StatefulWidget {
//   final int? contentId;
//   final bool? isReview;
//   final bool? isResumed;

//   TestAttemptPage(
//       {this.contentId, this.isReview = false, this.isResumed = false});

//   @override
//   _TestAttemptPageState createState() => _TestAttemptPageState();
//   Timer? _allTimer;
//   File? _image;
//   var _subjectiveController = TextEditingController();
//   var _pageViewController = PageController();
//   int? _currentSection = 0;
//   var _currentQuestion = 0;
//   var _currentQuestionNumber = 1;
//   int? _currentQuestionId;
//   ScrollController? _questionController;
//   bool imageClick;
//   List<TestAttemptBean>? _list = [];
//   Map<int, dynamic>? _sections = Map();
//   int? _timeType = 0;
//   int? _attemptId = 0;
//   var _durationMins = 0;
//   bool? _isSubmit = false;
//   bool? _showSubmitDialog = false;
//   StopWatchTimer? _stopWatchTimer = StopWatchTimer();
//   bool? _pageChange = true;
//   List<int>? _firstQuestions = [];
//   bool? _isOptionSelected = false;
//   bool? _isResumedLoading = false;
//   bool? _isTestResumed = false;
//   bool? _lastSave = false;
//   bool? _isEverythingOver = false;
//   int? _timeBoundType = 0;
//   DateTime? _serverTime;
//   DateTime? _endTime;
//   bool? _savedAnswer = false;
//   bool? _isContinued = false;
//   bool? _subjectiveTimeOver = false;
//   bool? _isSavedManually = false;
// }

// class IdMapper {
//   int? questionId;
//   String? color;
//   int? timeTaken;

//   IdMapper({this.questionId, this.color, this.timeTaken});

//   IdMapper.fromJson(Map<String, dynamic> json) {
//     questionId = json['questionId'];
//     color = json['color'];
//     timeTaken = json['timeTaken'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['questionId'] = this.questionId;
//     data['color'] = this.color;
//     data['timeTaken'] = this.timeTaken;
//     return data;
//   }
// }

// class _TestAttemptPageState extends State<TestAttemptPage>
//     with WidgetsBindingObserver {
//   final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
//   var _isLoading = false;
//   var _scaffoldContext;
//   AuthBloc? _authBloc;
//   Function eq = const ListEquality().equals;
//   int _currentTimeTaken = 0;
//   var htmlUnescape = HtmlUnescape();

//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) async {
//     if (state == AppLifecycleState.paused) {
//       if (widget._stopWatchTimer!.isRunning) {
//         _currentTimeTaken = widget._stopWatchTimer!.secondTime.value;
//         widget._stopWatchTimer!.onExecute.add(StopWatchExecute.stop);
//       }
//     }
//     if (state == AppLifecycleState.resumed) {
//       if (widget._stopWatchTimer!.isRunning == false) {
//         widget._stopWatchTimer = StopWatchTimer();
//         widget._stopWatchTimer!.setPresetSecondTime(_currentTimeTaken);
//         widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//       }
//     }
//     super.didChangeAppLifecycleState(state);
//   }

//   void _handleAttemptTestResponse(AttemptTestState state) {
//     try {
//       switch (state.apiState) {
//         case ApiStatus.LOADING:
//           this.setState(() {
//             _isLoading = true;
//           });
//           break;
//         case ApiStatus.SUCCESS:
//           if (state.response?.data != null) {
//             widget._list?.clear();
//             widget._attemptId = state.response!.data!.attemptId!;
//             widget._timeType = state.response!.data!.timedType!;
//             widget._timeBoundType = state.response!.data!.timeBoundType!;
//             widget._durationMins = state.response!.data!.durationMins!;
//             // widget._durationMins = 1;
//             widget._serverTime =
//                 DateTime.parse(state.response!.data!.serverTime!.split("+").first);
//             widget._endTime = state.response!.data!.endDateTime!.isNotEmpty
//                 ? DateTime.parse(
//                     state.response!.data!.endDateTime!.split("+").first)
//                 : null;
//             widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//             widget._allTimer = Timer.periodic(Duration(seconds: 1), (timer) {
//               if (widget._allTimer!.tick == (widget._durationMins * 60) &&
//                   widget._timeType == 1) {
//                 widget._isEverythingOver = true;
//               }
//               setState(() {});
//             });
//             for (int i = 0; i < state.response!.data!.sections!.length; i++) {
//               for (var data in state.response!.data!.sections![i].questions!) {
//                 widget._list?.add(
//                   TestAttemptBean(
//                       question: data,
//                       id: i,
//                       isVisited: 0,
//                       sectionId: state.response?.data?.sections?[i].id,
//                       title: state.response?.data?.sections?[i].title),
//                 );
//               }
//             }
//             if (widget._list!.length > 0) {
//               widget._currentQuestion =
//                   Preference.getInt(Preference.CURRENT_QUESTION, def: 0)!;
//               widget._currentQuestionId =
//                   Preference.getInt(Preference.CURRENT_QUESTION_ID, def: 0);
//               widget._currentSection =
//                   Preference.getInt(Preference.CURRENT_SECTION, def: 0);
//               if (widget._currentQuestionId == 0)
//                 widget._currentQuestionId = widget._list!.first.question!.id;
//               widget._list!.first.isVisited = 1;
//               if (widget.isResumed!) {
//                 widget._isResumedLoading = true;
//                 widget._isTestResumed = true;
//               }
//             }
//             for (var data in widget._list!) {
//               if (widget._sections!.containsKey(data.id)) {
//                 List<IdMapper> _sectionQuestionList = [];
//                 widget._sections![data.id].forEach((v) {
//                   _sectionQuestionList.add(IdMapper.fromJson(v));
//                 });
//                 _sectionQuestionList.add(
//                   IdMapper(
//                     questionId: data.question!.id,
//                     color: "#ffffff",
//                     timeTaken: 0,
//                   ),
//                 );
//                 widget._sections![data.id!] =
//                     _sectionQuestionList.map((v) => v.toJson()).toList();
//               } else {
//                 widget._firstQuestions?.add(data.question!.id!);
//                 widget._sections![data.id!] = [
//                   IdMapper(
//                     questionId: data.question!.id,
//                     color: "#ffffff",
//                     timeTaken: 0,
//                   )
//                 ].map((v) => v.toJson()).toList();
//               }
//               if (data.question?.analytics != null &&
//                   data.question!.analytics!.selectedOption!.length > 0 &&
//                   data.question?.questionTypeId != "6") {
//                 for (var selected = 0;
//                     selected < data.question!.options!.length;
//                     selected++) {
//                   if (data.question!.analytics!.selectedOption
//                       !.contains(data.question!.options![selected].id!)) {
//                     data.question?.options?[selected].selected = true;
//                     data.question?.analytics?.selectedOption?.forEach((element) {
//                       data.question?.selectedOption?.add(element.toString());
//                     });
//                     widget._sections?[data.id].forEach((v) {
//                       if (v['questionId'] == data.question?.id) {
//                         v['color'] = "#56affc";
//                       }
//                     });
//                   }
//                 }
//               } else if (data.question?.analytics != null &&
//                   data.question?.questionTypeId == "6") {
//                 data.question?.solutionImage = data.question?.analytics!.usersFile;
//                 data.question?.solutionText = data.question?.analytics!.solution;
//                 widget._sections![data.id].forEach((v) {
//                   if (v['questionId'] == data.question?.id &&
//                       ((data.question?.solutionText != null &&
//                               data.question!.solutionText!.isNotEmpty) ||
//                           data.question?.solutionImage != null &&
//                               data.question!.solutionImage!.isNotEmpty)) {
//                     v['color'] = "#56affc";
//                   }
//                 });
//               }
//             }
//           }
//           Utility.waitFor(2).then((value) {
//             widget._isResumedLoading = false;
//             widget._pageViewController.jumpToPage(widget._currentQuestion);
//           });
//           _isLoading = false;
//           break;
//         case ApiStatus.ERROR:
//           this.setState(() {
//             _isLoading = false;
//           });
//           break;
//         case ApiStatus.INITIAL:
//           break;
//       }
//     } catch (e) {}
//   }

//   void _handleSaveAnswerResponse(SaveAnswerState state) {
//     try {
//       setState(() {
//         switch (state.apiState) {
//           case ApiStatus.LOADING:
//             _isLoading = true;
//             break;
//           case ApiStatus.SUCCESS:
//             widget._isOptionSelected = false;
//             widget._savedAnswer = false;
//             widget._isSavedManually = false;
//             widget._sections?[widget._currentSection].forEach((v) {
//               if (v["questionId"] == widget._currentQuestionId) {
//                 widget._pageChange = false;
//                 widget._list?[widget._currentQuestion].question?.timeTaken =
//                     state.response?.data?.timeTaken?.toInt();
//                 v["timeTaken"] = state.response?.data?.timeTaken?.toInt();
//                 if (widget._list?[widget._currentQuestion].question
//                         ?.questionTypeId ==
//                     "6") {
//                   if (widget._list![widget._currentQuestion].question!.bookMark! &&
//                       (widget._list?[widget._currentQuestion].question
//                                   !.solutionImage !=
//                               null ||
//                           widget._list![widget._currentQuestion].question
//                                   !.solutionText !=
//                               null)) {
//                     v["color"] = "#c964e7";
//                   } else if (widget
//                           ._list![widget._currentQuestion].question!.bookMark! &&
//                       (widget._list![widget._currentQuestion].question
//                                   !.solutionImage ==
//                               null ||
//                           widget._list![widget._currentQuestion].question
//                                   !.solutionText ==
//                               null)) {
//                     v["color"] = "#512DA8";
//                   } else if (!widget
//                       ._list![widget._currentQuestion].question!.bookMark!) {
//                     v["color"] = (state.response!.data!.solution != null &&
//                                 state.response!.data!.solution!.isNotEmpty) ||
//                             (state.response!.data!.usersFile != null &&
//                                 state.response!.data!.usersFile!.isNotEmpty)
//                         ? "#56affc"
//                         : "#ff8080";
//                   }
//                 } else {
//                   if (widget._list![widget._currentQuestion].question!.bookMark! &&
//                       widget._list![widget._currentQuestion].question
//                               !.selectedOption!.length! >
//                           0) {
//                     v["color"] = "#c964e7";
//                   } else if (widget
//                           ._list![widget._currentQuestion].question!.bookMark! &&
//                       widget._list![widget._currentQuestion].question
//                               !.selectedOption!.length ==
//                           0) {
//                     v["color"] = "#512DA8";
//                   } else if (!widget
//                       ._list![widget._currentQuestion].question!.bookMark!) {
//                     v["color"] =
//                         state.response?.data?.selectedOptionId?.length == 0
//                             ? "#ff8080"
//                             : "#56affc";
//                   }
//                 }
//               }
//             });
//             bool _pageChanged = false;
//             if (widget._isContinued == false) {
//               if ((widget._list!.length - 1) == widget._currentQuestion &&
//                   widget._lastSave!) {
//                 widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//                 _isLoading = false;
//                 bool isEverythingOver = false;
//                 for (int i = 0; i < widget._list!.length; i++) {
//                   if (widget._list![i].question!.timeTaken! <
//                       widget._list![i].question!.durationSeconds!) {
//                     _pageChanged = true;
//                     widget._pageViewController.animateToPage(
//                       i,
//                       duration: Duration(microseconds: 100),
//                       curve: Curves.ease,
//                     );
//                     Utility.waitForMili(200).then((value) {
//                       widget._lastSave = false;
//                       widget._pageChange = true;
//                       widget._sections![widget._currentSection].forEach((v) {
//                         if (v["questionId"] == widget._currentQuestionId) {
//                           widget._stopWatchTimer!.onExecute
//                               .add(StopWatchExecute.reset);
//                           widget._stopWatchTimer!
//                               .setPresetSecondTime(v["timeTaken"]);
//                           widget._stopWatchTimer!.onExecute
//                               .add(StopWatchExecute.start);
//                           this.setState(() {});
//                         }
//                       });
//                     });
//                     break;
//                   }
//                   if (i == widget._list!.length - 1 && _pageChanged == false) {
//                     isEverythingOver = true;
//                     widget._isEverythingOver = true;
//                     break;
//                   }
//                   // else {
//                   //   print('Everything over');
//                   //   isEverythingOver = true;
//                   //   widget._isEverythingOver = true;
//                   //   break;
//                   // }
//                 }
//                 if (isEverythingOver == false) {
//                   return;
//                 }
//               }

//               // if ((widget._currentQuestion == (widget._list.length - 1))) {
//               //   print('ANSWER 2');
//               //   _submitAnswers();
//               //   return;
//               // } else {
//               if (widget._isEverythingOver == false) {
//                 if (widget._timeType == 2 &&
//                     widget._list![widget._currentQuestion + 1].question
//                             !.timeTaken! <
//                         widget._list![widget._currentQuestion + 1].question
//                             !.durationSeconds!) {
//                   _pageChanged = true;
//                   widget._pageViewController.nextPage(
//                     duration: Duration(milliseconds: 100),
//                     curve: Curves.ease,
//                   );
//                 } else if (widget._timeType == 2 &&
//                     widget._list![widget._currentQuestion + 1].question
//                             !.timeTaken! >=
//                         widget._list![widget._currentQuestion + 1].question
//                             !.durationSeconds!) {
//                   for (int i = widget._currentQuestion;
//                       i < widget._list!.length;
//                       i++) {
//                     if (widget._list![i].question!.timeTaken! <
//                             widget._list![i].question!.durationSeconds! &&
//                         i != widget._currentQuestion) {
//                       _pageChanged = true;
//                       widget._pageViewController.animateToPage(
//                         i,
//                         duration: Duration(microseconds: 100),
//                         curve: Curves.ease,
//                       );
//                       break;
//                     }
//                   }
//                   if (_pageChanged == false) {
//                     for (int i = 0; i < widget._currentQuestion; i++) {
//                       if (widget._list![i].question!.timeTaken! <
//                               widget._list![i].question!.durationSeconds! &&
//                           i != widget._currentQuestion) {
//                         _pageChanged = true;
//                         widget._pageViewController.animateToPage(
//                           i,
//                           duration: Duration(microseconds: 100),
//                           curve: Curves.ease,
//                         );
//                         break;
//                       }
//                     }
//                   }
//                 } else {
//                   _pageChanged = true;
//                   widget._pageViewController.nextPage(
//                     duration: Duration(milliseconds: 100),
//                     curve: Curves.ease,
//                   );
//                 }
//               }
//               Utility.waitForMili(500).then((value) {
//                 if (_pageChanged) {
//                   widget._pageChange = true;
//                 } else {
//                   widget._pageChange = false;
//                   widget._isEverythingOver = true;
//                   _submitAnswers();
//                 }
//                 widget._sections![widget._currentSection].forEach((v) {
//                   if (v["questionId"] == widget._currentQuestionId) {
//                     //widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//                     if (widget._list![widget._currentQuestion]?.question
//                                 ?.timeTaken !=
//                             null ||
//                         widget._list![widget._currentQuestion + 1].question
//                                 !.timeTaken !=
//                             0) {
//                       widget._stopWatchTimer = StopWatchTimer();
//                       widget._stopWatchTimer!.setPresetSecondTime(widget
//                           ._list![widget._currentQuestion].question!.timeTaken!);
//                       widget._stopWatchTimer!.onExecute
//                           .add(StopWatchExecute.start);
//                     } else {
//                       widget._stopWatchTimer = StopWatchTimer();
//                       widget._stopWatchTimer!
//                           .setPresetSecondTime(v["timeTaken"].toInt());
//                       widget._stopWatchTimer!.onExecute
//                           .add(StopWatchExecute.start);
//                     }
//                     this.setState(() {});
//                   }
//                 });
//               });
//               _isLoading = false;
//               //}
//             } else {
//               _isLoading = false;
//               widget._isContinued = false;
//               widget._pageChange = true;
//             }
//             break;
//           case ApiStatus.ERROR:
//             _isLoading = false;
//             break;
//           case ApiStatus.INITIAL:
//             break;
//         }
//       });
//     } catch (e) {}
//   }

//   void _handleSubmitAnswerResponse(SubmitAnswerState state) {
//     try {
//       setState(() {
//         widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//         widget._allTimer!.cancel();
//         widget._isSubmit = true;
//         switch (state.apiState) {
//           case ApiStatus.LOADING:
//             _isLoading = true;
//             break;
//           case ApiStatus.SUCCESS:
//             _isLoading = false;
//             Preference.setInt(Preference.CURRENT_QUESTION, 0);
//             Preference.setInt(Preference.CURRENT_QUESTION_ID, 0);
//             Preference.setInt(Preference.CURRENT_SECTION, 0);
//             AlertsWidget.alertWithOkBtn(
//               context: _scaffoldContext,
//               title: widget._isEverythingOver! ? "${LocaleKeys.assessment_time_over.tr()}" : "",
//               onOkClick: () {
//                 Navigator.pop(_scaffoldContext);
//               },
//               text: "${LocaleKeys.your_answers_are_saved_su.tr()}",
//             );
//             break;
//           case ApiStatus.ERROR:
//             _isLoading = false;
//             break;
//           case ApiStatus.INITIAL:
//             break;
//         }
//       });
//     } catch (e) {}
//   }

//   void _handleUploadImageResponse(UploadImageState state) {
//     try {
//       setState(() {
//         switch (state.apiState) {
//           case ApiStatus.LOADING:
//             _isLoading = true;
//             break;
//           case ApiStatus.SUCCESS:
//             _isLoading = false;
//             widget._image = null;
//             widget._list?[widget._currentQuestion]?.question?.solutionImage =
//                 state.response?.data?.image;
//             widget._list?[widget._currentQuestion]?.question?.solutionText =
//                 widget._subjectiveController.text.trim();
//             if (widget._isSavedManually == false) {
//               widget._list?[widget._currentQuestion]?.question?.solutionImage =
//                   null;
//             }
//             if (widget._subjectiveTimeOver!) {
//               if (Preference.exists(widget
//                       ._list![widget._currentQuestion].question!.questionId
//                       .toString() +
//                   "solutionText")) {
//                 widget._list![widget._currentQuestion]?.question?.solutionText =
//                     Preference.getString(widget
//                             ._list![widget._currentQuestion].question!.questionId
//                             .toString() +
//                         "solutionText");
//               }
//               if (Preference.exists(widget
//                       ._list![widget._currentQuestion].question!.questionId
//                       .toString() +
//                   "solutionImage")) {
//                 widget._list![widget._currentQuestion]?.question?.solutionImage =
//                     Preference.getString(widget
//                             ._list![widget._currentQuestion].question!.questionId
//                             .toString() +
//                         "solutionImage");
//               }
//             }
//             _authBloc.add(
//               SaveAnswerEvent(
//                 request: SaveAnswerRequest(
//                   attemptId: widget._attemptId,
//                   action: 1,
//                   timeTaken: widget._stopWatchTimer!.secondTime.value,
//                   questionId:
//                       widget._list![widget._currentQuestion]?.question?.id,
//                   image: widget
//                       ._list![widget._currentQuestion]?.question?.solutionImage,
//                   solution: widget
//                       ._list![widget._currentQuestion]?.question?.solutionText,
//                 ),
//               ),
//             );
//             widget._subjectiveTimeOver = false;
//             Preference.setString(
//                 widget._list![widget._currentQuestion].question!.questionId
//                         .toString() +
//                     "solutionText",
//                 widget._list![widget._currentQuestion].question?.solutionText ??
//                     "");
//             Preference.setString(
//                 widget._list![widget._currentQuestion].question!.questionId
//                         .toString() +
//                     "solutionImage",
//                 widget._list![widget._currentQuestion].question?.solutionImage ??
//                     "");
//             if (widget._isContinued == false) {
//               widget._subjectiveController.clear();
//             }
//             FocusScope.of(context).requestFocus(FocusNode());
//             break;
//           case ApiStatus.ERROR:
//             _isLoading = false;
//             break;
//           case ApiStatus.INITIAL:
//             break;
//         }
//       });
//     } catch (e) {}
//   }

//   @override
//   void dispose() {
//     if (widget._isSubmit! || widget._list?.length == 0) {
//       widget._stopWatchTimer!?.dispose();
//       widget._allTimer!?.cancel();
//     }
//     super.dispose();
//   }

//   @override
//   void initState() {
//     widget._questionController = ScrollController();
//     WidgetsBinding.instance.addObserver(this);
//     super.initState();
//     _downloadListener();
//   }

//   @override
//   Widget build(BuildContext context) {
//     Application(context);
//     _authBloc = BlocProvider.of<AuthBloc>(context);
//     return BlocManager(
//       initState: (context) {
//         if (widget._list?.length == 0) {
//           _authBloc.add(
//             AttemptTestEvent(
//               request: widget.contentId.toString(),
//             ),
//           );
//         }
//       },
//       child: BlocListener<AuthBloc, AuthState>(
//         listener: (context, state) {
//           if (state is AttemptTestState) _handleAttemptTestResponse(state);
//           if (state is SaveAnswerState) _handleSaveAnswerResponse(state);
//           if (state is SubmitAnswerState) _handleSubmitAnswerResponse(state);
//           if (state is UploadImageState) _handleUploadImageResponse(state);
//         },
//         child: Builder(builder: (_context) {
//           _scaffoldContext = _context;
//           return WillPopScope(
//             onWillPop: () async => false,
//             child: Scaffold(
//               key: _key,
//               appBar: widget._list?.length == 0
//                   ? AppBar(
//                       elevation: 0,
//                       leading: _back(),
//                       backgroundColor: context.appColors.WHITE,
//                     )
//                   : null,
//               bottomNavigationBar:
//                   widget._list?.length == 0 ? SizedBox() : _buildBottomAppBar(),
//               body: SafeArea(
//                 child: ScreenWithLoader(
//                   body: widget._list?.length == 0
//                       ? NoDataFound(
//                           text: _isLoading ? "${LocaleKeys.pleaseWait.tr()}" : "${LocaleKeys.noDataFound.tr()}",
//                         )
//                       : _content(),
//                   isLoading: _isLoading,
//                 ),
//               ),
//             ),
//           );
//         }),
//       ),
//     );
//   }

//   _back() {
//     return TapWidget(
//       onTap: () {
//         Navigator.pop(context);
//       },
//       child: Card(
//         elevation: 6,
//         margin: EdgeInsets.all(8),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.all(
//             Radius.circular(20),
//           ),
//         ),
//         color: context.appColors.SCHEDULE_BG,
//         child: Container(
//           width: 40,
//           height: 40,
//           child: Icon(
//             Icons.arrow_back_ios_outlined,
//             size: 20,
//             color: context.appColors.PRIMARY,
//           ),
//         ),
//       ),
//     );
//   }

//   _saveClick() {
//     if (widget._savedAnswer == false) {
//       if (widget._image == null) {
//         widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//       }
//       widget._savedAnswer = true;
//       if (widget._list[widget._currentQuestion].question?.questionTypeId ==
//           "6") {
//         if (widget._image == null) {
//           if (widget._subjectiveController.text.trim().isNotEmpty) {
//             _authBloc.add(
//               SaveAnswerEvent(
//                 request: SaveAnswerRequest(
//                     action: 1,
//                     attemptId: widget._attemptId,
//                     timeTaken: widget._stopWatchTimer!.secondTime.value,
//                     questionId:
//                         widget._list?[widget._currentQuestion].question?.id,
//                     solution: widget._subjectiveController.text.trim(),
//                     image: widget
//                         ._list?[widget._currentQuestion].question?.solutionImage),
//               ),
//             );
//             widget._list?[widget._currentQuestion].question?.timeTaken =
//                 widget._stopWatchTimer!.secondTime.value;
//             widget._list?[widget._currentQuestion].question?.solutionText =
//                 widget._subjectiveController.text.trim();
//             _setTimer();
//             if (widget._isContinued == false) {
//               widget._subjectiveController.clear();
//             }
//             FocusScope.of(context).requestFocus(FocusNode());
//           } else if (widget._timeType == 2) {
//             _authBloc.add(
//               SaveAnswerEvent(
//                 request: SaveAnswerRequest(
//                     action: 1,
//                     attemptId: widget._attemptId,
//                     timeTaken: widget._stopWatchTimer!.secondTime.value,
//                     questionId:
//                         widget._list![widget._currentQuestion].question!.id,
//                     solution: widget._subjectiveController.text.trim().isEmpty
//                         ? widget._list![widget._currentQuestion].question
//                             !.solutionText
//                         : widget._subjectiveController.text.trim(),
//                     image: widget
//                         ._list?[widget._currentQuestion].question?.solutionImage),
//               ),
//             );
//             widget._list?[widget._currentQuestion].question?.timeTaken =
//                 widget._stopWatchTimer!.secondTime.value;
//             _setTimer();
//             widget._list?[widget._currentQuestion].question?.solutionText =
//                 widget._subjectiveController.text.trim();
//             if (widget._isContinued == false) {
//               widget._subjectiveController.clear();
//             }
//             FocusScope.of(context).requestFocus(FocusNode());
//           } else {
//             _authBloc.add(
//               SaveAnswerEvent(
//                 request: SaveAnswerRequest(
//                   action: 1,
//                   attemptId: widget._attemptId,
//                   timeTaken: widget._stopWatchTimer!.secondTime.value,
//                   questionId: widget._list?[widget._currentQuestion].question?.id,
//                   solution: widget._subjectiveController.text.trim(),
//                   image: widget
//                       ._list?[widget._currentQuestion].question?.solutionImage,
//                 ),
//               ),
//             );
//             widget._list?[widget._currentQuestion].question?.timeTaken =
//                 widget._stopWatchTimer!.secondTime.value;
//             _setTimer();
//             widget._list?[widget._currentQuestion].question?.solutionText =
//                 widget._subjectiveController.text.trim();
//             if (widget._isContinued == false) {
//               widget._subjectiveController.clear();
//             }
//             FocusScope.of(context).requestFocus(FocusNode());
//           }
//         } else {
//           widget._list?[widget._currentQuestion].question?.timeTaken =
//               widget._stopWatchTimer!.secondTime.value;
//           _setTimer();
//           _authBloc.add(UploadImageEvent(request: widget._image?.path));
//         }
//       } else {
//         _authBloc.add(
//           SaveAnswerEvent(
//             request: SaveAnswerRequest(
//               attemptId: widget._attemptId,
//               action: 1,
//               timeTaken: widget._stopWatchTimer!.secondTime.value,
//               questionId: widget._list?[widget._currentQuestion].question?.id,
//               selectedOptionId:
//                   widget._list?[widget._currentQuestion].question?.selectedOption,
//               addSelect: true,
//             ),
//           ),
//         );
//         widget._list![widget._currentQuestion].question?.timeTaken =
//             widget._stopWatchTimer!.secondTime.value;
//         _setTimer();
//       }
//       Preference.setInt(
//           widget._list![widget._currentQuestion].question!.id.toString() + "tt",
//           widget._stopWatchTimer!.secondTime.value);
//       Preference.setInt(
//           widget._list![widget._currentQuestion].question!.id.toString() + "ts",
//           widget._stopWatchTimer!.secondTime.value);
//       if (widget._list![widget._currentQuestion].question?.questionTypeId ==
//           '3') {
//         final list = widget._list?[widget._currentQuestion].question?.options
//             ?.where((element) => element.selected)
//             .toList();
//         final options = list?.map((e) => e.id.toString()).toList();
//         Preference.setListString(
//             widget._list![widget._currentQuestion].question!.questionId
//                     .toString() +
//                 "so",
//             options!);
//       } else if (widget
//               ._list?[widget._currentQuestion].question?.questionTypeId ==
//           '6') {
//         if (widget._image == null) {
//           Preference.setString(
//               widget._list![widget._currentQuestion].question!.questionId
//                       .toString() +
//                   "solutionText",
//               widget._list![widget._currentQuestion].question!.solutionText ??
//                   "");
//           Preference.setString(
//               widget._list![widget._currentQuestion].question!.questionId
//                       .toString() +
//                   "solutionImage",
//               widget._list![widget._currentQuestion].question?.solutionImage ??
//                   "");
//         }
//       } else {
//         Preference.setListString(
//             widget._list![widget._currentQuestion].question!.questionId
//                     .toString() +
//                 "so",
//             widget._list![widget._currentQuestion].question!.selectedOption!);
//       }
//     }
//   }

//   void _setTimer() {
//     if ((widget._list!.length - 1) != widget._currentQuestion) {
//       if (widget._list?[widget._currentQuestion + 1]?.question?.timeTaken !=
//               null ||
//           widget._list?[widget._currentQuestion + 1].question?.timeTaken != 0) {
//         widget._stopWatchTimer!.setPresetSecondTime(
//             widget._list![widget._currentQuestion + 1].question!.timeTaken!);
//       } else {
//         widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//       }
//       widget._pageChange = false;
//     }
//   }

//   Widget _content() {
//     return Container(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _size(),
//           _timerSubmit(),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 20),
//             child: Wrap(
//               children: [
//                 TapWidget(
//                   onTap: () {
//                     _handleSectionPreviousButton();
//                   },
//                   child: Container(
//                     width: 20,
//                     height: 20,
//                     child: Text(
//                       "< ",
//                       style: Styles.regularGrey(size: 16),
//                     ),
//                   ),
//                 ),
//                 Text(
//                   widget._list![widget._currentQuestion]?.title ?? "",
//                   style: Styles.regularGrey(size: 16),
//                 ),
//                 TapWidget(
//                   onTap: () {
//                     _handleSectionNextButton();
//                   },
//                   child: Container(
//                     width: 20,
//                     height: 20,
//                     child: Text(
//                       " >",
//                       style: Styles.regularGrey(size: 16),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           _size(),
//           _questionCount(),
//           _pageView(),
//         ],
//       ),
//     );
//   }

//   _mainTimer(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Text(
//         widget._timeType == 2 ? _getIndividualTime(testAttemptBean) : "",
//         style: Styles.regularGrey(size: 16),
//       ),
//     );
//   }

//   _pageView() {
//     return Expanded(
//       child: Container(
//         child: PageView.builder(
//           itemBuilder: (context, index) {
//             return widget._isResumedLoading!
//                 ? Center(
//                     child: CircularProgressIndicator(),
//                   )
//                 : _pageItem(widget._list![index]);
//           },
//           onPageChanged: (pageNumber) {
//             setState(() {
//               widget._currentSection = widget._list?[pageNumber].id;
//               widget._currentQuestionId = widget._list?[pageNumber].question?.id;
//               widget._currentQuestion = pageNumber;
//               Preference.setInt(
//                   Preference.CURRENT_QUESTION, widget._currentQuestion);
//               Preference.setInt(
//                   Preference.CURRENT_QUESTION_ID, widget._currentQuestionId!);
//               Preference.setInt(
//                   Preference.CURRENT_SECTION, widget._currentSection!);
//               widget._list?[pageNumber].isVisited = 1;
//               widget._subjectiveController.text =
//                   widget._list![pageNumber].question!.solutionText!;
//               final questionsLength =
//                   widget._sections?[widget._currentSection].toList().length;
//               for (int data = 0; data < questionsLength; data++) {
//                 if (widget._sections?[widget._currentSection][data]
//                         ["questionId"] ==
//                     widget._currentQuestionId) {
//                   widget._currentQuestionNumber = data + 1;
//                   Utility.waitForMili(200).then((value) {
//                     if (widget._currentQuestionNumber >= questionsLength * .6) {
//                       if (widget._questionController?.position.pixels !=
//                           widget._questionController?.position.maxScrollExtent) {
//                         widget._questionController
//                             ?.jumpTo((data * 20).toDouble());
//                       }
//                     } else if (data <= questionsLength * .3) {
//                       if (widget._questionController?.position.pixels != 0) {
//                         widget._questionController?.jumpTo(0);
//                       }
//                     }
//                   });
//                   break;
//                 }
//               }
//             });
//           },
//           controller: widget._pageViewController,
//           itemCount: widget._list?.length,
//           physics: NeverScrollableScrollPhysics(),
//         ),
//       ),
//     );
//   }

//   _pageItem(TestAttemptBean testAttemptBean) {
//     return Container(
//       child: SingleChildScrollView(
//         physics: BouncingScrollPhysics(),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             _size(),
//             _mainTimer(testAttemptBean),
//             _questionNumber(testAttemptBean),
//             _size(height: 20),
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 20),
//               child: Text(htmlUnescape.convert(testAttemptBean.question!.statement!) ?? "",
//                 style: Styles.regularBlack(size: 16),
//               ),
//             ),
//             _size(height: 20),
//             _solutionType(
//                 testAttemptBean.question!.questionTypeId!, testAttemptBean),
//             _size(height: 20),
//           ],
//         ),
//       ),
//     );
//   }

//   _size({double height = 10}) {
//     return SizedBox(
//       height: height,
//     );
//   }

//   _getAllTime() {
//     String _timeH = "";
//     String _timeM = "";
//     String _timeS = "";
//     var localTime;
//     if (widget._timeBoundType == 2) {
//       localTime = (widget._endTime.difference(widget._serverTime).inSeconds) -
//           widget._allTimer!.tick;
//     } else {
//       localTime = (widget._durationMins * 60) - widget._allTimer!.tick;
//     }
//     if ((localTime / 3600).truncate() < 10) {
//       _timeH = "0${(localTime / 3600).truncate()}";
//     } else {
//       _timeH = (localTime / 3600).truncate().toString();
//     }
//     _timeM = (((localTime / 60).truncate()) % 60).toString().padLeft(2, '0');
//     if ((localTime % 60).truncate() < 10) {
//       _timeS = "0${(localTime % 60).truncate()}";
//     } else {
//       _timeS = (localTime % 60).truncate().toString();
//     }
//     if (widget._timeBoundType == 2) {
//       if (widget._allTimer!.tick ==
//               (widget._endTime.difference(widget._serverTime).inSeconds) &&
//           !widget._isSubmit) {
//         final List<String> list1 = Preference.getListString(
//             widget._list[widget._currentQuestion].question.id.toString() +
//                 "so");
//         final List<String> list2 =
//             widget._list[widget._currentQuestion].question.selectedOption;
//         if (eq(list1, list2) == false) {
//           widget._list[widget._currentQuestion].question.selectedOption =
//               list1 ?? [];
//         }
//         widget._isEverythingOver = true;
//         _submitAnswers();
//       }
//     } else {
//       if (widget._allTimer!.tick == (widget._durationMins * 60) &&
//           !widget._isSubmit) {
//         final List<String> list1 = Preference.getListString(
//             widget._list[widget._currentQuestion].question.id.toString() +
//                 "so");
//         final List<String> list2 =
//             widget._list[widget._currentQuestion].question.selectedOption;
//         if (eq(list1, list2) == false) {
//           widget._list[widget._currentQuestion].question.selectedOption =
//               list1 ?? [];
//         }
//         _submitAnswers();
//       }
//     }
//     return "$_timeH:$_timeM:$_timeS";
//   }

//   _getIndividualTime(TestAttemptBean testAttemptBean) {
//     String _timeM = "";
//     String _timeS = "";
//     var localTime = testAttemptBean.question.durationSeconds -
//         widget._stopWatchTimer!.secondTime.value;
//     if ((localTime / 60).truncate() < 10) {
//       _timeM = "0${(localTime / 60).truncate()}";
//     } else {
//       _timeM = "${(localTime / 60).truncate()}";
//     }
//     if ((localTime % 60).truncate() < 10) {
//       _timeS = "0${(localTime % 60).truncate()}";
//     } else {
//       _timeS = "${(localTime % 60).truncate()}";
//     }
//     if (widget._stopWatchTimer!.secondTime.value ==
//             testAttemptBean.question.durationSeconds &&
//         widget._pageChange) {

//       final List<String> list1 = Preference.getListString(
//           testAttemptBean.question.questionId.toString() + "so");
//       final List<String> list2 = testAttemptBean.question.selectedOption;
//       if (eq(list1, list2) == false) {
//         testAttemptBean.question.selectedOption = list1 ?? [];
//         // widget._list[widget._currentQuestion].question.selectedOption=list1??[];
//       }
//       if (testAttemptBean.question.questionTypeId == "6") {
//         widget._subjectiveTimeOver = true;
//         testAttemptBean.question.solutionText = Preference.getString(
//             testAttemptBean.question.questionId.toString() + "solutionText");
//         testAttemptBean.question.solutionImage = Preference.getString(
//             testAttemptBean.question.questionId.toString() + "solutionImage");
//       } else {
//         testAttemptBean.question.solutionText = null;
//         testAttemptBean.question.solutionImage = null;
//       }
//       widget._subjectiveController.text = "";
//       if (widget._list.length == 1) {
//         _authBloc.add(
//           SubmitAnswerEvent(
//             request: SubmitAnswerRequest(
//               attemptId: widget._attemptId,
//               questions: [
//                 Answers(
//                   isAnswer: testAttemptBean.question.questionTypeId == "6"
//                       ? (testAttemptBean.question.solutionText != null ||
//                               testAttemptBean.question.solutionImage != null
//                           ? 1
//                           : 0)
//                       : testAttemptBean.question.selectedOption.length > 0
//                           ? 1
//                           : 0,
//                   isVisited: 1,
//                   isSubjective: testAttemptBean.question.questionTypeId == "6",
//                   solution: testAttemptBean.question.solutionText,
//                   userFile: testAttemptBean.question.solutionImage,
//                   sectionId: testAttemptBean.sectionId,
//                   questionId: testAttemptBean.question.id,
//                   selectedOptionId: testAttemptBean.question.selectedOption,
//                   timeTaken: widget._stopWatchTimer!.secondTime.value,
//                 ),
//               ],
//             ),
//           ),
//         );
//       } else {
//         _saveClick();
//       }
//     }
//     return "$_timeM:$_timeS";
//   }

//   _timerSubmit() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Row(
//         children: [
//           if (!widget.isReview)
//             Text(
//               widget._timeType == 1 ? _getAllTime() : "",
//               style: Styles.boldGreen(size: 16),
//             ),
//           Spacer(),
//           TapWidget(
//             onTap: () {
//               if (widget.isReview) {
//                 Navigator.pushAndRemoveUntil(_scaffoldContext,
//                     NextPageRoute(DashboardPage()), (route) => false);
//               } else {
//                 if (!widget._isOptionSelected) {
//                   AlertsWidget.alertWithOkCancelBtn(
//                     context: _scaffoldContext,
//                     onOkClick: () {
//                       widget._stopWatchTimer!.onExecute
//                           .add(StopWatchExecute.reset);
//                       widget._allTimer!.cancel();
//                       _submitAnswers();
//                     },
//                     okText: "${LocaleKeys.submit.tr()}".toUpperCase(),
//                     cancelText: "${LocaleKeys.continueF.tr()}".toUpperCase(),
//                     text: "${LocaleKeys.you_still_have_time_left.tr()}",
//                     title: "${LocaleKeys.finish_assessment.tr()}",
//                   );
//                 } else {
//                   AlertsWidget.alertWithOkBtn(
//                     context: _scaffoldContext,
//                     onOkClick: () {
//                       widget._showSubmitDialog = true;
//                     },
//                     title: "",
//                     text: "${LocaleKeys.save_answer_ques.tr()}",
//                   );
//                 }
//               }
//             },
//             child: Text(
//               widget.isReview ? "${LocaleKeys.exit.tr()}" : LocaleKeys.submit.tr(),
//               style: Styles.boldBlue(size: 16),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   _questionCount() {
//     return widget._list.length == 0
//         ? SizedBox()
//         : Container(
//             height: 30,
//             width: MediaQuery.of(_scaffoldContext).size.width,
//             child: SingleChildScrollView(
//               physics: ClampingScrollPhysics(),
//               scrollDirection: Axis.horizontal,
//               controller: widget._questionController,
//               child: Row(
//                 children: [
//                   Container(
//                     alignment: Alignment.center,
//                     child: Container(
//                       width: 20,
//                       height: 1,
//                       color: Colors.grey,
//                     ),
//                   ),
//                   ListView.builder(
//                     shrinkWrap: true,
//                     physics: ClampingScrollPhysics(),
//                     // controller: widget._questionController,
//                     itemBuilder: (context, index) {
//                       return TapWidget(
//                         onTap: () {
//                           if (widget._isOptionSelected) {
//                             AlertsWidget.alertWithOkBtn(
//                                 context: _scaffoldContext,
//                                 title: "",
//                                 text: "${LocaleKeys.save_answer_question.tr()}");
//                             return;
//                           }
//                           if (widget._currentQuestionId ==
//                               widget._sections[widget._currentSection][index]
//                                   ["questionId"]) {
//                             return;
//                           }
//                           for (int i = 0; i < widget._list.length; i++) {
//                             if (widget._list[i].question.id ==
//                                 widget._sections[widget._currentSection][index]
//                                     ["questionId"]) {
//                               if (widget._list[i].question.durationSeconds !=
//                                   widget._sections[widget._currentSection]
//                                       [index]["timeTaken"]) {
//                                 widget._list[widget._currentQuestion].question
//                                         .timeTaken =
//                                     widget._stopWatchTimer!.secondTime.value;
//                                 Preference.setInt(
//                                     widget._list[widget._currentQuestion]
//                                             .question.id
//                                             .toString() +
//                                         "tt",
//                                     widget._stopWatchTimer!.secondTime.value);
//                                 Preference.setBool(
//                                     widget._list[widget._currentQuestion]
//                                             .question.id
//                                             .toString() +
//                                         "iv",
//                                     true);
//                                 widget._currentQuestionId =
//                                     widget._sections[widget._currentSection]
//                                         [index]["questionId"];
//                                 widget._currentQuestion = i;
//                                 widget._stopWatchTimer!.onExecute
//                                     .add(StopWatchExecute.reset);
//                                 widget._pageViewController.animateToPage(i,
//                                     duration: Duration(milliseconds: 100),
//                                     curve: Curves.ease);
//                                 Utility.waitForMili(200).then((value) {
//                                   widget._stopWatchTimer! = StopWatchTimer();
//                                   // widget._stopWatchTimer!.setPresetSecondTime(
//                                   //     widget._sections[widget._currentSection]
//                                   //         [index]["timeTaken"]);
//                                   widget._stopWatchTimer!.setPresetSecondTime(
//                                       widget._list[widget._currentQuestion]
//                                           .question.timeTaken);
//                                   widget._stopWatchTimer!.onExecute
//                                       .add(StopWatchExecute.start);
//                                   widget._subjectiveController.text = widget
//                                           ._list[widget._currentQuestion]
//                                           .question
//                                           ?.solutionText ??
//                                       "";
//                                 });
//                               } else {
//                                 AlertsWidget.alertWithOkBtn(
//                                   context: _scaffoldContext,
//                                   title: "",
//                                   text: "${LocaleKeys.time_question_over.tr()}",
//                                 );
//                               }
//                               break;
//                             }
//                           }
//                         },
//                         child: Row(
//                           children: [
//                             Container(
//                               alignment: Alignment.center,
//                               child: Container(
//                                 width: 10,
//                                 height: 1,
//                                 color: Colors.grey,
//                               ),
//                             ),
//                             Container(
//                               width: 30,
//                               height: 30,
//                               decoration: BoxDecoration(
//                                   borderRadius:
//                                       BorderRadius.all(Radius.circular(15)),
//                                   color: HexColor.fromHex(
//                                       widget._sections[widget._currentSection]
//                                           [index]["color"]),
//                                   border: Border.all(
//                                       color: Colors.black, width: 1)),
//                               alignment: Alignment.center,
//                               child: Text(
//                                 "${index + 1}",
//                                 style: Styles.regularBlack(),
//                               ),
//                             ),
//                             Container(
//                               alignment: Alignment.center,
//                               child: Container(
//                                 width: 10,
//                                 height: 1,
//                                 color: Colors.grey,
//                               ),
//                             )
//                           ],
//                         ),
//                       );
//                     },
//                     scrollDirection: Axis.horizontal,
//                     itemCount: widget._sections[widget._currentSection].length,
//                   ),
//                   Container(
//                     alignment: Alignment.center,
//                     child: Container(
//                       width: 20,
//                       height: 1,
//                       color: Colors.grey,
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           );
//   }

//   _questionNumber(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Row(
//         children: [
//           Text(
//             "${LocaleKeys.q.tr()}.${(widget._currentQuestionNumber).toString().padLeft(2, '0')}",
//             style: Styles.boldBlack(size: 22),
//           ),
//           SizedBox(
//             width: 10,
//           ),
//           Padding(
//             padding: const EdgeInsets.only(top: 8.0),
//             child: TapWidget(
//               onTap: () {
//                 setState(() {
//                   testAttemptBean.question!.bookMark =
//                       !testAttemptBean.question!.bookMark!;
//                   widget._sections![testAttemptBean.id].forEach((v) {
//                     if (testAttemptBean.question!.id == v["questionId"]) {
//                       if (testAttemptBean.question!.bookMark! &&
//                           testAttemptBean.question!.selectedOption!.length > 0) {
//                         v["color"] = "#c964e7";
//                       } else if (testAttemptBean.question!.bookMark! &&
//                           testAttemptBean.question!.selectedOption!.length == 0) {
//                         v["color"] = "#512DA8";
//                       } else if (!testAttemptBean.question!.bookMark! &&
//                           testAttemptBean.question!.selectedOption!.length == 0) {
//                         v["color"] = "#ffffff";
//                       }
//                     }
//                   });
//                 });
//               },
//               child: Icon(
//                 testAttemptBean.question!.bookMark!
//                     ? Icons.bookmark_outlined
//                     : Icons.bookmark_border_outlined,
//                 color: context.appColors.UN_SELECT_DOT,
//                 size: 35,
//               ),
//             ),
//           ),
//           SizedBox(
//             width: 10,
//           ),
//           Visibility(
//             visible: (widget._list[widget._currentQuestion].question
//                             ?.selectedOption?.length ??
//                         0) >
//                     0 ||
//                 (widget._list[widget._currentQuestion].!question.solutionImage !=
//                         null &&
//                     widget._list[widget._currentQuestio!n].question.solutionImage
//                         .isNotEmpty) ||
//                 (widget._list[widget._currentQuestion]!.question.solutionText !=
//                         null &&
//                     widget._list[widget._currentQuesti!on].question.solutionText
//                         .isNotEmpty) ||
//                 (widget._image != null ||
//                     widget._subjectiveController.text.isNotEmpty),
//             child: TapWidget(
//               onTap: () {
//                 setState(() {
//                   if (widget._list[widget._!currentQuestion].question
//                           .questionTypeId ==
//                       "6") {
//                     widget._list[widget._!currentQuestion].question
//                         .solutionText = null;
//                     widget._list[widget._!currentQuestion].question
//                         .solutionImage = null;
//                     widget._subjectiveController.text = "";
//                     widget._image = null;
//                     Preference.removePrefs(widget
//                             ._list[widget._currentQues!tion].question.questionId
//                             .toString() +
//                         "solutionText");
//                     Preference.removePrefs(widget
//                             ._list[widget._currentQues!tion].question.questionId
//                             .toString() +
//                         "solutionImage");
//                   } else {
//                     widget
//                         ._list[widget._currentQuestion!].question.selectedOption
//                         .clear();
//                     for (var data in widget
//                         ._list[widget._currentQues!tion].question.options) {
//                       data.selected = false;
//                     }
//                     Preference.removePrefs(widget
//                             ._list[widget._currentQues!tion].question.questionId
//                             .toString() +
//                         "so");
//                   }
//                   widget._sections[widget._currentSection].forEach((v) {
//                     if (v["questionId"] == widget._currentQuestionId) {
//                       v["color"] = "#ffffff";
//                     }
//                   });
//                   widget._isOptionSelected = false;
//                 });
//               },
//               child: Text(
//                 "${LocaleKeys.clear.tr()}",
//                 style: Styles.boldBlue(),
//               ),
//             ),
//           ),
//           Spacer(),
//           Visibility(
//             visible: testAttemptBean.question.marks != 0,
//             child: Container(
//               width: testAttemptBean.question.negativeMarking == null ||
//                       testAttemptBean.question.negativeMarking == 0
//                   ? 100
//                   : 50,
//               height: 40,
//               alignment: Alignment.center,
//               decoration: BoxDecoration(
//                 color: context.appColors.SETTING_BG,
//                 borderRadius: BorderRadius.only(
//                   bottomLeft: Radius.circular(20),
//                   topLeft: Radius.circular(20),
//                   topRight: testAttemptBean.question.negativeMarking == null ||
//                           testAttemptBean.question.negativeMarking == 0
//                       ? Radius.circular(20)
//                       : Radius.circular(0),
//                   bottomRight:
//                       testAttemptBean.question.negativeMarking == null ||
//                               testAttemptBean.question.negativeMarking == 0
//                           ? Radius.circular(20)
//                           : Radius.circular(0),
//                 ),
//               ),
//               child: Text(
//                 "+${testAttemptBean.question.marks.toString() ?? ""}",
//                 style: Styles.boldGreen(),
//               ),
//             ),
//           ),
//           testAttemptBean.question.negativeMarking == null ||
//                   testAttemptBean.question.negativeMarking == 0
//               ? SizedBox()
//               : Container(
//                   width: 50,
//                   height: 40,
//                   alignment: Alignment.center,
//                   decoration: BoxDecoration(
//                     color: context.appColors.error_TRANS,
//                     borderRadius: BorderRadius.only(
//                       bottomRight: Radius.circular(20),
//                       topRight: Radius.circular(20),
//                     ),
//                   ),
//                   child: Text(
//                     "-${testAttemptBean.question.negativeMarking.toString() ?? ""}",
//                     style: Styles.boldRed(),
//                   ),
//                 ),
//         ],
//       ),
//     );
//   }

//   _solutionType(String type, TestAttemptBean testAttemptBean) {
//     switch (type) {
//       case "1":
//         return _multiChoose(testAttemptBean); //MULTIPLE_CHOICE
//         break;
//       case "2":
//         return _options(testAttemptBean); //SINGLE_INTEGER
//         break;
//       case "3":
//         return _multiResponse(testAttemptBean); //MULTIPLE_RESPONSE
//         break;
//       case "4":
//         return _chooseOne(testAttemptBean); //FILL_IN_THE_BLANK
//         break;
//       case "5":
//         return _chooseOne(testAttemptBean); //TRUE_FALSE
//         break;
//       case "6":
//         return _subjective(testAttemptBean); //SUBJECTIVE
//         break;
//       case "7":
//         return Container(); //MATCHING
//         break;
//     }
//   }

//   _fullViewDialog({String image}) {
//     showDialog(
//       context: _scaffoldContext,
//       barrierColor: Colors.black,
//       builder: (bc) => SimpleDialog(
//         backgroundColor: Colors.transparent,
//         elevation: 200,
//         contentPadding: const EdgeInsets.all(0),
//         children: [
//           Image.network(
//             image,
//             fit: BoxFit.contain,
//           )
//         ],
//       ),
//     );
//   }

//   _subjective(TestAttemptBean testAttemptBean) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         if (widget._list[widget._currentQuestion].!question.image != null &&
//             widget._list[widget._currentQuestion].q!uestion.image.isNotEmpty)
//           _handleFile(
//               file: ApiConstants.IMAGE_BASE_URL +
//                   widget._list[widget._currentQ!uestion].question.image),
//         Padding(
//           padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
//           child: Text(
//             testAttemptBean.question.questionType ?? "",
//             style: Styles.boldBlack(size: 20),
//           ),
//         ),
//         Container(
//           height: 200,
//           padding: const EdgeInsets.symmetric(horizontal: 20.0),
//           child: TextField(
//             maxLines: 10,
//             controller: widget._subjectiveController,
//             textInputAction: TextInputAction.done,
//             onChanged: (text) {
//               setState(() {
//                 if (text.length > 0) {
//                   widget._isOptionSelected = true;
//                 } else if (widget._image == null && text.isEmpty) {
//                   widget._isOptionSelected = false;
//                 }
//               });
//             },
//             decoration: InputDecoration(
//               hintText: "${LocaleKeys.write_here.tr()}",
//               hintStyle: Styles.regularHintGrey(),
//               contentPadding: const EdgeInsets.all(15.0),
//               border: OutlineInputBorder(
//                 borderRadius: BorderRadius.all(Radius.circular(10)),
//                 borderSide: BorderSide(color: Colors.grey),
//               ),
//               focusedBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.all(Radius.circular(10)),
//                 borderSide: BorderSide(color: Colors.grey),
//               ),
//             ),
//           ),
//         ),
//         _size(height: 20),
//         if (widget._list[widget._currentQuestion].!question.solutionImage !=
//                 null &&
//             widget._list[widget._currentQuestio!n].question.solutionImage
//                 .isNotEmpty)
//           _handleFile(
//             file: ApiConstants.IMAGE_BASE_URL +
//                 widget._list[widget._currentQuestion!].question.solutionImage,
//             isSubjective: true,
//           ),
//         widget._list[widget._currentQuestion].question!.solutionImage != null &&
//                 widget._list[widget._currentQuestio!n].question.solutionImage
//                     .isNotEmpty
//             ? SizedBox()
//             : widget._image == null
//                 ? Align(
//                     alignment: Alignment.center,
//                     child: TapWidget(
//                       onTap: () async {
//                         FilePickerResult result = await FilePicker.platform
//                             .pickFiles(
//                                 type: FileType.custom,
//                                 allowedExtensions: [
//                               'pdf',
//                               'doc',
//                               'jpg',
//                               'jpeg',
//                               'png'
//                             ]);
//                         if (result != null) {
//                           setState(() {
//                             widget._isOptionSelected = true;
//                             widget._image = File(result.files.single.path);
//                           });
//                         } else {
//                           // User canceled the picker
//                         }
//                       },
//                       child: Row(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Icon(
//                             Icons.upload_file,
//                             size: 80,
//                           ),
//                           SizedBox(
//                             width: 10,
//                           ),
//                           Text(
//                             "${LocaleKeys.upload_file.tr()}",
//                             style: Styles.boldBlack(),
//                           )
//                         ],
//                       ),
//                     ),
//                   )
//                 : Container(
//                     width: MediaQuery.of(_scaffoldContext).size.width,
//                     height: 60,
//                     padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Container(
//                           width:
//                               MediaQuery.of(_scaffoldContext).size.width - 120,
//                           child: Text(
//                             widget._image.path.split('/').last,
//                             style: Styles.boldBlack(size: 12),
//                             maxLines: 2,
//                           ),
//                         ),
//                         TapWidget(
//                           onTap: () {
//                             setState(() {
//                               widget._image = null;
//                               if (widget._subjectiveController.text.isEmpty) {
//                                 widget._isOptionSelected = false;
//                               }
//                             });
//                           },
//                           child: Icon(
//                             Icons.highlight_remove_rounded,
//                             size: 30,
//                             color: Colors.black,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//         _size(),
//       ],
//     );
//   }

//   _handleFile({String file, bool isSubjective = false}) {
//     if (file.contains('jpg') || file.contains('jpeg') || file.contains('png'))
//       return TapWidget(
//         onTap: () async {
//           _fullViewDialog(image: file);
//         },
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20.0),
//           child: Stack(
//             children: [
//               Image.network(
//                 file,
//                 width: MediaQuery.of(_scaffoldContext).size.width,
//                 height: MediaQuery.of(_scaffoldContext).size.width / 1.5,
//                 fit: BoxFit.cover,
//               ),
//               Visibility(
//                 visible: isSubjective,
//                 child: Positioned(
//                   top: 10,
//                   right: 10,
//                   child: TapWidget(
//                     onTap: () {
//                       widget._list[widget._!currentQuestion].question
//                           .solutionImage = null;
//                     },
//                     child: Icon(
//                       Icons.highlight_remove,
//                       size: 30,
//                       color: Colors.black,
//                     ),
//                   ),
//                 ),
//               )
//             ],
//           ),
//         ),
//       );
//     else
//       return Padding(
//         padding: const EdgeInsets.symmetric(horizontal: 20.0),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Container(
//               width: MediaQuery.of(_scaffoldContext).size.width - 110,
//               child: Text(
//                 file.split('/').last,
//                 style: Styles.boldBlack(),
//                 maxLines: 5,
//               ),
//             ),
//             isSubjective
//                 ? TapWidget(
//                     onTap: () {
//                       widget._list[widget._!currentQuestion].question
//                           .solutionImage = null;
//                     },
//                     child: Icon(
//                       Icons.highlight_remove,
//                       size: 30,
//                       color: Colors.black,
//                     ),
//                   )
//                 : TapWidget(
//                     onTap: () async {
//                       if (await Permission.storage.request().isGranted) {
//                         var tempDir = Platform.isIOS
//                             ? await getApplicationDocumentsDirectory()
//                             : await getExternalStorageDirectory();
//                         String localPath =
//                             (tempDir.path) + Platform.pathSeparator + 'SOW';
//                         var savedDir = Directory(localPath);
//                         bool hasExisted = await savedDir.exists();
//                         if (!hasExisted) {
//                           savedDir = await savedDir.create();
//                         }
//                         download2(file, localPath);
//                       } else {
//                         Utility.showSnackBar(
//                             scaffoldContext: _scaffoldContext,
//                             message: "${LocaleKeys.msgEnableStoragePerm.tr()}");
//                       }
//                     },
//                     child: Icon(
//                       Icons.download_sharp,
//                       color: context.appColors.PRIMARY,
//                     ),
//                   ),
//           ],
//         ),
//       );
//   }

//   Future download2(String url, String savePath) async {
//     try {
//       // _key.currentState.showSnackBar(
//       //   SnackBar(
//       //     content: Text(
//       //       "${LocaleKeys.downloadingStart.tr()}",
//       //       style: Styles.boldWhite(),
//       //     ),
//       //     backgroundColor: context.appColors.BLACK,
//       //     duration: Duration(seconds: 2),
//       //   ),
//       // );
//       final taskId = await FlutterDownloader.enqueue(
//         url: url,
//         savedDir: savePath,
//         showNotification: true,
//         headers: {"auth": "test_for_sql_encoding"},
//         openFileFromNotification: true,
//       );
//     } catch (e) {
//       print(e);
//     }
//   }

//  static void downloadCallback(String id, int status, int progress) {
//   final SendPort send = IsolateNameServer.lookupPortByName('downloader_send_port');
//   send.send([id, status, progress]);
// }

// ReceivePort _port = ReceivePort();

// void _downloadListener() {
//   IsolateNameServer.registerPortWithName(_port.sendPort, 'downloader_send_port');
//   _port.listen((dynamic data) {
//     String id = data[0];
//     int status = data[1];
//     int progress = data[2];
//     if (status == 3 && progress == 100 && id != null) {
//       String query = "SELECT * FROM task WHERE task_id='" + id + "'";
//       var tasks = FlutterDownloader.loadTasksWithRawQuery(query: query);
//       // if the task exists, open it
//       if (tasks != null) FlutterDownloader.open(taskId: id);
//     }
//   });
//   FlutterDownloader.registerCallback(downloadCallback);
// }

//   _options(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (widget._list[widget._currentQuestion].!question.image != null &&
//               widget._list[widget._currentQuestion].q!uestion.image.isNotEmpty)
//             _handleFile(
//                 file: ApiConstants.IMAGE_BASE_URL +
//                     widget._list[widget._currentQ!uestion].question.image),
//           Padding(
//             padding: const EdgeInsets.symmetric(vertical: 20),
//             child: Text(
//               testAttemptBean.question.questionType ?? "",
//               style: Styles.boldBlack(size: 20),
//             ),
//           ),
//           Column(
//             children: List.generate(
//               widget._list[widget._currentQuestion]!.question.options.length,
//               (index) => Column(
//                 children: [
//                   TapWidget(
//                     onTap: () {
//                       widget._isOptionSelected = true;
//                       setState(() {
//                         widget._list[widget._!currentQuestion].question
//                             .selectedOption
//                             .clear();
//                         widget._list[widget._!currentQuestion].question
//                             .selectedOption
//                             .add(widget._list[widget._!currentQuestion].question
//                                 .options[index].id
//                                 .toString());
//                         for (var data = 0;
//                             data <
//                                 widget._list[widget._!currentQuestion].question
//                                     .options.length;
//                             data++) {
//                           if (widget._list[widget._!currentQuestion].question
//                                   .options[data].id
//                                   .toString() ==
//                               widget._list[widget._!currentQuestion].question
//                                   .selectedOption.first) {
//                             widget._list[widget._!currentQuestion].question
//                                 .options[data].selected = true;
//                           } else {
//                             widget._list[widget._!currentQuestion].question
//                                 .options[data].selected = false;
//                           }
//                         }
//                       });
//                     },
//                     child: Card(
//                       elevation: 5,
//                       margin: const EdgeInsets.symmetric(vertical: 10),
//                       child: Container(
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         alignment: Alignment.centerLeft,
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 15, vertical: 10),
//                         decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                               Radius.circular(5),
//                             ),
//                             border: Border.all(
//                               color: widget._list![widget._currentQuestion]
//                                       .question.options[index].selected
//                                   ? context.appColors.PRIMARY
//                                   : Colors.black,
//                             )),
//                         child: Text(htmlUnescape.convert(widget._list[widget._!currentQuestion].question
//                             .options[index].statement),
//                           style: Styles.regularBlack(size: 15),
//                         ),
//                       ),
//                     ),
//                   ),
//                   if (widget._list[widget._currentQues!tion].question.options !=
//                           null &&
//                       widget._list[widget._!currentQuestion].question
//                               .options[index].image !=
//                           null &&
//                       widget._list[widget._!currentQuestion].question
//                           .options[index].image.isNotEmpty)
//                     TapWidget(
//                       onTap: () async {
//                         _fullViewDialog(
//                             image: ApiConstants.IMAGE_BASE_URL +
//                                 widget._list[widget._!currentQuestion].question
//                                     .options[index].image);
//                       },
//                       child: Image.network(
//                         ApiConstants.IMAGE_BASE_URL +
//                             widget._list[widget._!currentQuestion].question
//                                 .options[index].image,
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         height:
//                             MediaQuery.of(_scaffoldContext).size.width / 1.5,
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   _size(height: 20),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   _multiChoose(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (widget._list[widget._currentQuestion].!question.image != null &&
//               widget._list[widget._currentQuestion].q!uestion.image.isNotEmpty)
//             _handleFile(
//                 file: ApiConstants.IMAGE_BASE_URL +
//                     widget._list[widget._currentQ!uestion].question.image),
//           Padding(
//             padding: const EdgeInsets.symmetric(vertical: 20),
//             child: Text(
//               testAttemptBean.question.questionType ?? "",
//               style: Styles.boldBlack(size: 20),
//             ),
//           ),
//           Column(
//             children: List.generate(
//               widget._list[widget._currentQuestion]!.question.options.length,
//               (index) => Column(
//                 children: [
//                   TapWidget(
//                     onTap: () {
//                       widget._isOptionSelected = true;
//                       setState(() {
//                         widget._list[widget._!currentQuestion].question
//                             .selectedOption
//                             .clear();
//                         widget._list[widget._!currentQuestion].question
//                             .selectedOption
//                             .add(widget._list[widget._!currentQuestion].question
//                                 .options[index].id
//                                 .toString());
//                         for (var data = 0;
//                             data <
//                                 widget._list![widget._currentQuestion].question
//                                     !.options!.length;
//                             data++) {
//                           if (widget._list?[widget._currentQuestion].question
//                                   ?.options?[data].id
//                                   .toString() ==
//                               widget._list?[widget._currentQuestion].question
//                                  ?.selectedOption?.first) {
//                             widget._list?[widget._currentQuestion].question
//                                 ?.options?[data].selected = true;
//                           } else {
//                             widget._list?[widget._currentQuestion].question
//                                 ?.options?[data].selected = false;
//                           }
//                         }
//                       });
//                     },
//                     child: Card(
//                       elevation: 5,
//                       margin: const EdgeInsets.symmetric(vertical: 10),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.all(
//                           Radius.circular(5),
//                         ),
//                         side: BorderSide(
//                             color: widget._list![widget._currentQuestion]
//                                     .question.options[index].selected
//                                 ? context.appColors.PRIMARY
//                                 : Colors.black,
//                             width: 1.5),
//                       ),
//                       child: Container(
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         alignment: Alignment.centerLeft,
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 15, vertical: 10),
//                         child: Text(htmlUnescape.convert(widget._list![widget._currentQuestion].question
//                             !.options![index].statement!),
//                           style: Styles.regularBlack(size: 15),
//                         ),
//                       ),
//                     ),
//                   ),
//                   if (widget._list![widget._currentQuestion].question!.options !=
//                           null &&
//                       widget._list![widget._currentQuestion].question
//                               !.options![index].image !=
//                           null &&
//                       widget._list![widget._currentQuestion].question
//                           !.options![index].image!.isNotEmpty)
//                     TapWidget(
//                       onTap: () async {
//                         _fullViewDialog(
//                             image: ApiConstants.IMAGE_BASE_URL +
//                                 widget._list![widget._currentQuestion].question
//                                     !.options![index].image!);
//                       },
//                       child: Image.network(
//                         ApiConstants.IMAGE_BASE_URL +
//                             widget._list![widget._currentQuestion].question
//                                 !.options![index].image!,
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         height:
//                             MediaQuery.of(_scaffoldContext).size.width / 1.5,
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   _size(height: 20),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   _multiResponse(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (widget._list![widget._currentQuestion].question!.image != null &&
//               widget._list![widget._currentQuestion].question!.image!.isNotEmpty)
//             _handleFile(
//                 file: ApiConstants.IMAGE_BASE_URL +
//                     widget._list![widget._currentQuestion].question!.image!),
//           Padding(
//             padding: const EdgeInsets.symmetric(vertical: 20),
//             child: Text(
//               testAttemptBean.question.questionType ?? "",
//               style: Styles.boldBlack(size: 20),
//             ),
//           ),
//           Column(
//             children: List.generate(
//               widget._list[widget._currentQuestion]!.question.options.length,
//               (index) => Column(
//                 children: [
//                   TapWidget(
//                     onTap: () {
//                       setState(() {
//                         if (widget._list![widget._currentQuestion].question
//                             !.selectedOption
//                             !.contains(widget._list![widget._currentQuestion]
//                                 .question!.options![index].id
//                                 .toString())) {
//                           widget._list![widget._currentQuestion].question!
//                               .selectedOption
//                               !.remove(widget._list![widget._currentQuestion]
//                                   .question!.options![index].id
//                                   .toString());
//                           widget._list![widget._currentQuestion].question
//                               !.options![index].selected = false;
//                         } else {
//                           widget._list![widget._currentQuestion].question
//                               !.selectedOption
//                               ?.add(widget._list![widget._currentQuestion]
//                                   .question!.options![index].id
//                                   .toString());
//                           widget._list![widget._currentQuestion].question
//                               !.options![index].selected = true;
//                         }
//                         if (widget._list![widget._currentQuestion].question
//                                 !.selectedOption!.length >
//                             0) {
//                           widget._isOptionSelected = true;
//                         } else {
//                           widget._isOptionSelected = false;
//                         }
//                       });
//                     },
//                     child: Card(
//                       elevation: 5,
//                       margin: const EdgeInsets.symmetric(vertical: 10),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.all(
//                           Radius.circular(5),
//                         ),
//                         side: BorderSide(
//                             color: widget._list![widget._currentQuestion]
//                                     .question!.options![index].selected!
//                                 ? context.appColors.PRIMARY
//                                 : Colors.black,
//                             width: 1.5),
//                       ),
//                       child: Container(
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         alignment: Alignment.centerLeft,
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 15, vertical: 10),
//                         child: Text(htmlUnescape.convert(widget._list![widget._currentQuestion].question
//                             !.options![index].statement!),
//                           style: Styles.regularBlack(size: 15),
//                         ),
//                       ),
//                     ),
//                   ),
//                   if (widget._list![widget._currentQuestion].question!.options !=
//                           null &&
//                       widget._list![widget._currentQuestion].question
//                               !.options![index].image !=
//                           null &&
//                       widget._list![widget._currentQuestion].question
//                           !.options![index].image!.isNotEmpty)
//                     TapWidget(
//                       onTap: () async {
//                         _fullViewDialog(
//                             image: ApiConstants.IMAGE_BASE_URL +
//                                 widget._list![widget._currentQuestion].question
//                                     !.options![index].image!);
//                       },
//                       child: Image.network(
//                         ApiConstants.IMAGE_BASE_URL +
//                             widget._list![widget._currentQuestion].question
//                                 !.options![index].image!,
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         height:
//                             MediaQuery.of(_scaffoldContext).size.width / 1.5,
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   _size(height: 20),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   _chooseOne(TestAttemptBean testAttemptBean) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (widget._list![widget._currentQuestion].question!.image != null &&
//               widget._list![widget._currentQuestion].question!.image!.isNotEmpty)
//             _handleFile(
//                 file: ApiConstants.IMAGE_BASE_URL +
//                     widget._list![widget._currentQuestion].question!.image!),
//           Padding(
//             padding: const EdgeInsets.symmetric(vertical: 20),
//             child: Text(
//               testAttemptBean.question!.questionType ?? "",
//               style: Styles.boldBlack(size: 20),
//             ),
//           ),
//           Column(
//             children: List.generate(
//               widget._list![widget._currentQuestion]!.question!.options!.length,
//               (index) => Column(
//                 children: [
//                   TapWidget(
//                     onTap: () {
//                       widget._isOptionSelected = true;
//                       setState(() {
//                         widget._list![widget._currentQuestion].question
//                             !.selectedOption
//                             !.clear();
//                         widget._list![widget._currentQuestion].question
//                             !.selectedOption
//                             ?.add(widget._list![widget._currentQuestion].question
//                                 !.options![index].id
//                                 .toString());
//                         for (var data = 0;
//                             data <
//                                 widget._list![widget._currentQuestion].question
//                                     !.options!.length;
//                             data++) {
//                           if (widget._list![widget._currentQuestion].question
//                                  ! .options![data].id
//                                   .toString() ==
//                               widget._list![widget._currentQuestion].question
//                                   !.selectedOption!.first) {
//                             widget._list![widget._currentQuestion].question
//                                 !.options![data].selected = true;
//                           } else {
//                             widget._list![widget._currentQuestion].question
//                                ! .options![data].selected = false;
//                           }
//                         }
//                       });
//                     },
//                     child: Card(
//                       elevation: 5,
//                       margin: const EdgeInsets.symmetric(vertical: 10),
//                       child: Container(
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         alignment: Alignment.centerLeft,
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 15, vertical: 10),
//                         decoration: BoxDecoration(
//                             borderRadius: BorderRadius.all(
//                               Radius.circular(5),
//                             ),
//                             border: Border.all(
//                               color: widget._list![widget._currentQuestion]
//                                       .question.options[index].selected
//                                   ? context.appColors.PRIMARY
//                                   : Colors.black,
//                             )),
//                         child: Text(htmlUnescape.convert(widget._list[widget._currentQuestion].question
//                             .options[index].statement),
//                           style: Styles.regularBlack(size: 15),
//                         ),
//                       ),
//                     ),
//                   ),
//                   if (widget._list[widget._currentQuestion].question.options !=
//                           null &&
//                       widget._list[widget._currentQuestion].question
//                               .options[index].image !=
//                           null &&
//                       widget._list[widget._currentQuestion].question
//                           .options[index].image.isNotEmpty)
//                     TapWidget(
//                       onTap: () async {
//                         _fullViewDialog(
//                             image: ApiConstants.IMAGE_BASE_URL +
//                                 widget._list[widget._currentQuestion].question
//                                     .options[index].image);
//                       },
//                       child: Image.network(
//                         ApiConstants.IMAGE_BASE_URL +
//                             widget._list[widget._currentQuestion].question
//                                 .options[index].image,
//                         width: MediaQuery.of(_scaffoldContext).size.width,
//                         height:
//                             MediaQuery.of(_scaffoldContext).size.width / 1.5,
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   _size(height: 20),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   void _submitAnswers() {
//     List<Answers> _answers = [];
//     for (var data in widget._list) {
//       if (data.isVisited == 1 ||
//           Preference.getBool(data.question.id.toString() + "iv")) {
//         int timeTakenFromSection =
//             getTimeFromSections(data.id, data.question.id);
//         _answers.add(
//           Answers(
//             isAnswer: data.question.questionTypeId == "6"
//                 ? (data.question.solutionText != null ||
//                         data.question.solutionImage != null
//                     ? 1
//                     : 0)
//                 : data.question.selectedOption.length > 0
//                     ? 1
//                     : 0,
//             isSubjective: data.question.questionTypeId == "6",
//             solution: data.question.solutionText,
//             userFile: data.question.solutionImage,
//             isVisited: data.isVisited,
//             sectionId: data.sectionId,
//             questionId: data.question.id,
//             selectedOptionId: data.question.selectedOption,
//             timeTaken: widget._isTestResumed &&
//                     Preference.exists(data.question.id.toString() + "ts")
//                 ? Preference.getInt(data.question.id.toString() + "ts")
//                 : widget._isTestResumed &&
//                         Preference.exists(data.question.id.toString() + "tt")
//                     ? Preference.getInt(data.question.id.toString() + "tt")
//                     : data.question.id == widget._currentQuestionId
//                         ? widget._stopWatchTimer!.secondTime.value
//                         : timeTakenFromSection == 0
//                             ? data.question.timeTaken
//                             : timeTakenFromSection,
//           ),
//         );
//         Preference.removePrefs(data.question.id.toString() + "tt");
//         Preference.removePrefs(data.question.id.toString() + "ts");
//         Preference.removePrefs(data.question.id.toString() + "iv");
//         Preference.removePrefs(data.question.id.toString() + "so");
//         Preference.removePrefs(data.question.questionId.toString() + "so");
//         Preference.removePrefs(
//             data.question.questionId.toString() + "solutionText");
//         Preference.removePrefs(
//             data.question.questionId.toString() + "solutionImage");
//       }
//     }
//     _authBloc.add(
//       SubmitAnswerEvent(
//         request: SubmitAnswerRequest(
//             attemptId: widget._attemptId, questions: _answers),
//       ),
//     );
//   }

//   void _handlePreviousButton() {
//     int currentPage1 = widget._pageViewController.page.toInt();
//     int currentPage;
//     if (currentPage1 != 0) {
//       currentPage = currentPage1 - 1;
//     }
//     int currentSectionQuestionIndex =
//         currentPage % widget._sections[widget._currentSection].length;
//     if (currentSectionQuestionIndex > 0) {
//       if (widget._list[currentPage].question.durationSeconds !=
//           widget._sections[widget._currentSection][currentSectionQuestionIndex]
//               ["timeTaken"]) {
//         widget._pageViewController.previousPage(
//           duration: Duration(milliseconds: 100),
//           curve: Curves.ease,
//         );
//         widget._list[widget._currentQuestion].question.timeTaken =
//             widget._stopWatchTimer!.secondTime.value;
//         Preference.setInt(
//             widget._list[widget._currentQuestion].question.id.toString() + "tt",
//             widget._stopWatchTimer!.secondTime.value);
//         Preference.setBool(
//             widget._list[widget._currentQuestion].question.id.toString() + "iv",
//             true);
//         Utility.waitForMili(200).then((value) {
//           widget._pageChange = true;
//           for (var data in widget._sections[widget._currentSection].toList()) {
//             if (data["questionId"] == widget._currentQuestionId) {
//               widget._stopWatchTimer = StopWatchTimer();
//               widget._stopWatchTimer!.setPresetSecondTime(
//                   widget._list[currentPage].question.timeTaken);
//               widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//               // widget._stopWatchTimer!.onExecute
//               //     .add(StopWatchExecute.reset);
//               // widget._stopWatchTimer! = StopWatchTimer();
//               // widget._stopWatchTimer!
//               //     .setPresetSecondTime(
//               //     data["timeTaken"].toInt());
//               // widget._stopWatchTimer!.onExecute
//               //     .add(StopWatchExecute.start);
//               this.setState(() {});
//               break;
//             }
//           }
//         });
//       } else {
//         AlertsWidget.alertWithOkBtn(
//           context: _scaffoldContext,
//           title: "",
//           text: "${LocaleKeys.time_question_over.tr()}",
//         );
//       }
//     } else {
//       if (widget._currentSection == 0) {
//         if (widget._list[currentPage].question.durationSeconds !=
//             widget._sections[widget._currentSection].first["timeTaken"]) {
//           widget._pageViewController.previousPage(
//             duration: Duration(milliseconds: 100),
//             curve: Curves.ease,
//           );
//           widget._list[widget._currentQuestion].question.timeTaken =
//               widget._stopWatchTimer!.secondTime.value;

//           Preference.setInt(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "tt",
//               widget._stopWatchTimer!.secondTime.value);
//           Preference.setBool(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "iv",
//               true);
//           Utility.waitForMili(200).then((value) {
//             widget._pageChange = true;
//             for (var data
//                 in widget._sections[widget._currentSection].toList()) {
//               if (data["questionId"] == widget._currentQuestionId) {
//                 widget._stopWatchTimer = StopWatchTimer();
//                 widget._stopWatchTimer!.setPresetSecondTime(
//                     widget._list[currentPage].question.timeTaken);
//                 widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//                 // widget._stopWatchTimer!.onExecute
//                 //     .add(StopWatchExecute.reset);
//                 // widget._stopWatchTimer! = StopWatchTimer();
//                 // widget._stopWatchTimer!
//                 //     .setPresetSecondTime(
//                 //     data["timeTaken"].toInt());
//                 // widget._stopWatchTimer!.onExecute
//                 //     .add(StopWatchExecute.start);
//                 this.setState(() {});
//                 break;
//               }
//             }
//           });
//         } else {
//           AlertsWidget.alertWithOkBtn(
//             context: _scaffoldContext,
//             title: "",
//             text: "${LocaleKeys.time_question_over.tr()}",
//           );
//         }
//       } else {
//         if (widget._list[currentPage].question.durationSeconds !=
//             widget._sections[widget._currentSection - 1].last["timeTaken"]) {
//           widget._pageViewController.previousPage(
//             duration: Duration(milliseconds: 100),
//             curve: Curves.ease,
//           );
//           widget._list[widget._currentQuestion].question.timeTaken =
//               widget._stopWatchTimer!.secondTime.value;
//           Preference.setInt(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "tt",
//               widget._stopWatchTimer!.secondTime.value);
//           Preference.setBool(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "iv",
//               true);
//           Utility.waitForMili(200).then((value) {
//             widget._pageChange = true;
//             for (var data
//                 in widget._sections[widget._currentSection].toList()) {
//               if (data["questionId"] == widget._currentQuestionId) {
//                 widget._stopWatchTimer = StopWatchTimer();
//                 widget._stopWatchTimer.setPresetSecondTime(
//                     widget._list[currentPage].question.timeTaken);
//                 widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//                 // widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//                 // widget._stopWatchTimer! = StopWatchTimer();
//                 // widget._stopWatchTimer!
//                 //     .setPresetSecondTime(data["timeTaken"].toInt());
//                 // widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//                 this.setState(() {});
//                 break;
//               }
//             }
//           });
//         } else {
//           AlertsWidget.alertWithOkBtn(
//             context: _scaffoldContext,
//             title: "",
//             text: "${LocaleKeys.time_question_over.tr()}",
//           );
//         }
//       }
//     }
//   }

//   void _handleSectionNextButton() {
//     if (widget._isOptionSelected) {
//       AlertsWidget.alertWithOkBtn(
//           context: _scaffoldContext,
//           title: "",
//           text: "${LocaleKeys.save_answer_question.tr()}");
//       return;
//     }
//     for (int i = 0; i < widget._list.length; i++) {
//       if (widget._currentSection < widget._list[i].id) {
//         widget._pageViewController.animateToPage(
//           i,
//           duration: Duration(microseconds: 100),
//           curve: Curves.ease,
//         );
//         widget._currentSection = widget._list[i].id;
//         widget._currentQuestionId = widget._list[i].question.id;
//         widget._list[widget._currentQuestion].question.timeTaken =
//             widget._stopWatchTimer!.secondTime.value;
//         Preference.setInt(
//             widget._list[widget._currentQuestion].question.id.toString() + "tt",
//             widget._stopWatchTimer!.secondTime.value);
//         Preference.setBool(
//             widget._list[widget._currentQuestion].question.id.toString() + "iv",
//             true);
//         widget._currentQuestion = i;
//         widget._pageChange = true;
//         if (widget._list[widget._currentQuestion!]?.question?.timeTaken !=
//                 null ||
//             widget._list[widget._currentQuestion + 1].question.timeTaken != 0) {
//           widget._stopWatchTimer = StopWatchTimer();
//           widget._stopWatchTimer!.setPresetSecondTime(
//               widget._list[widget._currentQuestion].question.timeTaken);
//           widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//         } else {
//           widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//         }
//         // Utility.waitForMili(500).then((value) {
//         //   widget._pageChange = true;
//         //   for (var data in widget
//         //       ._sections[widget._currentSection]
//         //       .toList()) {
//         //     if (data["questionId"] ==
//         //         widget._currentQuestionId) {
//         //       widget._stopWatchTimer!.onExecute
//         //           .add(StopWatchExecute.reset);
//         //       widget._stopWatchTimer! = StopWatchTimer();
//         //       widget._stopWatchTimer!.setPresetSecondTime(
//         //           data["timeTaken"].toInt());
//         //       widget._stopWatchTimer!.onExecute
//         //           .add(StopWatchExecute.start);
//         //       print("selected question $data");
//         //       print(data['timeTaken'].toInt());
//         //       widget._subjectiveController.text = widget
//         //           ._list![widget._currentQuestion]
//         //           .question
//         //           .solutionText ??
//         //           "";
//         //       this.setState(() {});
//         //       break;
//         //     }
//         //   }
//         // });
//         break;
//       }
//     }
//   }

//   void _handleSectionPreviousButton() {
//     if (widget._isOptionSelected) {
//       AlertsWidget.alertWithOkBtn(
//           context: _scaffoldContext,
//           title: "",
//           text: "${LocaleKeys.save_answer_question.tr()}");
//       return;
//     }
//     if (widget._currentSection != 0) {
//       bool _sectionChanged = false;
//       for (int i = 0; i < widget._list.length; i++) {
//         if ((widget._currentSection - 1)! == widget._list[i].id &&
//             widget._list[i].question.timeTaken <
//                 widget._list[i].question.durationSeconds) {
//           widget._pageViewController.animateToPage(
//             i,
//             duration: Duration(microseconds: 100),
//             curve: Curves.ease,
//           );
//           _sectionChanged = true;
//           widget._currentSection = widget._list[i].id;
//           widget._list[widget._currentQuestion].question.timeTaken =
//               widget._stopWatchTimer!.secondTime.value;
//           Preference.setInt(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "tt",
//               widget._stopWatchTimer!.secondTime.value);
//           Preference.setBool(
//               widget._list[widget._currentQuestion]!.question.id.toString() +
//                   "iv",
//               true);
//           widget._currentQuestionId = widget._list[i].question.id;
//           widget._currentQuestion = i;
//           // Utility.waitForMili(500).then((value) {
//           widget._pageChange = true;
//           if (widget._list[widget._currentQuestion!]?.question?.timeTaken !=
//                   null ||
//               widget._list[widget._currentQuestion +! 1].question.timeTaken !=
//                   0) {
//             widget._stopWatchTimer = StopWatchTimer();
//             widget._stopWatchTimer!.setPresetSecondTime(
//                 widget._list[widget._currentQuestion].question.timeTaken);
//             widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//           } else {
//             widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//           }
//           // for (var data in widget
//           //     ._sections[widget._currentSection]
//           //     .toList()) {
//           //   if (data["questionId"] ==
//           //       widget._currentQuestionId) {
//           //     widget._stopWatchTimer!.onExecute
//           //         .add(StopWatchExecute.reset);
//           //     widget._stopWatchTimer! = StopWatchTimer();
//           //     widget._stopWatchTimer!.setPresetSecondTime(
//           //         data["timeTaken"].toInt());
//           //     widget._stopWatchTimer!.onExecute
//           //         .add(StopWatchExecute.start);
//           //     print("selected question $data");
//           //     print(data['timeTaken'].toInt());
//           //     widget._subjectiveController.text = widget
//           //         ._list![widget._currentQuestion]
//           //         .question
//           //         .solutionText ??
//           //         "";
//           //     this.setState(() {});
//           //     break;
//           //   }
//           // }
//           //   });
//           break;
//         }
//       }
//       if (_sectionChanged == false && (widget._currentSection - 2 >= 0)) {
//         for (int j = widget._currentSection - 2; j >= 0; j--) {
//           for (int i = 0; i < widget._list.length; i++) {
//             if (j == widget._list?[i].id &&
//                 widget._list?[i].question.timeTaken <
//                     widget._list[i].question.durationSeconds) {
//               widget._pageViewController.animateToPage(
//                 i,
//                 duration: Duration(microseconds: 100),
//                 curve: Curves.ease,
//               );
//               _sectionChanged = true;
//               widget._currentSection = widget._list?[i].id;
//               widget._list?[widget._currentQuestion].question?.timeTaken =
//                   widget._stopWatchTimer!.secondTime.value;
//               Preference.setInt(
//                   widget._list?[widget._currentQuestion].question?.id.toString() +
//                       "tt",
//                   widget._stopWatchTimer!.secondTime.value);
//               Preference.setBool(
//                   widget._list[widget._currentQuestion].question?.id.toString() +
//                       "iv",
//                   true);
//               widget._currentQuestionId = widget._list?[i].question?.id;
//               widget._currentQuestion = i;
//               // Utility.waitForMili(500).then((value) {
//               widget._pageChange = true;
//               if (widget._list?[widget._currentQuestion]?.question?.timeTaken !=
//                       null ||
//                   widget._list?[widget._currentQuestion + 1].question
//                           ?.timeTaken !=
//                       0) {
//                 widget._stopWatchTimer = StopWatchTimer();
//                 widget._stopWatchTimer!.setPresetSecondTime(
//                     widget._list[widget._currentQuestion].question.timeTaken);
//                 widget._stopWatchTimer!.onExecute.add(StopWatchExecute.start);
//               } else {
//                 widget._stopWatchTimer!.onExecute.add(StopWatchExecute.reset);
//               }
//               // for (var data in widget
//               //     ._sections[widget._currentSection]
//               //     .toList()) {
//               //   if (data["questionId"] ==
//               //       widget._currentQuestionId) {
//               //     widget._stopWatchTimer!.onExecute
//               //         .add(StopWatchExecute.reset);
//               //     widget._stopWatchTimer! = StopWatchTimer();
//               //     widget._stopWatchTimer!.setPresetSecondTime(
//               //         data["timeTaken"].toInt());
//               //     widget._stopWatchTimer!.onExecute
//               //         .add(StopWatchExecute.start);
//               //     print("selected question $data");
//               //     print(data['timeTaken'].toInt());
//               //     widget._subjectiveController.text = widget
//               //         ._list[widget._currentQuestion]
//               //         .question
//               //         .solutionText ??
//               //         "";
//               //     this.setState(() {});
//               //     break;
//               //   }
//               // }
//               //   });
//               break;
//             }
//           }
//           if (_sectionChanged) {
//             break;
//           }
//         }
//       }
//     }
//   }

//   int getTimeFromSections(int id, int questionId) {
//     int timeTaken = 0;
//     widget._sections?[id].forEach((v) {
//       if (v["questionId"] == questionId) {
//         timeTaken = v["timeTaken"];
//       }
//     });
//     return timeTaken;
//   }

//   _buildBottomAppBar() {
//     if ((widget._list!.length - 1) == widget._currentQuestion) {
//       widget._lastSave = true;
//     }
//     return BottomAppBar(
//       elevation: 10,
//       child: Padding(
//         padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             TapWidget(
//               onTap: () {
//                 if (widget._isOptionSelected==false) {
//                   AlertsWidget.alertWithOkBtn(
//                     context: _scaffoldContext,
//                     onOkClick: () {
//                       widget._showSubmitDialog = true;
//                     },
//                     title: "${LocaleKeys.alert.tr()}",
//                     text: "${LocaleKeys.save_answer_ques.tr()}",
//                   );
//                   return;
//                 }
//                 _handlePreviousButton();
//               },
//               child: Text(
//                 widget._currentQuestion == 0 ? "" : "${LocaleKeys.previous.tr()}",
//                 style: Styles.boldBlue(size: 16),
//               ),
//             ),
//             TapWidget(
//               onTap: () {
//                 if ((widget._list!.length - 1) == widget._currentQuestion) {
//                   widget._lastSave = true;
//                   AlertsWidget.alertWithOkCancelBtn(
//                       context: _scaffoldContext,
//                       onOkClick: () {
//                         //  _saveClick();
//                         widget._stopWatchTimer!.onExecute
//                             .add(StopWatchExecute.reset);
//                         widget._allTimer!.cancel();
//                         _submitAnswers();
//                       },
//                       okText: "${LocaleKeys.submit.tr()}".toUpperCase(),
//                       cancelText: "${LocaleKeys.continueF.tr()}".toUpperCase(),
//                       text:
//                           "${LocaleKeys.you_still_have_time_left.tr()}",
//                       title: "${LocaleKeys.finish_assessment.tr()}",
//                       onCancelClick: () {
//                         widget._isOptionSelected = false;
//                         widget._isContinued = true;
//                         widget._lastSave = false;
//                         _saveClick();
//                       });
//                 } else {
//                   _saveClick();
//                   widget._isSavedManually = true;
//                 }
//               },
//               child: Text(
//                 ((widget._list!.length - 1) == widget._currentQuestion)
//                     ? "${LocaleKeys.save.tr()}"
//                     : "${LocaleKeys.save_next.tr()}",
//                 style: Styles.boldBlue(size: 16),
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }
