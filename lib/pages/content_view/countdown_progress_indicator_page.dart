import 'package:flutter/material.dart';
import 'dart:async';

import 'package:masterg/utils/theme/theme_extensions.dart';

class CountdownProgressIndicator extends StatefulWidget {
  final int duration; // Duration in seconds
  final VoidCallback onTimerEnd; // Callback when timer ends

  const CountdownProgressIndicator({
    super.key,
    required this.duration,
    required this.onTimerEnd,
  });

  @override
  State<CountdownProgressIndicator> createState() =>
      _CountdownProgressIndicatorState();
}

class _CountdownProgressIndicatorState
    extends State<CountdownProgressIndicator> {
  double _progress = 1.0; // Start at full progress
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    int elapsedSeconds = 0;

    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        elapsedSeconds++;
        _progress = 1.0 - (elapsedSeconds / widget.duration);

        if (elapsedSeconds >= widget.duration) {
          _timer.cancel(); // Stop the timer when the countdown finishes
          widget.onTimerEnd(); // Trigger the callback
        }
      });
    });
  }

  @override
  void dispose() {
    _timer
        .cancel(); // Make sure to cancel the timer when the widget is disposed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Text(
          "Time remaining: ${(widget.duration * _progress).toStringAsFixed(0)} seconds",
          style: TextStyle(fontSize: 14),
        ),
        SizedBox(height: 10),
        LinearProgressIndicator(
          value: _progress,
          backgroundColor: context.appColors.error,
          color: Colors.green,
        ),
      ],
    );
  }
}
