import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/notification_list_resp.dart';
import 'package:masterg/data/models/response/home_response/notification_read_resp.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/pages/notifications/widgets/notification_list_items.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:shimmer/shimmer.dart';
import 'package:badges/badges.dart' as badges;

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});
  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool? isLoading = false;
  bool? isLoadingMore = false;
  NotificationsListResp? notificationList;
  NotificationReadResp? notificationRead;
  int currentPage = 1;
  bool loadData = true;
  List<Data>? allNotifications = [];
  int readCount = 0;

  @override
  void initState() {
    getNotificationList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is NotificationsListState) {
            _handleNotificationListState(state);
          }

          if (state is NotificationsReadState) {
            _handleNotificationReadState(state);
          }
        },
        child: Scaffold(
            backgroundColor: context.appColors.background,
            appBar: AppBar(
              elevation: 0,
              backgroundColor: context.appColors.surface,
              title:
                  Text('notification', style: Styles.getBoldThemeStyle(context))
                      .tr(),
              leading: BackButton(color: context.appColors.textBlack),
              actions: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20.0, vertical: 16),
                  child: badges.Badge(
                    badgeContent: allNotifications!.isNotEmpty
                        ? Text('${_getCount(false)}',
                            style: Styles.bold(
                                color: context.appColors.textWhite, size: 10))
                        : Text(Preference.getString(Preference.GET_COUNT) ?? '',
                            style: Styles.bold(
                                color: context.appColors.textWhite, size: 10)),
                    child: Icon(Icons.notification_add_outlined,
                        color: context.appColors.textBlack),
                  ),
                )
              ],
            ),
            body: ScreenWithLoader(
                isLoading: isLoading,
                body: allNotifications!.isNotEmpty
                    ? NotificationsListItem(
                        allNotifications: allNotifications,
                        onTapCallback:
                            (notificationId, notiId, type, isRead, index) {
                          getReadNotification(
                              allNotifications?[index].notifiableId,
                              '${allNotifications?[index].id}',
                              '${allNotifications?[index].type}',
                              'is_read');
                          setState(() {
                            isRead = allNotifications?[index].readContent;
                          });
                        })
                    : isLoading == true
                        ? Container(
                            padding: EdgeInsets.only(top: 10),
                            child: ListView.separated(
                                shrinkWrap: true,
                                itemCount: 10,
                                separatorBuilder: (context, index) =>
                                    const SizedBox(height: 10),
                                itemBuilder: (BuildContext context,
                                        int index) =>
                                    Shimmer.fromColors(
                                      baseColor: context.appColors.shimmerBase,
                                      highlightColor:
                                          context.appColors.shimmerHighlight,
                                      child: Container(
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.1,
                                        margin: EdgeInsets.symmetric(
                                          horizontal: 10,
                                        ),
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: context.appColors.surface,
                                            borderRadius:
                                                BorderRadius.circular(6)),
                                      ),
                                    )),
                          )
                        : Center(child: Text('no_notification_fount').tr()))));
  }

  //allNotifications!.isNotEmpty
  int _getCount(bool isRead) {
    return allNotifications!
        .where((element) => element.readContent == (isRead ? "is_read" : null))
        .toList()
        .length;
  }

  void getNotificationList() {
    BlocProvider.of<HomeBloc>(context).add(
        NotificationsListEvent(fromValue: 1, toValue: 10, isInitial: true));
  }

  void _handleNotificationListState(NotificationsListState state) {
    setState(() {
      switch (state.apiState) {
        case NotificationStatus.loading:
          isLoading = true;
          Log.v("bbbSubscriptionCoursesState Loading.................");
          break;
        case NotificationStatus.loadingMore:
          Log.v("NotificationStatus Loading More.................");
          break;
        case NotificationStatus.success:
          try {
            if (state.response != null && state.response!.data != null) {
              // notificationList = state.response;
              allNotifications!.addAll(state.response!.data!);

              //Log.v('ALLNOTIFICATIONS:--${allNotifications?.length}');
              Preference.setString(
                  Preference.GET_COUNT, _getCount(false).toString());
              setState(() {
                isLoading = false;
              });
            } else {}
          } catch (e, stackTrace) {
            debugPrint('$stackTrace');
            setState(() {
              isLoading = false;
            });
          }
          break;

        case NotificationStatus.error:
          Log.v("SubscriptionCoursesState Error..........................");
          break;
        case NotificationStatus.initial:
          break;
      }
    });
  }

  void getReadNotification(
      int? notiId, String? id, String? type, String? isRead) {
    BlocProvider.of<HomeBloc>(context).add(NotificationReadEvent(
        notiId: notiId, id: id, type: type, isRead: isRead));
  }

  void _handleNotificationReadState(NotificationsReadState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Notification Read Loading.................");
          isLoading = true;
          debugPrint('call notifications event 2');
          break;
        case ApiStatus.SUCCESS:
          debugPrint('call notifications event 3');
          Log.v(
              "Notification Read Success ................... ${state.response}");
          try {
            if (state.response != null && state.response!.data != null) {
              notificationRead = state.response;
              // setState(() {
              //   isLoading = false;
              // });
            } else {}
          } catch (e, stacktrace) {
            debugPrint('$stacktrace');
            setState(() {
              isLoading = false;
            });
          }
          break;

        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("NotificationRead State Error..........................");
          print('call notifications event 4');
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
