import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';
import '../../data/api/api_service.dart';
import '../../data/models/response/home_response/course_category_list_id_response.dart';
import '../../data/providers/training_detail_provider.dart';
import '../../utils/Log.dart';
import '../singularis/competition/competition_detail.dart';
import '../training_pages/program_content/training_detail_page.dart';
import '../training_pages/training_service.dart';

class NotificationViewPage extends StatefulWidget {
  final String? notificationId;
  final String? notiTitle;
  final String? notiDesc;
  final String? dateTime;
  final String? type;
  final int? id;
  final String? route;

  const NotificationViewPage(
      {required this.notificationId,
      super.key,
      required this.notiTitle,
      required this.notiDesc,
      required this.dateTime,
      this.id,
      this.route,
      this.type});

  @override
  State<NotificationViewPage> createState() => _NotificationViewPageState();
}

class _NotificationViewPageState extends State<NotificationViewPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: context.appColors.surface,
        //title: Text('${widget.notiTitle}', style: Styles.getBoldThemeStyle(context)),
        title: Text('notification_details',
                style: Styles.getBoldThemeStyle(context))
            .tr(),
        leading: BackButton(color: context.appColors.textBlack),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                SizedBox(
                  height: 100,
                  child: Column(
                    children: [
                      Row(children: [
                        customImage(
                            color: Color(0xffEEF5FF), status: '${widget.type}'),
                        SizedBox(width: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: MediaQuery.of(context).size.width - 130,
                              child: Text('${widget.notiTitle}',
                                  style: Styles.getBoldThemeStyle(context,
                                      size: 16)),
                            ),
                            SizedBox(height: 5),
                            Text('${widget.dateTime}',
                                style: Styles.getRegularThemeStyle(context,
                                    size: 12)),
                            Text('${widget.type}',
                                style: Styles.getRegularThemeStyle(context,
                                    size: 12)),
                          ],
                        ),
                      ])
                    ],
                  ),
                ),
                Divider(),
                widget.notiDesc != null
                    ? (widget.notiDesc!.contains('<') &&
                            widget.notiDesc!.contains('>'))
                        ? Html(
                            data: """${widget.notiDesc}""",
                          )
                        : Text('${widget.notiDesc}',
                            style:
                                Styles.getRegularThemeStyle(context, size: 14))
                    : SizedBox(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          height: 50, // Adjust footer height
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          decoration: BoxDecoration(
            color: context.appColors.surface,
            borderRadius: BorderRadius.zero,
          ),
          child: ElevatedButton(
            onPressed: () {
              Log.v(widget.id);
              if (widget.type == 'event') {
                if (widget.id != 0) {
                  Navigator.of(context).pop();
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => CompetitionDetail(
                              competitionId: widget.id, isEvent: true)));
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('event_not_available').tr(),
                  ));
                }
              } else if (widget.type == 'assessment') {
                Navigator.push(
                    context,
                    NextPageRoute(
                        ChangeNotifierProvider<TrainingDetailProvider>(
                            create: (context) => TrainingDetailProvider(
                                TrainingService(ApiService()),
                                MProgram(id: widget.id)),
                            child: TrainingDetailPage())));
              }
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(50), // Full-width button
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8)), // Zero border radius
              ),
            ),
            child: const Text('continue').tr(),
          ),
        ),
      ),
    );
  }

  Widget customImage({Color? color, String? status}) {
    return Column(
      children: [
        Container(
          height: 80,
          width: 80,
          decoration: BoxDecoration(color: color),
          child: status?.toLowerCase() == 'assessment'
              ? Icon(Icons.quiz_outlined, size: 40)
              : status?.toLowerCase() == 'video_yts'
                  ? Icon(Icons.live_tv, size: 40)
                  : status?.toLowerCase() == 'video'
                      ? Icon(Icons.video_collection_outlined, size: 40)
                      : Icon(Icons.forward_to_inbox_outlined, size: 40),
        ),
      ],
    );
  }
}
