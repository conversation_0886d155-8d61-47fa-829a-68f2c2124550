import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/pages/custom_pages/card_loader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/training_pages/program_content/training_detail_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../data/models/response/semester_list_response.dart';
import '../../local/pref/Preference.dart';
import '../../utils/config.dart';

class MyCourses extends StatefulWidget {
  final fromDashboard;
  final verticalScroll;
  final viewAll;
  const MyCourses(
      {super.key,
      this.fromDashboard = false,
      this.verticalScroll = false,
      this.viewAll = false});

  @override
  State<MyCourses> createState() => _MyCoursesState();
}

class _MyCoursesState extends State<MyCourses> {
  int nocourseAssigned = 0;
  bool? _isCourseList1Loading = true;
  List<MProgram>? courseList1;
  List<SemesterData>? semesterList = [];

  String? errorMessage;
  int semTabSelection = 0;
  Box? box;

  @override
  void initState() {
    Log.v(
        'CURRENT_SEMESTER_ID---- ${Preference.getInt(Preference.CURRENT_SEMESTER_ID)}');
    Log.v('viewAll---- ${widget.viewAll}');
    super.initState();

    if (widget.viewAll == false) {
      _getAssignedCourses();
    } else {
      _getSemesterList();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.fromDashboard) {
      return _mainBody();
    } else {
      return Scaffold(
        appBar: AppBar(
          title: Text(
              tr(APK_DETAILS["package_name"] == "com.singulariswow.mec"
                  ? 'course_module'
                  : 'my_course'),
              style: Styles.getBoldThemeStyle(context, size: 18)),
          centerTitle: false,
          backgroundColor: context.appColors.surface,
          elevation: 0.0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: context.appColors.textBlack,
            ),
            onPressed: () {
              _getAssignedCourses();
              Navigator.pop(context);
            },
          ),
        ),
        body: _mainBody(),
      );
    }
  }

  BlocManager _mainBody() {
    return BlocManager(
        initState: (BuildContext context) {},
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is CourseCategoryListIDState) {
              _handleCourseList1Response(state);
            }
            if (state is SemesterListState) {
              _handleSemesterListResponse(state);
            }
          },
          child: _getCourses(),
        ));
  }

  void _handleCourseList1Response(CourseCategoryListIDState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isCourseList1Loading = true;

          break;
        case ApiStatus.SUCCESS:
          Log.v("CourseCategoryState....................");
          courseList1 = state.response!.data!.programs;
          courseList1 = courseList1
              ?.where((element) => element.isCompetition != 1)
              .toList();

          if (courseList1!.isEmpty) nocourseAssigned = 1;
          _isCourseList1Loading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CourseCategoryListIDState ..........................${loginState.error}");
          setState(() {
            errorMessage = state.response?.error![0];
            _isCourseList1Loading = false;
          });
          courseList1 = state.response!.data!.programs;
          courseList1 = courseList1
              ?.where((element) => element.isCompetition != 1)
              .toList();
//

          if (courseList1 == null || courseList1!.isEmpty) {
            nocourseAssigned = 1;
          }

          FirebaseAnalytics.instance.logEvent(name: 'my_courses', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleSemesterListResponse(SemesterListState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          Log.v("SemesterListState....................");
          semesterList = state.response?.data!;

          //TODO: New code by short
          DateTime now = DateTime.now();
// 1. Current semesters (ongoing now)
          List current = semesterList!.where((item) {
            DateTime start = DateTime.parse(item.startDate!);
            DateTime end = DateTime.parse(item.endDate!);
            return start.isBefore(now) && end.isAfter(now) ||
                start.isAtSameMomentAs(now) ||
                end.isAtSameMomentAs(now);
          }).toList();

// 2. Future semesters
          List future = semesterList!
              .where((item) => DateTime.parse(item.startDate!).isAfter(now))
              .toList();

// 3. Expired semesters
          List expired = semesterList!
              .where((item) => DateTime.parse(item.endDate!).isBefore(now))
              .toList();

          current.sort((a, b) => DateTime.parse(a.startDate!)
              .compareTo(DateTime.parse(b.startDate!)));
          future.sort((a, b) => DateTime.parse(a.startDate!)
              .compareTo(DateTime.parse(b.startDate!)));
          expired.sort((a, b) => DateTime.parse(a.startDate!)
              .compareTo(DateTime.parse(b.startDate!)));

          semesterList = [...current, ...future, ...expired];

          //TODO:Old code change by rohit because show greater than Semester in List 19 Jun 2025
          // semesterList!.sort((a, b) {
          //   DateTime dateA = DateTime.parse(a.startDate!);
          //   DateTime dateB = DateTime.parse(b.startDate!);
          //   return (dateA.difference(currentDate)).abs().compareTo((dateB.difference(currentDate)).abs());
          // });
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error SemesterListState ..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'SemesterList', parameters: {
            "ERROR": '${state.response?.error}',
          });
          _getAssignedCourses();
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getAssignedCourses({int index = 0}) {
    if (widget.viewAll == false) {
      BlocProvider.of<HomeBloc>(context).add(CourseCategoryListIDEvent(
          categoryId: 0,
          semesterID: Preference.getInt(Preference.CURRENT_SEMESTER_ID)));
    } else {
      if (semesterList?.length != 0) {
        BlocProvider.of<HomeBloc>(context).add(CourseCategoryListIDEvent(
            categoryId: 0, semesterID: semesterList?[index].id));
      } else {
        BlocProvider.of<HomeBloc>(context).add(CourseCategoryListIDEvent(
            categoryId: 0,
            semesterID: Preference.getInt(Preference.CURRENT_SEMESTER_ID)));
      }
    }
  }

  void _getSemesterList() {
    BlocProvider.of<HomeBloc>(context).add(SemesterListEvent());
  }

  Widget _getCourses() {
    if (APK_DETAILS['package_name'] == 'com.learn_build') {
      courseList1?.sort((a, b) => a.categoryName!.compareTo(b.categoryName!));
    }

    return courseList1 != null && courseList1!.isNotEmpty
        ? widget.verticalScroll == true
            ? SingleChildScrollView(
                physics: ScrollPhysics(),
                child: Container(
                  padding: EdgeInsets.only(left: 10.0, right: 10.0),
                  decoration:
                      BoxDecoration(color: context.appColors.background),
                  child: Column(
                    children: [
                      //TODO:- Semester filter
                      semesterList?.length != 0
                          ? Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 5),
                              child: SizedBox(
                                height: 45,
                                child: ListView.builder(
                                  itemCount: semesterList?.length ?? 0,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return InkWell(
                                      onTap: () {
                                        // When a category is clicked, update the selected category name and skills
                                        _getAssignedCourses(index: index);
                                        setState(() {
                                          semTabSelection = index;
                                          _isCourseList1Loading == true;
                                        });
                                      },
                                      child: Container(
                                        width: min(
                                                MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                280) *
                                            0.35,
                                        decoration: BoxDecoration(
                                          color: semTabSelection == index
                                              ? context.appColors.primaryDark
                                              : Colors.blue
                                                  .withValues(alpha: 0.08),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        margin: EdgeInsets.all(4),
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Center(
                                            child: Text(
                                              '${semesterList![index].name}',
                                              style: TextStyle(
                                                color: semTabSelection == index
                                                    ? context.appColors
                                                        .primaryForeground
                                                    : context
                                                        .appColors.textBlack,
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              softWrap: true,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            )
                          : SizedBox(),

                      //TODO: My Courses List
                      ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (
                            BuildContext context,
                            int index,
                          ) {
                            return _isCourseList1Loading == false
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      InkWell(
                                          onTap: () {
                                            Navigator.push(
                                                context,
                                                NextPageRoute(
                                                    ChangeNotifierProvider<
                                                            TrainingDetailProvider>(
                                                        create: (context) =>
                                                            TrainingDetailProvider(
                                                                TrainingService(
                                                                    ApiService()),
                                                                courseList1![
                                                                    index]),
                                                        // child: TrainingDetailPageOpt()),
                                                        child:
                                                            TrainingDetailPage()),
                                                    isMaintainState: true));
                                          },
                                          child: Container(
                                            padding: EdgeInsets.all(10),
                                            margin: EdgeInsets.only(
                                                top: 12,
                                                right:
                                                    widget.fromDashboard == true
                                                        ? 10
                                                        : 0),
                                            width: widget.fromDashboard
                                                ? min(width(context), 480) *
                                                    0.85
                                                : min(width(context), 480),
                                            decoration: BoxDecoration(
                                                color: widget.fromDashboard
                                                    ? context.appColors.surface
                                                        .withValues(alpha: 0.6)
                                                    : context.appColors.surface,
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                                border: Border.all(
                                                  color: context.appColors.grey,
                                                  width: 1,
                                                  style: BorderStyle.solid,
                                                )),
                                            child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  SizedBox(
                                                    width: 85,
                                                    height: 90,
                                                    child: ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      child: CachedNetworkImage(
                                                        imageUrl:
                                                            '${courseList1![index].image}',
                                                        width: 100,
                                                        height: 120,
                                                        errorWidget: (context,
                                                                url, error) =>
                                                            SvgPicture.asset(
                                                          'assets/images/gscore_postnow_bg.svg',
                                                        ),
                                                        fit: BoxFit.cover,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 10,
                                                  ),
                                                  SizedBox(
                                                    width: min(width(context),
                                                            480) *
                                                        0.55,
                                                    child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Text(
                                                              '${courseList1![index].name}',
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              softWrap: false,
                                                              style: Styles.bold(
                                                                  size: 16,
                                                                  color: context
                                                                      .appColors
                                                                      .headingTitle)),
                                                          SizedBox(height: 4),
                                                          Row(
                                                            children: [
                                                              Icon(Icons.alarm,
                                                                  size: 18,
                                                                  color: context
                                                                      .appColors
                                                                      .bodyText),
                                                              // SvgPicture.asset(
                                                              //   'assets/images/clock_icon.svg',
                                                              //   width: 15,
                                                              //   height: 15,
                                                              //   allowDrawingOutsideViewBox:
                                                              //       true,
                                                              // ),
                                                              SizedBox(
                                                                width: 5,
                                                              ),
                                                              Text(
                                                                  '${courseList1![index].duration}',
                                                                  maxLines: 1,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  softWrap:
                                                                      false,
                                                                  style: Styles.regular(
                                                                      size: 12,
                                                                      color: context
                                                                          .appColors
                                                                          .bodyText)),
                                                            ],
                                                          ),
                                                          SizedBox(height: 15),
                                                          Text(
                                                              '${courseList1![index].completionPer}% ${tr('completed')}',
                                                              style: Styles.regular(
                                                                  size: 12,
                                                                  color: context
                                                                      .appColors
                                                                      .bodyText)),
                                                          SizedBox(height: 10),
                                                          Container(
                                                            height: 10,
                                                            width: min(
                                                                    width(
                                                                        context),
                                                                    480) *
                                                                0.8,
                                                            decoration: BoxDecoration(
                                                                color: context
                                                                    .appColors
                                                                    .grey,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            10)),
                                                            child: Stack(
                                                              children: [
                                                                Container(
                                                                  height: 10,
                                                                  width: min(
                                                                          width(
                                                                              context),
                                                                          480) *
                                                                      0.6 *
                                                                      (courseList1![index]
                                                                              .completionPer! /
                                                                          100),
                                                                  decoration: BoxDecoration(
                                                                      color: context
                                                                          .appColors
                                                                          .green,
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              10)),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ]),
                                                  ),
                                                ]),
                                          )),
                                    ],
                                  )
                                : CardLoader();
                          },
                          itemCount: courseList1?.length ?? 0,
                          scrollDirection: widget.fromDashboard == true
                              ? Axis.horizontal
                              : Axis.vertical),
                    ],
                  ),
                ),
              )
            : Container(
                padding: EdgeInsets.only(left: 10.0, right: 10.0),
                decoration: BoxDecoration(color: context.appColors.background),
                child: ListView.builder(
                    itemBuilder: (
                      BuildContext context,
                      int index,
                    ) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    NextPageRoute(
                                        ChangeNotifierProvider<
                                                TrainingDetailProvider>(
                                            create: (context) =>
                                                TrainingDetailProvider(
                                                    TrainingService(
                                                        ApiService()),
                                                    courseList1![index]),
                                            // child: TrainingDetailPageOpt()),
                                            child: TrainingDetailPage()),
                                        isMaintainState: true));
                              },
                              child: Container(
                                padding: EdgeInsets.all(10),
                                margin: EdgeInsets.only(
                                    top: 12,
                                    right:
                                        widget.fromDashboard == true ? 10 : 0),
                                width: widget.fromDashboard
                                    ? min(width(context), 480) * 0.85
                                    : min(width(context), 480),
                                decoration: BoxDecoration(
                                    color: widget.fromDashboard
                                        ? context.appColors.surface
                                            .withValues(alpha: 0.6)
                                        : context.appColors.surface,
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: context.appColors.grey,
                                      width: 1,
                                      style: BorderStyle.solid,
                                    )),
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 85,
                                        height: 90,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                '${courseList1![index].image}',
                                            width: 100,
                                            height: 120,
                                            errorWidget:
                                                (context, url, error) =>
                                                    SvgPicture.asset(
                                              'assets/images/gscore_postnow_bg.svg',
                                            ),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      SizedBox(
                                        width: min(width(context), 480) * 0.55,
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                  '${courseList1![index].name}',
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  softWrap: false,
                                                  style: Styles.bold(
                                                      size: 16,
                                                      color: context.appColors
                                                          .headingTitle)),
                                              SizedBox(height: 4),
                                              Row(
                                                children: [
                                                  Icon(Icons.alarm,
                                                      size: 18,
                                                      color: context
                                                          .appColors.bodyText),
                                                  // SvgPicture.asset(
                                                  //   'assets/images/clock_icon.svg',
                                                  //   width: 15,
                                                  //   height: 15,
                                                  //   allowDrawingOutsideViewBox:
                                                  //       true,
                                                  // ),
                                                  SizedBox(
                                                    width: 5,
                                                  ),
                                                  Text(
                                                      '${courseList1![index].duration}',
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      softWrap: false,
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: context
                                                              .appColors
                                                              .bodyText)),
                                                ],
                                              ),
                                              SizedBox(height: 15),
                                              Text(
                                                  '${courseList1![index].completionPer}% ${tr('completed')}',
                                                  style: Styles.regular(
                                                      size: 12,
                                                      color: context
                                                          .appColors.bodyText)),
                                              SizedBox(height: 10),
                                              Container(
                                                height: 10,
                                                width:
                                                    min(width(context), 480) *
                                                        0.8,
                                                decoration: BoxDecoration(
                                                    color:
                                                        context.appColors.grey,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                child: Stack(
                                                  children: [
                                                    Container(
                                                      height: 10,
                                                      width: min(width(context),
                                                              480) *
                                                          0.6 *
                                                          (courseList1![index]
                                                                  .completionPer! /
                                                              100),
                                                      decoration: BoxDecoration(
                                                          color: context
                                                              .appColors.green,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      10)),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ]),
                                      ),
                                    ]),
                              )),
                        ],
                      );
                    },
                    itemCount: courseList1?.length ?? 0,
                    scrollDirection: widget.fromDashboard == true
                        ? Axis.horizontal
                        : Axis.vertical),
              )
        : _isCourseList1Loading == true
            ? widget.fromDashboard
                ? ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                          height: MediaQuery.of(context).size.height * 0.15,
                          margin: EdgeInsets.symmetric(
                              horizontal: 10, vertical: 20),
                          width: MediaQuery.of(context).size.width * 0.7,
                          decoration: BoxDecoration(
                              color: context.appColors.surface,
                              borderRadius: BorderRadius.circular(6)),
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                          height: MediaQuery.of(context).size.height * 0.15,
                          margin: EdgeInsets.symmetric(
                              horizontal: 10, vertical: 20),
                          width: MediaQuery.of(context).size.width * 0.7,
                          decoration: BoxDecoration(
                              color: context.appColors.surface,
                              borderRadius: BorderRadius.circular(6)),
                        ),
                      )
                    ],
                  )
                : CardLoader()
            : courseList1 != null && courseList1!.isEmpty
                ? Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                    child: Column(
                      children: [
                        SvgPicture.asset(
                          'assets/images/instructor_icon.svg',
                          height: 80.0,
                          width: 80.0,
                          allowDrawingOutsideViewBox: true,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Text(
                              '${Strings.of(context)?.subscribeToCourseToGetStarted}'),
                        )
                      ],
                    ),
                  )
                : Container(
                    height: height(context) * 0.8,
                    width: width(context),
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/instructor_icon.svg',
                          height: 80.0,
                          width: 80.0,
                          allowDrawingOutsideViewBox: true,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Text(
                            errorMessage ?? tr('no_active_courses'),
                            style: Styles.getBoldThemeStyle(context, size: 14),
                          ),
                        ),
                      ],
                    ),
                  );
  }
}
