import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// A utility class that manages dynamic height calculation for InAppWebView widgets
/// based on their HTML content, accounting for zoom levels and providing smooth transitions.
class DynamicWebViewHeightManager {
  static const double _defaultMinHeight = 50.0;
  static const double _defaultMaxHeight = 500.0;
  static const double _defaultZoomLevel = 2.5;
  static const Duration _defaultAnimationDuration = Duration(milliseconds: 300);

  /// Calculates the optimal height for WebView content
  ///
  /// [controller] - The InAppWebViewController instance
  /// [zoomLevel] - The zoom level applied to the content (default: 2.5)
  /// [minHeight] - Minimum height constraint (default: 50.0)
  /// [maxHeight] - Maximum height constraint (default: 500.0)
  ///
  /// Returns the calculated height or null if calculation fails
  static Future<double?> calculateContentHeight(
    InAppWebViewController controller, {
    double zoomLevel = _defaultZoomLevel,
    double minHeight = _defaultMinHeight,
    double maxHeight = _defaultMaxHeight,
  }) async {
    try {
      // Enhanced JavaScript to get accurate content height
      final heightResult = await controller.evaluateJavascript(source: """
        (function() {
          // Wait for any dynamic content to load
          return new Promise((resolve) => {
            setTimeout(() => {
              // Get all possible height measurements
              const body = document.body;
              const html = document.documentElement;
              
              // Calculate various height measurements
              const scrollHeight = Math.max(
                body.scrollHeight || 0,
                html.scrollHeight || 0
              );
              
              const offsetHeight = Math.max(
                body.offsetHeight || 0,
                html.offsetHeight || 0
              );
              
              const clientHeight = Math.max(
                body.clientHeight || 0,
                html.clientHeight || 0
              );
              
              // Get the bounding box of all content
              const range = document.createRange();
              range.selectNodeContents(body);
              const rect = range.getBoundingClientRect();
              const contentHeight = rect.height;
              
              // Use the maximum of all measurements for accuracy
              const finalHeight = Math.max(
                scrollHeight,
                offsetHeight,
                clientHeight,
                contentHeight,
                20 // Minimum fallback
              );
              
              resolve(finalHeight);
            }, 100); // Small delay to ensure content is rendered
          });
        })();
      """);

      if (heightResult != null) {
        double rawHeight =
            double.tryParse(heightResult.toString()) ?? minHeight;

        // Account for zoom level - if content is zoomed, we need more space
        double adjustedHeight = rawHeight * (zoomLevel / 1.0);

        // Apply constraints
        double finalHeight = adjustedHeight.clamp(minHeight, maxHeight);

        return finalHeight;
      }
    } catch (e) {
      debugPrint('Error calculating WebView height: $e');
    }

    return null;
  }

  /// Creates a JavaScript snippet to apply zoom and prepare content for height calculation
  static String getZoomAndPrepareScript(double zoomLevel) {
    return """
      // Apply zoom level
      document.body.style.zoom = '$zoomLevel';

      // Ensure proper styling for height calculation
      document.body.style.margin = '0';
      document.body.style.padding = '8px';
      document.body.style.boxSizing = 'border-box';
      document.body.style.overflow = 'hidden';

      // Ensure images and other media are properly sized
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
      });

      // Handle tables
      const tables = document.querySelectorAll('table');
      tables.forEach(table => {
        table.style.width = '100%';
        table.style.tableLayout = 'auto';
      });
    """;
  }

  /// Estimates height based on content analysis before WebView loads
  /// This provides a better initial height estimate
  static double estimateInitialHeight(String htmlContent,
      {double zoomLevel = _defaultZoomLevel}) {
    if (htmlContent.isEmpty) return _defaultMinHeight;

    // Count approximate content elements
    int lineBreaks = '<br>'.allMatches(htmlContent.toLowerCase()).length;
    int paragraphs = '<p>'.allMatches(htmlContent.toLowerCase()).length;
    int divs = '<div>'.allMatches(htmlContent.toLowerCase()).length;
    int images = '<img'.allMatches(htmlContent.toLowerCase()).length;
    int tables = '<table'.allMatches(htmlContent.toLowerCase()).length;

    // Estimate base height
    double estimatedHeight = _defaultMinHeight;

    // Add height for text content (rough estimation)
    int textLength = htmlContent.replaceAll(RegExp(r'<[^>]*>'), '').length;
    estimatedHeight += (textLength / 50) * 20; // ~20px per 50 characters

    // Add height for structural elements
    estimatedHeight += lineBreaks * 20;
    estimatedHeight += paragraphs * 25;
    estimatedHeight += divs * 15;
    estimatedHeight += images * 100; // Assume average image height
    estimatedHeight += tables * 80; // Assume average table height

    // Account for zoom level
    estimatedHeight *= (zoomLevel / 1.0);

    return estimatedHeight.clamp(_defaultMinHeight, _defaultMaxHeight);
  }

  /// Checks if content contains math equations (LaTeX)
  static bool containsMathEquations(String htmlContent) {
    final RegExp mathPattern = RegExp(
      r'src="https:\/\/latex\.codecogs\.com\/svg\.image\?.*?"',
      caseSensitive: false,
    );
    return mathPattern.hasMatch(htmlContent);
  }

  /// Gets appropriate height constraints based on content type
  static Map<String, double> getHeightConstraints(String htmlContent) {
    bool hasMath = containsMathEquations(htmlContent);
    bool hasImages = htmlContent.toLowerCase().contains('<img');
    bool hasTables = htmlContent.toLowerCase().contains('<table');

    double minHeight = _defaultMinHeight;
    double maxHeight = _defaultMaxHeight;

    if (hasMath) {
      minHeight = 80.0; // Math equations need more space
      maxHeight = 300.0;
    } else if (hasImages) {
      minHeight = 100.0;
      maxHeight = 400.0;
    } else if (hasTables) {
      minHeight = 60.0;
      maxHeight = 350.0;
    }

    return {
      'minHeight': minHeight,
      'maxHeight': maxHeight,
    };
  }
}

/// A widget that wraps InAppWebView with dynamic height management
class DynamicHeightWebView extends StatefulWidget {
  final String htmlContent;
  final double zoomLevel;
  final double minHeight;
  final double maxHeight;
  final Duration animationDuration;
  final InAppWebViewSettings? settings;
  final Function(InAppWebViewController)? onWebViewCreated;
  final Function(double)? onHeightChanged;
  final Widget? loadingWidget;
  final bool absorbing;

  const DynamicHeightWebView({
    Key? key,
    required this.htmlContent,
    this.zoomLevel = DynamicWebViewHeightManager._defaultZoomLevel,
    this.minHeight = DynamicWebViewHeightManager._defaultMinHeight,
    this.maxHeight = DynamicWebViewHeightManager._defaultMaxHeight,
    this.animationDuration =
        DynamicWebViewHeightManager._defaultAnimationDuration,
    this.settings,
    this.onWebViewCreated,
    this.onHeightChanged,
    this.loadingWidget,
    this.absorbing = true,
  }) : super(key: key);

  @override
  State<DynamicHeightWebView> createState() => _DynamicHeightWebViewState();
}

class _DynamicHeightWebViewState extends State<DynamicHeightWebView>
    with SingleTickerProviderStateMixin {
  late InAppWebViewController _controller;
  double _currentHeight = DynamicWebViewHeightManager._defaultMinHeight;
  bool _isLoading = true;
  Timer? _heightCalculationTimer;

  @override
  void initState() {
    super.initState();

    // Get smart height constraints based on content
    final constraints =
        DynamicWebViewHeightManager.getHeightConstraints(widget.htmlContent);
    final minHeight = constraints['minHeight'] ?? widget.minHeight;

    // Use estimated initial height for better UX
    _currentHeight = DynamicWebViewHeightManager.estimateInitialHeight(
      widget.htmlContent,
      zoomLevel: widget.zoomLevel,
    ).clamp(minHeight, widget.maxHeight);
  }

  @override
  void dispose() {
    _heightCalculationTimer?.cancel();
    super.dispose();
  }

  Future<void> _calculateAndUpdateHeight() async {
    if (!mounted) return;

    try {
      final newHeight =
          await DynamicWebViewHeightManager.calculateContentHeight(
        _controller,
        zoomLevel: widget.zoomLevel,
        minHeight: widget.minHeight,
        maxHeight: widget.maxHeight,
      );

      if (newHeight != null && mounted && newHeight != _currentHeight) {
        setState(() {
          _currentHeight = newHeight;
        });

        widget.onHeightChanged?.call(newHeight);
      } else if (newHeight == null && mounted) {
        // Fallback to estimated height if calculation fails
        final fallbackHeight =
            DynamicWebViewHeightManager.estimateInitialHeight(
          widget.htmlContent,
          zoomLevel: widget.zoomLevel,
        ).clamp(widget.minHeight, widget.maxHeight);

        if (fallbackHeight != _currentHeight) {
          setState(() {
            _currentHeight = fallbackHeight;
          });
          widget.onHeightChanged?.call(fallbackHeight);
        }
      }
    } catch (e) {
      debugPrint('Error calculating WebView height: $e');
      // Use fallback height on error
      final fallbackHeight = widget.minHeight + 30; // Small buffer
      if (mounted && fallbackHeight != _currentHeight) {
        setState(() {
          _currentHeight = fallbackHeight;
        });
        widget.onHeightChanged?.call(fallbackHeight);
      }
    }
  }

  void _scheduleHeightCalculation() {
    _heightCalculationTimer?.cancel();
    _heightCalculationTimer = Timer(const Duration(milliseconds: 200), () {
      _calculateAndUpdateHeight();
    });

    // Add a timeout fallback
    Timer(const Duration(seconds: 5), () {
      if (mounted && _isLoading) {
        debugPrint('WebView height calculation timeout, using fallback');
        setState(() {
          _isLoading = false;
          _currentHeight = DynamicWebViewHeightManager.estimateInitialHeight(
            widget.htmlContent,
            zoomLevel: widget.zoomLevel,
          ).clamp(widget.minHeight, widget.maxHeight);
        });
        widget.onHeightChanged?.call(_currentHeight);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.animationDuration,
      curve: Curves.easeInOut,
      height: _currentHeight,
      child: Stack(
        children: [
          if (widget.absorbing)
            AbsorbPointer(
              absorbing: true,
              child: _buildWebView(),
            )
          else
            _buildWebView(),
          if (_isLoading && widget.loadingWidget != null)
            Center(child: widget.loadingWidget!),
        ],
      ),
    );
  }

  Widget _buildWebView() {
    return InAppWebView(
      initialData: InAppWebViewInitialData(
        data: widget.htmlContent,
        mimeType: "text/html",
        encoding: "utf-8",
      ),
      initialSettings: widget.settings ??
          InAppWebViewSettings(
            javaScriptEnabled: true,
            supportZoom: false,
            transparentBackground: true,
            disableHorizontalScroll: true,
            disableVerticalScroll: true,
          ),
      onWebViewCreated: (controller) {
        _controller = controller;
        widget.onWebViewCreated?.call(controller);
      },
      onLoadStart: (controller, url) async {
        await controller.evaluateJavascript(
          source: DynamicWebViewHeightManager.getZoomAndPrepareScript(
              widget.zoomLevel),
        );
      },
      onLoadStop: (controller, url) async {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // Schedule height calculation after a short delay to ensure content is rendered
          _scheduleHeightCalculation();
        }
      },
      onConsoleMessage: (controller, consoleMessage) {
        debugPrint('WebView Console: ${consoleMessage.message}');
      },
      onReceivedError: (controller, request, error) {
        debugPrint(
            'WebView Load Error: ${error.description} (Code: ${error.type})');
        if (mounted) {
          setState(() {
            _isLoading = false;
            // Use minimum height on load error
            _currentHeight = widget.minHeight;
          });
        }
      },
      onReceivedHttpError: (controller, request, errorResponse) {
        debugPrint('WebView HTTP Error: ${errorResponse.statusCode}');
        if (mounted) {
          setState(() {
            _isLoading = false;
            _currentHeight = widget.minHeight;
          });
        }
      },
    );
  }
}
