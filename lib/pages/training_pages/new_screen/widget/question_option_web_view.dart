import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class QuestionOptionWebView extends StatefulWidget {
  final String html;
  final ValueChanged<List<String>> onValuesChanged;

  const QuestionOptionWebView({
    super.key,
    required this.html,
    required this.onValuesChanged,
  });

  @override
  State<QuestionOptionWebView> createState() => _QuestionOptionWebViewState();
}

class _QuestionOptionWebViewState extends State<QuestionOptionWebView>
    with AutomaticKeepAliveClientMixin {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    _controller = WebViewController()
      ..setBackgroundColor(const Color(0xFFFFFFFF))
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (url) {
            setState(() => _isLoading = false);
          },
        ),
      )
      ..addJavaScriptChannel(
        'FormChannel',
        onMessageReceived: (JavaScriptMessage message) {
          final values = List<String>.from(jsonDecode(message.message));
          widget.onValuesChanged(values);
        },
      )
      ..loadHtmlString(_injectScript(widget.html));
  }

  String _injectScript(String html) {
    return """
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <style>
    body {
      background-color: white;
      font-size: 16px;
      -webkit-text-size-adjust: 100%; /* Prevent iOS auto zoom */
      margin: 0;
      padding: 0;
    }
  </style>
</head>
<body>
  $html
  <script>
    function sendValues() {
      const values = [];
      document.querySelectorAll('input, select').forEach(el => {
        if (el.tagName.toLowerCase() === 'select') {
          values.push(el.options[el.selectedIndex].text);
        } else {
          values.push(el.value);
        }
      });
      FormChannel.postMessage(JSON.stringify(values));
    }

    // Attach listeners
    document.addEventListener('change', sendValues, true);
    document.addEventListener('input', sendValues, true);

    // fire once on load
    //window.onload = sendValues;
  </script>
</body>
</html>
""";
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SizedBox(
      height: 500,
      child: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
