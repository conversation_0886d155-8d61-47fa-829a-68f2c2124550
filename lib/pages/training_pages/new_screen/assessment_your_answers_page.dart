import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/utils/constant.dart';
import 'package:shimmer/shimmer.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';
import '../../../data/models/response/home_response/my_assessment_response.dart';
import '../../../data/models/response/home_response/test_review_response.dart';
import '../../../utils/styles.dart';
import '../../custom_pages/alert_widgets/alerts_widget.dart';
import '../../custom_pages/custom_widgets/next_page_routing.dart';
import '../../ghome/home_page.dart';
import 'package:masterg/pages/custom_pages/screen_with_loader.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class AssessmentYourAnswersPage extends StatefulWidget {
  final int? contentId;
  final bool isReview;
  final bool isOptionSelected;
  final Function? sendValue;
  final Map<int, bool> attemptList;
  final int? programId;
  final int? attemptAllowed;
  final bool? isEvent;
  final int? disableBackTracking;

  const AssessmentYourAnswersPage(
      {super.key,
      this.contentId,
      this.programId,
      required this.isReview,
      required this.isOptionSelected,
      required this.sendValue,
      required this.attemptList,
      this.attemptAllowed,
      this.isEvent,
      this.disableBackTracking});

  @override
  State<AssessmentYourAnswersPage> createState() =>
      _AssessmentYourAnswersPageState();
}

class _AssessmentYourAnswersPageState extends State<AssessmentYourAnswersPage> {
  List<AssessmentList>? assessmentList = [];
  var _isLoading = true;
  late HomeBloc _authBloc;
  final List<TestReviewBean> _list = [];
  // int? _currentQuestionId;
  // final bool _showSubmitDialog = false;
  String selectedOption = '';
  List selectedOptionList = [];

  @override
  void initState() {
    super.initState();
  }

  void _handleAttemptTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            _list.clear();
            selectedOptionList.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              _list.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );
            }

            if (_list.isNotEmpty) {
              // _currentQuestionId = _list.first.question!.questionId;
            }
          }
          setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  void _submitAnswers() {
    _authBloc.add(SubmitAnswerEvent(request: widget.contentId.toString()));
  }

  void _handleSubmitAnswerResponse(SubmitAnswerState state) {
    try {
      setState(() {
        switch (state.apiState) {
          case ApiStatus.LOADING:
            _isLoading = true;
            break;
          case ApiStatus.SUCCESS:
            _isLoading = false;
            setState(() {});

            AlertsWidget.alertWithOkBtn(
              context: context,
              onOkClick: () {
                if (widget.attemptAllowed == 1) {
                  Navigator.pop(context);
                  Navigator.pop(context);
                  Navigator.pop(context, 1);
                  /*Navigator.pushReplacement(context,
                      MaterialPageRoute(builder: (context) => CompetitionDetail(
                        competitionId: widget.programId,
                        isEvent: widget.isEvent,
                      )));*/
                } else {
                  Navigator.pop(context);
                  Navigator.pop(context);
                }
              },
              text: tr('app_assessment_submit_one'),
            );
            break;
          case ApiStatus.ERROR:
            _isLoading = false;
            setState(() {});
            widget.sendValue!(false);
            Navigator.pop(context);
            _isLoading = false;
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: context.appColors.background,
        appBar: AppBar(
          title: Text(
            'your_answer',
            style: Styles.getBoldThemeStyle(context, size: 18),
          ).tr(),
          centerTitle: true,
          backgroundColor: context.appColors.surface,
          elevation: 0.0,
          leading: widget.disableBackTracking == 1
              ? SizedBox()
              : IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: context.appColors.textWhite,
                  ),
                  onPressed: () {
                    //Navigator.pop(context);
                  },
                ),
        ),
        bottomNavigationBar: BottomAppBar(
          elevation: 0,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 30.0, top: 10.0, right: 30.0, bottom: 10.0),
            child: SizedBox(
              height: 120,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 23,
                        width: 23,
                        decoration: BoxDecoration(
                          color: context.appColors.primary,
                          borderRadius: BorderRadius.all(Radius.circular(100)),
                        ),
                      ),
                      Text(' ${tr('answers')}'),
                      SizedBox(
                        width: 20,
                      ),
                      Container(
                        height: 23,
                        width: 23,
                        decoration: BoxDecoration(
                          color: context.appColors.grey3,
                          borderRadius: BorderRadius.all(Radius.circular(100)),
                        ),
                      ),
                      Text(' ${tr('skipped')}'),
                    ],
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  InkWell(
                    onTap: () {
                      if (widget.isReview) {
                        Navigator.pushAndRemoveUntil(context,
                            NextPageRoute(HomePage()), (route) => false);
                      } else {
                        //TODO: Hide for go on previse page and not attempt change any and direct submit test 4 oct 2024
                        /*AlertsWidget.alertWithOkCancelBtn(
                        context: context,
                        onCancelClick: () {
                          Navigator.pop(context);
                        },
                        onOkClick: () {
                          _submitAnswers();
                        },
                        text: "${tr('app_assessment_submit_three')}",
                        title: "${tr('finish_test')}",
                      );*/

                        _submitAnswers();

                        // if (!widget.isOptionSelected) {
                        //   AlertsWidget.alertWithOkCancelBtn(
                        //     context: context,
                        //     onCancelClick: () {
                        //       Navigator.pop(context);
                        //     },
                        //     onOkClick: () {
                        //       _submitAnswers();
                        //     },
                        //     text: "${tr('app_assessment_submit_three')}",
                        //     title: "${tr('finish_test')}",
                        //   );
                        // } else {
                        //   AlertsWidget.alertWithOkBtn(
                        //     context: context,
                        //     onOkClick: () {
                        // _showSubmitDialog = true;
                        //       _submitAnswers();
                        //     },
                        //     text: "${tr('app_assessment_submit_three')}",
                        //   );
                        // }
                      }
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [
                          context.appColors.gradientLeft,
                          context.appColors.gradientRight,
                        ]),
                      ),
                      child: Center(
                          child: Text(
                        'submit_test',
                        style: Styles.textRegular(
                            size: 16,
                            color: context.appColors.primaryForeground),
                      ).tr()),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        body: ScreenWithLoader(
          isLoading: _isLoading,
          body: _mainBody(),
        ),
      ),
    );
  }

  BlocManager _mainBody() {
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
        initState: (context) async {
          await Future.delayed(Duration(seconds: 5));
          if (_list.isEmpty) {
            _authBloc.add(
              ReviewTestEvent(
                  request:
                      '${widget.contentId}?program_id=${widget.programId}'),
            );
          }
        },
        child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ReviewTestState) _handleAttemptTestResponse(state);
            if (state is SubmitAnswerState) _handleSubmitAnswerResponse(state);
          },
          child: _list.isNotEmpty
              ? Wrap(
                  children: [
                    for (var element in widget.attemptList.entries) ...[
                      //Text('element----${element.value}'),
                      Container(
                        width: height(context) * 0.1,
                        height: height(context) * 0.1,
                        margin: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: element.value == false
                                ? context.appColors.grey3
                                : context.appColors.primary,
                            borderRadius: BorderRadius.circular(8)),
                        child: Padding(
                          padding: EdgeInsets.all(10),
                          child: Center(
                            child: Text(
                              '${widget.attemptList.keys.toList().indexOf(element.key) + 1}',
                              style: Styles.textRegular(
                                  size: 16, color: context.appColors.textWhite),
                            ),
                          ),
                        ),
                      )
                    ],
                  ],
                )
              : _emptyBody(),
        ));
  }

  Widget _emptyBody() {
    return GridView.builder(
      padding: EdgeInsets.only(left: 10.0, right: 10.0, top: 30.0),
      gridDelegate:
          const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 5),
      itemCount: 10,
      itemBuilder: (BuildContext context, int index) {
        return Shimmer.fromColors(
          baseColor: context.appColors.shimmerBase,
          highlightColor: context.appColors.shimmerHighlight,
          child: Card(
            color: context.appColors.grey3,
          ),
        );
      },
    );
  }
}
