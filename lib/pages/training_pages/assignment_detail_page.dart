import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:masterg/utils/extensions/snackbar_extenstion.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/assignment_detail_response.dart';
import 'package:masterg/data/models/response/home_response/assignment_submissions_response.dart';
import 'package:masterg/data/providers/assignment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/announecment_pages/full_video_page.dart';
import 'package:masterg/pages/custom_pages/tap_widget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/training_pages/assignment_submissions.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/strings.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

import 'package:url_launcher/url_launcher.dart';

class AssignmentDetailPage extends StatefulWidget {
  final int? id;
  final bool fromCompetition;
  final String difficultyLevel;
  final bool fromJob;
  const AssignmentDetailPage(
      {super.key,
      required this.id,
      this.fromCompetition = false,
      this.fromJob = false,
      this.difficultyLevel = 'Easy'});
  @override
  State<AssignmentDetailPage> createState() => _AssignmentDetailPageState();
}

class _AssignmentDetailPageState extends State<AssignmentDetailPage> {
  File? file;
  final _userNotes = TextEditingController(text: "");
  AssignmentDetailProvider? assignmentDetailProvider;
  bool _isLoading = false;
  AssessmentDetails? data;
  List<SubmissionDetails>? _attempts = [];

  @override
  void initState() {
    _getData();
    _downloadListener();
    super.initState();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      // String id = data[0];
      // DownloadTaskStatus status = data[1];
      // int progress = data[2];
      setState(() {});
    });

    FlutterDownloader.registerCallback(downloadCallback);
  }

  static void downloadCallback(String id, int status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, DownloadTaskStatus.fromInt(status), progress]);
  }

  final ReceivePort _port = ReceivePort();

  Future<void> _downloadListener() async {
    await FlutterDownloader.initialize();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) async {
      String? id = data[0];
      DownloadTaskStatus? status = data[1];
      int? progress = data[2];
      if (status == DownloadTaskStatus.complete &&
          progress == 100 &&
          id != null) {
        String query = "SELECT * FROM task WHERE task_id='$id'";
        var tasks = await FlutterDownloader.loadTasksWithRawQuery(query: query);
        if (tasks != null) {
          Future.delayed(Duration(seconds: 2), () {
            FlutterDownloader.open(taskId: tasks.first.taskId);
          });
        }
      }
    });
    // FlutterDownloader.registerCallback(downloadCallback);
  }

  void download(String? usersFile) async {
    String localPath = "";
    if (Platform.isAndroid) {
      final path = (await getExternalStorageDirectories(
              type: StorageDirectory.downloads))!
          .first;

      localPath = path.path;

      final file = File("$localPath/${usersFile!.split('/').last}");
      if (file.existsSync()) {
        Utility.showSnackBar(
            scaffoldContext: context, message: "File already exists");

        await FlutterDownloader.open(taskId: usersFile.split('/').last);
        return;
      }
    } else {
      localPath = (await getApplicationDocumentsDirectory()).path;
    }

    var savedDir = Directory(localPath);
    bool hasExisted = await savedDir.exists();
    if (!hasExisted) {
      savedDir = await savedDir.create();
    }
    download2(usersFile!, localPath);

    // checkPermission();

    // if (await Permission.storage.request().isGranted ) {

    // } else {
    //   Utility.showSnackBar(
    //       scaffoldContext: context,
    //       message: "Please enable storage permission");
    // }
  }

  Future download2(String url, String savePath) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'downloading_start',
            style: Styles.boldWhite(),
          ).tr(),
          backgroundColor: context.appColors.textBlack,
          duration: Duration(seconds: 2),
        ),
      );
      final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        saveInPublicStorage: true,
        headers: {"auth": "test_for_sql_encoding"},
        openFileFromNotification: true,
      );
      Log.v(taskId);
    } catch (e) {
      Log.v(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    assignmentDetailProvider = Provider.of<AssignmentDetailProvider>(context);
    return Scaffold(
        backgroundColor: context.appColors.background,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          iconTheme: IconThemeData(
            color: context.appColors.grey1, //change your color here
          ),
          title: Text(
            'assignment',
            style: TextStyle(color: context.appColors.textBlack),
          ).tr(),
          backgroundColor: context.appColors.surface,
          elevation: 0,
        ),
        body: BlocManager(
            initState: (c) {},
            child: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) {
                if (state is AssignmentSubmissionsState) _handleResponse(state);
              },
              child: assignmentDetailProvider?.assignment != null
                  ? _buildBody()
                  : CustomProgressIndicator(true, context.appColors.surface),
            )));
  }

  Widget _buildBody() {
    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(
        assignmentDetailProvider!.assignment!.startDate! * 1000);
    DateTime endDate = DateTime.fromMillisecondsSinceEpoch(
        assignmentDetailProvider!.assignment!.endDate! * 1000);
    return SizedBox(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: ListView(children: [
            if (widget.fromCompetition) ...[
              Text(
                '${assignmentDetailProvider?.assignment?.title}',
                style: Styles.getBoldThemeStyle(context, size: 14),
              ),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${tr('submit_before')}: ',
                    style: Styles.regular(
                        size: 12, color: context.appColors.subHeadingTitle),
                  ),
                  Text(
                    assignmentDetailProvider!.assignment!.endDate != null
                        ? Utility.convertDateFromMillis(
                            assignmentDetailProvider!.assignment!.endDate!,
                            '${Strings.REQUIRED_DATE_DD_MMM_YYYY}, hh:mm a')
                        : '',
                    style: Styles.semibold(
                        size: 12, color: context.appColors.headingPrimaryColor),
                    textDirection: ui.TextDirection.ltr,
                  ),
                ],
              ),
              SizedBox(height: 8),
              if (widget.fromJob == false)
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                        assignmentDetailProvider?.assignment?.isGraded == 1
                            ? '${assignmentDetailProvider?.assignment?.maximumMarks} ${tr('marks')} '
                            : '${tr('non_graded')} ',
                        style: Styles.regular(
                            color: context.appColors.grey2, size: 14)),
                    Text('• ',
                        style: Styles.regular(
                            color: context.appColors.grey2, size: 14)),
                    Text('${tr('level')}: ',
                        style: Styles.regular(
                            color: context.appColors.grey2, size: 14)),
                    Text(
                      widget.difficultyLevel,
                      style: Styles.regular(
                          size: 14, color: context.appColors.green),
                    ),
                  ],
                ),
              SizedBox(height: 8),
              Text(
                '${assignmentDetailProvider?.assignment?.description}',
                style: Styles.regular(
                    size: 14, color: context.appColors.subHeadingTitle),
              ),
              Divider(),
              SizedBox(height: 8),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                ShaderMask(
                  blendMode: BlendMode.srcIn,
                  shaderCallback: (Rect bounds) {
                    return LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: <Color>[
                          context.appColors.gradientLeft,
                          context.appColors.gradientRight
                        ]).createShader(bounds);
                  },
                  child: Text('assignment_file',
                          style: Styles.getBoldThemeStyle(context))
                      .tr(),
                ),
                Row(
                  children: [
                    InkWell(
                      onTap: () async {
                        downloadAssignment(
                            fileUrl:
                                assignmentDetailProvider?.assignment!.file!);

                        // Utility.downloadFile(
                        //     assignmentDetailProvider?.assignment!.file!,
                        //     context);
                      },
                      child: ShaderMask(
                        blendMode: BlendMode.srcIn,
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight
                              ]).createShader(bounds);
                        },
                        child: SvgPicture.asset(
                          'assets/images/download_icon.svg',
                          height: 22,
                          width: 22,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                    ),
                    _size(width: 20),
                    InkWell(
                      onTap: () {
                        /*if (assignmentDetailProvider?.assignment!.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'zip' ||
                            assignmentDetailProvider?.assignment!.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'rar') {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('view_file_on_web').tr()));
                          return;
                        }
                        Navigator.push(
                            context,
                            NextPageRoute(FullContentPage(
                              contentType: "1",
                              resourcePath:
                                  assignmentDetailProvider?.assignment!.file!,
                            )));*/

                        if (assignmentDetailProvider?.assignment?.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'zip' ||
                            assignmentDetailProvider?.assignment?.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'rar') {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('view_file_on_web').tr()));
                          return;
                        }
                        Navigator.push(
                            context,
                            NextPageRoute(FullContentPage(
                              contentType: assignmentDetailProvider
                                  ?.assignments.contentType,
                              resourcePath:
                                  assignmentDetailProvider?.assignment?.file,
                            )));
                      },
                      child: ShaderMask(
                        blendMode: BlendMode.srcIn,
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight
                              ]).createShader(bounds);
                        },
                        child: SvgPicture.asset(
                          'assets/images/view_icon.svg',
                          height: 22,
                          width: 22,
                          allowDrawingOutsideViewBox: true,
                        ),
                      ),
                    )
                  ],
                ),
              ]),
              SizedBox(height: 10),
              _buildListBody(),
              Divider(),
              GestureDetector(
                onTap: () async {
                  if (currentIndiaTime!.isAfter(startDate) &&
                      currentIndiaTime!.isBefore(endDate)) {
                    bool disbaleUpload = false;

                    if (!disbaleUpload) {
                      if ((assignmentDetailProvider
                                  ?.assignment?.allowMultiple ==
                              0 &&
                          assignmentDetailProvider!.assignment?.totalAttempts !=
                              0)) {
                        AlertsWidget.showCustomDialog(
                            context: context,
                            title: tr('maximum_attempts_reached'),
                            text: "",
                            icon: 'assets/images/circle_alert_fill.svg',
                            showCancel: false,
                            oKText: tr('ok'),
                            onOkClick: () async {});
                      } else {
                        await _attachFile();
                        AlertsWidget.showCustomDialog(
                            context: context,
                            title: tr('upload_assignment'),
                            text: "",
                            icon: 'assets/images/circle_alert_fill.svg',
                            showCancel: true,
                            oKText: tr('upload'),
                            onOkClick: () async {
                              _submitAssignment();
                            });
                      }
                    }
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                        colors: currentIndiaTime!.isAfter(startDate) &&
                                currentIndiaTime!.isBefore(endDate)
                            ? [
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight,
                              ]
                            : [
                                context.appColors.grey3.withValues(alpha: 0.5),
                                context.appColors.grey3.withValues(alpha: 0.5),
                              ]),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                  margin:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'upload_assignment',
                        style: Styles.boldWhite(size: 14),
                      ).tr(),
                      SizedBox(width: 8),
                      Icon(Icons.file_upload_outlined,
                          color: context.appColors.textWhite)
                    ],
                  ),
                ),
              )
            ] else ...[
              _belowTitle(assignmentDetailProvider!),
              _body(assignmentDetailProvider!.assignment!),
              _buildListBody(),
            ]
          ]),
        ));
  }

  Widget _buildListBody() {
    return SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.52,
        child: _isLoading
            ? Center(
                child: CustomProgressIndicator(true, context.appColors.surface),
              )
            : _attempts!.isNotEmpty
                ? SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height * 0.1,
                    child: ListView.builder(
                        itemCount: _attempts?.length,
                        shrinkWrap: true,
                        itemBuilder: (BuildContext context, int currentIndex) =>
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.6,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                _attempts![currentIndex]
                                                    .file!
                                                    .split('/')
                                                    .last,
                                                overflow: TextOverflow.fade,
                                                maxLines: 2,
                                                softWrap: true,
                                                style:
                                                    Styles.getRegularThemeStyle(
                                                        context,
                                                        size: 14)),
                                            Text(
                                              _attempts![currentIndex]
                                                          .createdAt !=
                                                      null
                                                  ? Utility.convertDateFromMillis(
                                                      _attempts![currentIndex]
                                                          .createdAt!,
                                                      Strings
                                                          .REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS)
                                                  : '',
                                              style: Styles.regular(
                                                  size: 10,
                                                  color:
                                                      context.appColors.grey3),
                                              textDirection:
                                                  ui.TextDirection.ltr,
                                            )
                                          ],
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              InkWell(
                                                onTap: () async {
                                                  downloadAssignment(
                                                      fileUrl: _attempts![
                                                              currentIndex]
                                                          .file!);
                                                  // _downloadSubmission(
                                                  //     _attempts![currentIndex]
                                                  //         .file);

                                                  // Utility.downloadFile(
                                                  //     _attempts![currentIndex]
                                                  //         .file,
                                                  //     context);
                                                },
                                                child: ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          context.appColors
                                                              .gradientLeft,
                                                          context.appColors
                                                              .gradientRight
                                                        ]).createShader(bounds);
                                                  },
                                                  child: SvgPicture.asset(
                                                    'assets/images/download_icon.svg',
                                                    height: 25,
                                                    width: 25,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 20),
                                              InkWell(
                                                onTap: () {
                                                  if (_attempts![currentIndex]
                                                              .file!
                                                              .split('.')
                                                              .last
                                                              .toLowerCase() ==
                                                          'zip' ||
                                                      _attempts![currentIndex]
                                                              .file!
                                                              .split('.')
                                                              .last
                                                              .toLowerCase() ==
                                                          'rar') {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(SnackBar(
                                                            content: Text(
                                                                    'view_file_on_web')
                                                                .tr()));
                                                    return;
                                                  }
                                                  Navigator.push(
                                                      context,
                                                      NextPageRoute(
                                                          // PreviewSampleResume(
                                                          //     previewUrl: _attempts![
                                                          //             currentIndex]
                                                          //         .file,
                                                          //     title:
                                                          //         '${_attempts![currentIndex].title ?? ''}',
                                                          //     msg: tr(
                                                          //         'file_type_msg')),

                                                          FullContentPage(
                                                        contentType: _attempts![
                                                                currentIndex]
                                                            .extension,
                                                        resourcePath: _attempts![
                                                                currentIndex]
                                                            .file,
                                                      )));
                                                },
                                                child: ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          context.appColors
                                                              .gradientLeft,
                                                          context.appColors
                                                              .gradientRight
                                                        ]).createShader(bounds);
                                                  },
                                                  child: SvgPicture.asset(
                                                    'assets/images/view_icon.svg',
                                                    height: 22,
                                                    width: 22,
                                                    allowDrawingOutsideViewBox:
                                                        true,
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          if (!widget.fromJob)
                                            Row(
                                              children: [
                                                Container(
                                                  child: _attempts![
                                                                  currentIndex]
                                                              .reviewStatus ==
                                                          0
                                                      ? Text(
                                                          'under_review',
                                                        ).tr()
                                                      : Text(
                                                          data?.isGraded == 0
                                                              ? "${tr('non_graded')} "
                                                              : "${_attempts![currentIndex].marksObtained ?? 0}/${assignmentDetailProvider?.assignment?.maximumMarks}",
                                                          maxLines: 2,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          softWrap: true,
                                                          style: Styles.bold(
                                                              size: 12,
                                                              color: data?.isGraded ==
                                                                      0
                                                                  ? context
                                                                      .appColors
                                                                      .textBlack
                                                                  : context
                                                                      .appColors
                                                                      .green),
                                                        ),
                                                ),
                                                SizedBox(width: 6),
                                                SvgPicture.asset(
                                                  'assets/images/info.svg',
                                                  height: 14,
                                                  width: 14,
                                                  allowDrawingOutsideViewBox:
                                                      true,
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  if (_attempts![currentIndex].reviewStatus !=
                                          0 &&
                                      (_attempts![currentIndex].teacherFile !=
                                              null &&
                                          _attempts![currentIndex]
                                                  .teacherFile !=
                                              ''))
                                    if (widget.fromCompetition == true)
                                      InkWell(
                                          onTap: () {
                                            if (_attempts![currentIndex]
                                                        .teacherFile!
                                                        .split('.')
                                                        .last
                                                        .toLowerCase() ==
                                                    'zip' ||
                                                _attempts![currentIndex]
                                                        .teacherFile!
                                                        .split('.')
                                                        .last
                                                        .toLowerCase() ==
                                                    'rar') {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                      content: Text(
                                                              'view_file_on_web')
                                                          .tr()));
                                              return;
                                            }
                                            Navigator.push(
                                                context,
                                                NextPageRoute(FullContentPage(
                                                  contentType: isImage(_attempts![
                                                                  currentIndex]
                                                              .teacherFile!) ==
                                                          true
                                                      ? ""
                                                      : _attempts![currentIndex]
                                                          .extension,
                                                  resourcePath:
                                                      _attempts![currentIndex]
                                                          .teacherFile,
                                                )));
                                          },
                                          child: Text(
                                            'reviewed_file',
                                            style: Styles.regular(
                                                size: 14,
                                                color: context
                                                    .appColors.primaryBlue),
                                          ).tr()),
                                  if (_attempts![currentIndex].teacherNotes !=
                                          null &&
                                      widget.fromJob)
                                    SizedBox(
                                      height: 4,
                                    ),
                                  if (_attempts![currentIndex].teacherNotes !=
                                      null)
                                    Text(
                                      '${_attempts![currentIndex].teacherNotes}',
                                      style: Styles.textItalic(
                                          size: 14,
                                          color: context
                                              .appColors.subHeadingTitle),
                                    ),
                                ],
                              ),
                            )),
                  )
                : Center(
                    child: Text(
                      'no_assignment_submitted',
                      style: Styles.textBold(),
                    ).tr(),
                  ));
  }

  Future<void> downloadAssignment({String? fileUrl}) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      // final status =
      await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File("$localPath/${fileUrl!.split('/').last}");
      if (!file.existsSync()) {
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        // final id =
        await FlutterDownloader.enqueue(
          url: fileUrl,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
          saveInPublicStorage: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${fileUrl.split('/').last}");
        });
      } else {
        Utility.showSnackBar(scaffoldContext: context, message: 'file exists');
        OpenFilex.open("$localPath/${fileUrl.split('/').last}");
      }
    } else {
      launchUrl(Uri.parse(fileUrl!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }

  Padding _belowTitle(AssignmentDetailProvider assignmentDetailProvider) {
    return Padding(
      padding: const EdgeInsets.only(left: 18, right: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(assignmentDetailProvider.assignment?.title ?? '',
              style: Styles.bold(
                  color: context.appColors.gradientRight, size: 16)),
          _size(height: 5),
          assignmentDetailProvider.assignment!.submissionDate != null
              ? Row(
                  children: [
                    Text(
                      '${tr('submission_date')}:',
                      style: Styles.bold(
                          size: 12, color: context.appColors.textBlack),
                    ),
                    Text(
                      ' ${assignmentDetailProvider.assignment!.submissionDate != null ? Utility.convertDateFromMillis(assignmentDetailProvider.assignment!.submissionDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                      style:
                          Styles.bold(size: 12, color: context.appColors.grey3),
                      textDirection: ui.TextDirection.ltr,
                    ),
                  ],
                )
              : SizedBox(),
          _size(height: 3),
          Row(
            children: [
              Text(
                '${tr('end_date')}:',
                style:
                    Styles.bold(size: 12, color: context.appColors.textBlack),
              ),
              Text(
                ' ${assignmentDetailProvider.assignment!.endDate != null ? Utility.convertDateFromMillis(assignmentDetailProvider.assignment!.endDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                style: Styles.bold(size: 12, color: context.appColors.grey3),
                textDirection: ui.TextDirection.ltr,
              ),
            ],
          ),
          _size(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  data?.isGraded == 0
                      ? Text(
                          "${tr('non_graded')} ",
                          style: Styles.bold(
                              size: 14, color: context.appColors.textBlack),
                        )
                      : Text(
                          '${assignmentDetailProvider.assignment?.maximumMarks ?? ''} ${tr('marks')}',
                          style: Styles.bold(
                              size: 14, color: context.appColors.textBlack),
                        ),
                  Text(
                    assignmentDetailProvider.assignment?.allowMultiple != 0
                        ? ' • ${tr('multiple_attempt')}'
                        : ' • 1 ${tr('attempt')}',
                    style: Styles.bold(
                        size: 14, color: context.appColors.textBlack),
                  ),
                ],
              ),
              _size(height: 6),
              Text('${assignmentDetailProvider.assignment?.description ?? ''} ',
                  style: Styles.getRegularThemeStyle(context, size: 14)),
              _size(height: 30),
              Divider(),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Text(tr('assignment_file'),
                    style: Styles.bold(
                        color: context.appColors.gradientRight, size: 16)),
                Row(
                  children: [
                    InkWell(
                      onTap: () async {
                        // Utility.downloadFile(
                        //     assignmentDetailProvider.assignment!.file!,
                        //     context);
                        downloadAssignment(
                            fileUrl:
                                assignmentDetailProvider.assignment!.file!);
                      },
                      child: SvgPicture.asset(
                        'assets/images/download_icon.svg',
                        color: context.appColors.gradientRight,
                        height: 22,
                        width: 22,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
                    _size(width: 20),
                    InkWell(
                      onTap: () {
                        if (assignmentDetailProvider.assignment!.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'zip' ||
                            assignmentDetailProvider.assignment!.file!
                                    .split('.')
                                    .last
                                    .toLowerCase() ==
                                'rar') {
                          ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('view_file_on_web').tr()));
                          return;
                        }
                        Navigator.push(
                            context,
                            NextPageRoute(FullContentPage(
                              contentType: "1",
                              resourcePath:
                                  assignmentDetailProvider.assignment!.file!,
                            )));
                      },
                      child: SvgPicture.asset(
                        'assets/images/view_icon.svg',
                        color: context.appColors.gradientRight,
                        height: 22,
                        width: 22,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ),
                  ],
                )
              ]),
              Divider(),
            ],
          )
        ],
      ),
    );
  }

  SizedBox _size({double height = 20, double width = 0}) {
    return SizedBox(
      height: height,
      width: width,
    );
  }

  Container _body(Assignment assignment) {
    bool disbaleUpload = false;

    return Container(
      decoration: BoxDecoration(
          color: context.appColors.surface,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(25), topLeft: Radius.circular(25))),
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _size(height: 20),
                Text(
                  tr('user_note'),
                  style: Styles.textExtraBold(
                      size: 18, color: context.appColors.gradientRight),
                ),
                _size(height: 5),
                TextFormField(
                  maxLines: 3,
                  controller: _userNotes,
                  style: Styles.getTextBoldThemeStyle(context, size: 16),
                  keyboardType: TextInputType.text,
                  decoration: InputDecoration(
                    fillColor: Color(0x00ffffff),
                    hintStyle: TextStyle(
                      color: context.appColors.greyOutline,
                      fontSize: 16,
                    ),
                    filled: false,
                    labelStyle: TextStyle(
                      color: context.appColors.textDarkBlack,
                      fontSize: 16,
                    ),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: context.appColors.greyOutline,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                        bottomLeft: Radius.circular(4),
                        bottomRight: Radius.circular(4),
                      ),
                    ),
                    focusColor: context.appColors.greyOutline,
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: context.appColors.greyOutline,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                  ).copyWith(hintText: tr('write_something')),
                  validator: (value) =>
                      value!.isEmpty ? tr('enter_something') : null,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {},
                  onFieldSubmitted: (val) {},
                ),
              ],
            ),
            _size(),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    file == null
                        ? TapWidget(
                            onTap: () async {
                              DateTime now = currentIndiaTime!;
                              switch (Utility.classStatus(
                                  assignmentDetailProvider!
                                      .assignment!.startDate!,
                                  assignmentDetailProvider!
                                      .assignment!.endDate!,
                                  now)) {
                                case 1:
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title:
                                          tr('assignment_not_ready_submission'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      oKText: tr('ok'),
                                      onOkClick: () async {});
                                  return;

                                case 2:
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: tr('due_data_passed'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      oKText: tr('ok'),
                                      onOkClick: () async {});
                                  return;
                              }

                              if (!disbaleUpload) {
                                if (assignmentDetailProvider
                                            ?.assignment?.allowMultiple ==
                                        0 &&
                                    assignmentDetailProvider
                                            ?.assignment?.totalAttempts !=
                                        0) {
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: tr('maximum_attempts_reached'),
                                      text: "",
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      showCancel: false,
                                      onOkClick: () async {});
                                } else {
                                  _attachFile();
                                }
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(5),
                              width: MediaQuery.of(context).size.width * 0.65,
                              decoration: BoxDecoration(
                                  // gradient: LinearGradient(colors: [
                                  //   context.appColors.gradientLeft,
                                  //   context.appColors.gradientRight,
                                  // ]),
                                  color: context.appColors.gradientRight,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5))),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 8, right: 8, top: 4, bottom: 4),
                                child: assignmentDetailProvider!.isLoading
                                    ? Center(child: CircularProgressIndicator())
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'attach_file',
                                            style: Styles.textExtraBold(
                                                size: 14,
                                                color: context.appColors
                                                    .primaryForeground),
                                          ).tr(),
                                          _size(width: 10),
                                          Icon(
                                            Icons.attach_file,
                                            color: context
                                                .appColors.primaryForeground,
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                          )
                        : Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: MediaQuery.of(context).size.width * 0.7,
                                child: Text(
                                  file!.path.split("/").last,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  softWrap: true,
                                  style: TextStyle(
                                      decoration: TextDecoration.underline),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    file = null;
                                  });
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Icon(
                                    Icons.delete,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                    _size(height: 10),
                    if (file == null)
                      Row(
                        children: [
                          SizedBox(
                            width: 200,
                            child: Text(
                              '${tr('supported_format')} : pdf, doc, docx, xls, xlsx, csv, jpg, jpeg, png, gif, mp3, mp4, avi, zip, rar',
                              style: Styles.regular(
                                  size: 12, color: context.appColors.grey1),
                              maxLines: 2,
                            ),
                          ),
                        ],
                      ),
                    _size(),
                    TapWidget(
                      onTap: () {
                        if (!disbaleUpload) _submitAssignment();
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.65,
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            color: context.appColors.gradientRight,
                            borderRadius: BorderRadius.all(Radius.circular(5))),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8, right: 8, top: 4, bottom: 4),
                          child: assignmentDetailProvider!.isLoading
                              ? Center(child: CircularProgressIndicator())
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      tr('upload_assignment'),
                                      style: Styles.textExtraBold(
                                          size: 14,
                                          color: context
                                              .appColors.primaryForeground),
                                    ),
                                    _size(width: 10),
                                    Icon(Icons.file_upload_outlined,
                                        color:
                                            context.appColors.primaryForeground)
                                  ],
                                ),
                        ),
                      ),
                    ),
                    _size(height: 15),
                    if (assignmentDetailProvider?.assignment?.totalAttempts !=
                        0)
                      Row(
                        children: [
                          Text(
                            '${assignmentDetailProvider?.assignment?.totalAttempts} ',
                            style: Styles.regular(
                                size: 14, color: context.appColors.error),
                          ),
                          assignmentDetailProvider?.assignment?.totalAttempts ==
                                      0 ||
                                  assignmentDetailProvider
                                          ?.assignment?.totalAttempts ==
                                      1
                              ? Text(
                                  'attempt',
                                  style: Styles.regular(
                                      size: 14, color: context.appColors.error),
                                ).tr()
                              : Text(
                                  'attempts',
                                  style: Styles.regular(
                                      size: 14, color: context.appColors.error),
                                ).tr(),
                          Text(
                            ' ${tr('taken')}',
                            style: Styles.regular(
                                size: 14, color: context.appColors.error),
                          ),
                        ],
                      ),
                    _size(height: 15),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TapWidget(
                  onTap: () {
                    Navigator.push(
                        context,
                        NextPageRoute(
                            ReviewSubmissions(
                              maxMarks: assignmentDetailProvider
                                  ?.assignment?.maximumMarks,
                              contentId: widget.id,
                            ),
                            isMaintainState: true));
                  },
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: context.appColors.gradientRight)),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'view_submission',
                          style: Styles.textRegular(
                              color: context.appColors.gradientRight),
                        ).tr(),
                      ),
                    ),
                  ),
                )
              ],
            ),
            _size(height: 50)
          ],
        ),
      ),
    );
  }

  void _handleResponse(AssignmentSubmissionsState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("UserProfileState....................");
          data = state.response!.data!.assessmentDetails!.first;
          _attempts =
              state.response!.data!.assessmentDetails!.first.submissionDetails;

          _isLoading = false;
          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'assignment_detail', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> _attachFile() async {
    FilePickerResult? result;
    if (Platform.isIOS) {
      if (await Permission.storage.request().isGranted) {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.any,
        );
      }
    } else {
      result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.any,
      );
    }
    if (result != null) {
      var fileExtension = result.files.first.name.split('.').last.toLowerCase();
      // var fileExtension = extension(result.files.first.name!).toLowerCase();
      var allowedExtensions = [
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'csv',
        'jpg',
        'jpeg',
        'png',
        'gif',
        'mp3',
        'mp4',
        'avi',
        'zip',
        'rar'
      ];

      if (allowedExtensions.contains(fileExtension)) {
        setState(() {
          file = File(result!.files.first.path!);
        });
      } else {
        // File type not allowed
        // Handle accordingly (e.g., show error message)
        AlertsWidget.showCustomDialog(
            context: context,
            title: "file_not_supported".tr(),
            text: "",
            icon: 'assets/images/circle_alert_fill.svg',
            showCancel: false,
            oKText: tr('ok'),
            onOkClick: () async {});
      }
    }
  }

  void _getData() {
    BlocProvider.of<HomeBloc>(context)
        .add(AssignmentSubmissionsEvent(request: widget.id));
  }

  void _submitAssignment() async {
    if (file != null) {
      bool res = await assignmentDetailProvider!.uploadAssignment(
          notes: _userNotes.text, path: file!.path, id: widget.id);
      if (res) {
        _getData();
        if (mounted) {
          "assignment_submitted".tr().showSnackbar(context);
          // Using the extension method to show snackbar
          // This will use the SnackbarExtension defined in snackbar_extenstion.dart
        }
        // ScaffoldMessenger.of(context)
        //     .showSnackBar(SnackBar(content: Text('assignment_submitted').tr()));
        setState(() {
          file = null;
        });
        _userNotes.clear();
      } else {
        if (mounted) {
          "something_went_wrong".tr().showSnackbar(context);
          // Using the extension method to show snackbar
          //
        }
        // ScaffoldMessenger.of(context)
        //     .showSnackBar(SnackBar(content: Text('something_went_wrong').tr()));
      }
    } else {
      "attach_file"
          .tr()
          .showSnackbar(context); // Using the extension method to show snackbar
      // ScaffoldMessenger.of(context)
      //     .showSnackBar(SnackBar(content: Text('attach_file').tr()));
    }
  }
}

bool isImage(String url) {
  return url.endsWith('.png') ||
      url.endsWith('.jpg') ||
      url.endsWith('.jpeg') ||
      url.endsWith('.gif');
}
