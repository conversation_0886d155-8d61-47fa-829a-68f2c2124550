import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui;

import '../../../../utils/strings.dart';
import '../../../../utils/utility.dart';

class SessionView extends StatelessWidget {
  final Sessions sessions;
  const SessionView({super.key, required this.sessions});

  @override
  Widget build(BuildContext context) {
    String title = '';
    bool isButtonActive = true;
    if (sessions.liveclassAction.toString().toLowerCase() == 'concluded' ||
        sessions.liveclassStatus.toString().toLowerCase() == 'recorded') {
      title = tr('concluded');
      isButtonActive = false;
    }

    if (sessions.liveclassAction.toString().toLowerCase() == 'live' ||
        sessions.liveclassAction.toString().toLowerCase() == 'join class') {
      if (Utility.classStatus(
              sessions.startDate!, sessions.endDate!, currentIndiaTime!) ==
          2) {
        isButtonActive = false;
      } else {
        isButtonActive = true;
      }

      title = tr('join_now');
    }
    if (sessions.contentType.toString().toLowerCase() == 'offlineclass') {
      title = tr('in_progress');
      isButtonActive = false;
    }
    if (sessions.liveclassAction.toString().toLowerCase() == 'scheduled') {
      title = tr('scheduled');
      isButtonActive = false;
    }

    if (sessions.liveclassAction.toString().toLowerCase() == 'concluded') {
      title = tr('your_class_finished');
      isButtonActive = false;
    }

    Color? bgColor =
        !isButtonActive ? context.appColors.grey2 : context.appColors.primary;

    return Container(
        color: context.appColors.courseBg,
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.24,
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${sessions.startDate != null ? Utility.convertDateFromMillis(sessions.startDate!, Strings.CLASS_TIME_FORMAT) : ''} - ${sessions.endDate != null ? Utility.convertDateFromMillis(sessions.endDate!, Strings.CLASS_TIME_FORMAT) : ''} | ${sessions.startDate != null ? Utility.convertDateFromMillis(sessions.startDate!, Strings.DATE_MONTH) : ''}',
                  style: Styles.bold(
                      color: context.appColors.primaryForeground, size: 14),
                  textDirection: ui.TextDirection.ltr,
                ),
                Container(
                  height: 20,
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                      color: context.appColors.primaryForeground,
                      borderRadius: BorderRadius.circular(4)),
                  child: Center(
                      child: Text(
                    sessions.contentType == 'otherclass'
                        ? 'weblink'
                        : sessions.contentType == 'teamsclass'
                            ? 'teams'
                            : sessions.contentType == 'liveclass' ||
                                    sessions.contentType == 'zoomclass'
                                ? 'live'
                                : 'classroom',
                    style: TextStyle(color: context.appColors.darkBackground),
                  ).tr()),
                )
              ],
            ),
            SizedBox(height: 10),
            SizedBox(height: 5),
            Text(
              Utility().decrypted128('${sessions.trainerName}'),
              style: Styles.bold(
                size: 14,
                color: context.appColors.primaryForeground,
              ),
            ),
            SizedBox(height: 10),
            Text(
              '${sessions.title}',
              style: Styles.regular(
                size: 18,
                color: context.appColors.primaryForeground,
              ),
            ),
            Spacer(),
            InkWell(
              onTap: () {
                if (!isButtonActive) return;
                handleClick(context);
              },
              child: Container(
                  width: width(context),
                  height: 38,
                  margin:
                      const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                  decoration: BoxDecoration(
                      color: bgColor,
                      gradient: bgColor == context.appColors.primary
                          ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  context.appColors.gradientLeft,
                                  context.appColors.gradientRight
                                ])
                          : null,
                      borderRadius: BorderRadius.circular(8)),
                  child: Center(
                    child: Text(
                      title,
                      style: Styles.regular(
                          size: 14,
                          color: bgColor == context.appColors.grey2
                              ? context.appColors.grey3
                              : context.appColors.primaryForeground),
                      textAlign: TextAlign.center,
                    ),
                  )),
            )
          ],
        ));
  }

  void handleClick(BuildContext context) {
    String? currentZoomUrl;

    if (sessions.contentType == 'zoomclass' ||
        sessions.contentType == 'teamsclass') {
      currentZoomUrl = sessions.zoomUrl;

      if (currentZoomUrl != null) {
        BlocProvider.of<HomeBloc>(context).add(ZoomOpenUrlEvent(
          contentId: sessions.programContentId,
        ));
        launchUrl(Uri.parse(currentZoomUrl),
            mode: LaunchMode.externalApplication);
      } else {
        BlocProvider.of<HomeBloc>(context)
            .add(ZoomOpenUrlEvent(contentId: sessions.programContentId));
      }
    } else {
      launchUrl(Uri.parse('${sessions.liveclassUrl}'));
    }
  }
}
