import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:smooth_video_progress/smooth_video_progress.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class LearningShortVideoPlayer extends StatefulWidget {
  final LearningShots learningShorts;

  const LearningShortVideoPlayer({super.key, required this.learningShorts});

  @override
  State<LearningShortVideoPlayer> createState() =>
      _LearningShortVideoPlayerState();
}

class _LearningShortVideoPlayerState extends State<LearningShortVideoPlayer> {
  VideoPlayerController? _controller;
  YoutubePlayerController? ytController;
  int currentMin = 0;
  int prevMin = 0;
  String? youtubeVideoId;
  bool preparing = true;

  bool showControls = true;

  bool webViewPage = false;

  @override
  void initState() {
    convertToVertical();
    _initializeController();

    super.initState();
  }

  Future<void> _initializeController() async {
    youtubeVideoId =
        YoutubePlayer.convertUrlToId('${widget.learningShorts.url}');
    if (youtubeVideoId == null) {
      if (!Utility.isVideoUrl('${widget.learningShorts.url}')) {
        setState(() {
          preparing = true;
        });
        await Future.delayed(Duration(milliseconds: 200));
        setState(() {
          webViewPage = true;
          preparing = false;
        });
      } else {
        setState(() {
          preparing = true;
        });
        await Future.delayed(Duration(seconds: 1));

        Timer(Duration(seconds: 3), () {
          setState(() {
            showControls = false;
          });
        });
        //handle if not youtube video

        _controller = VideoPlayerController.networkUrl(
            Uri.parse('${widget.learningShorts.url}'));
        _controller?.initialize().then((_) {
          preparing = false;
          setState(() {});
          _controller?.play();
        });
      }
    } else {
      setState(() {
        preparing = true;
        ytController = null;
      });

      await Future.delayed(Duration(seconds: 0));
      ytController = YoutubePlayerController(
        initialVideoId: youtubeVideoId!,
        flags: YoutubePlayerFlags(
          autoPlay: true,
          mute: false,
        ),
      );
      setState(() {
        preparing = false;
      });
    }
  }

  @override
  void didUpdateWidget(covariant LearningShortVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.learningShorts.url != oldWidget.learningShorts.url) {
      _controller?.dispose();
      _initializeController();
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    convertToVertical();
    super.dispose();
  }

  void convertToVertical() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.bottom]);
  }

  @override
  Widget build(BuildContext context) {
    return preparing
        ? SizedBox(
            height: height(context) * 0.26,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        : youtubeVideoId != null
            ? VisibilityDetector(
                key: Key("ytVideoPlayer"),
                onVisibilityChanged: (VisibilityInfo info) {
                  if (info.visibleFraction == 1.0) {
                    currentMin = 0;
                    prevMin = 0;
                    listenYtVideoChanges(ytController);
                  }
                },
                child: Transform.scale(
                  scale: ytController?.value.isFullScreen == true ? 0.9 : 1,
                  child: YoutubePlayer(
                    controller: ytController!,
                    showVideoProgressIndicator: true,
                  ),
                ),
              )
            : _controller != null && _controller?.value.isInitialized == true
                ? urlVideoPlayer(isFullScreen: false)
                : webViewPage == true
                    ? SizedBox(
                        height: height(context) * 0.29,
                        child: InAppWebView(
                            onLoadStart: ((controller, url) {}),
                            onLoadStop: ((controller, url) {
                              controller.evaluateJavascript(
                                  source:
                                      "document.getElementsByClassName('sp-Stream-command-bar')[0].style.display = 'none';document.getElementsByClassName('sp-Stream-suite-nav')[0].style.display = 'none';document.getElementsByClassName('swa-below-video-container')[0].style.display = 'none';");
                              Future.delayed(const Duration(seconds: 1))
                                  .then((value) => setState(() {
                                        // webLoading = false;
                                      }));
                            }),
                            initialSettings: InAppWebViewSettings(
                              javaScriptEnabled: true,
                              mediaPlaybackRequiresUserGesture: true,
                              useShouldOverrideUrlLoading: true,
                              allowsInlineMediaPlayback: true,
                              allowsLinkPreview: true,
                            ),
                            initialUrlRequest: URLRequest(
                                url: WebUri.uri(Uri.parse(
                                    '${widget.learningShorts.url}')))),
                      )
                    : SizedBox(
                        height: height(context) * 0.26,
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
  }

  Widget urlVideoPlayer(
      {required bool isFullScreen,
      Function? onClose,
      Function? setStateLocal}) {
    // if (setStateLocal != null) {
    //   setStateLocal();
    // }
    return SizedBox(
        width: width(context),
        height: isFullScreen
            ? width(context) / _controller!.value.aspectRatio
            : kIsWeb && width(context) > 500
                ? height(context) * 0.5
                // : orientation == Orientation.landscape
                // ? height(context)
                : math.min(height(context) * 0.3,
                    width(context) / _controller!.value.aspectRatio),
        child: AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VisibilityDetector(
              key: Key("urlVideoPlayer"),
              onVisibilityChanged: (VisibilityInfo info) {
                if (info.visibleFraction == 1.0) {
                  currentMin = 0;
                  prevMin = 0;
                  listenVideoChanges(_controller);
                }
              },
              child: Stack(
                children: [
                  InkWell(
                      onTap: () {
                        setState(() {
                          showControls = !showControls;
                        });
                        //setStateLocal!();
                      },
                      child: VideoPlayer(_controller!)),
                  if (isFullScreen)
                    Positioned(
                      top: 6,
                      right: Utility().isRTL(context) ? null : 6,
                      left: Utility().isRTL(context) ? 6 : null,
                      child: GestureDetector(
                          onTap: () {
                            onClose!();
                          },
                          child: Icon(
                            Icons.close,
                            color: context.appColors.primaryForeground,
                          )),
                    ),
                  if (showControls) ...[
                    Positioned(
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                  onPressed: () {
                                    _controller?.pause().then((value) {
                                      _controller
                                          ?.seekTo(Duration(
                                              seconds: _controller!.value
                                                      .position.inSeconds -
                                                  10))
                                          .then((value) {
                                        setState(() {
                                          _controller?.play();
                                        });
                                      });
                                    });
                                  },
                                  icon: Icon(
                                    Icons.replay_10,
                                    color: context.appColors.primaryForeground,
                                    size: 32,
                                  )),
                              SizedBox(width: 50),
                              IconButton(
                                  onPressed: () {
                                    setState(() {
                                      if (_controller!.value.isPlaying) {
                                        _controller?.pause();
                                      } else {
                                        _controller?.play();
                                      }
                                    });
                                    setStateLocal!();
                                  },
                                  icon: Icon(
                                    _controller!.value.isPlaying
                                        ? Icons.pause_sharp
                                        : Icons.play_arrow_rounded,
                                    color: context.appColors.primaryForeground,
                                    size: 32,
                                  )),
                              SizedBox(width: 50),
                              IconButton(
                                  onPressed: () {
                                    _controller?.pause().then((value) {
                                      _controller
                                          ?.seekTo(Duration(
                                              seconds: _controller!.value
                                                      .position.inSeconds +
                                                  10))
                                          .then((value) {
                                        setState(() {
                                          _controller?.play();
                                        });
                                      });
                                    });
                                  },
                                  icon: Icon(
                                    Icons.forward_10_outlined,
                                    color: context.appColors.primaryForeground,
                                    size: 32,
                                  ))
                            ],
                          ),
                        )),
                    Positioned(
                        bottom: 0,
                        left: 6,
                        right: 6,
                        child: ValueListenableBuilder(
                          valueListenable: _controller!,
                          builder: (context, VideoPlayerValue value, child) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Transform.translate(
                                  offset: Offset(0, 12),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '${value.position.toString().substring(0, 7)}/${value.duration.toString().substring(0, 7)}',
                                          style: Styles.regular(
                                              color: context
                                                  .appColors.primaryForeground),
                                        ),
                                        if (!isFullScreen)
                                          GestureDetector(
                                              onTap: () {
                                                SystemChrome
                                                    .setPreferredOrientations([
                                                  DeviceOrientation
                                                      .landscapeRight,
                                                  DeviceOrientation
                                                      .landscapeLeft,
                                                ]).then((value) {
                                                  //hide top bar
                                                  SystemChrome
                                                      .setEnabledSystemUIMode(
                                                          SystemUiMode.manual,
                                                          overlays: [
                                                        SystemUiOverlay.bottom
                                                      ]);
                                                  showDialog(
                                                    useSafeArea: false,
                                                    context: context,
                                                    builder:
                                                        (BuildContext context) {
                                                      return StatefulBuilder(
                                                        builder: (BuildContext
                                                                context,
                                                            StateSetter
                                                                setState) {
                                                          Future.delayed(Duration(
                                                                  milliseconds:
                                                                      10))
                                                              .then((value) {
                                                            setState(() {});
                                                          });
                                                          return Scaffold(
                                                            body:
                                                                urlVideoPlayer(
                                                                    isFullScreen:
                                                                        true,
                                                                    onClose:
                                                                        () {
                                                                      Navigator.pop(
                                                                          context);
                                                                    },
                                                                    setStateLocal:
                                                                        () {
                                                                      setState(
                                                                          () {});
                                                                    }),
                                                          );
                                                        },
                                                      );
                                                    },
                                                  ).then((value) {
                                                    convertToVertical();
                                                  });
                                                });
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/full_screen_video.svg',
                                                color: context.appColors
                                                    .primaryForeground,
                                                height: 26,
                                                width: 26,
                                                allowDrawingOutsideViewBox:
                                                    true,
                                              ))
                                      ],
                                    ),
                                  ),
                                ),
                                videoSeekBar(),
                              ],
                            );
                          },
                        )),
                  ]
                ],
              )),
        ));
  }

  Widget videoSeekBar() {
    return Transform.rotate(
      angle: Utility().isRTL(context) ? -math.pi : 0,
      child: SmoothVideoProgress(
        controller: _controller!,
        builder: (context, position, duration, child) => SliderTheme(
          data: SliderThemeData(
              trackShape: CustomTrackShape(11),
              thumbColor: context.appColors.primaryForeground,
              overlayColor: context.appColors.primaryForeground,
              activeTrackColor: context.appColors.primaryForeground,
              inactiveTrackColor:
                  context.appColors.primaryForeground.withValues(alpha: 0.4)),
          child: Slider(
            onChangeStart: (_) => _controller?.pause(),
            onChangeEnd: (_) {
              _controller?.play().then((value) {
                setState(() {});
              });
            },
            onChanged: (value) =>
                _controller?.seekTo(Duration(milliseconds: value.toInt())),
            value: position.inMilliseconds.toDouble(),
            min: 0,
            max: duration.inMilliseconds.toDouble(),
          ),
        ),
      ),
    );
  }

  void listenVideoChanges(controller) {
    controller?.addListener(() {
      currentMin = controller.value.position.inMinutes;
      if (currentMin != 0 && prevMin != currentMin) {
        prevMin = currentMin;
        _updateCourseCompletion(currentMin, 0);
      }
    });
  }

  void listenYtVideoChanges(YoutubePlayerController? controller) {
    controller?.addListener(() {
      currentMin = controller.value.position.inMinutes;
      if (currentMin != 0 && prevMin != currentMin) {
        prevMin = currentMin;
        _updateCourseCompletion(currentMin, 0);
      }
    });
  }

  void _updateCourseCompletion(bookmark, int completionPer) async {
    //change bookmark with 25
    BlocProvider.of<HomeBloc>(context).add(UpdateVideoCompletionEvent(
        bookmark: bookmark,
        contentId: widget.learningShorts.programContentId,
        completionPercent: completionPer));
    setState(() {});
  }
}

class CustomTrackShape extends RoundedRectSliderTrackShape {
  final movefromleft;

  CustomTrackShape(this.movefromleft);

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final trackHeight = sliderTheme.trackHeight;
    final trackLeft = offset.dx;
    final trackTop = offset.dy + (parentBox.size.height - trackHeight!) / 2;
    final trackWidth = parentBox.size.width * 0.95;
    return Rect.fromLTWH(
        trackLeft + movefromleft, trackTop, trackWidth, trackHeight);
  }
}
