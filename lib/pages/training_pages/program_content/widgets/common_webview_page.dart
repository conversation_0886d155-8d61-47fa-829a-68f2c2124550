import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CommonWebviewPage extends StatefulWidget {
  final String? strUrl;
  final bool? enableAppBar;
  const CommonWebviewPage({this.strUrl, this.enableAppBar = true, super.key});

  @override
  State<CommonWebviewPage> createState() => _CommonWebviewPageState();
}

class _CommonWebviewPageState extends State<CommonWebviewPage> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.strUrl ?? ""));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.enableAppBar == true
          ? AppBar(
              title: Text('view').tr(),
            )
          : null,
      body: WebViewWidget(
        controller: _controller,
      ),
    );
  }
}
