import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:masterg/blocs/theme/theme_bloc.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import '../../../../utils/config.dart';
import '../../../auth_pages/terms_and_condition_page.dart';

class IntractiveView extends StatefulWidget {
  final InteractiveContent interactiveContent;
  const IntractiveView({super.key, required this.interactiveContent});

  @override
  State<IntractiveView> createState() => _IntractiveViewState();
}

class _IntractiveViewState extends State<IntractiveView> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        bool isButtonActive = false;
        Color bgColor;
        if (widget.interactiveContent.contentType?.toLowerCase() ==
            'interactive_content') {
          isButtonActive = true;
        } else {
          isButtonActive = false;
        }

        bgColor =
            !isButtonActive ? context.appColors.grey2 : context.primaryDark;
        return Container(
          color: context.appColors.courseBackground,
          height: MediaQuery.of(context).size.height * 0.24,
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row(
              //   children: [
              //     Text(
              //       '${tr('submit_before')} : ',
              //       style: Styles.bold(size: 14, color: context.appColors.white),
              //     ),
              //   ],
              // ),
              // SizedBox(height: 15),
              Text('${widget.interactiveContent.title}',
                  style: Styles.bold(
                      size: 16, color: context.appColors.textWhite)),
              if (widget.interactiveContent.description != null)
                Text('${widget.interactiveContent.description}',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: Styles.semibold(
                        size: 14, color: context.appColors.textWhite)),
              SizedBox(height: 8),
              Spacer(),
              InkWell(
                onTap: () {
                  if (isButtonActive) {
                    String? h5pUrl =
                        '${APK_DETAILS['domain_url']}/attemptH5pContentWebView/${widget.interactiveContent.h5PContentId.toString()}?user_id=${Preference.getInt(Preference.USER_ID)}';

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) {
                          return h5pUrl != '' && h5pUrl.isNotEmpty
                              ? SizedBox(
                                  height: height(context) * 0.29,
                                  child: TermsAndCondition(
                                    url: h5pUrl,
                                    title: tr(
                                        '${widget.interactiveContent.title}'),
                                  ),
                                )
                              : SizedBox(
                                  height: height(context) * 0.26,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                        },
                      ),
                    );
                  }
                },
                child: Container(
                    width: width(context),
                    height: 38,
                    margin:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    decoration: BoxDecoration(
                        color: bgColor,
                        gradient: bgColor == context.primaryDark
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    context.primaryDark,
                                    context.primaryDark.withValues(alpha: 0.8)
                                  ])
                            : null,
                        borderRadius: BorderRadius.circular(8)),
                    child: Center(
                      child: Text(
                        'open_h5p',
                        style: Styles.regular(
                            size: 14,
                            color: bgColor == context.appColors.grey2
                                ? context.appColors.grey3
                                : context.primaryForegroundColor),
                        textAlign: TextAlign.center,
                      ).tr(),
                    )),
              )
            ],
          ),
        );
      },
    );
  }
}
