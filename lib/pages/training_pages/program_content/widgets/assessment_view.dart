import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/data/providers/assessment_detail_provider.dart';
import 'package:masterg/data/providers/training_detail_provider.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/training_pages/assessment_page.dart';
import 'package:masterg/pages/training_pages/training_service.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../../../utils/strings.dart';

class AssessmentView extends StatelessWidget {
  final Assessments assessment;
  const AssessmentView({super.key, required this.assessment});

  @override
  Widget build(BuildContext context) {
    String title = '';

    if (assessment.status == 'Active') {
      if (assessment.attemptsRemaining == assessment.attemptAllowed) {
        title = tr('attempt');
      } else {
        title = '${tr('reattempt')} / ${tr('review')}';
      }
    }

    return Container(
      color: context.appColors.courseBg,
      height: MediaQuery.of(context).size.height * 0.24,
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${tr('submit_before')} : ',
                style: Styles.bold(
                    size: 14, color: context.appColors.primaryForeground),
              ),
              Text(
                assessment.endDate != null
                    ? Utility.convertDateFromMillis(assessment.endDate!,
                        Strings.REQUIRED_DATE_HH_MM_AAA_DD_MMM_YYYY)
                    : '',
                style: Styles.bold(
                    size: 14, color: context.appColors.primaryForeground),
                textDirection: ui.TextDirection.ltr,
              ),
              Spacer(),
              Text(
                '${assessment.durationInMinutes} ${assessment.durationInMinutes == 0 || assessment.durationInMinutes == 1 ? tr('min') : tr('mins')}',
                // '${selectedType}',
                style: Styles.bold(
                    size: 14, color: context.appColors.primaryForeground),
              ),
            ],
          ),
          SizedBox(height: 15),
          Text('${assessment.title}',
              style: Styles.bold(
                  size: 16, color: context.appColors.primaryForeground)),
          SizedBox(height: 8),
          Row(
            children: [
              assessment.attemptsRemaining != assessment.attemptAllowed
                  ? Text(
                      '${assessment.displayScorecard != 1 ? "" : '${assessment.overallScore}/${assessment.maximumMarks} ${tr('marks')} •'}${assessment.attemptAllowed == 0 ? tr('unlimited_attempt') : '${assessment.attemptsRemaining} ${tr('attempt_available')}'} ',
                      style: Styles.regular(
                          size: 14, color: context.appColors.primaryForeground),
                    )
                  : Text(
                      '${assessment.displayScorecard != 1 ? "" : '${assessment.maximumMarks} ${tr('marks')} •'} ${assessment.attemptAllowed == 0 ? tr('unlimited_attempt') : '${assessment.attemptsRemaining} ${tr('attempt_available')}'} ',
                      style: Styles.regular(
                          size: 14, color: context.appColors.primaryForeground),
                    ),
            ],
          ),
          Spacer(),
          InkWell(
            onTap: () {
              TrainingDetailProvider trainingDetailProvider =
                  Provider.of<TrainingDetailProvider>(context, listen: false);

              Navigator.push(
                  context,
                  NextPageRoute(
                      ChangeNotifierProvider<AssessmentDetailProvider>(
                          create: (context) => AssessmentDetailProvider(
                              TrainingService(ApiService()), assessment),
                          child: AssessmentDetailPage(
                              isCertified: assessment.overallResult
                                      .toString()
                                      .toLowerCase() ==
                                  'pass',
                              programName: trainingDetailProvider.program?.name,
                              programId: trainingDetailProvider.program?.id)),
                      isMaintainState: true));
            },
            child: Container(
                width: width(context),
                height: 38,
                margin: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
                decoration: BoxDecoration(
                    color: context.appColors.primary,
                    gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: <Color>[
                          context.appColors.gradientLeft,
                          context.appColors.gradientRight
                        ]),
                    borderRadius: BorderRadius.circular(8)),
                child: Center(
                  child: Text(
                    title,
                    style: Styles.regular(
                        size: 14, color: context.appColors.primaryForeground),
                    textAlign: TextAlign.center,
                  ).tr(),
                )),
          )
        ],
      ),
    );
  }
}
