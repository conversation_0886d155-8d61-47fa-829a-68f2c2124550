import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class FullScreenYoutubePlayer extends StatefulWidget {
  final YoutubePlayerController controller;

  const FullScreenYoutubePlayer({super.key, required this.controller});

  @override
  State<FullScreenYoutubePlayer> createState() =>
      _FullScreenYoutubePlayerState();
}

class _FullScreenYoutubePlayerState extends State<FullScreenYoutubePlayer> {
  @override
  void initState() {
    scrollandPlay();
    super.initState();
  }

  void scrollandPlay() {
    Future.delayed(Duration(seconds: 2)).then((value) {
      widget.controller.play();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Transform.scale(
          scale: 0.7,
          child: Container(
            decoration: BoxDecoration(
              color: context.appColors.textBlack.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: Icon<PERSON><PERSON>on(
              icon: Icon(
                Icons.arrow_back,
                size: 30,
                color: context.appColors.textWhite,
              ),
              onPressed: () {
                try {
                  Navigator.pop(context);
                } catch (e) {
                  debugPrint('exc $e');
                }
              },
            ),
          ),
        ),
      ),
      body: YoutubePlayer(
        onEnded: (YoutubeMetaData data) {
          Navigator.pop(context);
        },
        controller: widget.controller,
        showVideoProgressIndicator: true,
        bottomActions: [
          CurrentPosition(),
          RemainingDuration(),
        ],
      ),
    );
  }
}
