import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/home_response/training_detail_response.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';

enum FilterType {
  none,
  classes,
  videos,
  notes,
  assignment,
  quiz,
  scorm,
  intractive,
  text
}

class ProgramContentProvider extends ChangeNotifier {
  List<dynamic> shortContentList = [];
  List<dynamic> shortContentListFiltered = [];
  List<Modules>? modules;
  List<Modules>? filtermodules;
  FilterType appliedFilter = FilterType.none;
  dynamic selectedContent;
  bool loading = true;
  ApiService api = ApiService();

  ProgramContentProvider(List<Modules>? modules) {
    if (Preference.getBool(Preference.ENABLE_SKILL, def: false) == true) {
      modules?.sort((a, b) => a.skillId!.compareTo(b.skillId!));
    }

    this.modules = modules;
    this.filtermodules = modules;
    loadsortContent();
  }

  void updateSelectedContent({required dynamic selectedContent}) {
    this.selectedContent = selectedContent;
    notifyListeners();
  }

  void changeFilterType({required FilterType filterType}) async {
    try {
      this.appliedFilter = filterType;
      this.filtermodules = this.modules;
      shortContentListFiltered = [];
      shortContentListFiltered.addAll(shortContentList);

      Log.v('shortContentListFiltered---- ${shortContentListFiltered}');
      if (modules!.length != 0) {
        if (this.appliedFilter != FilterType.none) {
          for (int i = 0; i < this.modules!.length; i++) {
            shortContentListFiltered[i] =
                shortContentListFiltered[i].where((element) {
              if (filterType == FilterType.classes) {
                return element['sorted_content'] == 'sessions';
              } else if (filterType == FilterType.quiz) {
                return element['sorted_content'] == 'assessments';
              } else if (filterType == FilterType.assignment) {
                return element['sorted_content'] == 'assignments';
              } else if (filterType == FilterType.notes) {
                return element['sorted_content'] == 'learning_shots' &&
                    element['content_type'] == 'notes';
              } else if (filterType == FilterType.videos) {
                return element['sorted_content'] == 'learning_shots' &&
                    (element['content_type'] == 'video' ||
                        element['content_type'] == 'video_yts');
              } else if (filterType == FilterType.scorm) {
                return element['sorted_content'] == 'scorm';
              } else if (filterType == FilterType.intractive) {
                return element['sorted_content'] == 'interactive_content';
              } else if (filterType == FilterType.text) {
                return element['sorted_content'] == 'learning_shots' &&
                    (element['content_type'] == 'text');
              }
              return false;
            }).toList();
          }

          //re-assign filterModule from orginal module before removing
          this.filtermodules = [];
          this.filtermodules?.addAll(modules!);

          //remove module which does not contains content
          for (int i = 0; i < shortContentListFiltered.length; i++) {
            if (shortContentListFiltered[i].length == 0) {
              shortContentListFiltered.removeAt(i);
              this.filtermodules?.removeAt(i);
              i--;
            }
          }

          if (shortContentListFiltered.length == 0) {
            this.selectedContent = null;
          }
        }
        selectDefault();
      }
    } catch (e, stackTrace) {
      Log.v("Error while changeFilterType:: $e");
      Log.v("Stack changeFilterType:: $stackTrace");
    }
    notifyListeners();
  }

  void selectDefault() {
    log("current data is ${shortContentListFiltered.length}",
        name: "selectDefault ");

    try {
      final firstContentList = shortContentListFiltered.firstWhere(
          (element) => element != null && element.length > 0,
          orElse: () => null);

      if (firstContentList != null) {
        final firstContentMap = firstContentList.first;
        final String sortedContent = firstContentMap['sorted_content'];
        selectedContent = switch (sortedContent) {
          'sessions' => Sessions.fromJson(firstContentMap),
          'assessments' => Assessments.fromJson(firstContentMap),
          'assignments' => Assignments.fromJson(firstContentMap),
          'learning_shots' => LearningShots.fromJson(firstContentMap),
          'scorm' => Scorm.fromJson(firstContentMap),
          'interactive_content' => InteractiveContent.fromJson(firstContentMap),
          _ => null, // Handle unknown content types if necessary
        };
      } else {
        selectedContent = null;
      }
    } catch (e, stackTrace) {
      log("error setting default selected content $e");
      log("error setting default selected content $stackTrace");
      selectedContent = null;
    } finally {
      loading = false;
      notifyListeners();
    }
  }

  // void selectDefault() {
  //   try {
  //     if (this.shortContentListFiltered.first.first['sorted_content'] ==
  //         'sessions') {
  //       this.selectedContent =
  //           Sessions.fromJson(this.shortContentListFiltered.first.first);
  //     }
  //     if (this.shortContentListFiltered.first.first['sorted_content'] ==
  //         'assessments') {
  //       this.selectedContent =
  //           Assessments.fromJson(this.shortContentListFiltered.first.first);
  //     }
  //     if (this.shortContentListFiltered.first.first['sorted_content'] ==
  //         'assignments') {
  //       this.selectedContent =
  //           Assignments.fromJson(this.shortContentListFiltered.first.first);
  //     }
  //     if (this.shortContentListFiltered.first.first['sorted_content'] ==
  //         'learning_shots') {
  //       this.selectedContent =
  //           LearningShots.fromJson(this.shortContentListFiltered.first.first);
  //     }
  //     if (this.shortContentListFiltered.first.first['sorted_content'] ==
  //         'scorm') {
  //       this.selectedContent =
  //           Scorm.fromJson(this.shortContentListFiltered.first.first);
  //     }
  //   } catch (e, stackTrace) {
  //     log("error setting default selected content $e");
  //     log("error setting default selected content $stackTrace");
  //   } finally {
  //     loading = false;
  //     notifyListeners();
  //   }
  // }

  void loadsortContent() async {
    //load from cache first
    // custom_cache_https://digital.mescdigital.org/api/courses-list?category_id=0
    bool loadedCache = false;
    for (int index = 0; index < modules!.length; index++) {
      try {
        String urlKey = 'custom_cache_' +
            '${api.dio.options.baseUrl}' +
            ApiConstants.MODULES +
            '/${modules?[index].id}' +
            "/${UserSession.userContentLanguageId}";

        await _getFromCache(urlKey).then((value) {
          TrainingModuleResponse trainingModuleResponse =
              TrainingModuleResponse.fromJson(value);

          this.shortContentList.add(trainingModuleResponse.data?.shortContent);
          this
              .shortContentListFiltered
              .add(trainingModuleResponse.data?.shortContent);
        });
        notifyListeners();
      } catch (e) {
        log("error while fetching module data11::: $e");
      }
    }
    if (shortContentListFiltered.isNotEmpty &&
        shortContentListFiltered.length == this.modules?.length) {
      loadedCache = true;
    }

    if (loadedCache) {
      selectDefault();
    } else {
      shortContentList = List.generate(this.modules!.length, (index) => null);
    }
    notifyListeners();

    try {
      // for (int index = 0; index < this.modules!.length; index++) {
      //   try {
      //     await getDetailContent(modules?[index].id, true).then((value) {
      //       ApiResponse apiResponse = value;

      //       if (apiResponse.success) {
      //         TrainingModuleResponse trainingModuleResponse =
      //             TrainingModuleResponse.fromJson(value.body);

      //         this.shortContentList[index] =
      //             trainingModuleResponse.data?.shortContent;
      //       }
      //     });
      //   } catch (e) {
      //     log("error while fetching module data");
      //   }
      // }
      final List<Future<ApiResponse?>> futures = modules!
          .map((modules) => getDetailContent(modules.id, true))
          .toList();
      final List<ApiResponse?> responses = await Future.wait(futures);
      for (int index = 0; index < responses.length; index++) {
        ApiResponse? apiResponse = responses[index];
        if (apiResponse != null && apiResponse.success) {
          TrainingModuleResponse trainingModuleResponse =
              TrainingModuleResponse.fromJson(apiResponse.body);
          this.shortContentList[index] =
              trainingModuleResponse.data?.shortContent;
        } else {
          this.shortContentList[index] = [];
        }
      }
      shortContentListFiltered = [];
      shortContentListFiltered.addAll(shortContentList);
    } catch (e) {
      log("error while fetching module data $e");
    }
    loading = false;
    if (!loadedCache) {
      selectDefault();
    } else {
      changeFilterType(filterType: this.appliedFilter);
    }
    notifyListeners();
  }

  Future<ApiResponse?> getDetailContent(int? moduleId, bool cache) async {
    try {
      final response = await api.dio.get(
          ApiConstants.MODULES +
              '/$moduleId' +
              "/${UserSession.userContentLanguageId}",
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  Future<dynamic> _getFromCache(String key) async {
    String? jsonData = Preference.getString(key);
    log("cache get $jsonData", name: "dio_cache");

    return jsonData != null ? json.decode(jsonData) : null;
  }
}
