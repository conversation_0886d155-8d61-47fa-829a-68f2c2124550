import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/styles.dart';
import 'package:video_player/video_player.dart';

class OfflineVideoScreen extends StatefulWidget {
  final String? url;
  final String? title;
  const OfflineVideoScreen({super.key, this.url, this.title});

  @override
  State<OfflineVideoScreen> createState() => _OfflineVideoScreenState();
}

class _OfflineVideoScreenState extends State<OfflineVideoScreen> {
  FlickManager? flickManager;

  bool isLoading = true;

  @override
  void initState() {
    _getData();
    super.initState();
  }

  @override
  void dispose() {
    flickManager?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FlickVideoPlayer(
              flickVideoWithControls: FlickVideoWithControls(
                videoFit: BoxFit.cover,
              ),
              flickManager: flickManager!),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              widget.title ?? "",
              style: Styles.textBold(),
            ),
          )
        ],
      ),
    );
  }

  void _getData() async {
    flickManager = FlickManager(
      autoInitialize: true,
      videoPlayerController: VideoPlayerController.networkUrl(Uri.parse(widget.url!)),
    );
  }
}
