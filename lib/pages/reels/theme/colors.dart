import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

// Theme-aware colors for reels
class ReelsColors {
  static Color appBgColor(BuildContext context) => const Color(0xFF121212);

  static const primary = Color(0xFFFC2D55);
  static const secondary = Color(0xFF19D5F1);

  static Color white(BuildContext context) => Colors.white;

  static Color black(BuildContext context) =>
      context.isDarkMode ? const Color(0xFF000000) : const Color(0xFF000000);
}

// Legacy constants for backward compatibility
const appBgColor = Color(0xFF000000);
const primary = Color(0xFFFC2D55);
const secondary = Color(0xFF19D5F1);
const white = Colors.white;
const black = Color(0xFF000000);
