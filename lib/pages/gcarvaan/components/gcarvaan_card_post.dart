import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive/hive.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/next_page_routing.dart';
import 'package:masterg/pages/gcarvaan/comment/comment_view_page.dart';
import 'package:masterg/pages/ghome/video_player_screen.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/pdf_view_page.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/get_widget_size.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/video_screen.dart';
import 'package:masterg/utils/widget_size.dart';
import 'package:open_filex/open_filex.dart';
import 'package:share_plus/share_plus.dart';
//import 'package:open_file_safe/open_file_safe.dart';
import 'package:shimmer/shimmer.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:zoom_pinch_overlay/zoom_pinch_overlay.dart';

import '../../../utils/constant.dart';

class GCarvaanCardPost extends StatefulWidget {
  final String? imagePath;
  final String? profilePath;
  final String? userName;
  final String date;
  // int? height;
  final String userStatus;
  // int? width;
  final String? description;
  final int commentCount;
  final bool commentVisible;
  final int? likeCount;
  final int index;
  final int? viewCount;
  final bool? islikedPost;
  final int? contentId;
  final List<String>? fileList;
  final GCarvaanListModel? value;
  final String? resourceType;
  final List<Dimension>? dimension;
  final int? userID;
  final bool? fromDasboard;
  final double? aspectRatio;
  final bool? fromUserActivity;
  final bool? flagCommunity;

  const GCarvaanCardPost(
      {super.key,
      required this.imagePath,
      required this.date,
      required this.description,
      required this.commentCount,
      required this.userName,
      required this.profilePath,
      required this.index,
      required this.userStatus,
      required this.commentVisible,
      this.likeCount,
      this.viewCount,
      this.islikedPost,
      this.contentId,
      this.fileList,
      // this.height,
      // this.width,
      this.dimension,
      this.value,
      this.resourceType,
      this.userID,
      this.fromDasboard = false,
      this.aspectRatio,
      this.fromUserActivity,
      this.flagCommunity = true});

  @override
  State<GCarvaanCardPost> createState() => _GCarvaanCardPostState();
}

class _GCarvaanCardPostState extends State<GCarvaanCardPost> {
  // VideoPlayerController _videoController;
  bool isShowPlaying = false;
  // GlobalKey<FormState> _abcKey = GlobalKey<FormState>();
  int? likeCount;
  int currentIndex = 0;
  // Download download = new Download();
  FlickManager? flickManager;
  double videoHeight = 0.0;
  Box? box;

  int currentPageView = 0;
  @override
  void initState() {
    super.initState();
    box = Hive.box(DB.CONTENT);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var millis = int.tryParse(widget.date) ?? 0;
    DateTime date = DateTime.fromMillisecondsSinceEpoch(
      millis * 1000,
    );
    final now = DateTime.now();
    // int? previousIndex = -1;

    String calculateTimeDifferenceBetween(
        DateTime startDate, DateTime endDate) {
      int seconds = endDate.difference(startDate).inSeconds.abs();
      if (seconds < 60) {
        if (seconds.abs() < 4) return tr('now');
        return '${seconds.abs()} ${tr('second')}';
      } else if (seconds >= 60 && seconds < 3600)
        return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
      else if (seconds >= 3600 && seconds < 86400)
        return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
      else {
        // convert day to month
        int days = startDate.difference(endDate).inDays.abs();
        if (days < 30 && days > 7) {
          return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
        }
        if (days > 30) {
          int month = (startDate.difference(endDate).inDays ~/ 30).abs();
          return '$month ${tr('months')}';
        } else {
          return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
        }
      }
    }

    // Widget isPlaying() {
    //   return _videoController.value.isPlaying && !isShowPlaying
    //       ? Container()
    //       : Icon(
    //           Icons.play_arrow,
    //           size: 80,
    //           color: Color.white.withValues(alpha:0.5),
    //         );
    // }

    var itemCount = int.tryParse('${widget.fileList?.length}') ?? 0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Container(
        margin: widget.fromDasboard == true
            ? null
            : const EdgeInsets.symmetric(vertical: 6),
        decoration: BoxDecoration(
            color: context.appColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: context.appColors.grey4)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Center(
                    child: ClipOval(
                      child: widget.userStatus.toLowerCase() != "active"
                          ? SvgPicture.asset(
                              'assets/images/default_user.svg',
                              height: 50,
                              width: 50,
                              allowDrawingOutsideViewBox: true,
                            )
                          : widget.profilePath != null
                              ? Image.network(
                                  widget.profilePath ?? '',
                                  height: 45,
                                  width: 45,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, url, error) {
                                    return SvgPicture.asset(
                                      'assets/images/default_user.svg',
                                      height: 50,
                                      width: 50,
                                      allowDrawingOutsideViewBox: true,
                                    );
                                  },
                                  loadingBuilder: (BuildContext context,
                                      Widget child,
                                      ImageChunkEvent? loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Shimmer.fromColors(
                                      baseColor: context.appColors.shimmerBase,
                                      highlightColor:
                                          context.appColors.shimmerHighlight,
                                      child: Container(
                                          height: 45,
                                          margin: EdgeInsets.only(left: 2),
                                          width: 45,
                                          decoration: BoxDecoration(
                                            color: context.appColors.surface,
                                            shape: BoxShape.circle,
                                          )),
                                    );
                                  },
                                )
                              : Icon(Icons.account_circle_rounded,
                                  size: 50, color: context.appColors.grey),
                    ),
                  ),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 8.0,
                            right: 8.0,
                          ),
                          child: Text(
                            Utility().decrypted128('${widget.userName}'),
                            style: Styles.semibold(
                                size: 14,
                                color:
                                    widget.userStatus.toLowerCase() != "active"
                                        ? context.appColors.grey3
                                            .withValues(alpha: 0.3)
                                        : context.appColors.textBlack),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0, right: 8),
                          child: Text(
                            calculateTimeDifferenceBetween(
                                DateTime.parse(
                                    date.toString().substring(0, 19)),
                                now),
                            style:
                                Styles.getRegularThemeStyle(context, size: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      bool reportPostFormEnabled = false;
                      // bool reportInprogress = false;
                      await showModalBottomSheet(
                          context: context,
                          backgroundColor: context.appColors.pureBlack,
                          builder: (context) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: <Widget>[
                                Center(
                                  child: Container(
                                    padding: EdgeInsets.all(10),
                                    margin: EdgeInsets.only(top: 10),
                                    height: 4,
                                    width: 70,
                                    decoration: BoxDecoration(
                                        color:
                                            context.appColors.primaryForeground,
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                ),
                                ListTile(
                                  leading: Icon(
                                    Icons.report,
                                    color: context.appColors.primaryForeground,
                                  ),
                                  title: Text(
                                    'report_post',
                                    style: TextStyle(
                                        color: context
                                            .appColors.primaryForeground),
                                  ).tr(),
                                  onTap: () {
                                    setState(() {
                                      reportPostFormEnabled = true;
                                    });
                                    return Navigator.pop(context);
                                  },
                                ),
                                ListTile(
                                  leading: Icon(
                                    Icons.hide_image_outlined,
                                    color: context.appColors.primaryForeground,
                                  ),
                                  title: Text(
                                    'remove_hide_post',
                                    style: TextStyle(
                                        color: context
                                            .appColors.primaryForeground),
                                  ).tr(),
                                  onTap: () {
                                    reportPost(
                                        'remove', widget.contentId, '', '');
                                    widget.value?.hidePost(widget.index);

                                    deleteHivePost();

                                    return Navigator.pop(context);
                                  },
                                ),
                                Preference.getInt(Preference.USER_ID) ==
                                        widget.userID
                                    ? ListTile(
                                        leading: Icon(
                                          Icons.delete,
                                          color: context
                                              .appColors.primaryForeground,
                                        ),
                                        title: Text(
                                          'delete_post',
                                          style: TextStyle(
                                              color: context
                                                  .appColors.primaryForeground),
                                        ).tr(),
                                        onTap: () async {
                                          Navigator.pop(context);

                                          AlertsWidget.showCustomDialog(
                                              context: context,
                                              title: tr('delete_post'),
                                              text: tr(
                                                  'confirm_deletion_textone'),
                                              icon:
                                                  'assets/images/circle_alert_fill.svg',
                                              oKText: tr('ok'),
                                              onOkClick: () async {
                                                deletePost(widget.contentId);
                                                widget.value
                                                    ?.hidePost(widget.index);

                                                if (widget.index < 10) {
                                                  List<GCarvaanPostElement>?
                                                      gcarvaanPosts = box
                                                          ?.get("gcarvaan_post")
                                                          .map((e) =>
                                                              GCarvaanPostElement
                                                                  .fromJson(Map<
                                                                          String,
                                                                          dynamic>.from(
                                                                      e)))
                                                          .cast<
                                                              GCarvaanPostElement>()
                                                          .toList();

                                                  box?.put(
                                                      "gcarvaan_post",
                                                      gcarvaanPosts?.removeAt(
                                                          widget.index));
                                                }
                                              });
                                        },
                                      )
                                    : SizedBox(),
                              ],
                            );
                          });

                      void handleReport(ReportState state) {
                        var reportState = state;
                        setState(() {
                          switch (reportState.apiState) {
                            case ApiStatus.LOADING:
                              // reportInprogress = true;
                              break;
                            case ApiStatus.SUCCESS:
                              Navigator.pop(context);
                              widget.value?.hidePost(widget.index);
                              deleteHivePost();

                              Utility.showSnackBar(
                                  scaffoldContext: context,
                                  message: '${reportState.response?.message}');
                              // reportInprogress = false;
                              break;
                            case ApiStatus.ERROR:
                              // reportInprogress = false;
                              break;
                            case ApiStatus.INITIAL:
                              break;
                          }
                        });
                      }

                      if (reportPostFormEnabled) {
                        bool showTextField = false;
                        TextEditingController reportController =
                            TextEditingController();

                        List<dynamic> reportList = Utility.getReportList();

                        showModalBottomSheet(
                            context: context,
                            backgroundColor: context.appColors.pureBlack,
                            builder: (BuildContext context) {
                              return FractionallySizedBox(
                                heightFactor: 1,
                                child: BlocManager(
                                  initState: (BuildContext context) {},
                                  child: BlocListener<HomeBloc, HomeState>(
                                    listener: (BuildContext context, state) {
                                      if (state is ReportState) {
                                        handleReport(state);
                                      }
                                    },
                                    child: BottomSheet(
                                        onClosing: () {},
                                        builder: (BuildContext context) {
                                          return StatefulBuilder(
                                            builder: (BuildContext context,
                                                    setState) =>
                                                SingleChildScrollView(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisSize: MainAxisSize.min,
                                                children: <Widget>[
                                                  Center(
                                                    child: Container(
                                                      padding:
                                                          EdgeInsets.all(10),
                                                      margin: EdgeInsets.only(
                                                          top: 10),
                                                      height: 4,
                                                      width: 70,
                                                      decoration: BoxDecoration(
                                                          color: context
                                                              .appColors.grey4,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8)),
                                                    ),
                                                  ),
                                                  Center(
                                                    child: Text(
                                                      'report',
                                                      style: Styles
                                                          .getBoldThemeStyle(
                                                              context),
                                                    ).tr(),
                                                  ),
                                                  Divider(),
                                                  Text('reporting_this_post',
                                                          style: Styles.regular(
                                                              color: context
                                                                  .appColors
                                                                  .textBlack))
                                                      .tr(),
                                                  if (showTextField == false)
                                                    Column(
                                                      children: [
                                                        MediaQuery
                                                            .removePadding(
                                                          context: context,
                                                          removeBottom: true,
                                                          child:
                                                              ListView.builder(
                                                                  physics:
                                                                      BouncingScrollPhysics(),
                                                                  shrinkWrap:
                                                                      true,
                                                                  itemCount:
                                                                      reportList
                                                                          .length,
                                                                  itemBuilder:
                                                                      (BuildContext
                                                                              context,
                                                                          int index) {
                                                                    return SizedBox(
                                                                      height:
                                                                          50,
                                                                      child: ListTile(
                                                                          onTap: () {
                                                                            reportPost(
                                                                                'offensive',
                                                                                widget.contentId,
                                                                                '${reportList[index]['value']}',
                                                                                reportController.value.text);
                                                                          },
                                                                          title: Text('${reportList[index]['title']}')),
                                                                    );
                                                                  }),
                                                        ),
                                                        Container(
                                                          height: 20,
                                                          margin:
                                                              EdgeInsets.only(
                                                                  bottom: 50),
                                                          child: ListTile(
                                                              onTap: () {
                                                                setState(() {
                                                                  showTextField =
                                                                      true;
                                                                });
                                                              },
                                                              title: Text(
                                                                      'something_else')
                                                                  .tr()),
                                                        ),
                                                      ],
                                                    ),
                                                  if (showTextField == true)
                                                    Container(
                                                      margin:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 14,
                                                              vertical: 8),
                                                      child:
                                                          SingleChildScrollView(
                                                        child: Column(
                                                          children: [
                                                            TextFormField(
                                                              controller:
                                                                  reportController,
                                                              style: Styles
                                                                  .getBoldThemeStyle(
                                                                context,
                                                                size: 14,
                                                              ),
                                                              decoration:
                                                                  InputDecoration(
                                                                hintText: tr(
                                                                    'Trying_report_this_post'),
                                                                isDense: true,
                                                                helperStyle: Styles.regular(
                                                                    size: 12,
                                                                    color: context
                                                                        .appColors
                                                                        .grey3
                                                                        .withValues(
                                                                            alpha:
                                                                                0.1)),
                                                                counterText: "",
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              height: 20,
                                                            ),
                                                            InkWell(
                                                                onTap: () {
                                                                  if (reportController
                                                                          .value
                                                                          .text ==
                                                                      '') {
                                                                    return;
                                                                  }
                                                                  reportPost(
                                                                      'offensive',
                                                                      widget
                                                                          .contentId,
                                                                      '',
                                                                      reportController
                                                                          .value
                                                                          .text);
                                                                },
                                                                child:
                                                                    Container(
                                                                  margin: EdgeInsets
                                                                      .symmetric(
                                                                          vertical:
                                                                              12),
                                                                  width: double
                                                                      .infinity,
                                                                  height: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .height *
                                                                      WidgetSize
                                                                          .AUTH_BUTTON_SIZE,
                                                                  decoration: BoxDecoration(
                                                                      color: context.appColors.primary,
                                                                      gradient: reportController.value.text == ''
                                                                          ? LinearGradient(colors: [
                                                                              context.appColors.grey3,
                                                                              context.appColors.grey3,
                                                                            ])
                                                                          : LinearGradient(colors: [
                                                                              context.appColors.gradientLeft,
                                                                              context.appColors.gradientRight,
                                                                            ]),
                                                                      borderRadius: BorderRadius.circular(10)),
                                                                  child: Center(
                                                                      child:
                                                                          Text(
                                                                    'submit',
                                                                    style: Styles
                                                                        .regular(
                                                                      color: context
                                                                          .appColors
                                                                          .textWhite,
                                                                    ),
                                                                  ).tr()),
                                                                )),
                                                          ],
                                                        ),
                                                      ),
                                                    )
                                                ],
                                              ),
                                            ),
                                          );
                                        }),
                                  ),
                                ),
                              );
                            });
                      }
                    },
                    child: Icon(
                      Icons.more_vert,
                      color: context.appColors.headingTitle,
                    ),
                  ),
                ],
              ),
            ),

            ///Add New and changed on post card and fun pending on Api side---
            Center(
              /*child: ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 500),
                child:
              ),*/
              child: Stack(
                children: [
                  AspectRatio(
                    aspectRatio: widget.aspectRatio ?? 1,
                    child: PageView.builder(
                        scrollDirection: Axis.horizontal,
                        controller:
                            PageController(initialPage: 0, viewportFraction: 1),
                        itemCount: itemCount,
                        onPageChanged: (int index) {
                          setState(() {
                            currentPageView = index;
                          });
                        },
                        itemBuilder: (BuildContext context, int index) {
                          //var filePath = download.getFilePath('${widget.fileList![index]}');

                          //  return Text("he ${widget.dimension?.first.height} and");
                          return Column(children: [
                            VisibilityDetector(
                              key: ObjectKey('${widget.contentId}'),
                              onVisibilityChanged: (visibility) async {
                                var visiblePercentage =
                                    visibility.visibleFraction * 100;
                                if (visiblePercentage.round() <= 70 &&
                                    mounted) {
                                } else {
                                  await Future.delayed(Duration(seconds: 2));
                                  updateLikeandViews(null);
                                  // setState(() {});
                                }
                              },
                              child: Center(
                                  child: widget.fileList![index]
                                              .contains('.mp4') ||
                                          widget.fileList![index]
                                              .contains('.mov')
                                      ? widget.fromUserActivity == true
                                          ?
                                          // VideoPlayerWidget(
                                          //     videoUrl:
                                          //         '${widget.fileList![index]}',
                                          //     maintainAspectRatio: false,
                                          //   )
                                          ConstrainedBox(
                                              constraints: const BoxConstraints(
                                                  maxHeight: 500),
                                              child: AspectRatio(
                                                aspectRatio:
                                                    widget.aspectRatio ?? 1,
                                                child: VideoPlayerWidget(
                                                  videoUrl:
                                                      widget.fileList![index],
                                                  maintainAspectRatio: true,
                                                ),
                                              ),
                                            )
                                          : CustomVideoPlayer(
                                              url: widget.fileList![index],
                                              isLocalVideo: false,
                                              likeCount: widget.likeCount,
                                              viewCount: widget.viewCount,
                                              commentCount: widget.commentCount,
                                              height: min(
                                                  widget.aspectRatio!,
                                                  MediaQuery.of(context)
                                                          .size
                                                          .height -
                                                      MediaQuery.of(context)
                                                              .size
                                                              .height *
                                                          0.25),
                                              index: index,
                                              desc: widget.description,
                                              //userName: widget.userName,
                                              userName: Utility().decrypted128(
                                                  '${widget.userName}'),
                                              profilePath: widget.profilePath,
                                              time:
                                                  calculateTimeDifferenceBetween(
                                                      DateTime.parse(date
                                                          .toString()
                                                          .substring(0, 19)),
                                                      now),
                                            )
                                      : widget.fileList![index]
                                              .contains('.docx')
                                          ? InkWell(
                                              onTap: () {
                                                OpenFilex.open(
                                                    widget.fileList![index]);
                                              },
                                              child: Image.asset(
                                                'assets/images/docx.png',
                                                height: 120,
                                                fit: BoxFit.contain,
                                              ))
                                          : widget.fileList![index]
                                                  .contains('.pdf')
                                              ? InkWell(
                                                  onTap: () {
                                                    Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                          ViewPdfPage(
                                                            path: widget
                                                                    .fileList![
                                                                index],
                                                          ),
                                                          isMaintainState: true,
                                                        ));
                                                  },
                                                  child: Image.asset(
                                                    'assets/images/pdf.png',
                                                    height: 120,
                                                    fit: BoxFit.contain,
                                                  ),
                                                )
                                              : widget.fileList?[index] != null
                                                  ? InkWell(
                                                      onTap: () {
                                                        _displayDialog(
                                                          context: context,
                                                          imgUrl: widget
                                                              .fileList![index],
                                                          fileList:
                                                              widget.fileList,
                                                          likeCount:
                                                              widget.likeCount,
                                                          viewCount:
                                                              widget.viewCount,
                                                          commentCount: widget
                                                              .commentCount,
                                                          index: index,
                                                          desc: widget
                                                              .description,
                                                          //userName: widget.userName,
                                                          userName: Utility()
                                                              .decrypted128(
                                                                  '${widget.userName}'),
                                                          profilePath: widget
                                                              .profilePath,
                                                          time: calculateTimeDifferenceBetween(
                                                              DateTime.parse(date
                                                                  .toString()
                                                                  .substring(
                                                                      0, 19)),
                                                              now),
                                                        );
                                                      },
                                                      child: ZoomOverlay(
                                                        minScale:
                                                            0.5, // Optional
                                                        maxScale:
                                                            3.0, // Optional
                                                        twoTouchOnly: true,
                                                        child:
                                                            CachedNetworkImage(
                                                          filterQuality:
                                                              FilterQuality
                                                                  .medium,
                                                          imageUrl: widget
                                                              .fileList![index],
                                                          progressIndicatorBuilder:
                                                              (context, url,
                                                                      downloadProgress) =>
                                                                  Shimmer
                                                                      .fromColors(
                                                            baseColor: Color(
                                                                0xffe6e4e6),
                                                            highlightColor:
                                                                Color(
                                                                    0xffeaf0f3),
                                                            child: Container(
                                                              // height: double
                                                              //     .infinity,
                                                              margin: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          10,
                                                                      vertical:
                                                                          10),
                                                              width:
                                                                  MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width,
                                                              decoration: BoxDecoration(
                                                                  color: context
                                                                      .appColors
                                                                      .surface,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              6)),
                                                            ),
                                                          ),
                                                          errorWidget: (context,
                                                                  url, error) =>
                                                              Icon(Icons.error),
                                                        ),
                                                      ), // child: Image.network(
                                                    )
                                                  : MeasureSize(
                                                      onChange: (Size size) {
                                                        widget.value
                                                            ?.updateSize(
                                                                size, index);
                                                      },
                                                      child: InkWell(
                                                          onTap: () {
                                                            _displayDialog(
                                                              context: context,
                                                              imgUrl: widget
                                                                      .fileList![
                                                                  index],
                                                              fileList: widget
                                                                  .fileList,
                                                              likeCount: widget
                                                                  .likeCount,
                                                              viewCount: widget
                                                                  .viewCount,
                                                              commentCount: widget
                                                                  .commentCount,
                                                              index: index,
                                                              desc: widget
                                                                  .description,
                                                              //userName: widget.userName,
                                                              userName: Utility()
                                                                  .decrypted128(
                                                                      '${widget.userName}'),
                                                              profilePath: widget
                                                                  .profilePath,
                                                              time: calculateTimeDifferenceBetween(
                                                                  DateTime.parse(date
                                                                      .toString()
                                                                      .substring(
                                                                          0,
                                                                          19)),
                                                                  now),
                                                            );
                                                          },
                                                          child: Image.network(
                                                            widget.fileList![
                                                                index],
                                                            filterQuality:
                                                                FilterQuality
                                                                    .low,
                                                            fit: BoxFit.contain,
                                                            width:
                                                                MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width,
                                                            loadingBuilder: (BuildContext
                                                                    context,
                                                                Widget child,
                                                                ImageChunkEvent
                                                                    loadingProgress) {
                                                              return Shimmer
                                                                  .fromColors(
                                                                baseColor: context
                                                                    .appColors
                                                                    .shimmerBase,
                                                                highlightColor:
                                                                    context
                                                                        .appColors
                                                                        .shimmerHighlight,
                                                                child:
                                                                    Container(
                                                                  height: double
                                                                      .infinity,
                                                                  margin: EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          10,
                                                                      vertical:
                                                                          10),
                                                                  width: MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width,
                                                                  decoration: BoxDecoration(
                                                                      color: context
                                                                          .appColors
                                                                          .surface,
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              6)),
                                                                ),
                                                              );
                                                            } as Widget Function(
                                                                BuildContext,
                                                                Widget,
                                                                ImageChunkEvent?)?,
                                                          )),
                                                    )),
                            ),
                          ]);
                        }),
                  ),
                  if (itemCount > 1)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        // height: 22,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: context.appColors.pureBlack),
                        child: Center(
                          child: Text(
                            '${currentPageView + 1}/$itemCount',
                            style: Styles.semiBoldWhite(),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            //Change on this singh hide for temproary
            /*GestureDetector(
              onTap: () {
                if(widget.flagCommunity == true) {
                  Navigator.push(context, NextPageRoute(
                      GcarvaanPostShortByCommunity(
                        postId: int.parse('4372'),
                        title: "Singh Community",
                      )));
                }
              },
              child: Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 5.0, bottom: 0.0, top: 10.0)
                      : EdgeInsets.only(left: 5.0, bottom: 0.0, top: 10.0),
                  child: Row(
                    children: [
                      Icon(Icons.group_work_sharp, color: context.appColors.grey),
                      Padding(
                        padding: const EdgeInsets.only(left: 5.0),
                        child: Text('Singh Community',
                          style: Styles.bold(size: 13, color: context.appColors.primaryDark),
                        ),
                      ),
                    ],
                  )),
            ),*/

            Padding(
                padding: widget.description != null
                    ? const EdgeInsets.only(
                        bottom: 7, left: 10, right: 10, top: 13)
                    : const EdgeInsets.only(
                        bottom: 0, left: 10, right: 10, top: 0),
                child: ReadMoreText(
                    // height: height(context) * 0.4,
                    text: widget.description ?? '')),

            Padding(
                padding: Utility().isRTL(context)
                    ? EdgeInsets.only(right: 10.0, bottom: 8.0, top: 8.0)
                    : EdgeInsets.only(left: 10.0, bottom: 8.0, top: 8.0),
                child: Text(
                  widget.viewCount != 0
                      ? '${widget.viewCount} ${tr('views')}'
                      : '',
                  style: Styles.regular(
                      size: 12, color: context.appColors.textBlack),
                )),
            Padding(
              padding: const EdgeInsets.only(top: 1.0, left: 8.0, right: 8.0),
              child: Container(
                color: context.appColors.grey3.withValues(alpha: 0.5),
                height: 0.5,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 10.0,
                horizontal: 10.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  InkWell(
                    onTap: () {
                      setState(() {
                        if (widget.value?.isLiked(widget.index) == true) {
                          updateLikeandViews(0);

                          widget.value?.updateIsLiked(widget.index, 0);

                          widget.value?.decrementLike(widget.index);
                        } else {
                          updateLikeandViews(1);

                          widget.value?.updateIsLiked(widget.index, 1);

                          widget.value?.incrementLike(widget.index);
                        }
                      });
                    },
                    child: Row(
                      children: <Widget>[
                        Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(
                                  left: 4.0,
                                )
                              : EdgeInsets.only(
                                  right: 4.0,
                                ),
                          child: SvgPicture.asset(
                            widget.value?.isLiked(widget.index) == false
                                ? 'assets/images/like_icon.svg'
                                : 'assets/images/liked_icon.svg',
                            height: 18.8,
                            width: 17.86,
                            colorFilter: ColorFilter.mode(
                                widget.value?.isLiked(widget.index) == false
                                    ? context.appColors.headingTitle
                                    : context.appColors.primary,
                                BlendMode.srcIn),
                            // color: widget.value?.isLiked(widget.index) == false
                            //     ? context.appColors.headingTitle
                            //     : context.appColors.primary,

                            allowDrawingOutsideViewBox: true,
                          ),
                        ),
                        // switch( (widget.value?.getLikeCount(widget.index) ?? 0)){
                        //   => return Text('sdf');
                        // }
                        Text(
                          () {
                            int likeCount =
                                (widget.value?.getLikeCount(widget.index) ?? 0);

                            if (likeCount == 0) {
                              return tr('like');
                            }
                            if (likeCount == 1) {
                              return '$likeCount ${tr('like')}';
                            }
                            return '$likeCount ${tr('likes')}';
                          }(),
                          style: Styles.getRegularThemeStyle(
                            context,
                            size: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      showModalBottomSheet(
                          context: context,
                          backgroundColor: context.appColors.surface,
                          isScrollControlled: true,
                          builder: (context) {
                            return FractionallySizedBox(
                              heightFactor: 0.7,
                              child: CommentViewPage(
                                postId: widget.contentId,
                                value: widget.value,
                              ),
                            );
                          });
                    },
                    child: Row(
                      children: <Widget>[
                        Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(
                                  left: 4.0,
                                )
                              : EdgeInsets.only(
                                  right: 4.0,
                                ),
                          child: SvgPicture.asset(
                            'assets/images/comment_icon.svg',
                            height: 18.8,
                            width: 17.86,
                            colorFilter: ColorFilter.mode(
                                context.appColors.textBlack, BlendMode.srcIn),
                            allowDrawingOutsideViewBox: true,
                          ),
                        ),
                        Text(
                          widget.commentCount != 0
                              ? widget.commentCount == 1
                                  ? '${widget.commentCount} ${tr('comment')}'
                                  : '${widget.commentCount} ${tr('comments')}'
                              : ' ${tr('comment')}',
                          style: Styles.regular(
                              size: 12, color: context.appColors.textBlack),
                        ),
                        // if (widget.commentCount > 1 &&
                        //     Preference.getInt(Preference.APP_LANGUAGE) == 1)
                        //   Text(
                        //     Preference.getInt(Preference.APP_LANGUAGE) == 1
                        //         ? 's'
                        //         : '',
                        //     style: Styles.regular(
                        //         size: 12, color: context.appColors.textBlack),
                        //   )
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      SharePlus.instance.share(ShareParams(
                          text:
                              "${APK_DETAILS['domain_url']}g-carvaan/${widget.contentId}"));
                      return;
                      // Share.share('${widget.fileList?[currentPageView]}');
                    },
                    child: Row(
                      children: <Widget>[
                        Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(
                                  left: 4.0,
                                )
                              : EdgeInsets.only(
                                  right: 4.0,
                                ),
                          child: SvgPicture.asset(
                            'assets/images/share_icon.svg',
                            height: 18.8,
                            width: 17.86,
                            colorFilter: ColorFilter.mode(
                                context.appColors.textBlack, BlendMode.srcIn),
                            allowDrawingOutsideViewBox: true,
                          ),
                        ),
                        Text(
                          'share',
                          style: Styles.regular(
                              size: 12, color: context.appColors.textBlack),
                        ).tr()
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void updateLikeandViews(int? like) async {
    BlocProvider.of<HomeBloc>(context).add(LikeContentEvent(
        contentId: widget.contentId, like: like, type: 'contents'));
  }

  void reportPost(
      String? status, int? postId, String category, String comment) {
    BlocProvider.of<HomeBloc>(context).add(ReportEvent(
        status: status, postId: postId, comment: comment, category: category));
  }

  void deletePost(int? postId) {
    BlocProvider.of<HomeBloc>(context).add(DeletePostEvent(postId: postId));
  }

  void deleteHivePost() {
    if (widget.index < 10) {
      List<GCarvaanPostElement>? gcarvaanPosts = box
          ?.get("gcarvaan_post")
          .map(
              (e) => GCarvaanPostElement.fromJson(Map<String, dynamic>.from(e)))
          .cast<GCarvaanPostElement>()
          .toList();

      gcarvaanPosts = gcarvaanPosts
          ?.where((element) => element.id != widget.contentId)
          .toList();
      box?.put("gcarvaan_post", gcarvaanPosts);
      gcarvaanPosts = box?.get("gcarvaan_post");
    }
  }

  void _displayDialog(
      {required BuildContext context,
      final String? imgUrl,
      final List<String>? fileList,
      final int? likeCount,
      final int? viewCount,
      final int? commentCount,
      final int? index,
      final String? desc,
      final String? profilePath,
      final String? userName,
      final String? time}) {
    showGeneralDialog(
      context: context,
      //barrierDismissible: false,
      barrierDismissible: false,
      useRootNavigator: false,
      // barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      transitionDuration: Duration(milliseconds: 200),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: animation,
            child: child,
          ),
        );
      },
      pageBuilder: (context, animation, secondaryAnimation) {
        return Scaffold(
          body: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: context.appColors.surface,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    height: 30.0,
                  ),
                  Container(
                    height: 40.0,
                    margin: EdgeInsets.only(left: 18.0, right: 18.0),
                    child: Align(
                      alignment: Utility().isRTL(context)
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Icon(Icons.close,
                            color: context.appColors.textBlack),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 8.0, right: 8.0, top: 15.0, bottom: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Center(
                          child: ClipOval(
                              child: Image.network(
                            widget.profilePath ?? '',
                            height: 50,
                            width: 50,
                            fit: BoxFit.cover,
                            errorBuilder: (context, url, error) {
                              return SvgPicture.asset(
                                'assets/images/default_user.svg',
                                height: 50,
                                width: 50,
                                allowDrawingOutsideViewBox: true,
                              );
                            },
                            loadingBuilder: (BuildContext context, Widget child,
                                ImageChunkEvent? loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Shimmer.fromColors(
                                baseColor: context.appColors.shimmerBase,
                                highlightColor:
                                    context.appColors.shimmerHighlight,
                                child: Container(
                                    height: 50,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 50,
                                    decoration: BoxDecoration(
                                      color:
                                          context.appColors.primaryForeground,
                                      shape: BoxShape.circle,
                                    )),
                              );
                            },
                          )),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, right: 8.0, top: 2.0),
                                child: Text(
                                  userName ?? '',
                                  style: Styles.getTextRegularThemeStyle(
                                      context,
                                      size: 14),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, right: 8.0),
                                child: Text(
                                  time ?? '',
                                  style: Styles.regular(
                                      size: 10,
                                      color: context.appColors.darkBlue),
                                ),
                              ),
                              /*Padding(
                                padding:
                                    const EdgeInsets.only(bottom: 2, left: 4),
                                child: Text(
                                  desc ?? '',
                                  style: Styles.getTextRegularThemeStyle(context,size: 14),
                                ),
                              ),*/
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(0),
                      child: SizedBox(
                          //height: MediaQuery.of(context).size.height * 0.65,
                          width: MediaQuery.of(context).size.width,
                          child: fileList![index!].isNotEmpty
                              ? fileList[index].contains('.mp4')
                                  ? VisibilityDetector(
                                      key: ObjectKey(flickManager),
                                      onVisibilityChanged: (visibility) {
                                        if (visibility.visibleFraction == 0 &&
                                            mounted) {
                                          flickManager?.flickControlManager
                                              ?.pause(); //pausing  functionality
                                        } else {
                                          flickManager?.flickControlManager
                                              ?.play(); //playing functionality
                                        }
                                      },
                                      child: FlickVideoPlayer(
                                        flickManager: flickManager!,
                                      ),
                                    )
                                  : CachedNetworkImage(
                                      imageUrl: "$imgUrl",
                                      progressIndicatorBuilder:
                                          (context, url, downloadProgress) =>
                                              Shimmer.fromColors(
                                        baseColor:
                                            context.appColors.shimmerBase,
                                        highlightColor:
                                            context.appColors.shimmerHighlight,
                                        child: Container(
                                          // height: double
                                          //     .infinity,
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 10),
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: context
                                                  .appColors.primaryForeground,
                                              borderRadius:
                                                  BorderRadius.circular(6)),
                                        ),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          Icon(Icons.error),
                                    )
                              // Image.network(
                              //     imgUrl!,
                              //     fit: BoxFit.fitWidth,
                              //     loadingBuilder:
                              //         (context, child, loadingProgress) {
                              //       if (loadingProgress == null) {
                              //         return child;
                              //       }
                              //       return Shimmer.fromColors(
                              //         baseColor: Colors.grey[300]!,
                              //         highlightColor: Colors.grey[100]!,
                              //         enabled: true,
                              //         child: Container(
                              //           width: MediaQuery.of(context)
                              //               .size
                              //               .width,
                              //           height: 300,
                              //           color: context.appColors.primaryForeground,
                              //         ),
                              //       );
                              //     },
                              //   )
                              : SizedBox(
                                  // child: Text('no data'),
                                  )),
                    ),
                  ]),
                  Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: Align(
                          alignment: Utility().isRTL(context)
                              ? Alignment.bottomRight
                              : Alignment.bottomLeft,
                          /*child: Text(
                            desc ?? '',
                            style: Styles.getTextRegularThemeStyle(context,size: 14),
                          ),*/
                          child: ReadMoreText(
                              // height: height(context) * 0.4,
                              text: desc ?? ''),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
