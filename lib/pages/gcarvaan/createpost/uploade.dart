import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:get/get.dart';
import 'package:masterg/data/providers/reel_controller.dart';
import 'package:masterg/utils/constant.dart';

import '../../../utils/styles.dart';

class UploadBar extends StatefulWidget {
  const UploadBar({super.key});

  @override
  State<UploadBar> createState() => _UploadBarState();
}

class _UploadBarState extends State<UploadBar> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('upload')),
      body: SingleChildScrollView(
        child: Container(
          color: context.appColors.grey1,
          child: Column(
            children: [
              InkWell(
                onTap: () {
                  final ReelUploadController controller =
                      Get.put(ReelUploadController());

                  Get.rawSnackbar(
                    padding: const EdgeInsets.all(0),
                    messageText: Card(
                      child: Column(
                        children: [
                          Container(
                            height: MediaQuery.of(context).size.height * 0.05,
                            width: MediaQuery.of(context).size.width * 2,
                            padding: EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4),
                              ),
                              gradient: LinearGradient(colors: [
                                context.appColors.gradientLeft,
                                context.appColors.gradientRight,
                              ]),
                            ),
                            child: Align(
                                alignment: Alignment.centerLeft,
                                child: Text('Uploading your file',
                                    style: Styles.regular(
                                        color: context.appColors.textWhite))),
                          ),
                          Container(
                            height: 7,
                            margin: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 6),
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: context.appColors.grey,
                                borderRadius: BorderRadius.circular(10)),
                            child: Stack(
                              children: [
                                Obx(() => Container(
                                      height: 7,
                                      width: MediaQuery.of(context).size.width *
                                          (controller.count.value / 100),
                                      decoration: BoxDecoration(
                                          gradient: LinearGradient(colors: [
                                            context.appColors.gradientLeft,
                                            context.appColors.gradientRight,
                                          ]),
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                    )),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    margin: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 20),
                    snackPosition: SnackPosition.TOP,
                    duration: Duration(minutes: 20),
                    isDismissible: true,
                    backgroundColor: context.appColors.surface,
                    borderRadius: 4,
                    boxShadows: [
                      BoxShadow(
                          color: Color(0xff898989).withValues(alpha: 0.1),
                          offset: Offset(0, 4.0),
                          blurRadius: 11)
                    ],
                  );
                },
                child: Text('open'),
              ),
              uic(context),
            ],
          ),
        ),
      ),
    );
  }
}

Widget uic(context) {
  return Card(
    child: Column(
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.05,
          width: MediaQuery.of(context).size.width * 2,
          padding: EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(4),
            ),
            gradient: LinearGradient(colors: [
              context.appColors.gradientLeft,
              context.appColors.gradientRight,
            ]),
          ),
          child: Align(
              alignment: Alignment.centerLeft,
              child: Text('Uploading your file',
                  style: Styles.regular(
                      color: context.appColors.primaryForeground))),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [Icon(Icons.file_copy), Text('81%')],
          ),
        ),
        Container(
          height: 7,
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              color: context.appColors.grey,
              borderRadius: BorderRadius.circular(10)),
          child: Stack(
            children: [
              Container(
                height: 7,
                width: MediaQuery.of(context).size.width * (20 / 100),
                decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                      context.appColors.gradientLeft,
                      context.appColors.gradientRight,
                    ]),
                    borderRadius: BorderRadius.circular(10)),
              ),
            ],
          ),
        )
      ],
    ),
  );
}

Widget Ui(context) {
  return Container(
    height: MediaQuery.of(context).size.height * 0.16,
    width: MediaQuery.of(context).size.width * 2,
    margin: EdgeInsets.all(10),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.all(
        Radius.circular(10),
      ),
      gradient: LinearGradient(colors: [
        context.appColors.gradientLeft,
        context.appColors.gradientRight,
      ]),
    ),
    child: Column(children: [
      Padding(
        padding: const EdgeInsets.all(8.0),
        child: Align(
          alignment: Alignment.topLeft,
          child: Text(
            'uploading_your_reel_please_wait',
            style: Styles.regular(
                size: 14, color: context.appColors.primaryForeground),
          ).tr(),
        ),
      ),
      Container(
        decoration: BoxDecoration(
          color: context.appColors.surface,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10),
          ),
        ),
        width: width(context),
        height: MediaQuery.of(context).size.height * 0.13,
      )
    ]),
  );
}
