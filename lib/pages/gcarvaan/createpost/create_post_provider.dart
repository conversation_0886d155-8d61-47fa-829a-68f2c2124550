import 'package:flutter/material.dart';
class CreatePostProvider extends ChangeNotifier {
  bool? isUploadingPost;
  List<String?>? files = [];
  int totalMBSize = 0;
CreatePostProvider(List<String?>? files, bool? isUploading) {
   if(isUploadingPost != null)   isUploadingPost = isUploading;

    if (files != null) {
      this.files = files;
    }

    notifyListeners();
  }
   void addSize(int size) {
    totalMBSize += size;
    notifyListeners();
  }

  void subSize(int size) {
    totalMBSize -= size;
    notifyListeners();
  }

  void addToList(String? path) {
    files!.insert(0, path);
    notifyListeners();
  }

  void updateList(List<String?>? list){
    files  = list;
    notifyListeners();
  }

  List<String?>? getFiles(){
    return files;
  }

  void updateAtIndex(String? path, int index){
    files![index] = path;
    notifyListeners();
  }

  void removeFromList(int index) {
    
    files!.removeAt(index);
    notifyListeners();
  }

  void clearList() {
    files!.clear();
    notifyListeners();
  }

  void postStatus(bool isUploading ){
    isUploadingPost = isUploading;
    notifyListeners();
  }

  bool? getPostStatus(){
    return isUploadingPost;
  }
}