// import 'dart:io';
import 'package:dio/dio.dart' as dio;
import 'package:easy_localization/easy_localization.dart';
// import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/create_post_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/providers/reel_controller.dart';
import 'package:masterg/pages/gcarvaan/components/gcarvaan_card_post.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_gcarvaan_page.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:masterg/utils/log.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
// import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';
import '../../../utils/constant.dart';

class GCarvaanPostPage extends StatefulWidget {
  List<dio.MultipartFile>? fileToUpload;
  String? desc;
  List<String?>? filesPath;
  bool? formCreatePost;
  bool fromDashboard;
  bool recentActivities;
  bool fromUserActivity;
  final int? postId;

  GCarvaanPostPage({
    super.key,
    this.fileToUpload,
    this.desc,
    this.filesPath,
    this.formCreatePost,
    this.fromDashboard = false,
    this.recentActivities = false,
    this.fromUserActivity = false,
    this.postId,
  });

  @override
  State<GCarvaanPostPage> createState() => _GCarvaanPostPageState();
}

bool visible = false;
bool isGCarvaanPostLoading = true;

class _GCarvaanPostPageState extends State<GCarvaanPostPage> {
  Box? box;
  List<GCarvaanPostElement>? gcarvaanPosts;
  bool isPostedLoading = false;
  CreatePostResponse? responseData;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int callCount = 0;

  @override
  void initState() {
    super.initState();
    updateApiListner();
    gcarvaanPosts = [];
    if (widget.formCreatePost!) {
      createPost();
    } else {
      _getPosts(++callCount, postId: widget.postId);
    }
  }

  void updateApiListner() {
    ReelUploadController controller = Get.put(ReelUploadController());
    controller.uploadingStatus.listen((status) {
      if (status == UploadingStatus.end) {
        try {
          callCount = 0;
          _getPosts(++callCount);
        } catch (e) {
          Log.v('Something went wrong $e');
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _getPosts(int callCount, {postId}) {
    box = Hive.box(DB.CONTENT);
    if (widget.formCreatePost == false && callCount == 1) {
      try {
        gcarvaanPosts = box!
                .get("gcarvaan_post")
                .map((e) =>
                    GCarvaanPostElement?.fromJson(Map<String, dynamic>.from(e)))
                .cast<GCarvaanPostElement>()
                .toList() ??
            [];

        Future.delayed(Duration(milliseconds: 50)).then((value) {
          _refreshController.requestRefresh();
        });
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
    }
    BlocProvider.of<HomeBloc>(context).add(GCarvaanPostEvent(
        callCount: callCount,
        postId: postId,
        userActivity: widget.fromUserActivity));
  }

  void createPost() {
    if (widget.fileToUpload != null) {
      setState(() {
        isPostedLoading = true;
      });

      BlocProvider.of<HomeBloc>(context).add(CreatePostEvent(
          contentType: 2,
          title: '',
          description: widget.desc,
          postType: 'caravan',
          filePath: widget.filesPath,
          context: context));

      widget.fileToUpload = null;
      widget.desc = null;
      widget.filesPath = null;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider<GCarvaanListModel>(
            create: (context) => GCarvaanListModel(gcarvaanPosts),
          ),
          ChangeNotifierProvider<CreatePostProvider>(
            create: (context) => CreatePostProvider([], null),
          ),
        ],
        child: Scaffold(
            backgroundColor: widget.fromDashboard == true
                ? context.appColors.background
                : context.appColors.background,
            body: SmartRefresher(
              enablePullDown: !widget.fromDashboard,
              enablePullUp: !widget.fromDashboard,
              controller: _refreshController,
              onRefresh: () async {
                callCount = 0;
                gcarvaanPosts = [];
                if (!widget.recentActivities) widget.formCreatePost = true;

                _getPosts(++callCount);
                Future.delayed(Duration(seconds: 2)).then((_) {
                  setState(() {
                    isPostedLoading = false;
                  });
                });
              },
              onLoading: () async {
                _getPosts(++callCount);
              },
              footer: CustomFooter(
                loadStyle: LoadStyle.ShowWhenLoading,
                builder: (context, mode) {
                  if (mode == LoadStatus.loading) {
                    return Container(
                      color: context.appColors.surface,
                      height: 60.0,
                      child: SizedBox(
                        height: 20.0,
                        width: 20.0,
                        child: CupertinoActivityIndicator(),
                      ),
                    );
                  } else {
                    return Container(
                      color: context.appColors.surface,
                    );
                  }
                },
              ),
              header: CustomHeader(
                builder: (context, mode) {
                  return Container(
                    height: 20.0,
                    width: 20.0,
                    padding: EdgeInsets.only(left: 10.0, bottom: 10),
                    child: CupertinoActivityIndicator(
                      radius: 10,
                    ),
                  );
                },
              ),
              child: Consumer2<GCarvaanListModel, CreatePostProvider>(
                builder:
                    (context, carvaanListModel, createPostProvider, child) =>
                        BlocManager(
                  initState: (context) {},
                  child: BlocListener<HomeBloc, HomeState>(
                    listener: (context, state) async {
                      if (state is GCarvaanPostState) {
                        _handleGCarvaanPostResponse(state, carvaanListModel);
                      }
                      if (state is CreatePostState) {
                        _handleCreatePostResponse(
                          state,
                        );
                      }
                    },
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          if (isPostedLoading ||
                              isGCarvaanPostLoading &&
                                  widget.fileToUpload != null)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: MediaQuery.of(context).size.width,
                                  height: 16.0,
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.blue[800]!,
                                    highlightColor: Colors.blue[200]!,
                                    child: Container(
                                      margin: EdgeInsets.only(bottom: 8),
                                      width: MediaQuery.of(context).size.width,
                                      decoration: BoxDecoration(
                                          color: context.appColors.surface,
                                          borderRadius:
                                              BorderRadius.circular(2)),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          if (!widget.fromDashboard)
                            Consumer<CreatePostProvider>(
                              builder: (context, value, child) => Container(
                                width: MediaQuery.of(context).size.width,
                                padding: EdgeInsets.only(
                                    left: 10.0,
                                    top: 10.0,
                                    right: 10.0,
                                    bottom: 10.0),
                                child: widget.recentActivities == false
                                    ? Column(
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) =>
                                                          CreateGCarvaanPage(
                                                            isReelsPost: false,
                                                            fileToUpload: [],
                                                            filesPath:
                                                                value.files,
                                                            provider: value,
                                                          ))).then((info) {
                                                value.clearList();
                                                _refreshController
                                                    .requestRefresh();
                                              });
                                            },
                                            child: Container(
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              padding: EdgeInsets.symmetric(
                                                vertical: 10,
                                                horizontal: 15,
                                              ),
                                              margin: EdgeInsets.symmetric(
                                                vertical: 10,
                                              ),
                                              decoration: BoxDecoration(
                                                  color:
                                                      context.appColors.surface,
                                                  border: Border.all(
                                                      color: context
                                                          .appColors.grey,
                                                      width: 1,
                                                      style: BorderStyle.solid),
                                                  borderRadius:
                                                      BorderRadius.circular(8)),
                                              child: Row(
                                                children: [
                                                  Image.asset(
                                                    'assets/images/create.png',
                                                    height: 30,
                                                    width: 30,
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 5.0),
                                                    child: Text(
                                                            'create_post_community',
                                                            style: Styles.regular(
                                                                color: context
                                                                    .appColors
                                                                    .grey4,
                                                                size: 14))
                                                        .tr(),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                    : SizedBox(),
                              ),
                            ),
                          Container(
                            margin: widget.fromDashboard == true
                                ? null
                                : const EdgeInsets.only(
                                    top: 3,
                                  ),
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            width: double.infinity,
                            color: widget.fromDashboard
                                ? context.appColors.background
                                : context.appColors.background,
                            child: Container(
                              child: _postListWidget(
                                  carvaanListModel.list, carvaanListModel),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            )));
  }

  // void _initFilePiker(CreatePostProvider provider, isVideo) async {
  //   FilePickerResult? result;
  //   if (await Permission.storage.request().isGranted) {
  //     if (Platform.isIOS) {
  //       result = await FilePicker.platform.pickFiles(
  //           allowMultiple: true,
  //           type: isVideo ? FileType.video : FileType.image,
  //           allowedExtensions: []);
  //     } else {
  //       result = await FilePicker.platform.pickFiles(
  //           allowMultiple: true,
  //           type: FileType.custom,
  //           allowedExtensions: isVideo ? ['mp4'] : ['jpg', 'png', 'jpeg']);
  //     }

  //     if (result != null) {
  //       for (int i = 0; i < result.paths.length; i++) {
  //         if (i == 4) break;
  //         provider.addToList(result.paths[i]);
  //       }

  //       if (provider.files!.length > 4) {
  //         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //           content: Text('only_four_files_are_allowed').tr(),
  //         ));
  //       }

  //       Navigator.push(
  //           context,
  //           MaterialPageRoute(
  //               builder: (context) => CreateGCarvaanPage(
  //                   isReelsPost: false,
  //                   fileToUpload: [],
  //                   filesPath: provider.files,
  //                   provider: provider))).then((value) {
  //         provider.postStatus(true);
  //         _refreshController.requestRefresh();
  //       });
  //     }
  //   }
  // }

  Widget _postListWidget(gcarvaanPosts, GCarvaanListModel value) {
    if (value.list?.isEmpty == true && isGCarvaanPostLoading == false) {
      return Container(
          margin:
              EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.3),
          child: Center(
              child: Text('no_content_found',
                      style: Styles.getRegularThemeStyle(context))
                  .tr()));
    }

    return gcarvaanPosts.length != 0 || value.list?.isNotEmpty == false
        ? ListView.builder(
            scrollDirection: Axis.vertical,
            itemCount: gcarvaanPosts == null
                ? 0
                : widget.fromDashboard == true
                    ? 1
                    : gcarvaanPosts.length,
            physics: BouncingScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              int dimesionLen =
                  gcarvaanPosts[index]?.multiFileUploadsDimension != null
                      ? gcarvaanPosts[index]?.multiFileUploadsDimension.length
                      : 0;
              return gcarvaanPosts != null &&
                      gcarvaanPosts[index].resourcePath != null
                  ? GCarvaanCardPost(
                      index: index,
                      value: value,
                      userStatus: gcarvaanPosts[index].userStatus ?? '',
                      imagePath: gcarvaanPosts[index].resourcePath,
                      date: gcarvaanPosts[index].createdAt.toString(),
                      description: gcarvaanPosts[index].description,
                      commentCount: gcarvaanPosts[index].commentCount ?? 0,
                      userName: gcarvaanPosts[index].name,
                      profilePath: gcarvaanPosts[index].profileImage,
                      likeCount: gcarvaanPosts[index].likeCount ?? 0,
                      viewCount: gcarvaanPosts[index].viewCount ?? 0,
                      islikedPost:
                          gcarvaanPosts[index].userLiked == 1 ? true : false,
                      contentId: gcarvaanPosts[index].id,
                      fileList: gcarvaanPosts[index].multiFileUploads,
                      commentVisible: false,
                      // height: gcarvaanPosts[index].dimension.height,
                      dimension: gcarvaanPosts[index].multiFileUploadsDimension,
                      // width: gcarvaanPosts[index].dimension.width,
                      resourceType: gcarvaanPosts[index].resourceType,
                      userID: gcarvaanPosts[index].userId,
                      fromUserActivity: widget.fromUserActivity,
                      aspectRatio: dimesionLen == 0
                          ? 1
                          : gcarvaanPosts[index]
                                  ?.multiFileUploadsDimension
                                  ?.first
                                  .aspectRatio ??
                              1)
                  : Container();
            })
        : _emptyPostListWidget();
  }

  Widget _emptyPostListWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Center(
                child: ClipOval(
                    child: Shimmer.fromColors(
                  baseColor: context.appColors.shimmerBase,
                  highlightColor: context.appColors.shimmerHighlight,
                  child: Container(
                      height: 50,
                      margin: EdgeInsets.only(left: 2),
                      width: 50,
                      decoration: BoxDecoration(
                        color: context.appColors.surface,
                        shape: BoxShape.circle,
                      )),
                )),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8.0,
                        right: 8.0,
                      ),
                      child: Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                            height: 13,
                            margin: EdgeInsets.only(left: 2),
                            width: 150,
                            decoration: BoxDecoration(
                              color: context.appColors.surface,
                            )),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 5.0),
                      child: Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                            height: 12,
                            margin: EdgeInsets.only(left: 2),
                            width: 50,
                            decoration: BoxDecoration(
                              color: context.appColors.surface,
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding:
              const EdgeInsets.only(bottom: 7, left: 4, top: 5.0, right: 20.0),
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 13,
                margin: EdgeInsets.only(left: 2),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
        SizedBox(
          height: 330,
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 330,
                margin: EdgeInsets.only(left: 2),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 0.0, top: 7.0),
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 13,
                margin: EdgeInsets.only(left: 0),
                width: 200,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
        SizedBox(
          height: 15.0,
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Center(
                child: ClipOval(
                    child: Shimmer.fromColors(
                  baseColor: context.appColors.shimmerBase,
                  highlightColor: context.appColors.shimmerHighlight,
                  child: Container(
                      height: 50,
                      margin: EdgeInsets.only(left: 2),
                      width: 50,
                      decoration: BoxDecoration(
                        color: context.appColors.surface,
                        shape: BoxShape.circle,
                      )),
                )),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8.0,
                        right: 8.0,
                      ),
                      child: Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                            height: 13,
                            margin: EdgeInsets.only(left: 2),
                            width: 150,
                            decoration: BoxDecoration(
                              color: context.appColors.surface,
                            )),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0, top: 5.0),
                      child: Shimmer.fromColors(
                        baseColor: context.appColors.shimmerBase,
                        highlightColor: context.appColors.shimmerHighlight,
                        child: Container(
                            height: 12,
                            margin: EdgeInsets.only(left: 2),
                            width: 50,
                            decoration: BoxDecoration(
                              color: context.appColors.surface,
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding:
              const EdgeInsets.only(bottom: 7, left: 4, top: 5.0, right: 20.0),
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 13,
                margin: EdgeInsets.only(left: 2),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
        SizedBox(
          height: 330,
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 330,
                margin: EdgeInsets.only(left: 2),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 0.0, top: 7.0),
          child: Shimmer.fromColors(
            baseColor: context.appColors.shimmerBase,
            highlightColor: context.appColors.shimmerHighlight,
            child: Container(
                height: 13,
                margin: EdgeInsets.only(left: 0),
                width: 200,
                decoration: BoxDecoration(
                  color: context.appColors.surface,
                )),
          ),
        ),
      ],
    );
  }

  void _handleGCarvaanPostResponse(
      GCarvaanPostState state, GCarvaanListModel model) {
    var loginState = state;
    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................");
        isGCarvaanPostLoading = true;
        break;
      case ApiStatus.SUCCESS:
        isPostedLoading = false;
        isGCarvaanPostLoading = false;

        if (callCount == 1) gcarvaanPosts = [];
        if (state.response!.data!.list!.isEmpty) callCount--;
        gcarvaanPosts!.addAll(state.response!.data!.list!);
        model.refreshList(gcarvaanPosts!);

        var seen = Set<GCarvaanPostElement>();
        List<GCarvaanPostElement> uniquelist =
            gcarvaanPosts!.where((element) => seen.add(element)).toList();

        gcarvaanPosts = uniquelist;
        if (model.list != null ||
            model.list!.isEmpty ||
            model.list?.first.id != gcarvaanPosts?.first.id) {
          model.refreshList(gcarvaanPosts!);
        }
        _refreshController.refreshCompleted();
        _refreshController.loadComplete();
        break;

      case ApiStatus.ERROR:
        isGCarvaanPostLoading = false;
        Log.v(
          "Error..........................",
        );
        Log.v("ErrorHome..........................${loginState.error}");
        _refreshController.refreshFailed();
        _refreshController.loadFailed();
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  void _handleCreatePostResponse(CreatePostState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isPostedLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          isPostedLoading = false;
          responseData = state.response;
          if (responseData!.status == 1) {
            isPostedLoading = true;

            _refreshController.requestRefresh();
          }
          break;
        case ApiStatus.ERROR:
          isPostedLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }
}
