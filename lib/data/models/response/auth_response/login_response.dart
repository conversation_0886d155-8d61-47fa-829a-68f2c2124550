import 'dart:convert';

LoginResponse loginResponseFromJson(String str) => LoginResponse.fromJson(json.decode(str));

String loginResponseToJson(LoginResponse data) => json.encode(data.toJson());

class LoginResponse {
    LoginResponse({
        this.status,
        this.error,
        this.data,
        this.message,
    });

    int? status;
    List<String>? error;
    Data? data;
    String? message;

    factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        status: json["status"],
        error: List<String>.from(json["error"].map((x) => x)),
        data: Data.fromJson(json["data"]),
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "error": List<dynamic>.from(error!.map((x) => x)),
        "data": data?.toJson(),
        "message": message,
    };
}

class Data {
    Data({
        this.otp,
    });

    String? otp;

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        otp: json["otp"],
    );

    Map<String, dynamic> toJson() => {
        "otp": otp,
    };
}
