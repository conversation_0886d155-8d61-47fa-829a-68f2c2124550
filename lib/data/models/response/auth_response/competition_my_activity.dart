import 'dart:convert';

CompetitionMyActivityResponse competitionMyActivityResponseFromJson(
        String str) =>
    CompetitionMyActivityResponse.fromJson(json.decode(str));

String competitionMyActivityResponseToJson(
        CompetitionMyActivityResponse data) =>
    json.encode(data.toJson());

class CompetitionMyActivityResponse {
  CompetitionMyActivityResponse({
    required this.status,
    required this.data,
    required this.error,
  });

  int status;
  List<CompetitionActivityElement> data;
  List<dynamic> error;

  factory CompetitionMyActivityResponse.fromJson(Map<String, dynamic> json) =>
      CompetitionMyActivityResponse(
        status: json["status"],
        data: List<CompetitionActivityElement>.from(
            json["data"].map((x) => CompetitionActivityElement.fromJson(x))),
        error: List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "error": List<dynamic>.from(error.map((x) => x)),
      };
}

class CompetitionActivityElement {
  CompetitionActivityElement(
      {required this.id,
      required this.name,
      this.status,
      required this.totalActivitiesCompleted,
      this.activityStatus,
      required this.pImage,
      required this.totalContents,
      this.desc,
      this.competitionLevel,
      this.organizedBy,
      this.starDate,
      this.endDate,
      this.pName,
      this.gscore,
      this.activityStatusNumeric,
      this.totalCompeleted,
      this.score,
      this.progressStatus,
      this.eventStatus
      });

  int id;
  String? name;
  String? status;
  int totalActivitiesCompleted;
  dynamic activityStatus;
  String pImage;
  String? pName;
  int totalContents;
  String? desc;
  String? competitionLevel;
  String? organizedBy;
  String? starDate;
  String? endDate;
  int? gscore;
  dynamic score;
  int? activityStatusNumeric;
  int? totalCompeleted;
  String? progressStatus;
  String? eventStatus;

  factory CompetitionActivityElement.fromJson(Map<String, dynamic> json) =>
      CompetitionActivityElement(
          id: json["id"],
          name: json["name"],
          status: json["status"],
          totalActivitiesCompleted: json["total_activities_completed"],
          activityStatus: json["activity_status"],
          pImage: json["p_image"],
          totalContents: json["total_contents"],
          desc: json['description'],
          competitionLevel: json['competition_level'],
          organizedBy: json['organized_by'],
          starDate: json['start_date'],
          gscore: json['g_score'],
          pName: json['p_name'],
          score: json['score'],
          activityStatusNumeric: json['activity_status_numeric'],
          totalCompeleted:  json['total_activities_completed'],
          progressStatus:  json['progress_status'],
          eventStatus :json[ 'event_status' ],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "status": status,
        "total_activities_completed": totalActivitiesCompleted,
        "activity_status": activityStatus,
        "p_image": pImage,
        "total_contents": totalContents,
        "description" :desc ,
        "competition_level" : competitionLevel,
        "organized_by" : organizedBy,
        "start_date" : starDate,
        "end_date" : endDate,
        "g_score" : gscore,
        "score": score,
        "activity_status_numeric" : activityStatusNumeric,
        "p_name" :pName,
        "progress_status" :progressStatus,
        "event_status": "Upcoming"

      };
}
