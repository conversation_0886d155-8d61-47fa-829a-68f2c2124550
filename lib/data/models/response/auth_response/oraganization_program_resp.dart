// // To parse this JSON data, do
// //
// //     final organizationProgramListResp = organizationProgramListRespFromJson(jsonString);

// import 'dart:convert';

// class OrganizationProgramListResp {
//   int? status;
//   List<OragnizationProgram>? data;
//   List<dynamic>? error;

//   OrganizationProgramListResp({
//     this.status,
//     this.data,
//     this.error,
//   });

//   factory OrganizationProgramListResp.fromRawJson(String str) =>
//       OrganizationProgramListResp.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory OrganizationProgramListResp.fromJson(Map<String, dynamic> json) =>
//       OrganizationProgramListResp(
//         status: json["status"],
//         data: json["data"] == null
//             ? []
//             : List<OragnizationProgram>.from(
//                 json["data"]!.map((x) => OragnizationProgram.fromJson(x))),
//         error: json["error"] == null
//             ? []
//             : List<dynamic>.from(json["error"]!.map((x) => x)),
//       );

//   Map<String, dynamic> toJson() => {
//         "status": status,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//         "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
//       };
// }

// class OragnizationProgram {
//   String? interestarea;
//   String? iconUrl;

//   OragnizationProgram({this.interestarea, this.iconUrl});

//   factory OragnizationProgram.fromRawJson(String str) =>
//       OragnizationProgram.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory OragnizationProgram.fromJson(Map<String, dynamic> json) =>
//       OragnizationProgram(
//         interestarea: json["interestarea"],
//         iconUrl: json["icon_url"],
//       );

//   Map<String, dynamic> toJson() =>
//       {"interestarea": interestarea, "icon_url": iconUrl};
// }

// To parse this JSON data, do
//
//     final organizationProgramListResp = organizationProgramListRespFromJson(jsonString);

import 'dart:convert';

OrganizationProgramListResp organizationProgramListRespFromJson(String str) =>
    OrganizationProgramListResp.fromJson(json.decode(str));

String organizationProgramListRespToJson(OrganizationProgramListResp data) =>
    json.encode(data.toJson());

class OrganizationProgramListResp {
  int? status;
  List<OragnizationProgram>? data;
  List<dynamic>? error;

  OrganizationProgramListResp({
    this.status,
    this.data,
    this.error,
  });

  factory OrganizationProgramListResp.fromJson(Map<String, dynamic> json) =>
      OrganizationProgramListResp(
        status: json["status"],
        data: json["data"] == null
            ? []
            : List<OragnizationProgram>.from(
                json["data"]!.map((x) => OragnizationProgram.fromJson(x))),
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class OragnizationProgram {
  int? id;
  String? interestarea;
  String? addeddate;
  String? addedby;
  String? lastupdateDate;
  String? lastupdateby;
  String? iconUrl;
  dynamic refUrl;
  String? industryDomain;
  String? interestareaDescription;
  String? status;
  dynamic image;
  dynamic isMapped;

  OragnizationProgram({
    this.id,
    this.interestarea,
    this.addeddate,
    this.addedby,
    this.lastupdateDate,
    this.lastupdateby,
    this.iconUrl,
    this.refUrl,
    this.industryDomain,
    this.interestareaDescription,
    this.status,
    this.image,
    this.isMapped,
  });

  factory OragnizationProgram.fromJson(Map<String, dynamic> json) =>
      OragnizationProgram(
        id: json["id"],
        interestarea: json["name"], //for digital domain
        // interestarea: json["interestarea"], // singularis  domain
        addeddate: json["addeddate"],
        addedby: json["addedby"],
        lastupdateDate: json["lastupdate_date"],
        lastupdateby: json["lastupdateby"],
        iconUrl: json["icon_url"],
        refUrl: json["ref_url"],
        industryDomain: json["industry_domain"],
        interestareaDescription: json["description"],
        status: json["status"],
        image: json["image"],
        isMapped: json["is_mapped"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "interestarea": interestarea,
        "addeddate": addeddate,
        "addedby": addedby,
        "lastupdate_date": lastupdateDate,
        "lastupdateby": lastupdateby,
        "icon_url": iconUrl,
        "ref_url": refUrl,
        "industry_domain": industryDomain,
        "interestarea_description": interestareaDescription,
        "status": status,
        "image": image,
        "is_mapped": isMapped,
      };
}
