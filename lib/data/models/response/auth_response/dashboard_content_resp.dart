import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:masterg/data/models/response/auth_response/oraganization_program_resp.dart';

class DashboardContentResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  DashboardContentResponse({
    this.status,
    this.data,
    this.error,
  });

  DashboardContentResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'] as int?;
    data = (json['data'] as Map<String, dynamic>?) != null
        ? Data.fromJson(json['data'] as Map<String, dynamic>)
        : null;
    error = json['error'] as List?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['status'] = status;
    json['data'] = data?.toJson();
    json['error'] = error;
    return json;
  }
}

class Data {
  List<DashboardRecommendedCoursesLimit>? dashboardRecommendedCoursesLimit;
   List<MatchingJobs>? matchingJobs;
  List<DashboardReelsLimit>? dashboardReelsLimit;
  List<DashboardCarvanLimit>? dashboardCarvanLimit;
  List<DashboardFeaturedContentLimit>? dashboardFeaturedContentLimit;
  List<DashboardMyCoursesLimit>? dashboardMyCoursesLimit;
  List<DashboardSessionsLimit>? dashboardSessionsLimit;
  ToDoActivities? todoActivitiesLimit;
  List<JobDashboard>? jobDashboard;
  List<OragnizationProgram>? interestArea;
  List<JobDomain>? jobDomain;
  List<FutureTrends>? futureTrends;
  List<Banners>? banners;
  dynamic bannerLink;
  String? bannerUrl;
  String? bannerType;
  String? learnUrl;
  int? enableMecat;
  String? jobRoleSelected;
  int? jobRoleId;
  Map<String, UserSkillAssessment>? userSkillAssessment;

  Data(
      {this.dashboardRecommendedCoursesLimit,
       this.matchingJobs,
      this.dashboardReelsLimit,
      this.dashboardCarvanLimit,
      this.dashboardFeaturedContentLimit,
      this.dashboardMyCoursesLimit,
      this.dashboardSessionsLimit,
      this.todoActivitiesLimit,
      this.jobDashboard,
      this.interestArea,
      this.jobDomain,
      this.bannerLink,
      this.bannerUrl,
      this.futureTrends,
      this.banners,
      this.bannerType,
      this.enableMecat,
      this.learnUrl,
      this.jobRoleSelected,
      this.jobRoleId,
      this.userSkillAssessment,});

  Data.fromJson(Map<String, dynamic> json) {
    dashboardRecommendedCoursesLimit =
        (json['dashboard_recommended_courses_limit'] as List?)
            ?.map((dynamic e) => DashboardRecommendedCoursesLimit.fromJson(
                e as Map<String, dynamic>))
            .toList();
             matchingJobs = (json['matching_jobs'] as List?) ?.map((dynamic e) => MatchingJobs.fromJson(e as Map<String, dynamic>)).toList() ;

            //  matchingJobs: json["matching_jobs"] == null ? [] : List<Event>.from(json["matching_jobs"]!.map((x) => Event.fromJson(x)));
    dashboardReelsLimit = (json['dashboard_reels_limit'] as List?)
        ?.map((dynamic e) =>
            DashboardReelsLimit.fromJson(e as Map<String, dynamic>))
        .toList();
    dashboardCarvanLimit = (json['dashboard_carvan_limit'] as List?)
        ?.map((dynamic e) =>
            DashboardCarvanLimit.fromJson(e as Map<String, dynamic>))
        .toList();
    dashboardFeaturedContentLimit = (json['dashboard_featured_content_limit']
            as List?)
        ?.map((dynamic e) =>
            DashboardFeaturedContentLimit.fromJson(e as Map<String, dynamic>))
        .toList();
    dashboardMyCoursesLimit = (json['dashboard_my_courses_limit'] as List?)
        ?.map((dynamic e) =>
            DashboardMyCoursesLimit.fromJson(e as Map<String, dynamic>))
        .toList();
    dashboardSessionsLimit = (json['dashboard_sessions_limit'] as List?)
        ?.map((dynamic e) =>
            DashboardSessionsLimit.fromJson(e as Map<String, dynamic>))
        .toList();
    todoActivitiesLimit = ToDoActivities.fromJson(json["to_do_activities"]);
    jobDashboard = (json['job_dashboard'] as List?)
        ?.map((dynamic e) => JobDashboard.fromJson(e as Map<String, dynamic>))
        .toList();
    interestArea = (json['interest_area'] as List?)
        ?.map((dynamic e) =>
            OragnizationProgram.fromJson(e as Map<String, dynamic>))
        .toList();

    jobDomain = (json['job_domain'] as List?)
        ?.map((dynamic e) => JobDomain.fromJson(e as Map<String, dynamic>))
        .toList();

    futureTrends = (json['future_trends'] as List?)
        ?.map((dynamic e) => FutureTrends.fromJson(e as Map<String, dynamic>))
        .toList();

    banners = (json['banners'] as List?)
        ?.map((dynamic e) => Banners.fromJson(e as Map<String, dynamic>))
        .toList();

    bannerLink = json['banner_link'];
    bannerUrl = json['banner_url'] as String?;
    bannerType = json['banner_type'] as String?;
    enableMecat = json['enable_mecat'] as int?;
    learnUrl = json['learn_url'] as String?;
    jobRoleSelected = json['jobrole_selected'] as String?;
    jobRoleId = json['jobrole_id'] as int?;

    var userSkillAssessmentData = json['user_skill_assessment'];
    if(userSkillAssessmentData is List && userSkillAssessmentData.isEmpty){
    }else{
      userSkillAssessment = (json['user_skill_assessment'] as Map<String, dynamic>)
          .map((key, value) => MapEntry(
          key, UserSkillAssessment.fromJson(value as Map<String, dynamic>)));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['dashboard_recommended_courses_limit'] =
        dashboardRecommendedCoursesLimit?.map((e) => e.toJson()).toList();
        // "matching_jobs": matchingJobs == null ? [] : List<dynamic>.from(matchingJobs!.map((x) => x.toJson()));
    json['matching_jobs'] = matchingJobs?.map((e) => e.toJson()).toList();
    json['dashboard_reels_limit'] =
        dashboardReelsLimit?.map((e) => e.toJson()).toList();
    json['dashboard_carvan_limit'] =
        dashboardCarvanLimit?.map((e) => e.toJson()).toList();
    json['dashboard_featured_content_limit'] =
        dashboardFeaturedContentLimit?.map((e) => e.toJson()).toList();
    json['dashboard_my_courses_limit'] =
        dashboardMyCoursesLimit?.map((e) => e.toJson()).toList();
    json['dashboard_sessions_limit'] =
        dashboardSessionsLimit?.map((e) => e.toJson()).toList();
    json["to_do_activities"] = todoActivitiesLimit?.toJson();
    json['job_dashboard'] = jobDashboard?.map((e) => e.toJson()).toList();
    json['interest_area'] = interestArea?.map((e) => e.toJson()).toList();
    json['job_domain'] = jobDomain?.map((e) => e.toJson()).toList();
    json['future_trends'] = futureTrends?.map((e) => e.toJson()).toList();
    json['banner_link'] = bannerLink;
    json['banner_url'] = bannerUrl;
    json['banner_type'] = bannerType;
    json['enable_mecat'] = enableMecat;
    json['learn_url'] = learnUrl;
    json['user_skill_assessment'] = userSkillAssessment;
    json['jobrole_selected'] = jobRoleSelected;
    json['jobrole_id'] = jobRoleId;
    return json;
  }
}

class DashboardRecommendedCoursesLimit {
  String? image;
  int? id;
  int? organizationId;
  String? name;
  String? progName;
  String? description;
  String? progDesc;
  int? isGlobal;
  int? gScore;
  dynamic viewCount;
  int? categoryId;
  String? categoryName;
  double? regularPrice;
  double? salePrice;
  String? shortCode;
  int? admissionStartDate;
  int? admissionEndDate;
  dynamic type;
  int? startDate;
  int? endDate;
  String? duration;
  String? contents;
  dynamic totalCoins;
  String? subscriptionType;
  bool? isSubscribed;
  String? approvalStatus;
  String? trainer;
  int? enrolmentCount;
  dynamic totalView;
  double? completionPer;

  DashboardRecommendedCoursesLimit({
    this.image,
    this.id,
    this.organizationId,
    this.name,
    this.progName,
    this.description,
    this.progDesc,
    this.isGlobal,
    this.gScore,
    this.viewCount,
    this.categoryId,
    this.categoryName,
    this.regularPrice,
    this.salePrice,
    this.shortCode,
    this.admissionStartDate,
    this.admissionEndDate,
    this.type,
    this.startDate,
    this.endDate,
    this.duration,
    this.contents,
    this.totalCoins,
    this.subscriptionType,
    this.isSubscribed,
    this.approvalStatus,
    this.trainer,
    this.enrolmentCount,
    this.totalView,
    this.completionPer,
  });

  DashboardRecommendedCoursesLimit.fromJson(Map<String, dynamic> json) {
    image = json['image'] as String?;
    id = json['id'] as int?;
    organizationId = json['organization_id'] as int?;
    name = json['name'] as String?;
    progName = json['prog_name'] as String?;
    description = json['description'] as String?;
    progDesc = json['prog_desc'] as String?;
    isGlobal = json['is_global'] as int?;
    gScore = json['g_score'] as int?;
    viewCount = json['view_count'];
    categoryId = json['category_id'] as int?;
    categoryName = json['category_name'] as String?;
    //regularPrice = json['regular_price'] as double?;
    regularPrice = json["regular_price"] == null ? null : json["regular_price"];
    salePrice = json["sale_price"] == null ? null : json["sale_price"];
    //salePrice = json['sale_price'] as double?;
    shortCode = json['short_code'] as String?;
    admissionStartDate = json['admission_start_date'] as int?;
    admissionEndDate = json['admission_end_date'] as int?;
    type = json['type'];
    startDate = json['start_date'] as int?;
    endDate = json['end_date'] as int?;
    duration = json['duration'] as String?;
    contents = json['contents'] as String?;
    totalCoins = json['total_coins'];
    subscriptionType = json['subscription_type'] as String?;
    isSubscribed = json['is_subscribed'] as bool?;
    approvalStatus = json['approval_status'] as String?;
    trainer = json['trainer'] as String?;
    enrolmentCount = json['enrolment_count'] as int?;
    totalView = json['total_view'];
    completionPer = json['completion_per'].toDouble();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['image'] = image;
    json['id'] = id;
    json['organization_id'] = organizationId;
    json['name'] = name;
    json['prog_name'] = progName;
    json['description'] = description;
    json['prog_desc'] = progDesc;
    json['is_global'] = isGlobal;
    json['g_score'] = gScore;
    json['view_count'] = viewCount;
    json['category_id'] = categoryId;
    json['category_name'] = categoryName;
    json['regular_price'] = regularPrice == null ? null : regularPrice;
    //json['regular_price'] = regularPrice;
    //json['sale_price'] = salePrice;
    json['sale_price'] = salePrice == null ? null : salePrice;
    json['short_code'] = shortCode;
    json['admission_start_date'] = admissionStartDate;
    json['admission_end_date'] = admissionEndDate;
    json['type'] = type;
    json['start_date'] = startDate;
    json['end_date'] = endDate;
    json['duration'] = duration;
    json['contents'] = contents;
    json['total_coins'] = totalCoins;
    json['subscription_type'] = subscriptionType;
    json['is_subscribed'] = isSubscribed;
    json['approval_status'] = approvalStatus;
    json['trainer'] = trainer;
    json['enrolment_count'] = enrolmentCount;
    json['total_view'] = totalView;
    json['completion_per'] = completionPer;
    return json;
  }
}

class DashboardReelsLimit {
  int? id;
  String? title;
  String? description;
  int? createdAt;
  int? createdBy;
  int? updatedAt;
  int? updatedBy;
  String? status;
  int? parentId;
  int? categoryId;
  String? contentType;
  String? resourcePath;
  String? language;
  dynamic tag;
  int? likeCount;
  int? programContentId;
  int? startDate;
  int? endDate;
  int? isMultilingual;
  int? visibilityValue;
  int? visibility;
  //Dimension? dimension;
  List<String>? multiFileUploads;
  int? viewCount;
  dynamic multipleFileUpload;
  int? userId;
  String? name;
  String? email;
  String? profileImage;
  String? userStatus;
  dynamic userLikeTrackingsId;
  int? userLiked;
  String? resourceType;
  List<dynamic>? multiFileUploadsCount;
  String? thumbnailUrl;
  int? isAttempt;
  String? userSubmittedFile;
  List<dynamic>? userSubmittedMultipleFile;

  DashboardReelsLimit({
    this.id,
    this.title,
    this.description,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.status,
    this.parentId,
    this.categoryId,
    this.contentType,
    this.resourcePath,
    this.language,
    this.tag,
    this.likeCount,
    this.programContentId,
    this.startDate,
    this.endDate,
    this.isMultilingual,
    this.visibilityValue,
    this.visibility,
    //this.dimension,
    this.multiFileUploads,
    this.viewCount,
    this.multipleFileUpload,
    this.userId,
    this.name,
    this.email,
    this.profileImage,
    this.userStatus,
    this.userLikeTrackingsId,
    this.userLiked,
    this.resourceType,
    this.multiFileUploadsCount,
    this.thumbnailUrl,
    this.isAttempt,
    this.userSubmittedFile,
    this.userSubmittedMultipleFile,
  });

  DashboardReelsLimit.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    title = json['title'] as String?;
    description = json['description'] as String?;
    createdAt = json['created_at'] as int?;
    createdBy = json['created_by'] as int?;
    updatedAt = json['updated_at'] as int?;
    updatedBy = json['updated_by'] as int?;
    status = json['status'] as String?;
    parentId = json['parent_id'] as int?;
    categoryId = json['category_id'] as int?;
    contentType = json['content_type'] as String?;
    resourcePath = json['resource_path'] as String?;
    language = json['language'] as String?;
    tag = json['tag'];
    likeCount = json['like_count'] as int?;
    programContentId = json['program_content_id'] as int?;
    startDate = json['start_date'] as int?;
    endDate = json['end_date'] as int?;
    isMultilingual = json['is_multilingual'] as int?;
    visibilityValue = json['visibility_value'] as int?;
    visibility = json['visibility'] as int?;
    //dimension = (json['dimension'] as Map<String,dynamic>?) != null ? Dimension.fromJson(json['dimension'] as Map<String,dynamic>) : null;
    multiFileUploads = (json['multi_file_uploads'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    viewCount = json['view_count'] as int?;
    multipleFileUpload = json['multiple_file_upload'];
    userId = json['user_id'] as int?;
    name = json['name'] as String?;
    email = json['email'] as String?;
    profileImage = json['profile_image'] as String?;
    userStatus = json['user_status'] as String?;
    userLikeTrackingsId = json['user_like_trackings_id'];
    userLiked = json['user_liked'] as int?;
    resourceType = json['resource_type'] as String?;
    multiFileUploadsCount = json['multi_file_uploads_count'] as List?;
    thumbnailUrl = json['thumbnail_url'] as String?;
    isAttempt = json['is_attempt'] as int?;
    userSubmittedFile = json['user_submitted_file'] as String?;
    userSubmittedMultipleFile = json['user_submitted_multiple_file'] as List?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['title'] = title;
    json['description'] = description;
    json['created_at'] = createdAt;
    json['created_by'] = createdBy;
    json['updated_at'] = updatedAt;
    json['updated_by'] = updatedBy;
    json['status'] = status;
    json['parent_id'] = parentId;
    json['category_id'] = categoryId;
    json['content_type'] = contentType;
    json['resource_path'] = resourcePath;
    json['language'] = language;
    json['tag'] = tag;
    json['like_count'] = likeCount;
    json['program_content_id'] = programContentId;
    json['start_date'] = startDate;
    json['end_date'] = endDate;
    json['is_multilingual'] = isMultilingual;
    json['visibility_value'] = visibilityValue;
    json['visibility'] = visibility;
    //json['dimension'] = dimension?.toJson();
    json['multi_file_uploads'] = multiFileUploads;
    json['view_count'] = viewCount;
    json['multiple_file_upload'] = multipleFileUpload;
    json['user_id'] = userId;
    json['name'] = name;
    json['email'] = email;
    json['profile_image'] = profileImage;
    json['user_status'] = userStatus;
    json['user_like_trackings_id'] = userLikeTrackingsId;
    json['user_liked'] = userLiked;
    json['resource_type'] = resourceType;
    json['multi_file_uploads_count'] = multiFileUploadsCount;
    json['thumbnail_url'] = thumbnailUrl;
    json['is_attempt'] = isAttempt;
    json['user_submitted_file'] = userSubmittedFile;
    json['user_submitted_multiple_file'] = userSubmittedMultipleFile;
    return json;
  }
}

class Dimension {
  int? height;
  int? width;

  Dimension({
    this.height,
    this.width,
  });

  Dimension.fromJson(Map<String, dynamic> json) {
    height = json['height'] as int?;
    width = json['width'] as int?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['height'] = height;
    json['width'] = width;
    return json;
  }
}

class DashboardCarvanLimit {
  int? id;
  String? title;
  String? description;
  int? createdAt;
  int? createdBy;
  int? updatedAt;
  int? updatedBy;
  String? status;
  int? parentId;
  int? categoryId;
  String? contentType;
  String? resourcePath;
  String? language;
  dynamic tag;
  int? likeCount;
  int? commentCount;
  int? programContentId;
  int? startDate;
  int? endDate;
  int? isMultilingual;
  int? visibilityValue;
  int? visibility;
  Dimension? dimension;
  List<String>? multiFileUploads;
  int? viewCount;
  dynamic multipleFileUpload;
  int? userId;
  String? name;
  String? email;
  String? profileImage;
  String? userStatus;
  dynamic userLikeTrackingsId;
  int? userLiked;
  String? resourceType;
  List<dynamic>? multiFileUploadsCount;
  List<dynamic>? multiFileUploadsDimension;
  String? thumbnailUrl;
  int? isAttempt;
  String? userSubmittedFile;
  List<dynamic>? userSubmittedMultipleFile;

  DashboardCarvanLimit({
    this.id,
    this.title,
    this.description,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.status,
    this.parentId,
    this.categoryId,
    this.contentType,
    this.resourcePath,
    this.language,
    this.tag,
    this.likeCount,
    this.commentCount,
    this.programContentId,
    this.startDate,
    this.endDate,
    this.isMultilingual,
    this.visibilityValue,
    this.visibility,
    this.dimension,
    this.multiFileUploads,
    this.viewCount,
    this.multipleFileUpload,
    this.userId,
    this.name,
    this.email,
    this.profileImage,
    this.userStatus,
    this.userLikeTrackingsId,
    this.userLiked,
    this.resourceType,
    this.multiFileUploadsCount,
    this.multiFileUploadsDimension,
    this.thumbnailUrl,
    this.isAttempt,
    this.userSubmittedFile,
    this.userSubmittedMultipleFile,
  });

  DashboardCarvanLimit.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    title = json['title'] as String?;
    description = json['description'] as String?;
    createdAt = json['created_at'] as int?;
    createdBy = json['created_by'] as int?;
    updatedAt = json['updated_at'] as int?;
    updatedBy = json['updated_by'] as int?;
    status = json['status'] as String?;
    parentId = json['parent_id'] as int?;
    categoryId = json['category_id'] as int?;
    contentType = json['content_type'] as String?;
    resourcePath = json['resource_path'] as String?;
    language = json['language'] as String?;
    tag = json['tag'];
    likeCount = json['like_count'] as int?;
    commentCount = json['comment_count'] as int?;
    programContentId = json['program_content_id'] as int?;
    startDate = json['start_date'] as int?;
    endDate = json['end_date'] as int?;
    isMultilingual = json['is_multilingual'] as int?;
    visibilityValue = json['visibility_value'] as int?;
    visibility = json['visibility'] as int?;
    dimension = (json['dimension'] as Map<String, dynamic>?) != null
        ? Dimension.fromJson(json['dimension'] as Map<String, dynamic>)
        : null;
    multiFileUploads = (json['multi_file_uploads'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    viewCount = json['view_count'] as int?;
    multipleFileUpload = json['multiple_file_upload'];
    userId = json['user_id'] as int?;
    name = json['name'] as String?;
    email = json['email'] as String?;
    profileImage = json['profile_image'] as String?;
    userStatus = json['user_status'] as String?;
    userLikeTrackingsId = json['user_like_trackings_id'];
    userLiked = json['user_liked'] as int?;
    resourceType = json['resource_type'] as String?;
    multiFileUploadsCount = json['multi_file_uploads_count'] as List?;
    multiFileUploadsDimension = json['multi_file_uploads_dimension'] as List?;
    thumbnailUrl = json['thumbnail_url'] as String?;
    isAttempt = json['is_attempt'] as int?;
    userSubmittedFile = json['user_submitted_file'] as String?;
    userSubmittedMultipleFile = json['user_submitted_multiple_file'] as List?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['title'] = title;
    json['description'] = description;
    json['created_at'] = createdAt;
    json['created_by'] = createdBy;
    json['updated_at'] = updatedAt;
    json['updated_by'] = updatedBy;
    json['status'] = status;
    json['parent_id'] = parentId;
    json['category_id'] = categoryId;
    json['content_type'] = contentType;
    json['resource_path'] = resourcePath;
    json['language'] = language;
    json['tag'] = tag;
    json['like_count'] = likeCount;
    json['comment_count'] = commentCount;
    json['program_content_id'] = programContentId;
    json['start_date'] = startDate;
    json['end_date'] = endDate;
    json['is_multilingual'] = isMultilingual;
    json['visibility_value'] = visibilityValue;
    json['visibility'] = visibility;
    //json['dimension'] = dimension?.toJson();
    json['multi_file_uploads'] = multiFileUploads;
    json['view_count'] = viewCount;
    json['multiple_file_upload'] = multipleFileUpload;
    json['user_id'] = userId;
    json['name'] = name;
    json['email'] = email;
    json['profile_image'] = profileImage;
    json['user_status'] = userStatus;
    json['user_like_trackings_id'] = userLikeTrackingsId;
    json['user_liked'] = userLiked;
    json['resource_type'] = resourceType;
    json['multi_file_uploads_count'] = multiFileUploadsCount;
    json['multi_file_uploads_dimension'] = multiFileUploadsDimension;
    json['thumbnail_url'] = thumbnailUrl;
    json['is_attempt'] = isAttempt;
    json['user_submitted_file'] = userSubmittedFile;
    json['user_submitted_multiple_file'] = userSubmittedMultipleFile;
    return json;
  }
}

/*class Dimension {
  int? height;
  int? width;

  Dimension({
    this.height,
    this.width,
  });

  Dimension.fromJson(Map<String, dynamic> json) {
    height = json['height'] as int?;
    width = json['width'] as int?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['height'] = height;
    json['width'] = width;
    return json;
  }
}*/

class DashboardFeaturedContentLimit {
  int? id;
  String? title;
  String? description;
  int? createdAt;
  int? createdBy;
  int? updatedAt;
  int? updatedBy;
  String? status;
  int? parentId;
  int? categoryId;
  String? contentType;
  String? resourcePath;
  String? resourcePathThumbnail;
  String? language;
  dynamic tag;
  dynamic totalLikes;
  int? programContentId;
  dynamic template;
  int? startDate;
  int? endDate;
  int? isMultilingual;
  int? visibilityValue;
  int? visibility;
  String? multiFileUploads;
  dynamic multipleFileUpload;
  int? viewCount;
  int? likeCount;
  int? commentCount;
  int? isFeatured;
  dynamic userLikeTrackingsId;
  dynamic actionUrl;
  String? youtubeUrl;

  DashboardFeaturedContentLimit({
    this.id,
    this.title,
    this.description,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.status,
    this.parentId,
    this.categoryId,
    this.contentType,
    this.resourcePath,
    this.resourcePathThumbnail,
    this.language,
    this.tag,
    this.totalLikes,
    this.programContentId,
    this.template,
    this.startDate,
    this.endDate,
    this.isMultilingual,
    this.visibilityValue,
    this.visibility,
    this.multiFileUploads,
    this.multipleFileUpload,
    this.viewCount,
    this.likeCount,
    this.commentCount,
    this.isFeatured,
    this.userLikeTrackingsId,
    this.actionUrl,
    this.youtubeUrl,
  });

  DashboardFeaturedContentLimit.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    title = json['title'] as String?;
    description = json['description'] as String?;
    createdAt = json['created_at'] as int?;
    createdBy = json['created_by'] as int?;
    updatedAt = json['updated_at'] as int?;
    updatedBy = json['updated_by'] as int?;
    status = json['status'] as String?;
    parentId = json['parent_id'] as int?;
    categoryId = json['category_id'] as int?;
    contentType = json['content_type'] as String?;
    resourcePath = json['resource_path'] as String?;
    resourcePathThumbnail = json['resource_path_thumbnail'] as String?;
    language = json['language'] as String?;
    tag = json['tag'];
    totalLikes = json['total_likes'];
    programContentId = json['program_content_id'] as int?;
    template = json['template'];
    startDate = json['start_date'] as int?;
    endDate = json['end_date'] as int?;
    isMultilingual = json['is_multilingual'] as int?;
    visibilityValue = json['visibility_value'] as int?;
    visibility = json['visibility'] as int?;
    multiFileUploads = json['multi_file_uploads'] as String?;
    multipleFileUpload = json['multiple_file_upload'];
    viewCount = json['view_count'] as int?;
    likeCount = json['like_count'] as int?;
    commentCount = json['comment_count'] as int?;
    isFeatured = json['is_featured'] as int?;
    userLikeTrackingsId = json['user_like_trackings_id'];
    actionUrl = json['action_url'];
    youtubeUrl = json['youtube_url'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['title'] = title;
    json['description'] = description;
    json['created_at'] = createdAt;
    json['created_by'] = createdBy;
    json['updated_at'] = updatedAt;
    json['updated_by'] = updatedBy;
    json['status'] = status;
    json['parent_id'] = parentId;
    json['category_id'] = categoryId;
    json['content_type'] = contentType;
    json['resource_path'] = resourcePath;
    json['resource_path_thumbnail'] = resourcePathThumbnail;
    json['language'] = language;
    json['tag'] = tag;
    json['total_likes'] = totalLikes;
    json['program_content_id'] = programContentId;
    json['template'] = template;
    json['start_date'] = startDate;
    json['end_date'] = endDate;
    json['is_multilingual'] = isMultilingual;
    json['visibility_value'] = visibilityValue;
    json['visibility'] = visibility;
    json['multi_file_uploads'] = multiFileUploads;
    json['multiple_file_upload'] = multipleFileUpload;
    json['view_count'] = viewCount;
    json['like_count'] = likeCount;
    json['comment_count'] = commentCount;
    json['is_featured'] = isFeatured;
    json['user_like_trackings_id'] = userLikeTrackingsId;
    json['action_url'] = actionUrl;
    json['youtube_url'] = youtubeUrl;
    return json;
  }
}

class DashboardMyCoursesLimit {
  int? id;
  String? image;
  String? name;
  int? hours;
  int? enrollments;
  double? completion;

  DashboardMyCoursesLimit({
    this.id,
    this.image,
    this.name,
    this.hours,
    this.enrollments,
    this.completion,
  });

  DashboardMyCoursesLimit.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    image = json['image'] as String?;
    name = json['name'] as String?;
    hours = json['hours'] as int?;
    enrollments = json['enrollments'] as int?;
    completion = json['completion'].toDouble();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['image'] = image;
    json['name'] = name;
    json['hours'] = hours;
    json['enrollments'] = enrollments;
    json['completion'] = completion;
    return json;
  }
}

class DashboardSessionsLimit {
  int? id;
  String? contentType;
  String? name;
  String? description;
  int? duration;
  int? fromDate;
  String? presenterName;
  int? programId;
  String? programName;
  String? level;
  dynamic zoomUrl;
  dynamic zoomPasskey;
  String? url;
  String? openUrl;
  String? callToAction;
  int? endDate;
  String? liveclassStatus;
  String? startTime;
  String? endTime;

  DashboardSessionsLimit({
    this.id,
    this.contentType,
    this.name,
    this.description,
    this.duration,
    this.fromDate,
    this.presenterName,
    this.programId,
    this.programName,
    this.level,
    this.zoomUrl,
    this.zoomPasskey,
    this.url,
    this.openUrl,
    this.callToAction,
    this.endDate,
    this.liveclassStatus,
    this.startTime,
    this.endTime,
  });

  DashboardSessionsLimit.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    contentType = json['content_type'] as String?;
    name = json['name'] as String?;
    description = json['description'] as String?;
    duration = json['duration'] as int?;
    fromDate = json['from_date'] as int?;
    presenterName = json['presenter_name'] as String?;
    programId = json['program_id'] as int?;
    programName = json['program_name'] as String?;
    level = json['level'] as String?;
    zoomUrl = json['zoom_url'];
    zoomPasskey = json['zoom_passkey'];
    url = json['url'] as String?;
    openUrl = json['open_url'];
    callToAction = json['call_to_action'].toString();
    endDate = json['end_date'] as int?;
    liveclassStatus = '${json['liveclass_status']}';
    startTime = json['start_time'] as String?;
    endTime = json['end_time'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['content_type'] = contentType;
    json['name'] = name;
    json['description'] = description;
    json['duration'] = duration;
    json['from_date'] = fromDate;
    json['presenter_name'] = presenterName;
    json['program_id'] = programId;
    json['program_name'] = programName;
    json['level'] = level;
    json['zoom_url'] = zoomUrl;
    json['open_url'] = openUrl;
    json['zoom_passkey'] = zoomPasskey;
    json['url'] = url;
    json['call_to_action'] = callToAction;
    json['end_date'] = endDate;
    json['liveclass_status'] = liveclassStatus;
    json['start_time'] = startTime;
    json['end_time'] = endTime;
    return json;
  }
}

class ToDoActivities {
  ToDoActivities({
    this.todayClasses,
    this.recentActivity,
  });

  List<DashboardSessionsLimit>? todayClasses;
  List<RecentActivity>? recentActivity;

  factory ToDoActivities.fromJson(Map<String, dynamic> json) => ToDoActivities(
        todayClasses: json["today_classes"] == null
            ? []
            : List<DashboardSessionsLimit>.from(json["today_classes"]!
                .map((x) => DashboardSessionsLimit.fromJson(x))),
        recentActivity: json["recent_activity"] == null
            ? []
            : List<RecentActivity>.from(json["recent_activity"]!
                .map((x) => RecentActivity.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "today_classes": todayClasses == null
            ? []
            : List<dynamic>.from(todayClasses!.map((x) => x.toJson())),
        "recent_activity": recentActivity == null
            ? []
            : List<dynamic>.from(recentActivity!.map((x) => x.toJson())),
      };
}

class RecentActivity {
  RecentActivity({
    this.contentId,
    this.title,
    this.description,
    this.startDate,
    this.endDate,
    this.submissionDate,
    this.allowMultiple,
    this.isGraded,
    this.submissionMode,
    this.maximumMarks,
    this.contentType,
    this.languageId,
    this.moduleId,
    this.durationInMins,
    this.totalAttempts,
    this.file,
    this.passingMarks,
    this.questionCount,
    this.attemptAllowed,
    this.durationInMinutes,
    this.attemptCount,
    this.difficultyLevel,
    this.module,
    this.skill,
    this.program,
    this.attemptedOn,
    this.score,
    this.status,
    this.displayScorecard,
  });

  int? contentId;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  int? submissionDate;
  int? allowMultiple;
  int? isGraded;
  int? submissionMode;
  int? maximumMarks;
  String? contentType;
  int? languageId;
  int? moduleId;
  int? durationInMins;
  int? totalAttempts;
  String? file;
  int? passingMarks;
  int? questionCount;
  int? attemptAllowed;
  int? durationInMinutes;
  int? attemptCount;
  String? difficultyLevel;
  int? module;
  int? skill;
  int? program;
  int? attemptedOn;
  dynamic score;
  String? status;
  dynamic displayScorecard;

  factory RecentActivity.fromJson(Map<String, dynamic> json) => RecentActivity(
      contentId: json["content_id"],
      title: json["title"],
      description: json["description"],
      startDate: json["start_date"],
      endDate: json["end_date"],
      submissionDate: json["submission_date"],
      allowMultiple: json["allow_multiple"],
      isGraded: json["is_graded"],
      submissionMode: json["submission_mode"],
      maximumMarks: json["maximum_marks"],
      contentType: json["content_type"],
      languageId: json["language_id"],
      moduleId: json["module_id"],
      durationInMins: json["duration_in_mins"],
      totalAttempts: json["total_attempts"],
      file: json["file"],
      passingMarks: json["passing_marks"],
      questionCount: json["question_count"],
      attemptAllowed: json["attempt_allowed"],
      durationInMinutes: json["duration_in_minutes"],
      attemptCount: json["attempt_count"],
      difficultyLevel: json["difficulty_level"],
      module: json["module"],
      skill: json["skill"],
      program: json["program"],
      attemptedOn: json["attempted_on"],
      score: json["score"],
      status: json["status"],
      displayScorecard: json["display_scorecard"]);

  Map<String, dynamic> toJson() => {
        "content_id": contentId,
        "title": title,
        "description": description,
        "start_date": startDate,
        "end_date": endDate,
        "submission_date": submissionDate,
        "allow_multiple": allowMultiple,
        "is_graded": isGraded,
        "submission_mode": submissionMode,
        "maximum_marks": maximumMarks,
        "content_type": contentType,
        "language_id": languageId,
        "module_id": moduleId,
        "duration_in_mins": durationInMins,
        "total_attempts": totalAttempts,
        "file": file,
        "passing_marks": passingMarks,
        "question_count": questionCount,
        "attempt_allowed": attemptAllowed,
        "duration_in_minutes": durationInMinutes,
        "attempt_count": attemptCount,
        "difficulty_level": difficultyLevel,
        "module": module,
        "skill": skill,
        "program": program,
        "attempted_on": attemptedOn,
        "score": score,
        "status": status,
        "display_scorecard": displayScorecard,
      };
}

class JobDashboard {
  int? domains;
  int? companies;
  int? jobPosting;
  int? jobRoles;
  int? location;
  double? vacancies;

  JobDashboard({
    this.domains,
    this.companies,
    this.jobPosting,
    this.jobRoles,
    this.location,
    this.vacancies,
  });

  JobDashboard.fromJson(Map<String, dynamic> json) {
    domains = json['tot_domains'] as int?;
    companies = json['tot_companies'] as int?;
    jobPosting = json['tot_job_posting'] as int?;
    jobRoles = json['tot_job_roles'] as int?;
    location = json['tot_location'] as int?;
    vacancies = json['tot_vacancies'] as double?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['tot_domains'] = domains;
    json['tot_companies'] = companies;
    json['tot_job_posting'] = jobPosting;
    json['tot_job_roles'] = jobRoles;
    json['tot_location'] = location;
    json['tot_vacancies'] = vacancies;
    return json;
  }
}

class FutureTrends {
  FutureTrends({
    required this.id,
    this.name,
    this.description,
    this.background,
    this.status,
    this.organizationId,
    this.createdAt,
    this.updatedAt,
    this.numberOfJobs,
    this.growth,
    this.growthType,
    this.skillId,
    this.jobCount,
  });

  int id;
  String? name;
  String? description;
  dynamic background;
  String? status;
  int? organizationId;
  String? createdAt;
  String? updatedAt;
  String? numberOfJobs;
  String? growth;
  String? growthType;
  dynamic skillId;
  int? jobCount;

  factory FutureTrends.fromJson(Map<String, dynamic> json) => FutureTrends(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        background: json["background"],
        status: json["status"],
        organizationId: json["organization_id"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        numberOfJobs: json["number_of_jobs"],
        growth: json["growth"],
        growthType: json["growth_type"],
        skillId: json["skill_id"],
        jobCount: json["job_count"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "background": background,
        "status": status,
        "organization_id": organizationId,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "number_of_jobs": numberOfJobs,
        "growth": growth,
        "growth_type": growthType,
        "skill_id": skillId,
        "job_count": jobCount,
      };
}

class JobDomain {
  String? sectorIndustryDomain;
  double? totVacancies;
  int? totJobRoles;
  int? totCompanies;
  String? icon;

  JobDomain(
      {this.sectorIndustryDomain,
      this.totVacancies,
      this.totJobRoles,
      this.totCompanies,
      this.icon});

  factory JobDomain.fromRawJson(String str) =>
      JobDomain.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory JobDomain.fromJson(Map<String, dynamic> json) => JobDomain(
        sectorIndustryDomain: json["sector_industry_Domain"],
        totVacancies: json["tot_vacancies"],
        totJobRoles: json["tot_job_roles"],
        totCompanies: json["tot_companies"],
        icon: json["icon"],
      );

  Map<String, dynamic> toJson() => {
        "sector_industry_Domain": sectorIndustryDomain,
        "tot_vacancies": totVacancies,
        "tot_job_roles": totJobRoles,
        "tot_companies": totCompanies,
        "icon": icon,
      };
}

class Banners {
  String? bannerUrl;
  String? bannerLink;
  String? bannerType;
  String? extraParams;

  Banners({this.bannerUrl, this.bannerLink, this.bannerType, this.extraParams});

  Banners.fromJson(Map<String, dynamic> json) {
    bannerUrl = json['banner_url'];
    bannerLink = json['banner_link'];
    bannerType = json['banner_type'];
    extraParams = json['extra_params'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['banner_url'] = this.bannerUrl;
    data['banner_link'] = this.bannerLink;
    data['banner_type'] = this.bannerType;
    data['extra_params'] = this.extraParams;
    return data;
  }
}

class MatchingJobs {
    int? id;
    dynamic parentId;
    dynamic categoryId;
    dynamic sessionId;
    String? level;
    String? name;
    String? description;
    String? image;
    String? startDate;
    String? endDate;
    dynamic duration;
    int? createdBy;
    String? status;
    String? createdAt;
    String? updatedAt;
    int? organizationId;
    int? isGlobalProgram;
    int? registrationNeedApproval;
    dynamic assignedRuleId;
    dynamic weightage;
    int? certificateId;
    String? certificateNumberPattern;
    int? certificateLatestNumber;
    String? type;
    dynamic shortCode;
    dynamic gScore;
    dynamic subscriptionType;
    dynamic isStructured;
    int? isCompetition;
    dynamic terminationDays;
    dynamic organizedBy;
    String? competitionLevel;
    int? isPopular;
    int? isPublished;
    int? isJob;
    dynamic isRecommended;
    int? stepNo;
    dynamic isInternship;
    int? organizedById;
    dynamic sisRefModuleId;
    dynamic languageId;
    dynamic sisModuleId;
    int? contentApproval;
    dynamic departmentId;
    String? jobId;
    dynamic pgScore;
    int? enableContentLock;
    String? compType;
    dynamic landingPageUrl;
    int? domainId;
    dynamic domainName;
    dynamic location;
    dynamic experience;
    dynamic skillNames;
    String? jobStatus;
    String? workAddress;
    dynamic jobStatusNumeric;
    dynamic score;
    String? progressStatus;
    String? eventStatus;
    int? numOfVacancy;
    dynamic minExperience;
    dynamic maxExperience;
    int? gulfjobId;
    int? programId;
    int? userId;
    dynamic matchingPer;

    MatchingJobs({
        this.id,
        this.parentId,
        this.categoryId,
        this.sessionId,
        this.level,
        this.name,
        this.description,
        this.image,
        this.startDate,
        this.endDate,
        this.duration,
        this.createdBy,
        this.status,
        this.createdAt,
        this.updatedAt,
        this.organizationId,
        this.isGlobalProgram,
        this.registrationNeedApproval,
        this.assignedRuleId,
        this.weightage,
        this.certificateId,
        this.certificateNumberPattern,
        this.certificateLatestNumber,
        this.type,
        this.shortCode,
        this.gScore,
        this.subscriptionType,
        this.isStructured,
        this.isCompetition,
        this.terminationDays,
        this.organizedBy,
        this.competitionLevel,
        this.isPopular,
        this.isPublished,
        this.isJob,
        this.isRecommended,
        this.stepNo,
        this.isInternship,
        this.organizedById,
        this.sisRefModuleId,
        this.languageId,
        this.sisModuleId,
        this.contentApproval,
        this.departmentId,
        this.jobId,
        this.pgScore,
        this.enableContentLock,
        this.compType,
        this.landingPageUrl,
        this.domainId,
        this.domainName,
        this.location,
        this.experience,
        this.skillNames,
        this.jobStatus,
        this.workAddress,
        this.jobStatusNumeric,
        this.score,
        this.progressStatus,
        this.eventStatus,
        this.numOfVacancy,
        this.minExperience,
        this.maxExperience,
        this.gulfjobId,
        this.programId,
        this.userId,
        this.matchingPer,
    });

    factory MatchingJobs.fromJson(Map<String, dynamic> json) => MatchingJobs(
        id: json["id"],
        parentId: json["parent_id"],
        categoryId: json["category_id"],
        sessionId: json["session_id"],
        level: json["level"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        startDate: json["start_date"] ,
        endDate: json["end_date"] ,
        duration: json["duration"],
        createdBy: json["created_by"],
        status: json["status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        organizationId: json["organization_id"],
        isGlobalProgram: json["is_global_program"],
        registrationNeedApproval: json["registration_need_approval"],
        assignedRuleId: json["assigned_rule_id"],
        weightage: json["weightage"],
        certificateId: json["certificate_id"],
        certificateNumberPattern: json["certificate_number_pattern"],
        certificateLatestNumber: json["certificate_latest_number"],
        type: json["type"],
        shortCode: json["short_code"],
        gScore: json["g_score"],
        subscriptionType: json["subscription_type"],
        isStructured: json["is_structured"],
        isCompetition: json["is_competition"],
        terminationDays: json["termination_days"],
        organizedBy: json["organized_by"],
        competitionLevel: json["competition_level"],
        isPopular: json["is_popular"],
        isPublished: json["is_published"],
        isJob: json["is_job"],
        isRecommended: json["is_recommended"],
        stepNo: json["step_no"],
        isInternship: json["is_internship"],
        organizedById: json["organized_by_id"],
        sisRefModuleId: json["sis_ref_module_id"],
        languageId: json["language_id"],
        sisModuleId: json["sis_module_id"],
        contentApproval: json["content_approval"],
        departmentId: json["department_id"],
        jobId: json["job_id"],
        pgScore: json["pg_score"],
        enableContentLock: json["enable_content_lock"],
        compType: json["comp_type"],
        landingPageUrl: json["landing_page_url"],
        domainId: json["domain_id"],
        domainName: json["domain_name"],
        location: json["location"],
        experience: json["experience"],
        skillNames: json["skill_names"],
        jobStatus: json["job_status"],
        workAddress: json["work_address"],
        jobStatusNumeric: json["job_status_numeric"],
        score: json["score"],
        progressStatus: json["progress_status"],
        eventStatus: json["event_status"],
        numOfVacancy: json["num_of_vacancy"],
        minExperience: json["min_experience"],
        maxExperience: json["max_experience"],
        gulfjobId: json["gulfjob_id"],
        programId: json["program_id"],
        userId: json["user_id"],
        matchingPer: json["matching_per"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "category_id": categoryId,
        "session_id": sessionId,
        "level": level,
        "name": name,
        "description": description,
        "image": image,
        "start_date": startDate,
        "end_date": endDate,
        "duration": duration,
        "created_by": createdBy,
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "organization_id": organizationId,
        "is_global_program": isGlobalProgram,
        "registration_need_approval": registrationNeedApproval,
        "assigned_rule_id": assignedRuleId,
        "weightage": weightage,
        "certificate_id": certificateId,
        "certificate_number_pattern": certificateNumberPattern,
        "certificate_latest_number": certificateLatestNumber,
        "type": type,
        "short_code": shortCode,
        "g_score": gScore,
        "subscription_type": subscriptionType,
        "is_structured": isStructured,
        "is_competition": isCompetition,
        "termination_days": terminationDays,
        "organized_by": organizedBy,
        "competition_level": competitionLevel,
        "is_popular": isPopular,
        "is_published": isPublished,
        "is_job": isJob,
        "is_recommended": isRecommended,
        "step_no": stepNo,
        "is_internship": isInternship,
        "organized_by_id": organizedById,
        "sis_ref_module_id": sisRefModuleId,
        "language_id": languageId,
        "sis_module_id": sisModuleId,
        "content_approval": contentApproval,
        "department_id": departmentId,
        "job_id": jobId,
        "pg_score": pgScore,
        "enable_content_lock": enableContentLock,
        "comp_type": compType,
        "landing_page_url": landingPageUrl,
        "domain_id": domainId,
        "domain_name": domainName,
        "location": location,
        "experience": experience,
        "skill_names": skillNames,
        "job_status": jobStatus,
        "work_address": workAddress,
        "job_status_numeric": jobStatusNumeric,
        "score": score,
        "progress_status": progressStatus,
        "event_status": eventStatus,
        "num_of_vacancy": numOfVacancy,
        "min_experience": minExperience,
        "max_experience": maxExperience,
        "gulfjob_id": gulfjobId,
        "program_id": programId,
        "user_id": userId,
        "matching_per": matchingPer,
    };
}

class Skill {
  final int id;
  final int? skillId;
  final String name;
  final String description;
  final int skillWeightage;
  final int? userWeightage;
  final double userWeightagePer;
  final int? userWeightagePerNo;
  final String userWeightageLabel;

  Skill({
    required this.id,
    this.skillId,
    required this.name,
    required this.description,
    required this.skillWeightage,
    this.userWeightage,
    required this.userWeightagePer,
    this.userWeightagePerNo,
    required this.userWeightageLabel,
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      id: json['id'],
      skillId: json['skill_id'],
      name: json['name'],
      description: json['description'],
      skillWeightage: json['skill_weightage'],
      userWeightage: json['user_weightage'],
      userWeightagePer: double.parse(json['user_weightage_per'].toString()),
      userWeightageLabel: json['user_weightage_per_label'],
      userWeightagePerNo: json['user_weightage_per_no'],
    );
  }
}

class UserSkillAssessment {
  final int categoryId;
  final String categoryName;
  final List<Skill> skills;

  UserSkillAssessment({
    required this.categoryId,
    required this.categoryName,
    required this.skills,
  });

  factory UserSkillAssessment.fromJson(Map<String, dynamic> json) {
    var skillsList = (json['skills'] as List)
        .map((skillJson) => Skill.fromJson(skillJson))
        .toList();

    return UserSkillAssessment(
      categoryId: json['c_id'],
      categoryName: json['c_name'],
      skills: skillsList,
    );
  }
}


class MatchingJobsDashboardProvider extends ChangeNotifier {
  List<MatchingJobs?> list = [];
  

  MatchingJobsDashboardProvider(List<MatchingJobs?>? list) {
    if (list != null) this.list = list;
    notifyListeners();
  }

  void updateAppliedStatus(int index) {
    this.list[index]?.jobStatus = 'Applied';
    notifyListeners();
  }

  void resetList(List<MatchingJobs?> newData) {
    this.list = newData;
    notifyListeners();
  }

  void addItemList(List<MatchingJobs?> newData) {
    this.list.addAll(newData);
    notifyListeners();
  }

  void resetValue() {
    this.list = [];
    notifyListeners();
  }
}

