import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../../../../local/pref/Preference.dart';

BottomBarResponse bottomBarResponseFromJson(String str) =>
    BottomBarResponse.fromJson(json.decode(str));

String bottomBarResponseToJson(BottomBarResponse data) =>
    json.encode(data.toJson());

class BottomBarResponse {
  BottomBarResponse({
    this.status,
    this.data,
    this.error,
  });

  int? status;
  Data? data;
  List<dynamic>? error;

  factory BottomBarResponse.fromJson(Map<String, dynamic> json) =>
      BottomBarResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? null
            : List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error":
            error == null ? null : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  Data({
    this.menu,
    this.menuSide,
  });

  List<Menu>? menu;
  List<Menu>? menuSide;


  factory Data.fromJson(Map<String, dynamic> json) => Data(
        menu: List<Menu>.from(json["menu"].map((x) => Menu.fromJson(x))),
        menuSide: List<Menu>.from(json["menu_side"].map((x) => Menu.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "menu": List<dynamic>.from(menu!.map((x) => x.toJson())),
        "menu_side": List<dynamic>.from(menuSide!.map((x) => x.toJson())),
      };
}

class Menu {
  Menu(
      {this.order,
      this.url,
      this.linkType,
      this.image,
      this.isChecked,
      this.isCheckedMobile,
      this.label,
      this.role,
      this.inAppOrder, this.iconCode});

  String? order;
  String? url;
  int? linkType;
  String? image;
  int? isChecked;
  int? isCheckedMobile;
  String? label;
  String? role;
  String? inAppOrder;
  String? iconCode;

  factory Menu.fromJson(Map<String, dynamic> json) => Menu(
        order: json["order"],
        url: json["url"],
        linkType: json["link_type"],
        image: json["image"],
        isChecked: json["is_checked"],
        isCheckedMobile: json["is_checked_mobile"],
        label: json["label"],
        role: json["role"],
        inAppOrder: json["order_in_app"],
        iconCode: json["icon_code"],
      );

  Map<String, dynamic> toJson() => {
        "order": order,
        "url": url,
        "link_type": linkType,
        "image": image,
        "is_checked": isChecked,
        "is_checked_mobile": isCheckedMobile,
        "label": label,
        "role": role,
        "order_in_app": inAppOrder,
        "icon_code": iconCode
      };
}

class MenuListProvider extends ChangeNotifier {
  List<Menu>? _list = [];
  List<Menu>? get list => _list;
  int? currentIndex;
  int? itemIndexForMenu = 0;
  MenuListProvider(List<Menu> list, {int? index}) {
    currentIndex = index;
    itemIndexForMenu = index;

    if (list.length > 0) this._list = list;
    notifyListeners();
  }

  void updateList(List<Menu> newData) {
    this._list!.addAll(newData);
    notifyListeners();
  }

  int getCurrentIndex() {
    String? route = Preference.getString('initialRoute');
    if (route != '') {
      return this.currentIndex!;
    } else {
      int index = -1;
      for (var item in this._list!) {
        index++;
        if (item.url == route) break;
      }
      Preference.setString('initialRoute', '');
      return index;
    }
  }

  int getItemIndex() {
    return this.itemIndexForMenu!;
  }

  void updateItemIndex(int index) {
    this.itemIndexForMenu = index;
    notifyListeners();
  }

  void updateCurrentIndex(String route) {
    print("route is $route");
    int index = -1;
    for (var item in this._list!) {
      index++;
      if (item.url == route) break;
    }
    this.currentIndex = index;
    print('menu check $index');
    notifyListeners();
  }

  void refreshList(List<Menu> list) {
    this._list = list;
    notifyListeners();
  }

  List<Menu>? getList() {
    return this._list;
  }
}


class SecondaryMenuListProvider extends ChangeNotifier {
  List<Menu>? _list = [];
  List<Menu>? get list => _list;
  int? currentIndex;
  int? itemIndexForMenu = 0;
  SecondaryMenuListProvider(List<Menu> list, {int? index}) {
    currentIndex = index;
    itemIndexForMenu = index;

    if (list.length > 0) this._list = list;
    notifyListeners();
  }

  void updateList(List<Menu> newData) {
    this._list!.addAll(newData);
    notifyListeners();
  }

  int getCurrentIndex() {
    String? route = Preference.getString('initialRoute');
    if (route != '') {
      return this.currentIndex!;
    } else {
      int index = -1;
      for (var item in this._list!) {
        index++;
        if (item.url == route) break;
      }
      Preference.setString('initialRoute', '');
      return index;
    }
  }

  int getItemIndex() {
    return this.itemIndexForMenu!;
  }

  void updateItemIndex(int index) {
    this.itemIndexForMenu = index;
    notifyListeners();
  }

  void updateCurrentIndex(String route) {
    print("route is $route");
    int index = -1;
    for (var item in this._list!) {
      index++;
      if (item.url == route) break;
    }
    this.currentIndex = index;
    print('menu check $index');
    notifyListeners();
  }

  void refreshList(List<Menu> list) {
    this._list = list;
    notifyListeners();
  }

  List<Menu>? getList() {
    return this._list;
  }
}

