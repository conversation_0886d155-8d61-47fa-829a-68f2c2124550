
import 'dart:convert';

class ChangePasswordResponse {
    int? status;
    List<dynamic>? error;
    Data? data;

    ChangePasswordResponse({
        this.status,
        this.error,
        this.data,
    });

    factory ChangePasswordResponse.fromRawJson(String str) => ChangePasswordResponse.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory ChangePasswordResponse.fromJson(Map<String, dynamic> json) => ChangePasswordResponse(
        status: json["status"],
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
        "data": data != null ?  data?.toJson() : Data(message: ''),
    };
}

class Data {
    String? message;

    Data({
        this.message,
    });

    factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "message": message,
    };
}
