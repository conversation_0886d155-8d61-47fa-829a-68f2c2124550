// To parse this JSON data, do
//
//     final particiapteResp = particiapteRespFromJson(jsonString);

import 'dart:convert';

ParticiapteResp particiapteRespFromJson(String str) => ParticiapteResp.fromJson(json.decode(str));

String particiapteRespToJson(ParticiapteResp data) => json.encode(data.toJson());

class ParticiapteResp {
    int? status;
    Data? data;
    List<dynamic>? error;

    ParticiapteResp({
        this.status,
        this.data,
        this.error,
    });

    // factory ParticiapteResp.fromJson(Map<String, dynamic> json) => ParticiapteResp(
    //     status: json["status"],
    //     data: json["data"] == null ? null : Data.fromJson(json["data"]),
    //     error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    // );
    factory ParticiapteResp.fromJson(Map<String, dynamic> json) {
    // Check if json["data"] is null or not of type Map<String, dynamic>
    Data? data;
    if (json["data"] != null && json["data"] is Map<String, dynamic>) {
        data = Data.fromJson(json["data"]);
    }

    // Check if json["error"] is null or not of type List<dynamic>
    List<dynamic>? error;
    if (json["error"] != null && json["error"] is List<dynamic>) {
        error = List<dynamic>.from(json["error"]?.map((x) => x) ?? []);
    }

    return ParticiapteResp(
        status: json["status"],
        data: data,
        error: error,
    );
}


    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    String? token;
    User? user;
    String? pId;

    Data({
        this.token,
        this.user,
        this.pId,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        token: json["token"],
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        pId: json["p_id"],
    );

    Map<String, dynamic> toJson() => {
        "token": token,
        "user": user?.toJson(),
        "p_id": pId,
    };
}

class User {
    int? id;
    int? organizationId;
    String? name;
    String? email;
    String? department;
    String? designation;
    String? mobileNo;
    int? isTrainer;
    String? profileImage;

    User({
        this.id,
        this.organizationId,
        this.name,
        this.email,
        this.department,
        this.designation,
        this.mobileNo,
        this.isTrainer,
        this.profileImage,
    });

    factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        organizationId: json["organization_id"],
        name: json["name"],
        email: json["email"],
        department: json["department"],
        designation: json["designation"],
        mobileNo: json["mobile_no"],
        isTrainer: json["is_trainer"],
        profileImage: json["profile_image"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "organization_id": organizationId,
        "name": name,
        "email": email,
        "department": department,
        "designation": designation,
        "mobile_no": mobileNo,
        "is_trainer": isTrainer,
        "profile_image": profileImage,
    };
}
