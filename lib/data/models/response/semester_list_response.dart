class SemesterListResponse {
  int? status;
  List<SemesterData>? data;
  List<dynamic>? error;

  SemesterListResponse({this.status, this.data, this.error});

  // Factory method to create an instance from JSON
  factory SemesterListResponse.fromJson(Map<String, dynamic> json) {
    return SemesterListResponse(
      status: json['status'],
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => SemesterData.fromJson(item))
          .toList(),
      error: json['error'] != null ? List<dynamic>.from(json['error']) : [],
    );
  }

  // Method to convert instance back to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data?.map((item) => item.toJson()).toList(),
      'error': error,
    };
  }
}

class SemesterData {
  int? id;
  String? name;
  String? startDate;
  String? endDate;
  int? organizationId;
  int? weightage;
  String? createdAt;
  String? updatedAt;

  SemesterData({
    this.id,
    this.name,
    this.startDate,
    this.endDate,
    this.organizationId,
    this.weightage,
    this.createdAt,
    this.updatedAt,
  });

  // Factory method to create an instance from JSON
  factory SemesterData.fromJson(Map<String, dynamic> json) {
    return SemesterData(
      id: json['id'],
      name: json['name'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      organizationId: json['organization_id'],
      weightage: json['weightage'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  // Method to convert instance back to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'start_date': startDate,
      'end_date': endDate,
      'organization_id': organizationId,
      'weightage': weightage,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
