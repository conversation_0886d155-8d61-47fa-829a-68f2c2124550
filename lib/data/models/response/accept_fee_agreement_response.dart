class AcceptFeeAgreementResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  AcceptFeeAgreementResponse({this.status, this.data, this.error});

  AcceptFeeAgreementResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = List<dynamic>.from(json["error"].map((x) => x));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (this.error != null) {
      data['error'] = this.error!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? id;
  String? mecRegdId;
  int? isAccepted;
  String? acceptanceTime;
  String? agreementFile;
  String? createdAt;
  String? updatedAt;
  User? user;

  Data(
      {this.id,
        this.mecRegdId,
        this.isAccepted,
        this.acceptanceTime,
        this.agreementFile,
        this.createdAt,
        this.updatedAt,
        this.user});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    mecRegdId = json['mec_regd_id'];
    isAccepted = json['is_accepted'];
    acceptanceTime = json['acceptance_time'];
    agreementFile = json['agreement_file'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['mec_regd_id'] = this.mecRegdId;
    data['is_accepted'] = this.isAccepted;
    data['acceptance_time'] = this.acceptanceTime;
    data['agreement_file'] = this.agreementFile;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? name;
  String? email;
  String? mecRegdId;
  String? status;

  User({this.id, this.name, this.email, this.mecRegdId, this.status});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    mecRegdId = json['mec_regd_id'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['email'] = this.email;
    data['mec_regd_id'] = this.mecRegdId;
    data['status'] = this.status;
    return data;
  }
}