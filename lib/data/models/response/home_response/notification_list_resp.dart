// To parse this JSON data, do
//
//     final notificationsListResp = notificationsListRespFromJson(jsonString);

import 'dart:convert';

NotificationsListResp notificationsListRespFromJson(String str) => NotificationsListResp.fromJson(json.decode(str));

String notificationsListRespToJson(NotificationsListResp data) => json.encode(data.toJson());

class NotificationsListResp {
    int? status;
    List<Data>? data;
    List<dynamic>? error;

    NotificationsListResp({
        this.status,
        this.data,
        this.error,
    });

    factory NotificationsListResp.fromJson(Map<String, dynamic> json) => NotificationsListResp(
        status: json["status"],
        data: List<Data>.from(json["data"].map((x) => Data.fromJson(x))),
        error: List<dynamic>.from(json["error"].map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
        "error": List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    String? id;
    int? notifiableId;
    String? readContent;
    String? createdAt;
    String? updatedAt;
    String? type;
    dynamic title;
    dynamic description;
    final NotificationDetail? dataDetails;

    Data({
         this.id,
         this.notifiableId,
         this.readContent,
         this.createdAt,
         this.updatedAt,
         this.type,
         this.title,
         this.description,
         this.dataDetails,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        notifiableId: json["notifiable_id"],
        readContent: json["read_content"],
        createdAt:json["created_at"],
        updatedAt:json["updated_at"],
        type: json["type"],
        title: json["title"],
        description: json["description"],
        dataDetails: NotificationDetail.fromMap(json["data"] ?? {}),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "notifiable_id": notifiableId,
        "read_content": readContent,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "type": type,
        "title": title,
        "description": description,
        "data": dataDetails?.toMap(),
    };
}

class NotificationDetail {
    final String? title;
    final String? body;
    final String? name;
    final String? subject;
    final String? message;
    final int? id;
    final String? route;
    final String? sound;
    final String? contentType;
    final int? organizationId;

    NotificationDetail({
        this.title,
        this.body,
        this.name,
        this.subject,
        this.message,
        this.id,
        this.route,
        this.sound,
        this.contentType,
        this.organizationId,
    });

    factory NotificationDetail.fromMap(Map<String, dynamic> json) =>
        NotificationDetail(
            title: json["title"],
            body: json["body"],
            name: json["name"],
            subject: json["subject"],
            message: json["message"],
            id: json["id"],
            route: json["route"],
            sound: json["sound"],
            contentType: json["content_type"],
            organizationId: json["organization_id"],
        );

    Map<String, dynamic> toMap() => {
        "title": title,
        "body": body,
        "name": name,
        "subject": subject,
        "message": message,
        "id": id,
        "route": route,
        "sound": sound,
        "content_type": contentType,
        "organization_id": organizationId,
    };
}

enum Type {
    ASSESSMENT,
    OTHERCLASS,
    VIDEO,
    VIDEO_YTS
}

final typeValues = EnumValues({
    "assessment": Type.ASSESSMENT,
    "otherclass": Type.OTHERCLASS,
    "video": Type.VIDEO,
    "video_yts": Type.VIDEO_YTS
});

class EnumValues<T> {
    Map<String, T> map;
    late Map<T, String> reverseMap;

    EnumValues(this.map);

    Map<T, String> get reverse {
        reverseMap = map.map((k, v) => MapEntry(v, k));
        return reverseMap;
    }
}
