import 'dart:convert';

ListVideoResumeResponse listVideoResumeResponseFromJson(String str) =>
    ListVideoResumeResponse.fromJson(json.decode(str));

String listVideoResumeResponseToJson(ListVideoResumeResponse data) =>
    json.encode(data.toJson());

class ListVideoResumeResponse {
  int? status;
  List<Video>? data;
  List<dynamic>? error;

  ListVideoResumeResponse({
    this.status,
    this.data,
    this.error,
  });

  factory ListVideoResumeResponse.fromJson(Map<String, dynamic> json) =>
      ListVideoResumeResponse(
        status: json["status"],
        data: List<Video>.from(json["data"].map((x) => Video.fromJson(x))),
        error: List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
        "error": List<dynamic>.from(error!.map((x) => x)),
      };
}

class Video {
  String? url;
  String? videoTitle;
  String? videoText;
  String type;
  String? date;
  String? thumbnail;

  Video(
      {required this.url,
      required this.videoTitle,
      required this.videoText,
      required this.type,
      this.date,
      this.thumbnail
      });

  factory Video.fromJson(Map<String, dynamic> json) => Video(
      url: json["url"],
      videoTitle: json["video_title"],
      videoText: json["video_text"],
      type: json.containsKey('type') ? json['type'] : '',
      date: json.containsKey('created_date') ? json['created_date'] : '',
      thumbnail: json.containsKey('video_thumbnail') ? json['video_thumbnail'] : ''
      
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "video_title": videoTitle,
        "video_text": videoText,
        "type": type,
        "created_date": date,
        "thumbnail" : thumbnail
      };
}
