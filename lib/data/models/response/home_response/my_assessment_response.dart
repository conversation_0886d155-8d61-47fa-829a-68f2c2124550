import 'package:easy_localization/easy_localization.dart';

class MyAssessmentResponse {
  int? status;
  Data? data;
  List<String>? error;

  MyAssessmentResponse({this.status, this.data, this.error});

  MyAssessmentResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();

    
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (error != null) {
      data['error'] = error!.map((v) => v).toList();
    }
    return data;
  }
}

class Data {
  List<AssessmentList>? assessmentList;

  Data({this.assessmentList});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['assessment_list'] != null) {
      assessmentList = <AssessmentList>[];
      json['assessment_list'].forEach((v) {
        assessmentList!.add(AssessmentList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (assessmentList != null) {
      data['assessment_list'] =
          assessmentList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AssessmentList {
  int? contentId;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  int? maximumMarks;
  int? passingMarks;
  int? questionCount;
  int? attemptAllowed;
  int? durationInMinutes;
  int? attemptCount;
  String? difficultyLevel;
  int? module;
  int? skill;
  int? program;
  int? attemptedOn;
  //int? score;
  dynamic score;
  int? displayScorecard;
  String? programName;
  String? status;

  AssessmentList(
      {this.contentId,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.maximumMarks,
      this.passingMarks,
      this.questionCount,
      this.attemptAllowed,
      this.durationInMinutes,
      this.attemptCount,
      this.difficultyLevel,
      this.module,
      this.skill,
      this.program,
      this.attemptedOn,
      this.score,
      this.displayScorecard,
      this.programName,
      this.status});

  AssessmentList.fromJson(Map<String, dynamic> json) {
    contentId = int.tryParse('${json['content_id']}');
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    maximumMarks = json['maximum_marks'];
    passingMarks = json['passing_marks'];
    questionCount = json['question_count'];
    attemptAllowed = json['attempt_allowed'];
    durationInMinutes = json['duration_in_minutes'];
    attemptCount = json['attempt_count'];
    difficultyLevel = tr(json['difficulty_level'].toString().toLowerCase());
    module = json['module'];
    skill = json['skill'];
    program = json['program'];
    attemptedOn = json['attempted_on'];
    score = json['score'];
    displayScorecard = json['display_scorecard'];
    programName = json['program_name'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['content_id'] = contentId;
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['maximum_marks'] = maximumMarks;
    data['passing_marks'] = passingMarks;
    data['question_count'] = questionCount;
    data['attempt_allowed'] = attemptAllowed;
    data['duration_in_minutes'] = durationInMinutes;
    data['attempt_count'] = attemptCount;
    data['difficulty_level'] = difficultyLevel;
    data['module'] = module;
    data['skill'] = skill;
    data['program'] = program;
    data['attempted_on'] = attemptedOn;
    data['score'] = score;
    data['display_scorecard'] = displayScorecard;
    data['program_name'] = programName;
    data['status'] = status;
    return data;
  }
}

