class AddSkillResponse {
  int? status;
  String? data;
  List<String>? error;

  AddSkillResponse({this.status, this.data, this.error});

  AddSkillResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'];
    if (json['error'] != null) {
      error = <String>[];
      json['error'].forEach((v) {
        error?.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['data'] = this.data;
    if (error != null) {
      data['error'] = error?.map((v) => v).toList();
    }
    return data;
  }
}
