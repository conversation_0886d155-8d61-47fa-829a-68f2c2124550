class SurveyDataResp {
  int? status;
  Data? data;
  List<String>? error;

  SurveyDataResp({this.status, this.data, this.error});

  SurveyDataResp.fromJson(Map<String, dynamic> json,int type) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data'],type) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data?.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  ListSurvey? listSurvey;

  Data({this.listSurvey});

  Data.fromJson(Map<String, dynamic> json,int type) {
    listSurvey =
        json['list'] != null ? ListSurvey.fromJson(type==1? json['list'] : json['list'].first) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listSurvey != null) {
      data['list'] = listSurvey?.toJson();
    }
    return data;
  }
}

class ListSurvey {
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  int? questionCount;
  List<Questions>? questions;

  ListSurvey(
      {this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.questionCount,
      this.questions});

  ListSurvey.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    questionCount = json['question_count'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions?.add(Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['question_count'] = questionCount;
    if (questions != null) {
      data['questions'] = questions?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Questions {
  int? questionId;
  String? question;
  String? questionType;
  int? questionTypeId;
  int? attempted;
  List<Options>? options;

  Questions(
      {this.questionId,
      this.question,
      this.questionType,
      this.questionTypeId,
      this.attempted,
      this.options});

  Questions.fromJson(Map<String, dynamic> json) {
    questionId = json['question_id'];
    question = json['question'];
    questionType = json['question_type'];
    questionTypeId = json['question_type_id'];
    attempted = json['attempted'];
    if (json['options'] != null) {
      options = <Options>[];
      json['options'].forEach((v) {
        options?.add(Options.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['question_id'] = questionId;
    data['question'] = question;
    data['question_type'] = questionType;
    data['question_type_id'] = questionTypeId;
    data['attempted'] = attempted;
    if (options != null) {
      data['options'] = options?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Options {
  int? optionId;
  String? optionStatement;
  int? attempted;

  Options({this.optionId, this.optionStatement, this.attempted});

  Options.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id'];
    optionStatement = json['option_statement'];
    attempted = json['attempted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['option_id'] = optionId;
    data['option_statement'] = optionStatement;
    data['attempted'] = attempted;
    return data;
  }
}
