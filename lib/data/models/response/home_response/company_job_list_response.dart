import 'dart:convert';

CompanyJobListResponse companyJobListResponseFromJson(String str) => CompanyJobListResponse.fromJson(json.decode(str));

String companyJobListResponseToJson(CompanyJobListResponse data) => json.encode(data.toJson());

class CompanyJobListResponse {
    int? status;
    List<Job>? data;
    List<dynamic>? error;

    CompanyJobListResponse({
        this.status,
        this.data,
        this.error,
    });

    factory CompanyJobListResponse.fromJson(Map<String, dynamic> json) => CompanyJobListResponse(
        status: json["status"],
        data: json["data"] == null ? [] : List<Job>.from(json["data"]!.map((x) => Job.fromJson(x))),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Job {
    int? id;
    dynamic parentId;
    dynamic categoryId;
    dynamic sessionId;
    String? level;
    String? name;
    String? description;
    String? image;
    DateTime? startDate;
    DateTime? endDate;
    dynamic duration;
    int? createdBy;
    String? status;
    DateTime? createdAt;
    DateTime? updatedAt;
    int? organizationId;
    int? isGlobalProgram;
    int? registrationNeedApproval;
    dynamic assignedRuleId;
    dynamic weightage;
    int? certificateId;
    String? certificateNumberPattern;
    int? certificateLatestNumber;
    dynamic type;
    dynamic shortCode;
    dynamic gScore;
    dynamic subscriptionType;
    dynamic isStructured;
    dynamic isCompetition;
    dynamic terminationDays;
    String? organizedBy;
    dynamic competitionLevel;
    int? isPopular;
    int? isPublished;
    int? isJob;
    dynamic isRecommended;
    dynamic stepNo;
    dynamic isInternship;
    int? jobId;
    int? domainId;
    String? domainName;
    String? location;
    String? experience;
    String? skillNames;
    dynamic jobStatus;
    dynamic jobStatusNumeric;
    dynamic score;

    Job({
        this.id,
        this.parentId,
        this.categoryId,
        this.sessionId,
        this.level,
        this.name,
        this.description,
        this.image,
        this.startDate,
        this.endDate,
        this.duration,
        this.createdBy,
        this.status,
        this.createdAt,
        this.updatedAt,
        this.organizationId,
        this.isGlobalProgram,
        this.registrationNeedApproval,
        this.assignedRuleId,
        this.weightage,
        this.certificateId,
        this.certificateNumberPattern,
        this.certificateLatestNumber,
        this.type,
        this.shortCode,
        this.gScore,
        this.subscriptionType,
        this.isStructured,
        this.isCompetition,
        this.terminationDays,
        this.organizedBy,
        this.competitionLevel,
        this.isPopular,
        this.isPublished,
        this.isJob,
        this.isRecommended,
        this.stepNo,
        this.isInternship,
        this.jobId,
        this.domainId,
        this.domainName,
        this.location,
        this.experience,
        this.skillNames,
        this.jobStatus,
        this.jobStatusNumeric,
        this.score,
    });

    factory Job.fromJson(Map<String, dynamic> json) => Job(
        id: json["id"],
        parentId: json["parent_id"],
        categoryId: json["category_id"],
        sessionId: json["session_id"],
        level: json["level"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        startDate: json["start_date"] == null ? null : DateTime.parse(json["start_date"]),
        endDate: json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
        duration: json["duration"],
        createdBy: json["created_by"],
        status: json["status"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        organizationId: json["organization_id"],
        isGlobalProgram: json["is_global_program"],
        registrationNeedApproval: json["registration_need_approval"],
        assignedRuleId: json["assigned_rule_id"],
        weightage: json["weightage"],
        certificateId: json["certificate_id"],
        certificateNumberPattern: json["certificate_number_pattern"],
        certificateLatestNumber: json["certificate_latest_number"],
        type: json["type"],
        shortCode: json["short_code"],
        gScore: json["g_score"],
        subscriptionType: json["subscription_type"],
        isStructured: json["is_structured"],
        isCompetition: json["is_competition"],
        terminationDays: json["termination_days"],
        organizedBy: json["organized_by"],
        competitionLevel: json["competition_level"],
        isPopular: json["is_popular"],
        isPublished: json["is_published"],
        isJob: json["is_job"],
        isRecommended: json["is_recommended"],
        stepNo: json["step_no"],
        isInternship: json["is_internship"],
        jobId: json["job_id"],
        domainId: json["domain_id"],
        domainName: json["domain_name"],
        location: json["location"],
        experience: json["experience"],
        skillNames: json["skill_names"],
        jobStatus: json["job_status"],
        jobStatusNumeric: json["job_status_numeric"],
        score: json["score"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "category_id": categoryId,
        "session_id": sessionId,
        "level": level,
        "name": name,
        "description": description,
        "image": image,
        "start_date": startDate?.toIso8601String(),
        "end_date": endDate?.toIso8601String(),
        "duration": duration,
        "created_by": createdBy,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "organization_id": organizationId,
        "is_global_program": isGlobalProgram,
        "registration_need_approval": registrationNeedApproval,
        "assigned_rule_id": assignedRuleId,
        "weightage": weightage,
        "certificate_id": certificateId,
        "certificate_number_pattern": certificateNumberPattern,
        "certificate_latest_number": certificateLatestNumber,
        "type": type,
        "short_code": shortCode,
        "g_score": gScore,
        "subscription_type": subscriptionType,
        "is_structured": isStructured,
        "is_competition": isCompetition,
        "termination_days": terminationDays,
        "organized_by": organizedBy,
        "competition_level": competitionLevel,
        "is_popular": isPopular,
        "is_published": isPublished,
        "is_job": isJob,
        "is_recommended": isRecommended,
        "step_no": stepNo,
        "is_internship": isInternship,
        "job_id": jobId,
        "domain_id": domainId,
        "domain_name": domainName,
        "location": location,
        "experience": experience,
        "skill_names": skillNames,
        "job_status": jobStatus,
        "job_status_numeric": jobStatusNumeric,
        "score": score,
    };
}
