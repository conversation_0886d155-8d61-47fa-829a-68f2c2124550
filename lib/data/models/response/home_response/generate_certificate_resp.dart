import 'dart:convert';

GenerateCertificateResponse generateCertificateResponseFromJson(String str) => GenerateCertificateResponse.fromJson(json.decode(str));

String generateCertificateResponseTo<PERSON>son(GenerateCertificateResponse data) => json.encode(data.toJson());

class GenerateCertificateResponse {
    GenerateCertificateResponse({
        this.status,
        this.url,
        this.error,
    });

    int? status;
    String? url;
    List<dynamic>? error;

    factory GenerateCertificateResponse.fromJson(Map<String, dynamic> json) => GenerateCertificateResponse(
        status: json["status"],
        url: json["url"],
        error: List<dynamic>.from(json["error"].map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "url": url,
        "error": List<dynamic>.from(error!.map((x) => x)),
    };
}