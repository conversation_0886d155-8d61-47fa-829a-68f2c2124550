class MasterLanguageResponse {
  int? status;
  Data? data;
  List<String>? error;

  MasterLanguageResponse({this.status, this.data, this.error});

  MasterLanguageResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<ListLanguage>? listData;

  Data({this.listData});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      listData = <ListLanguage>[];
      json['list'].forEach((v) {
        listData!.add(ListLanguage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listData != null) {
      data['list'] = listData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListLanguage {
  int? id;
  int? languageId;
  int? organizationId;
  String? name;
  String? createdAt;
  String? updatedAt;
  String? englishName;
  String? languageCode;
  String? title;
  int? isPrimaryLanguage;

  ListLanguage(
      {this.id,
      this.languageId,
      this.organizationId,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.englishName,
      this.languageCode, this.title, this.isPrimaryLanguage});

  ListLanguage.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    languageId = json['language_id'];
    organizationId = json['organization_id'];
    name = json['name'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    englishName = json['english_name'];
    languageCode = json['language_code'];
    title = json['title'];
    isPrimaryLanguage = json['is_primary_language'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['language_id'] = languageId;
    data['organization_id'] = organizationId;
    data['name'] = name;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['english_name'] = englishName;
    data['language_code'] = languageCode;
    data['title'] = title;
    data['is_primary_language'] = isPrimaryLanguage;
    return data;
  }
}
