// To parse this JSON data, do
//
//     final facultyBatchAssessmentResponse = facultyBatchAssessmentResponseFromJson(jsonString);

import 'dart:convert';

FacultyBatchAssessmentResponse facultyBatchAssessmentResponseFromJson(String str) => FacultyBatchAssessmentResponse.fromJson(json.decode(str));

String facultyBatchAssessmentResponseToJson(FacultyBatchAssessmentResponse data) => json.encode(data.toJson());

class FacultyBatchAssessmentResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    FacultyBatchAssessmentResponse({
        this.status,
        this.data,
        this.error,
    });

    factory FacultyBatchAssessmentResponse.fromJson(Map<String, dynamic> json) => FacultyBatchAssessmentResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<Assessment>? assessment;
    List<Batch>? batches;

    Data({
        this.assessment,
        this.batches,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        assessment: json["assessment"] == null ? [] : List<Assessment>.from(json["assessment"]!.map((x) => Assessment.fromJson(x))),
        batches: json["batches"] == null ? [] : List<Batch>.from(json["batches"]!.map((x) => Batch.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "assessment": assessment == null ? [] : List<dynamic>.from(assessment!.map((x) => x.toJson())),
        "batches": batches == null ? [] : List<dynamic>.from(batches!.map((x) => x.toJson())),
    };
}

class Assessment {
    String? title;
    int? id;
    int? startDate;
    int? endDate;
    String? batchName;
    String? programName;
    int? expectedDuration;
    String? contentType;
    int? moduleId;
    String? batch;

    Assessment({
        this.title,
        this.id,
        this.startDate,
        this.endDate,
        this.batchName,
        this.programName,
        this.expectedDuration,
        this.contentType,
        this.moduleId,
        this.batch,
    });

    factory Assessment.fromJson(Map<String, dynamic> json) => Assessment(
        title: json["title"],
        id: json["id"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        batchName: json["batch_name"],
        programName: json["program_name"],
        expectedDuration: json["expected_duration"],
        contentType: json["content_type"],
        moduleId: json["module_id"],
        batch: json["batch"],
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "id": id,
        "start_date": startDate,
        "end_date": endDate,
        "batch_name": batchName,
        "program_name": programName,
        "expected_duration": expectedDuration,
        "content_type": contentType,
        "module_id": moduleId,
        "batch": batch,
    };
}

class Batch {
    int? id;
    String? title;

    Batch({
        this.id,
        this.title,
    });

    factory Batch.fromJson(Map<String, dynamic> json) => Batch(
        id: json["id"],
        title: json["title"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
    };
}
