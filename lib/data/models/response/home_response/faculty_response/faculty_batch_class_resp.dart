// To parse this JSON data, do
//
//     final facultyBatchClassResponse = facultyBatchClassResponseFromJson(jsonString);

import 'dart:convert';

FacultyBatchClassResponse facultyBatchClassResponseFromJson(String str) => FacultyBatchClassResponse.fromJson(json.decode(str));

String facultyBatchClassResponseToJson(FacultyBatchClassResponse data) => json.encode(data.toJson());

class FacultyBatchClassResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    FacultyBatchClassResponse({
        this.status,
        this.data,
        this.error,
    });

    factory FacultyBatchClassResponse.fromJson(Map<String, dynamic> json) => FacultyBatchClassResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<LiveClass>? liveClass;
    List<Batch>? batches;

    Data({
        this.liveClass,
        this.batches,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        liveClass: json["live_class"] == null ? [] : List<LiveClass>.from(json["live_class"]!.map((x) => LiveClass.fromJson(x))),
        batches: json["batches"] == null ? [] : List<Batch>.from(json["batches"]!.map((x) => Batch.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "live_class": liveClass == null ? [] : List<dynamic>.from(liveClass!.map((x) => x.toJson())),
        "batches": batches == null ? [] : List<dynamic>.from(batches!.map((x) => x.toJson())),
    };
}

class Batch {
    int? id;
    String? title;

    Batch({
        this.id,
        this.title,
    });

    factory Batch.fromJson(Map<String, dynamic> json) => Batch(
        id: json["id"],
        title: json["title"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
    };
}

class LiveClass {
    String? title;
    int? id;
    String? status;
    int? startDate;
    String? programName;
    int? expectedDuration;
    String? contentType;
    String? recordUrl;
    int? batchId;
    String? content;
    String? batch;
    int? liveclassPresenterId;
    int? endDate;
    String? classStatus;

    LiveClass({
        this.title,
        this.id,
        this.status,
        this.startDate,
        this.programName,
        this.expectedDuration,
        this.contentType,
        this.recordUrl,
        this.batchId,
        this.content,
        this.batch,
        this.liveclassPresenterId,
        this.endDate,
        this.classStatus,
    });

    factory LiveClass.fromJson(Map<String, dynamic> json) => LiveClass(
        title: json["title"],
        id: json["id"],
        status: json["status"],
        startDate: json["start_date"],
        programName: json["program_name"],
        expectedDuration: json["expected_duration"],
        contentType: json["content_type"],
        recordUrl: json["record_url"],
        batchId: json["batch_id"],
        content: json["content"],
        batch: json["batch"],
        liveclassPresenterId: json["liveclass_presenter_id"],
        endDate: json["end_date"],
        classStatus: json["class_status"],
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "id": id,
        "status": status,
        "start_date": startDate,
        "program_name": programName,
        "expected_duration": expectedDuration,
        "content_type": contentType,
        "record_url": recordUrl,
        "batch_id": batchId,
        "content": content,
        "batch": batch,
        "liveclass_presenter_id": liveclassPresenterId,
        "end_date": endDate,
        "class_status": classStatus,
    };
}
