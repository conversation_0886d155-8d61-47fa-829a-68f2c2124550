// To parse this JSON data, do
//
//     final facultyBatchAssignmentResponse = facultyBatchAssignmentResponseFromJson(jsonString);

import 'dart:convert';

FacultyBatchAssignmentResponse facultyBatchAssignmentResponseFromJson(String str) => FacultyBatchAssignmentResponse.fromJson(json.decode(str));

String facultyBatchAssignmentResponseToJson(FacultyBatchAssignmentResponse data) => json.encode(data.toJson());

class FacultyBatchAssignmentResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    FacultyBatchAssignmentResponse({
        this.status,
        this.data,
        this.error,
    });

    factory FacultyBatchAssignmentResponse.fromJson(Map<String, dynamic> json) => FacultyBatchAssignmentResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<Assignment>? assignment;
    List<Batch>? batches;

    Data({
        this.assignment,
        this.batches,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        assignment: json["assignment"] == null ? [] : List<Assignment>.from(json["assignment"]!.map((x) => Assignment.fromJson(x))),
        batches: json["batches"] == null ? [] : List<Batch>.from(json["batches"]!.map((x) => Batch.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "assignment": assignment == null ? [] : List<dynamic>.from(assignment!.map((x) => x.toJson())),
        "batches": batches == null ? [] : List<dynamic>.from(batches!.map((x) => x.toJson())),
    };
}

class Assignment {
    String? title;
    int? id;
    int? startDate;
    int? endDate;
    int? submissionDate;
    String? batchName;
    String? programName;
    int? expectedDuration;
    String? contentType;
    int? moduleId;
    String? batch;

    Assignment({
        this.title,
        this.id,
        this.startDate,
        this.endDate,
        this.submissionDate,
        this.batchName,
        this.programName,
        this.expectedDuration,
        this.contentType,
        this.moduleId,
        this.batch,
    });

    factory Assignment.fromJson(Map<String, dynamic> json) => Assignment(
        title: json["title"],
        id: json["id"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        submissionDate: json["submission_date"],
        batchName: json["batch_name"],
        programName: json["program_name"],
        expectedDuration: json["expected_duration"],
        contentType: json["content_type"],
        moduleId: json["module_id"],
        batch: json["batch"],
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "id": id,
        "start_date": startDate,
        "end_date": endDate,
        "submission_date": submissionDate,
        "batch_name": batchName,
        "program_name": programName,
        "expected_duration": expectedDuration,
        "content_type": contentType,
        "module_id": moduleId,
        "batch": batch,
    };
}

class Batch {
    int? id;
    String? title;

    Batch({
        this.id,
        this.title,
    });

    factory Batch.fromJson(Map<String, dynamic> json) => Batch(
        id: json["id"],
        title: json["title"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
    };
}
