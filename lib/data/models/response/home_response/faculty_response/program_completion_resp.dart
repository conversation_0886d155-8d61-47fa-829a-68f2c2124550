// To parse this JSON data, do
//
//     final programCompletionResponse = programCompletionResponseFromJson(jsonString);

import 'dart:convert';

ProgramCompletionResponse programCompletionResponseFromJson(String str) => ProgramCompletionResponse.fromJson(json.decode(str));

String programCompletionResponseToJson(ProgramCompletionResponse data) => json.encode(data.toJson());

class ProgramCompletionResponse {
    int? status;
    dynamic programCompletion;
    List<dynamic>? error;

    ProgramCompletionResponse({
        this.status,
        this.programCompletion,
        this.error,
    });

    factory ProgramCompletionResponse.fromJson(Map<String, dynamic> json) => ProgramCompletionResponse(
        status: json["status"],
        programCompletion: json["program_completion"],
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "program_completion": programCompletion,
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}
