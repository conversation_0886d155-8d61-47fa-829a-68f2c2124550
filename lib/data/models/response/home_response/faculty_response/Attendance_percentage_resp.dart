// To parse this JSON data, do
//
//     final attendancePercentageResponse = attendancePercentageResponseFromJson(jsonString);

import 'dart:convert';

AttendancePercentageResponse attendancePercentageResponseFromJson(String str) =>
    AttendancePercentageResponse.fromJson(json.decode(str));

String attendancePercentageResponseToJson(AttendancePercentageResponse data) =>
    json.encode(data.toJson());

class AttendancePercentageResponse {
  int? status;
  int? attendanceCompletion;
  List<dynamic>? error;

  AttendancePercentageResponse({
    this.status,
    this.attendanceCompletion,
    this.error,
  });

  factory AttendancePercentageResponse.fromJson(Map<String, dynamic> json) =>
      AttendancePercentageResponse(
        status: json["status"],
        attendanceCompletion: json["attendance_completion"],
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "attendance_completion": attendanceCompletion,
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}
