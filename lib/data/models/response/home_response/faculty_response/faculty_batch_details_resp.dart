// To parse this JSON data, do
//
//     final facultyBatchDetailsResponse = facultyBatchDetailsResponseFromJson(jsonString);

import 'dart:convert';

FacultyBatchDetailsResponse facultyBatchDetailsResponseFromJson(String str) =>
    FacultyBatchDetailsResponse.fromJson(json.decode(str));

String facultyBatchDetailsResponseToJson(FacultyBatchDetailsResponse data) =>
    json.encode(data.toJson());

class FacultyBatchDetailsResponse {
  int? status;
  Data? data;
  List<String>? error;

  FacultyBatchDetailsResponse({
    this.status,
    this.data,
    this.error,
  });

  factory FacultyBatchDetailsResponse.fromJson(Map<String, dynamic> json) =>
      FacultyBatchDetailsResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? []
            : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  List<dynamic>? hodPrograms;
  List<ProgramsModule>? programsModuleLead;
  List<ProgramsModule>? programsModuleFaculty;
  List<LiveClass>? ongoingLiveClass;
  List<LiveClass>? concludedLiveClass;
  int? totalLiveClass;
  int? totalAssessment;
  int? totalAssignment;
  List<FacultyCourse>? facultyCourses;

  Data({
    this.hodPrograms,
    this.programsModuleLead,
    this.programsModuleFaculty,
    this.ongoingLiveClass,
    this.concludedLiveClass,
    this.totalLiveClass,
    this.totalAssessment,
    this.totalAssignment,
    this.facultyCourses,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        hodPrograms: json["hod_programs"] == null
            ? []
            : List<dynamic>.from(json["hod_programs"]!.map((x) => x)),
        programsModuleLead: json["programs_module_lead"] == null
            ? []
            : List<ProgramsModule>.from(json["programs_module_lead"]!
                .map((x) => ProgramsModule.fromJson(x))),
        programsModuleFaculty: json["programs_module_faculty"] == null
            ? []
            : List<ProgramsModule>.from(json["programs_module_faculty"]!
                .map((x) => ProgramsModule.fromJson(x))),
        ongoingLiveClass: json["ongoing_live_class"] == null
            ? []
            : List<LiveClass>.from(
                json["ongoing_live_class"]!.map((x) => LiveClass.fromJson(x))),
        concludedLiveClass: json["concluded_live_class"] == null
            ? []
            : List<LiveClass>.from(json["concluded_live_class"]!
                .map((x) => LiveClass.fromJson(x))),
        totalLiveClass:
            json["total_live_class"] == '' ? 0 : json["total_live_class"],
        totalAssessment:
            json["total_assessment"] == '' ? 0 : json["total_assessment"],
        totalAssignment:
            json["total_assignment"] == '' ? 0 : json["total_assignment"],
        facultyCourses: json["faculty_courses"] == null || json["faculty_courses"] == ""
            ? []
            : List<FacultyCourse>.from(
                json["faculty_courses"]!.map((x) => FacultyCourse.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "hod_programs": hodPrograms == null
            ? []
            : List<dynamic>.from(hodPrograms!.map((x) => x)),
        "programs_module_lead": programsModuleLead == null
            ? []
            : List<dynamic>.from(programsModuleLead!.map((x) => x.toJson())),
        "programs_module_faculty": programsModuleFaculty == null
            ? []
            : List<dynamic>.from(programsModuleFaculty!.map((x) => x.toJson())),
        "ongoing_live_class": ongoingLiveClass == null
            ? []
            : List<dynamic>.from(ongoingLiveClass!.map((x) => x.toJson())),
        "concluded_live_class": concludedLiveClass == null
            ? []
            : List<dynamic>.from(concludedLiveClass!.map((x) => x.toJson())),
        "total_live_class": totalLiveClass,
        "total_assessment": totalAssessment,
        "total_assignment": totalAssignment,
        "faculty_courses": facultyCourses == null
            ? []
            : List<dynamic>.from(facultyCourses!.map((x) => x.toJson())),
      };
}

class LiveClass {
  int? id;
  String? title;
  String? status;
  int? startDate;
  String? programName;
  int? expectedDuration;
  String? contentType;
  String? recordUrl;
  String? content;
  int? liveclassPresenterId;
  String? batch;
  String? pcStatus;
  String? classStatus;
  int? endDate;

  LiveClass({
    this.id,
    this.title,
    this.status,
    this.startDate,
    this.programName,
    this.expectedDuration,
    this.contentType,
    this.recordUrl,
    this.content,
    this.liveclassPresenterId,
    this.batch,
    this.pcStatus,
    this.classStatus,
    this.endDate,
  });

  factory LiveClass.fromJson(Map<String, dynamic> json) => LiveClass(
        id: json["id"],
        title: json["title"],
        status: json["status"],
        startDate: json["start_date"],
        programName: json["program_name"],
        expectedDuration: json["expected_duration"],
        contentType: json["content_type"],
        recordUrl: json["record_url"],
        content: json['content'] == '' || json['content'] == null
            ? null
            : json['content'],
        liveclassPresenterId: json["liveclass_presenter_id"],
        batch: json["batch"],
        pcStatus: json["pc_status"],
        classStatus: json["class_status"],
        endDate: json["end_date"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "status": status,
        "start_date": startDate,
        "program_name": programName,
        "expected_duration": expectedDuration,
        "content_type": contentType,
        "record_url": recordUrl,
        "content": content,
        "liveclass_presenter_id": liveclassPresenterId,
        "batch": batch,
        "pc_status": pcStatus,
        "class_status": classStatus,
        "end_date": endDate,
      };
}

class FacultyCourse {
  int? courseId;
  String? courseName;

  FacultyCourse({
    this.courseId,
    this.courseName,
  });

  factory FacultyCourse.fromJson(Map<String, dynamic> json) => FacultyCourse(
        courseId: json["course_id"],
        courseName: json["course_name"],
      );

  Map<String, dynamic> toJson() => {
        "course_id": courseId,
        "course_name": courseName,
      };
}

class ProgramsModule {
  int? id;
  dynamic parentId;
  int? categoryId;
  int? sessionId;
  String? level;
  String? name;
  String? description;
  String? image;
  int? startDate;
  int? endDate;
  dynamic duration;
  int? createdBy;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? organizationId;
  int? isGlobalProgram;
  int? registrationNeedApproval;
  dynamic assignedRuleId;
  dynamic weightage;
  int? certificateId;
  String? certificateNumberPattern;
  int? certificateLatestNumber;
  dynamic type;
  String? shortCode;
  dynamic gScore;
  String? subscriptionType;
  dynamic isStructured;
  dynamic isCompetition;
  dynamic terminationDays;
  dynamic organizedBy;
  dynamic competitionLevel;
  int? isPopular;
  int? isPublished;
  dynamic isJob;
  dynamic isRecommended;
  int? stepNo;
  dynamic isInternship;
  dynamic organizedById;
  dynamic sisRefModuleId;
  int? languageId;
  dynamic sisModuleId;
  int? contentApproval;
  int? departmentId;
  String? batchName;
  int? totLearners;
  String? courseName;
  String? facultyNames;

  ProgramsModule({
    this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    this.level,
    this.name,
    this.description,
    this.image,
    this.startDate,
    this.endDate,
    this.duration,
    this.createdBy,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.organizationId,
    this.isGlobalProgram,
    this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    this.certificateId,
    this.certificateNumberPattern,
    this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    this.organizedBy,
    this.competitionLevel,
    this.isPopular,
    this.isPublished,
    this.isJob,
    this.isRecommended,
    this.stepNo,
    this.isInternship,
    this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    this.contentApproval,
    this.departmentId,
    this.batchName,
    this.totLearners,
    this.courseName,
    this.facultyNames,
  });

  factory ProgramsModule.fromJson(Map<String, dynamic> json) => ProgramsModule(
        id: json["id"],
        parentId: json["parent_id"],
        categoryId: json["category_id"],
        sessionId: json["session_id"],
        level: json["level"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        duration: json["duration"],
        createdBy: json["created_by"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        organizationId: json["organization_id"],
        isGlobalProgram: json["is_global_program"],
        registrationNeedApproval: json["registration_need_approval"],
        assignedRuleId: json["assigned_rule_id"],
        weightage: json["weightage"],
        certificateId: json["certificate_id"],
        certificateNumberPattern: json["certificate_number_pattern"],
        certificateLatestNumber: json["certificate_latest_number"],
        type: json["type"],
        shortCode: json["short_code"],
        gScore: json["g_score"],
        subscriptionType: json["subscription_type"],
        isStructured: json["is_structured"],
        isCompetition: json["is_competition"],
        terminationDays: json["termination_days"],
        organizedBy: json["organized_by"],
        competitionLevel: json["competition_level"],
        isPopular: json["is_popular"],
        isPublished: json["is_published"],
        isJob: json["is_job"],
        isRecommended: json["is_recommended"],
        stepNo: json["step_no"],
        isInternship: json["is_internship"],
        organizedById: json["organized_by_id"],
        sisRefModuleId: json["sis_ref_module_id"],
        languageId: json["language_id"],
        sisModuleId: json["sis_module_id"],
        contentApproval: json["content_approval"],
        departmentId: json["department_id"],
        batchName: json["batch_name"],
        totLearners: json["tot_learners"],
        courseName: json["course_name"],
        facultyNames: json["faculty_names"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "category_id": categoryId,
        "session_id": sessionId,
        "level": level,
        "name": name,
        "description": description,
        "image": image,
        "start_date": startDate,
        "end_date": endDate,
        "duration": duration,
        "created_by": createdBy,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "organization_id": organizationId,
        "is_global_program": isGlobalProgram,
        "registration_need_approval": registrationNeedApproval,
        "assigned_rule_id": assignedRuleId,
        "weightage": weightage,
        "certificate_id": certificateId,
        "certificate_number_pattern": certificateNumberPattern,
        "certificate_latest_number": certificateLatestNumber,
        "type": type,
        "short_code": shortCode,
        "g_score": gScore,
        "subscription_type": subscriptionType,
        "is_structured": isStructured,
        "is_competition": isCompetition,
        "termination_days": terminationDays,
        "organized_by": organizedBy,
        "competition_level": competitionLevel,
        "is_popular": isPopular,
        "is_published": isPublished,
        "is_job": isJob,
        "is_recommended": isRecommended,
        "step_no": stepNo,
        "is_internship": isInternship,
        "organized_by_id": organizedById,
        "sis_ref_module_id": sisRefModuleId,
        "language_id": languageId,
        "sis_module_id": sisModuleId,
        "content_approval": contentApproval,
        "department_id": departmentId,
        "batch_name": batchName,
        "tot_learners": totLearners,
        "course_name": courseName,
        "faculty_names": facultyNames,
      };
}
