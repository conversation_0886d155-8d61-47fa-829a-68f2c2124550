// To parse this JSON data, do
//
//     final assignLearnerResponse = assignLearnerResponseFromJson(jsonString);

import 'dart:convert';

AssignLearnerResponse assignLearnerResponseFromJson(String str) => AssignLearnerResponse.fromJson(json.decode(str));

String assignLearnerResponseToJson(AssignLearnerResponse data) => json.encode(data.toJson());

class AssignLearnerResponse {
    int? status;
    String? message;
    Data? data;

    AssignLearnerResponse({
        this.status,
        this.message,
        this.data,
    });

    factory AssignLearnerResponse.fromJson(Map<String, dynamic> json) => AssignLearnerResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
    };
}

class Data {
    int? currentPage;
    List<Datum>? data;
    String? firstPageUrl;
    int? from;
    int? lastPage;
    String? lastPageUrl;
    dynamic nextPageUrl;
    String? path;
    int? perPage;
    dynamic prevPageUrl;
    int? to;
    int? total;

    Data({
        this.currentPage,
        this.data,
        this.firstPageUrl,
        this.from,
        this.lastPage,
        this.lastPageUrl,
        this.nextPageUrl,
        this.path,
        this.perPage,
        this.prevPageUrl,
        this.to,
        this.total,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        currentPage: json["current_page"],
        data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
        firstPageUrl: json["first_page_url"],
        from: json["from"],
        lastPage: json["last_page"],
        lastPageUrl: json["last_page_url"],
        nextPageUrl: json["next_page_url"],
        path: json["path"],
        perPage: json["per_page"],
        prevPageUrl: json["prev_page_url"],
        to: json["to"],
        total: json["total"],
    );

    Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
        "first_page_url": firstPageUrl,
        "from": from,
        "last_page": lastPage,
        "last_page_url": lastPageUrl,
        "next_page_url": nextPageUrl,
        "path": path,
        "per_page": perPage,
        "prev_page_url": prevPageUrl,
        "to": to,
        "total": total,
    };
}

class Datum {
    int? id;
    int? userId;
    int? programId;
    String? role;
    DateTime? createdAt;
    DateTime? updatedAt;
    int? approvalStatus;
    dynamic approvedBy;
    dynamic approvedOn;
    String? certificate;
    dynamic assignedCertificateUser;
    dynamic assignedCertificateDate;
    dynamic certificateNumber;
    int? batchId;
    dynamic gScore;
    dynamic jobStatus;
    dynamic mecUserSisRefId;
    dynamic mecProgramSisRefId;
    dynamic mecModuleSequence;
    dynamic mecModuleSemester;
    dynamic mecModuleModuleStatus;
    dynamic mecModuleModuleUserStatus;
    dynamic sessionId;
    dynamic semesterId;
    dynamic creditsGained;
    dynamic result;
    String? batchName;
    int? wordpressUserId;
    String? name;
    String? email;
    dynamic emailVerifiedAt;
    String? status;
    int? organizationId;
    int? locationId;
    dynamic departmentId;
    dynamic designationId;
    dynamic reportsToId;
    String? mobileNo;
    dynamic pincode;
    dynamic permanentAddress;
    dynamic dateOfBirth;
    dynamic employeeCode;
    dynamic profileImage;
    dynamic fatherName;
    dynamic motherName;
    int? isDemoUser;
    dynamic demoUrl;
    int? demoStatus;
    dynamic functionalArea;
    dynamic dateOfJoining;
    dynamic designationLevelId;
    dynamic username1;
    String? username;
    dynamic description;
    dynamic score;
    dynamic locale;
    dynamic userType;
    dynamic empType;
    dynamic actualRole;
    dynamic mecRegdId;
    dynamic ssoToken;
    dynamic sisUserActualRole;
    dynamic sisUserType;
    dynamic loginType;
    dynamic tagline;
    dynamic ageGroup;
    dynamic experience;
    Pivot? pivot;

    Datum({
        this.id,
        this.userId,
        this.programId,
        this.role,
        this.createdAt,
        this.updatedAt,
        this.approvalStatus,
        this.approvedBy,
        this.approvedOn,
        this.certificate,
        this.assignedCertificateUser,
        this.assignedCertificateDate,
        this.certificateNumber,
        this.batchId,
        this.gScore,
        this.jobStatus,
        this.mecUserSisRefId,
        this.mecProgramSisRefId,
        this.mecModuleSequence,
        this.mecModuleSemester,
        this.mecModuleModuleStatus,
        this.mecModuleModuleUserStatus,
        this.sessionId,
        this.semesterId,
        this.creditsGained,
        this.result,
        this.batchName,
        this.wordpressUserId,
        this.name,
        this.email,
        this.emailVerifiedAt,
        this.status,
        this.organizationId,
        this.locationId,
        this.departmentId,
        this.designationId,
        this.reportsToId,
        this.mobileNo,
        this.pincode,
        this.permanentAddress,
        this.dateOfBirth,
        this.employeeCode,
        this.profileImage,
        this.fatherName,
        this.motherName,
        this.isDemoUser,
        this.demoUrl,
        this.demoStatus,
        this.functionalArea,
        this.dateOfJoining,
        this.designationLevelId,
        this.username1,
        this.username,
        this.description,
        this.score,
        this.locale,
        this.userType,
        this.empType,
        this.actualRole,
        this.mecRegdId,
        this.ssoToken,
        this.sisUserActualRole,
        this.sisUserType,
        this.loginType,
        this.tagline,
        this.ageGroup,
        this.experience,
        this.pivot,
    });

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        userId: json["user_id"],
        programId: json["program_id"],
        role: json["role"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        approvalStatus: json["approval_status"],
        approvedBy: json["approved_by"],
        approvedOn: json["approved_on"],
        certificate: json["certificate"],
        assignedCertificateUser: json["assigned_certificate_user"],
        assignedCertificateDate: json["assigned_certificate_date"],
        certificateNumber: json["certificate_number"],
        batchId: json["batch_id"],
        gScore: json["g_score"],
        jobStatus: json["job_status"],
        mecUserSisRefId: json["mec_user_sis_ref_id"],
        mecProgramSisRefId: json["mec_program_sis_ref_id"],
        mecModuleSequence: json["mec_module_sequence"],
        mecModuleSemester: json["mec_module_semester"],
        mecModuleModuleStatus: json["mec_module_module_status"],
        mecModuleModuleUserStatus: json["mec_module_module_user_status"],
        sessionId: json["session_id"],
        semesterId: json["semester_id"],
        creditsGained: json["credits_gained"],
        result: json["result"],
        batchName: json["batch_name"],
        wordpressUserId: json["wordpress_user_id"],
        name: json["name"],
        email: json["email"],
        emailVerifiedAt: json["email_verified_at"],
        status: json["status"],
        organizationId: json["organization_id"],
        locationId: json["location_id"],
        departmentId: json["department_id"],
        designationId: json["designation_id"],
        reportsToId: json["reports_to_id"],
        mobileNo: json["mobile_no"],
        pincode: json["pincode"],
        permanentAddress: json["permanent_address"],
        dateOfBirth: json["date_of_birth"],
        employeeCode: json["employee_code"],
        profileImage: json["profile_image"],
        fatherName: json["father_name"],
        motherName: json["mother_name"],
        isDemoUser: json["is_demo_user"],
        demoUrl: json["demo_url"],
        demoStatus: json["demo_status"],
        functionalArea: json["functional_area"],
        dateOfJoining: json["date_of_joining"],
        designationLevelId: json["designation_level_id"],
        username1: json["username1"],
        username: json["username"],
        description: json["description"],
        score: json["score"],
        locale: json["locale"],
        userType: json["user_type"],
        empType: json["emp_type"],
        actualRole: json["actual_role"],
        mecRegdId: json["mec_regd_id"],
        ssoToken: json["sso_token"],
        sisUserActualRole: json["sis_user_actual_role"],
        sisUserType: json["sis_user_type"],
        loginType: json["login_type"],
        tagline: json["tagline"],
        ageGroup: json["age_group"],
        experience: json["experience"],
        pivot: json["pivot"] == null ? null : Pivot.fromJson(json["pivot"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "program_id": programId,
        "role": role,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "approval_status": approvalStatus,
        "approved_by": approvedBy,
        "approved_on": approvedOn,
        "certificate": certificate,
        "assigned_certificate_user": assignedCertificateUser,
        "assigned_certificate_date": assignedCertificateDate,
        "certificate_number": certificateNumber,
        "batch_id": batchId,
        "g_score": gScore,
        "job_status": jobStatus,
        "mec_user_sis_ref_id": mecUserSisRefId,
        "mec_program_sis_ref_id": mecProgramSisRefId,
        "mec_module_sequence": mecModuleSequence,
        "mec_module_semester": mecModuleSemester,
        "mec_module_module_status": mecModuleModuleStatus,
        "mec_module_module_user_status": mecModuleModuleUserStatus,
        "session_id": sessionId,
        "semester_id": semesterId,
        "credits_gained": creditsGained,
        "result": result,
        "batch_name": batchName,
        "wordpress_user_id": wordpressUserId,
        "name": name,
        "email": email,
        "email_verified_at": emailVerifiedAt,
        "status": status,
        "organization_id": organizationId,
        "location_id": locationId,
        "department_id": departmentId,
        "designation_id": designationId,
        "reports_to_id": reportsToId,
        "mobile_no": mobileNo,
        "pincode": pincode,
        "permanent_address": permanentAddress,
        "date_of_birth": dateOfBirth,
        "employee_code": employeeCode,
        "profile_image": profileImage,
        "father_name": fatherName,
        "mother_name": motherName,
        "is_demo_user": isDemoUser,
        "demo_url": demoUrl,
        "demo_status": demoStatus,
        "functional_area": functionalArea,
        "date_of_joining": dateOfJoining,
        "designation_level_id": designationLevelId,
        "username1": username1,
        "username": username,
        "description": description,
        "score": score,
        "locale": locale,
        "user_type": userType,
        "emp_type": empType,
        "actual_role": actualRole,
        "mec_regd_id": mecRegdId,
        "sso_token": ssoToken,
        "sis_user_actual_role": sisUserActualRole,
        "sis_user_type": sisUserType,
        "login_type": loginType,
        "tagline": tagline,
        "age_group": ageGroup,
        "experience": experience,
        "pivot": pivot?.toJson(),
    };
}

class Pivot {
    int? programId;
    int? userId;

    Pivot({
        this.programId,
        this.userId,
    });

    factory Pivot.fromJson(Map<String, dynamic> json) => Pivot(
        programId: json["program_id"],
        userId: json["user_id"],
    );

    Map<String, dynamic> toJson() => {
        "program_id": programId,
        "user_id": userId,
    };
}
