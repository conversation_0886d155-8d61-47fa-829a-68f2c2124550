// To parse this JSON data, do
//
//     final moduleLeaderProgramListResponse = moduleLeaderProgramListResponseFromJson(jsonString);

import 'dart:convert';

ModuleLeaderProgramListResponse moduleLeaderProgramListResponseFromJson(
        String str) =>
    ModuleLeaderProgramListResponse.fromJson(json.decode(str));

String moduleLeaderProgramListResponseTo<PERSON>son(
        ModuleLeaderProgramListResponse data) =>
    json.encode(data.toJson());

class ModuleLeaderProgramListResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  ModuleLeaderProgramListResponse({
    this.status,
    this.data,
    this.error,
  });

  factory ModuleLeaderProgramListResponse.fromJson(Map<String, dynamic> json) =>
      ModuleLeaderProgramListResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  List<Program>? programs;
  List<FacultyCourse>? facultyCourses;

  Data({
    this.programs,
    this.facultyCourses,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        programs: json["programs"] == null
            ? []
            : List<Program>.from(
                json["programs"]!.map((x) => Program.fromJson(x))),
        facultyCourses: json["faculty_courses"] == null ||
                json["faculty_courses"] == ""
            ? []
            : List<FacultyCourse>.from(
                json["faculty_courses"]!.map((x) => FacultyCourse.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "programs": programs == null
            ? []
            : List<dynamic>.from(programs!.map((x) => x.toJson())),
        "faculty_courses": facultyCourses == null || facultyCourses == ""
            ? []
            : List<dynamic>.from(facultyCourses!.map((x) => x.toJson())),
      };
}

class FacultyCourse {
  int? courseId;
  String? courseName;

  FacultyCourse({
    this.courseId,
    this.courseName,
  });

  factory FacultyCourse.fromJson(Map<String, dynamic> json) => FacultyCourse(
        courseId: json["course_id"],
        courseName: json["course_name"],
      );

  Map<String, dynamic> toJson() => {
        "course_id": courseId,
        "course_name": courseName,
      };
}

class Program {
  int? id;
  dynamic parentId;
  int? categoryId;
  int? sessionId;
  String? level;
  String? name;
  String? description;
  String? image;
  int? startDate;
  int? endDate;
  dynamic duration;
  int? createdBy;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? organizationId;
  int? isGlobalProgram;
  int? registrationNeedApproval;
  dynamic assignedRuleId;
  dynamic weightage;
  int? certificateId;
  String? certificateNumberPattern;
  int? certificateLatestNumber;
  dynamic type;
  String? shortCode;
  dynamic gScore;
  String? subscriptionType;
  dynamic isStructured;
  dynamic isCompetition;
  dynamic terminationDays;
  dynamic organizedBy;
  dynamic competitionLevel;
  int? isPopular;
  int? isPublished;
  dynamic isJob;
  dynamic isRecommended;
  int? stepNo;
  dynamic isInternship;
  dynamic organizedById;
  dynamic sisRefModuleId;
  int? languageId;
  dynamic sisModuleId;
  int? contentApproval;
  int? departmentId;
  String? batchName;
  String? facultyNames;
  int? totLearners;
  String? courseName;

  Program({
    this.id,
    this.parentId,
    this.categoryId,
    this.sessionId,
    this.level,
    this.name,
    this.description,
    this.image,
    this.startDate,
    this.endDate,
    this.duration,
    this.createdBy,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.organizationId,
    this.isGlobalProgram,
    this.registrationNeedApproval,
    this.assignedRuleId,
    this.weightage,
    this.certificateId,
    this.certificateNumberPattern,
    this.certificateLatestNumber,
    this.type,
    this.shortCode,
    this.gScore,
    this.subscriptionType,
    this.isStructured,
    this.isCompetition,
    this.terminationDays,
    this.organizedBy,
    this.competitionLevel,
    this.isPopular,
    this.isPublished,
    this.isJob,
    this.isRecommended,
    this.stepNo,
    this.isInternship,
    this.organizedById,
    this.sisRefModuleId,
    this.languageId,
    this.sisModuleId,
    this.contentApproval,
    this.departmentId,
    this.batchName,
    this.facultyNames,
    this.totLearners,
    this.courseName,
  });

  factory Program.fromJson(Map<String, dynamic> json) => Program(
        id: json["id"],
        parentId: json["parent_id"],
        categoryId: json["category_id"],
        sessionId: json["session_id"],
        level: json["level"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        duration: json["duration"],
        createdBy: json["created_by"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        organizationId: json["organization_id"],
        isGlobalProgram: json["is_global_program"],
        registrationNeedApproval: json["registration_need_approval"],
        assignedRuleId: json["assigned_rule_id"],
        weightage: json["weightage"],
        certificateId: json["certificate_id"],
        certificateNumberPattern: json["certificate_number_pattern"],
        certificateLatestNumber: json["certificate_latest_number"],
        type: json["type"],
        shortCode: json["short_code"],
        gScore: json["g_score"],
        subscriptionType: json["subscription_type"],
        isStructured: json["is_structured"],
        isCompetition: json["is_competition"],
        terminationDays: json["termination_days"],
        organizedBy: json["organized_by"],
        competitionLevel: json["competition_level"],
        isPopular: json["is_popular"],
        isPublished: json["is_published"],
        isJob: json["is_job"],
        isRecommended: json["is_recommended"],
        stepNo: json["step_no"],
        isInternship: json["is_internship"],
        organizedById: json["organized_by_id"],
        sisRefModuleId: json["sis_ref_module_id"],
        languageId: json["language_id"],
        sisModuleId: json["sis_module_id"],
        contentApproval: json["content_approval"],
        departmentId: json["department_id"],
        batchName: json["batch_name"],
        facultyNames: json["faculty_names"],
        totLearners: json["tot_learners"],
        courseName: json["course_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent_id": parentId,
        "category_id": categoryId,
        "session_id": sessionId,
        "level": level,
        "name": name,
        "description": description,
        "image": image,
        "start_date": startDate,
        "end_date": endDate,
        "duration": duration,
        "created_by": createdBy,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "organization_id": organizationId,
        "is_global_program": isGlobalProgram,
        "registration_need_approval": registrationNeedApproval,
        "assigned_rule_id": assignedRuleId,
        "weightage": weightage,
        "certificate_id": certificateId,
        "certificate_number_pattern": certificateNumberPattern,
        "certificate_latest_number": certificateLatestNumber,
        "type": type,
        "short_code": shortCode,
        "g_score": gScore,
        "subscription_type": subscriptionType,
        "is_structured": isStructured,
        "is_competition": isCompetition,
        "termination_days": terminationDays,
        "organized_by": organizedBy,
        "competition_level": competitionLevel,
        "is_popular": isPopular,
        "is_published": isPublished,
        "is_job": isJob,
        "is_recommended": isRecommended,
        "step_no": stepNo,
        "is_internship": isInternship,
        "organized_by_id": organizedById,
        "sis_ref_module_id": sisRefModuleId,
        "language_id": languageId,
        "sis_module_id": sisModuleId,
        "content_approval": contentApproval,
        "department_id": departmentId,
        "batch_name": batchName,
        "faculty_names": facultyNames,
        "tot_learners": totLearners,
        "course_name": courseName,
      };
}
