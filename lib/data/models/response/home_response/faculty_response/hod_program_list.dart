// To parse this JSON data, do
//
//     final hodProgramListtResponse = hodProgramListtResponseFromJson(jsonString);

import 'dart:convert';

HodProgramListtResponse hodProgramListtResponseFromJson(String str) => HodProgramListtResponse.fromJson(json.decode(str));

String hodProgramListtResponseToJson(HodProgramListtResponse data) => json.encode(data.toJson());

class HodProgramListtResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    HodProgramListtResponse({
        this.status,
        this.data,
        this.error,
    });

    factory HodProgramListtResponse.fromJson(Map<String, dynamic> json) => HodProgramListtResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    String? programs;
    List<FacultyCourse>? facultyCourses;

    Data({
        this.programs,
        this.facultyCourses,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        programs: json["programs"],
        facultyCourses: json["faculty_courses"] == null ? [] : List<FacultyCourse>.from(json["faculty_courses"]!.map((x) => FacultyCourse.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "programs": programs,
        "faculty_courses": facultyCourses == null ? [] : List<dynamic>.from(facultyCourses!.map((x) => x.toJson())),
    };
}

class FacultyCourse {
    int? courseId;
    String? courseName;

    FacultyCourse({
        this.courseId,
        this.courseName,
    });

    factory FacultyCourse.fromJson(Map<String, dynamic> json) => FacultyCourse(
        courseId: json["course_id"],
        courseName: json["course_name"],
    );

    Map<String, dynamic> toJson() => {
        "course_id": courseId,
        "course_name": courseName,
    };
}
