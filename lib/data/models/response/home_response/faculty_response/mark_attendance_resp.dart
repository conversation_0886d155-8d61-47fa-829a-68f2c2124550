// To parse this JSON data, do
//
//     final markAttendanceResponse = markAttendanceResponseFromJson(jsonString);

import 'dart:convert';

MarkAttendanceResponse markAttendanceResponseFromJson(String str) => MarkAttendanceResponse.fromJson(json.decode(str));

String markAttendanceResponseToJson(MarkAttendanceResponse data) => json.encode(data.toJson());

class MarkAttendanceResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    MarkAttendanceResponse({
        this.status,
        this.data,
        this.error,
    });

    factory MarkAttendanceResponse.fromJson(Map<String, dynamic> json) => MarkAttendanceResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<LiveClassUser>? liveClassUser;
    List<ContentDetail>? contentDetails;
    List<Batch>? batches;
    String? contentId;

    Data({
        this.liveClassUser,
        this.contentDetails,
        this.batches,
        this.contentId,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        liveClassUser: json["live_class_user"] == null ? [] : List<LiveClassUser>.from(json["live_class_user"]!.map((x) => LiveClassUser.fromJson(x))),
        contentDetails: json["content_details"] == null ? [] : List<ContentDetail>.from(json["content_details"]!.map((x) => ContentDetail.fromJson(x))),
        batches: json["batches"] == null ? [] : List<Batch>.from(json["batches"]!.map((x) => Batch.fromJson(x))),
        contentId: json["content_id"],
    );

    Map<String, dynamic> toJson() => {
        "live_class_user": liveClassUser == null ? [] : List<dynamic>.from(liveClassUser!.map((x) => x.toJson())),
        "content_details": contentDetails == null ? [] : List<dynamic>.from(contentDetails!.map((x) => x.toJson())),
        "batches": batches == null ? [] : List<dynamic>.from(batches!.map((x) => x.toJson())),
        "content_id": contentId,
    };
}

class Batch {
    String? title;
    int? id;
    int? programId;

    Batch({
        this.title,
        this.id,
        this.programId,
    });

    factory Batch.fromJson(Map<String, dynamic> json) => Batch(
        title: json["title"],
        id: json["id"],
        programId: json["program_id"],
    );

    Map<String, dynamic> toJson() => {
        "title": title,
        "id": id,
        "program_id": programId,
    };
}

class ContentDetail {
    dynamic image;
    int? id;
    String? description;
    String? title;
    String? content;
    DateTime? createdAt;
    DateTime? startDate;
    String? contentType;
    dynamic perCompletion;
    int? moduleId;
    int? skillId;
    int? programId;

    ContentDetail({
        this.image,
        this.id,
        this.description,
        this.title,
        this.content,
        this.createdAt,
        this.startDate,
        this.contentType,
        this.perCompletion,
        this.moduleId,
        this.skillId,
        this.programId,
    });

    factory ContentDetail.fromJson(Map<String, dynamic> json) => ContentDetail(
        image: json["image"],
        id: json["id"],
        description: json["description"],
        title: json["title"],
        content: json["content"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        startDate: json["start_date"] == null ? null : DateTime.parse(json["start_date"]),
        contentType: json["content_type"],
        perCompletion: json["per_completion"],
        moduleId: json["module_id"],
        skillId: json["skill_id"],
        programId: json["program_id"],
    );

    Map<String, dynamic> toJson() => {
        "image": image,
        "id": id,
        "description": description,
        "title": title,
        "content": content,
        "created_at": createdAt?.toIso8601String(),
        "start_date": startDate?.toIso8601String(),
        "content_type": contentType,
        "per_completion": perCompletion,
        "module_id": moduleId,
        "skill_id": skillId,
        "program_id": programId,
    };
}

class LiveClassUser {
    int? userId;
    String? email;
    String? name;
    String? status;

    LiveClassUser({
        this.userId,
        this.email,
        this.name,
        this.status,
    });

    factory LiveClassUser.fromJson(Map<String, dynamic> json) => LiveClassUser(
        userId: json["user_id"],
        email: json["email"],
        name: json["name"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "user_id": userId,
        "email": email,
        "name": name,
        "status": status,
    };
}
