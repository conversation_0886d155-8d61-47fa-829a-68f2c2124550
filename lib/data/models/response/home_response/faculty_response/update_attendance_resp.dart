// To parse this JSON data, do
//
//     final updateAttendanceResp = updateAttendanceRespFromJson(jsonString);

import 'dart:convert';

UpdateAttendanceResp updateAttendanceRespFromJson(String str) => UpdateAttendanceResp.fromJson(json.decode(str));

String updateAttendanceRespToJson(UpdateAttendanceResp data) => json.encode(data.toJson());

class UpdateAttendanceResp {
    int? status;
    Data? data;
    List<dynamic>? error;

    UpdateAttendanceResp({
        this.status,
        this.data,
        this.error,
    });

    factory UpdateAttendanceResp.fromJson(Map<String, dynamic> json) => UpdateAttendanceResp(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    List<UpdateLiveClassUser>? liveClassUser;

    Data({
        this.liveClassUser,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        liveClassUser: json["live_class_user"] == null ? [] : List<UpdateLiveClassUser>.from(json["live_class_user"]!.map((x) => UpdateLiveClassUser.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "live_class_user": liveClassUser == null ? [] : List<dynamic>.from(liveClassUser!.map((x) => x.toJson())),
    };
}

class UpdateLiveClassUser {
    int? userId;
    String? email;
    String? name;
    String? status;

    UpdateLiveClassUser({
        this.userId,
        this.email,
        this.name,
        this.status,
    });

    factory UpdateLiveClassUser.fromJson(Map<String, dynamic> json) => UpdateLiveClassUser(
        userId: json["user_id"],
        email: json["email"],
        name: json["name"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "user_id": userId,
        "email": email,
        "name": name,
        "status": status,
    };
}
