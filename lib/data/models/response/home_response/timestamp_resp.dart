

import 'dart:convert';

class TimeStampResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    TimeStampResponse({
        this.status,
        this.data,
        this.error,
    });

    factory TimeStampResponse.fromRawJson(String str) => TimeStampResponse.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory TimeStampResponse.fromJson(Map<String, dynamic> json) => TimeStampResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    ListClass? list;

    Data({
        this.list,
    });

    factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        list: json["list"] == null ? null : ListClass.fromJson(json["list"]),
    );

    Map<String, dynamic> toJson() => {
        "list": list?.toJson(),
    };
}

class ListClass {
    dynamic timeStamp;

    ListClass({
        this.timeStamp,
    });

    factory ListClass.fromRawJson(String str) => ListClass.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory ListClass.fromJson(Map<String, dynamic> json) => ListClass(
        timeStamp: json["time_stamp"],
    );

    Map<String, dynamic> toJson() => {
        "time_stamp": timeStamp,
    };
}
