// class SaveAnswerResponse {
//   int? status;
//   String? message;

//   SaveAnswerResponse({this.status, this.message});

//   SaveAnswerResponse.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['status'] = this.status;
//     data['message'] = this.message;
//     return data;
//   }
// }
import 'dart:convert';

class SaveAnswerResponse {
    int? status;
    Data? data;
    String? message;

    SaveAnswerResponse({
        this.status,
        this.data,
        this.message,
    });

    factory SaveAnswerResponse.fromRawJson(String str) => SaveAnswerResponse.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory SaveAnswerResponse.fromJson(Map<String, dynamic> json) => SaveAnswerResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data!.toJson(),
        "message": message,
    };
}

class Data {
    int? attemptId;

    Data({
        this.attemptId,
    });

    factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        attemptId: json["attempt_id"],
    );

    Map<String, dynamic> toJson() => {
        "attempt_id": attemptId,
    };
}
