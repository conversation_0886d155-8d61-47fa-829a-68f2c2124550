class ContentTagsResp {
  int? status;
  Data? data;
  List<String>? error;

  ContentTagsResp({this.status, this.data, this.error});

  ContentTagsResp.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data?.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<ListTags>? listTags;

  Data({this.listTags});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      listTags = <ListTags>[];
      json['list'].forEach((v) {
        listTags?.add(ListTags.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listTags != null) {
      data['list'] = listTags?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListTags {
  int? id;
  String? name;
  int? organizationId;
  int? categoryId;
  String? createdAt;
  String? updatedAt;

  ListTags(
      {this.id,
      this.name,
      this.organizationId,
      this.categoryId,
      this.createdAt,
      this.updatedAt});

  ListTags.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    organizationId = json['organization_id'];
    categoryId = json['category_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['organization_id'] = organizationId;
    data['category_id'] = categoryId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
