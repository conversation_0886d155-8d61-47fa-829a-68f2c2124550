import 'package:masterg/utils/utility.dart';

class UserInfoResponse {
  int? status;
  Data? data;
  List<String>? error;

  UserInfoResponse({this.status, this.data, this.error});

  UserInfoResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  UserData? userData;

  Data({this.userData});

  Data.fromJson(Map<String, dynamic> json) {
    userData = json['list'] != null ? UserData.fromJson(json['list']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (userData != null) {
      data['list'] = userData!.toJson();
    }
    return data;
  }
}

class UserData {
  int? id;
  String? name;
  String? username;
  String? profileImage;
  int? dateOfBirth;
  String? email;
  String? mobileNo;
  String? firmName;
  String? alternateMobileNo;
  String? whatsappId;
  String? instagram;
  String? facebookId;
  String? twitter;
  String? spouseOccupation;
  String? spouseDob;
  String? spouseLastName;
  String? spouseFirstName;
  int? isAgree;
  int? isChildren;
  String? gender;
  String? educationQualification;
  String? address1;
  String? address2;
  String? landmark;
  String? postOffice;
  String? state;
  int? city;
  int? maritalStatus;
  int? totalCoins;
  String? department;
  String? designation;
  String? senior;
  String? langauge;
  String? location;
  String? branch;
  String? area;
  String? territory;

  UserData(
      {this.id,
      this.name,
      this.profileImage,
      this.dateOfBirth,
      this.email,
      this.mobileNo,
      this.firmName,
      this.alternateMobileNo,
      this.whatsappId,
      this.instagram,
      this.facebookId,
      this.twitter,
      this.username,
      this.spouseOccupation,
      this.spouseDob,
      this.spouseLastName,
      this.spouseFirstName,
      this.isAgree,
      this.isChildren,
      this.gender,
      this.educationQualification,
      this.address1,
      this.address2,
      this.landmark,
      this.postOffice,
      this.state,
      this.city,
      this.maritalStatus,
      this.designation,
      this.department,
      this.senior,
      this.totalCoins,
      this.langauge,
      this.location,
      this.branch,
      this.area,
      this.territory});

  UserData.fromJson(Map<String, dynamic> json) {
    id = json['user_id'];
    name = json['name'];
    username = json['username'];
    profileImage = json['profile_image'] ?? "";
    dateOfBirth = json['date_of_birth'] ?? 0;
    email = json['email'] ?? "";
    mobileNo = json['mobile_no'] is String ? json['mobile_no'] : "";
    firmName = json['firm_name'] ?? "";
    alternateMobileNo = json['alternate_mobile_no'];
    whatsappId = json['whatsapp_id'];
    instagram = json['instagram'];
    facebookId = json['facebook_id'];
    twitter = json['twitter'];
    spouseOccupation = json['spouse_occupation'];
    spouseDob = json['spouse_dob'];
    spouseLastName = json['spouse_last_name'];
    spouseFirstName = json['spouse_first_name'];
    isAgree = json['is_agree'];
    isChildren = json['is_children'];
    gender = json['gender'];
    educationQualification = json['education_qualification'];
    address1 = json['Address1'];
    address2 = json['Address2'];
    landmark = json['landmark'];
    postOffice = json['post_office'];
    state = json['state'] is int ? json['state'].toString() : "";
    department = json['department'];
    totalCoins = json['total_coins'] ?? 0;
    designation = json['designation'];

    senior = json['senior'];

    langauge = json['mother_tongue'];

    location = json['base_location'];

    branch = json['branch'];

    area = json['asm_area'];

    territory = json['dse_territory'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['username'] = username;
    data['profile_image'] = profileImage;
    data['date_of_birth'] = dateOfBirth;
    data['email'] = email;
    data['mobile_no'] = mobileNo;
    data['firm_name'] = firmName;
    data['alternate_mobile_no'] = alternateMobileNo;
    data['whatsapp_id'] = whatsappId;
    data['instagram'] = instagram;
    data['facebook_id'] = facebookId;
    data['twitter'] = twitter;
    data['spouse_occupation'] = spouseOccupation;
    data['spouse_dob'] = spouseDob;
    data['spouse_last_name'] = spouseLastName;
    data['spouse_first_name'] = spouseFirstName;
    data['is_agree'] = isAgree;
    data['is_children'] = isChildren;
    data['gender'] = gender;
    data['education_qualification'] = educationQualification;
    data['Address1'] = address1;
    data['Address2'] = address2;
    data['landmark'] = landmark;
    data['post_office'] = postOffice;
    data['state'] = state;
    data['city'] = city;
    data['marital_status'] = maritalStatus;
    data['total_coins'] = totalCoins;
    data['department'] = department;
    data['designation'] = designation;
    data['branch'] = branch;
    data['asm_area'] = area;
    data['dse_territory'] = territory;
    data['base_location'] = location;
    data['mother_tongue'] = langauge;
    data['senior'] = senior;

    return data;
  }
}

class RelationData {
  String? relation;
  String? firstName;
  String? lastName;
  String? dob;
  String? mobileNo;
  String? occupation;
  String? email;

  RelationData(
      {this.relation,
      this.firstName,
      this.lastName,
      this.dob,
      this.mobileNo,
      this.email,
      this.occupation});

  RelationData.fromJson(Map<String, dynamic> json) {
    relation = json['relation'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    dob = json['dob'] is String
        ? json['dob']
        : Utility.convertDateFromMillis(json['dob'], "MM/dd/yyyy");
    mobileNo = json['mobile_no'];
    occupation = json['occupation'];
    email = json['email'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['relation'] = relation;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['dob'] = dob;
    data['mobile_no'] = mobileNo;
    data['occupation'] = occupation;
    data['email'] = email;
    return data;
  }
}
