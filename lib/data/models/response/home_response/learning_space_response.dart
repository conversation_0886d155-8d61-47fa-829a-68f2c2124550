class LearningSpaceResponse {
  int? status;
  Data? data;
  String? datetime;
  

  LearningSpaceResponse({this.status, this.data, this.datetime,});

  LearningSpaceResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    datetime = json['datetime'];
    
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['datetime'] = datetime;
   
    return data;
  }
}

class Data {
  List<LearningSpace>? learningSpace;

  Data({this.learningSpace});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['learning_space'] != null) {
      learningSpace = <LearningSpace>[];
      json['learning_space'].forEach((v) {
        learningSpace!.add(LearningSpace.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (learningSpace != null) {
      data['learning_space'] = learningSpace!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LearningSpace {
  int? type;
  int? userId;
  int? createdAt;
  LearningData? data;

  LearningSpace({this.type, this.userId, this.createdAt, this.data});

  LearningSpace.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    userId = json['user_id'];
    createdAt = json['created_at'];
    data =
        json['data'] != null ? LearningData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['user_id'] = userId;
    data['created_at'] = createdAt;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class LearningData {
  int? id;
  String? name;
  String? description;
  int? durationInDays;
  int? durationInMinutes;
  int? startDate;
  int? endDate;
  String? categoryLevel;
  int? organizationId;
  String? level;
  String? message;
  String? actionUrl;
  String? presenter;
  String? liveclassAction;
  String? liveclassStatus;
  String? mode;
  String? categoryName;
  String? resources;
  int? totalSessions;
  int? totalEnrolled;
  List<Trainers>? trainers;
  int? totalSkills;
  String? status;
  String? readByMonths;
  int? pageCount;

  LearningData(
      {this.id,
      this.name,
      this.description,
      this.durationInDays,
      this.durationInMinutes,
      this.startDate,
      this.endDate,
      this.categoryLevel,
      this.organizationId,
      this.level,
      this.message,
      this.actionUrl,
      this.presenter,
      this.liveclassAction,
      this.liveclassStatus,
      this.mode,
      this.categoryName,
      this.resources,
      this.totalSessions,
      this.totalEnrolled,
      this.trainers,
      this.totalSkills,
      this.status,
      this.readByMonths,
      this.pageCount});

  LearningData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    durationInDays = json['duration_in_days'];
    durationInMinutes = json['duration_in_minutes'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    categoryLevel = json['category_level'];
    organizationId = json['organization_id'];
    level = json['level'];
    message = json['message'];
    actionUrl = json['action_url'];
    presenter = json['presenter'];
    liveclassAction = json['liveclass_action'];
    liveclassStatus = json['liveclass_status'];
    mode = json['mode'];
    categoryName = json['category_name'];
    resources = json['resources'];
    totalSessions = json['total_sessions'];
    totalEnrolled = json['total_enrolled'];
    if (json['trainers'] != null) {
      trainers = <Trainers>[];
      json['trainers'].forEach((v) {
        trainers!.add(Trainers.fromJson(v));
      });
    }
    totalSkills = json['total_skills'];
    status = json['status'];
    readByMonths = json['read_by_months'];
    pageCount = json['page_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['duration_in_days'] = durationInDays;
    data['duration_in_minutes'] = durationInMinutes;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['category_level'] = categoryLevel;
    data['organization_id'] = organizationId;
    data['level'] = level;
    data['message'] = message;
    data['action_url'] = actionUrl;
    data['presenter'] = presenter;
    data['liveclass_action'] = liveclassAction;
    data['liveclass_status'] = liveclassStatus;
    data['mode'] = mode;
    data['category_name'] = categoryName;
    data['resources'] = resources;
    data['total_sessions'] = totalSessions;
    data['total_enrolled'] = totalEnrolled;
    if (trainers != null) {
      data['trainers'] = trainers!.map((v) => v.toJson()).toList();
    }
    data['total_skills'] = totalSkills;
    data['status'] = status;
    data['read_by_months'] = readByMonths;
    data['page_count'] = pageCount;
    return data;
  }
}

class Trainers {
  String? name;

  Trainers({this.name});

  Trainers.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}
