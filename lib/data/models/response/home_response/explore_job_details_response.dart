class ExploreJobDetailsResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  ExploreJobDetailsResponse({this.status, this.data, this.error});

  ExploreJobDetailsResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = List<dynamic>.from(json["error"].map((x) => x));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (error != null) {
      data['error'] = error!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  ResArr? resArr;
  List<String>? skills;
  List<Program>? program;

  Data({this.resArr, this.skills, this.program});

  Data.fromJson(Map<String, dynamic> json) {
    resArr =
        json['resArr'] != null ? ResArr.fromJson(json['resArr']) : null;
    skills = json['skills'].cast<String>();
    if (json['program'] != null) {
      program = <Program>[];
      json['program'].forEach((v) {
        program!.add(Program.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (resArr != null) {
      data['resArr'] = resArr!.toJson();
    }
    data['skills'] = skills;
    if (program != null) {
      data['program'] = program!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResArr {
  int? id;
  String? designation;
  String? location;
  String? jobinfo;
  String? description;
  String? minExperience;
  String? maxExperience;
  String? company;
  String? jdUrl;
  String? vacancies;
  String? logoUrl;
  String? teLogoUrl;
  String? whiteListedKeywords;
  String? keywords;
  String? keywordsAr;
  String? email;
  String? isEasyApply;
  String? jobId;
  String? jobPostedDateTime;
  String? minSalary;
  String? maxSalary;
  String? country;
  String? state;
  String? city;
  String? functionalDomain;
  String? sectorIndustryDomain;
  String? icon;
  String? skills;
  String? programIds;
  int? gulfId;
  String? education;
  String? nationality;
  String? gender;
  String? chartKey;
  String? jobRole;
  String? jobDesignation;
  String? status;
  String? createdAt;
  String? updatedAt;
  int? jobPostedDateTimestamp;
  int? isApplied;

  ResArr(
      {this.id,
      this.designation,
      this.location,
      this.jobinfo,
      this.description,
      this.minExperience,
      this.maxExperience,
      this.company,
      this.jdUrl,
      this.vacancies,
      this.logoUrl,
      this.teLogoUrl,
      this.whiteListedKeywords,
      this.keywords,
      this.keywordsAr,
      this.email,
      this.isEasyApply,
      this.jobId,
      this.jobPostedDateTime,
      this.minSalary,
      this.maxSalary,
      this.country,
      this.state,
      this.city,
      this.functionalDomain,
      this.sectorIndustryDomain,
      this.icon,
      this.skills,
      this.programIds,
      this.gulfId,
      this.education,
      this.nationality,
      this.gender,
      this.chartKey,
      this.jobRole,
      this.jobDesignation,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.jobPostedDateTimestamp,
      this.isApplied});

  ResArr.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    designation = json['designation'];
    location = json['location'];
    jobinfo = json['jobinfo'];
    description = json['description'];
    minExperience = json['min_experience'];
    maxExperience = json['max_experience'];
    company = json['company'];
    jdUrl = json['jd_url'];
    vacancies = json['vacancies'];
    logoUrl = json['logo_url'];
    teLogoUrl = json['te_logo_url'];
    whiteListedKeywords = json['white_listed_keywords'];
    keywords = json['keywords'];
    keywordsAr = json['keywords_ar'];
    email = json['email'];
    isEasyApply = json['is_easy_apply'];
    jobId = json['job_id'];
    jobPostedDateTime = json['job_posted_date_time'];
    minSalary = json['min_Salary'];
    maxSalary = json['max_Salary'];
    country = json['Country'];
    state = json['State'];
    city = json['City'];
    functionalDomain = json['functional_Domain'];
    sectorIndustryDomain = json['sector_industry_Domain'];
    icon = json['icon'];
    skills = json['skills'];
    programIds = json['program_ids'];
    gulfId = json['gulf_id'];
    education = json['education'];
    nationality = json['nationality'];
    gender = json['gender'];
    chartKey = json['chart_key'];
    jobRole = json['job_role'];
    jobDesignation = json['job_designation'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    isApplied = json['is_applied'];
    jobPostedDateTimestamp =
        int.tryParse('${json['job_posted_date_timestamp']}') ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['designation'] = designation;
    data['location'] = location;
    data['jobinfo'] = jobinfo;
    data['description'] = description;
    data['min_experience'] = minExperience;
    data['max_experience'] = maxExperience;
    data['company'] = company;
    data['jd_url'] = jdUrl;
    data['vacancies'] = vacancies;
    data['logo_url'] = logoUrl;
    data['te_logo_url'] = teLogoUrl;
    data['white_listed_keywords'] = whiteListedKeywords;
    data['keywords'] = keywords;
    data['keywords_ar'] = keywordsAr;
    data['email'] = email;
    data['is_easy_apply'] = isEasyApply;
    data['job_id'] = jobId;
    data['job_posted_date_time'] = jobPostedDateTime;
    data['min_Salary'] = minSalary;
    data['max_Salary'] = maxSalary;
    data['Country'] = country;
    data['State'] = state;
    data['City'] = city;
    data['functional_Domain'] = functionalDomain;
    data['sector_industry_Domain'] = sectorIndustryDomain;
    data['icon'] = icon;
    data['skills'] = skills;
    data['program_ids'] = programIds;
    data['gulf_id'] = gulfId;
    data['education'] = education;
    data['nationality'] = nationality;
    data['gender'] = gender;
    data['chart_key'] = chartKey;
    data['job_role'] = jobRole;
    data['job_designation'] = jobDesignation;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['is_applied'] = isApplied;
    data['job_posted_date_timestamp'] = jobPostedDateTimestamp;
    return data;
  }
}

class Program {
  String? programName;

  Program({this.programName});

  Program.fromJson(Map<String, dynamic> json) {
    programName = json['program_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_name'] = programName;
    return data;
  }
}
