// import 'dart:convert';

// DomainListResponse domainListResponseFromJson(String str) =>
//     DomainListResponse.fromJson(json.decode(str));

// String domainListResponseToJson(DomainListResponse data) =>
//     json.encode(data.toJson());

// class DomainListResponse {
//   DomainListResponse({
//     this.data,
//     this.status,
//     this.error,
//   });

//   Data? data;
//   int? status;
//   List<dynamic>? error;

//   factory DomainListResponse.fromJson(Map<String, dynamic> json) =>
//       DomainListResponse(
//         data: Data.fromJson(json["data"]),
//         status: json["status"],
//         error: List<dynamic>.from(json["error"].map((x) => x)),
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//         "status": status,
//         "error": List<dynamic>.from(error!.map((x) => x)),
//       };
// }

// class Data {
//   Data({
//     required this.list,
//   });

//   List<ListElement> list;

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         list: List<ListElement>.from(
//             json["list"].map((x) => ListElement.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "list": List<dynamic>.from(list.map((x) => x.toJson())),
//       };
// }

// class ListElement {
//   ListElement({
//     required this.id,
//     required this.name,
//     required this.description,
//     this.background,
//     required this.status,
//     required this.organizationId,
//     required this.createdAt,
//     required this.updatedAt,
//     required this.numberOfJobs,
//     required this.growth,
//     required this.growthType,
//     this.skillId,
//     this.jobCount,
//   });

//   int id;
//   String name;
//   String description;
//   dynamic background;
//   String status;
//   int organizationId;
//   String createdAt;
//   String updatedAt;
//   String numberOfJobs;
//   String growth;
//   String growthType;
//   dynamic skillId;
//   int? jobCount;

//   factory ListElement.fromJson(Map<String, dynamic> json) => ListElement(
//         id: json["id"],
//         name: json["name"],
//         description: json["description"],
//         background: json["background"],
//         status: json["status"],
//         organizationId: json["organization_id"],
//         createdAt: json["created_at"],
//         updatedAt: json["updated_at"],
//         numberOfJobs: json["number_of_jobs"],
//         growth: json["growth"],
//         growthType: json["growth_type"],
//         skillId: json["skill_id"],
//         jobCount: json["job_count"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "description": description,
//         "background": background,
//         "status": status,
//         "organization_id": organizationId,
//         "created_at": createdAt,
//         "updated_at": updatedAt,
//         "number_of_jobs": numberOfJobs,
//         "growth": growth,
//         "growth_type": growthType,
//         "skill_id": skillId,
//         "job_count": jobCount,
//       };
// }

//TODO: Response change for MESC APP  https://digital.mescdigital.org/
class DomainListResponse {
  Data? data;
  int? status;
  List<String>? error;

  DomainListResponse({this.data, this.status, this.error});

  DomainListResponse.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    status = json['status'];
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status'] = status;
    data['error'] = error;
    return data;
  }
}

class Data {
  List<ListElement>? list;

  Data({this.list});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <ListElement>[];
      json['list'].forEach((v) {
        list!.add(ListElement.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListElement {
  int? id;
  String? name;
  String? description;
  String? keywords;
  String? addedby;
  String? addedOn;
  String? updatedby;
  String? lastupdated;
  String? status;
  String? iconUrl;
  String? numberOfJobs;
  String? growth;
  String? growthType;
  String? createdAt;
  String? updatedAt;
  int? parentId;
  int? jobCount;

  ListElement(
      {this.id,
      this.name,
      this.description,
      this.keywords,
      this.addedby,
      this.addedOn,
      this.updatedby,
      this.lastupdated,
      this.status,
      this.iconUrl,
      this.numberOfJobs,
      this.growth,
      this.growthType,
      this.createdAt,
      this.updatedAt,
      this.parentId,
      this.jobCount});

  ListElement.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    keywords = json['keywords'];
    addedby = json['addedby'];
    addedOn = json['added_on'];
    updatedby = json['updatedby'];
    lastupdated = json['lastupdated'];
    status = json['status'];
    iconUrl = json['icon_url'];
    numberOfJobs = json['number_of_jobs'];
    growth = json['growth'];
    growthType = json['growth_type'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    parentId = json['parent_id'];
    jobCount = json['job_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['keywords'] = keywords;
    data['addedby'] = addedby;
    data['added_on'] = addedOn;
    data['updatedby'] = updatedby;
    data['lastupdated'] = lastupdated;
    data['status'] = status;
    data['icon_url'] = iconUrl;
    data['number_of_jobs'] = numberOfJobs;
    data['growth'] = growth;
    data['growth_type'] = growthType;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['parent_id'] = parentId;
    data['job_count'] = jobCount;
    return data;
  }
}
