import 'dart:convert';

MyAssignmentResponse myAssignmentResponseFromJson(String str) =>
    MyAssignmentResponse.fromJson(json.decode(str));

String myAssignmentResponseToJson(MyAssignmentResponse data) =>
    json.encode(data.toJson());

class MyAssignmentResponse {
  MyAssignmentResponse({
    this.status,
    this.data,
    this.error,
  });

  int? status;
  Data? data;
  List<dynamic>? error;

  factory MyAssignmentResponse.fromJson(Map<String, dynamic> json) =>
      MyAssignmentResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        error: List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  Data({
    this.list,
  });

  List<AssignmentList>? list;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        list: List<AssignmentList>.from(
            json["list"].map((x) => AssignmentList.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "list": List<dynamic>.from(list!.map((x) => x.toJson())),
      };
}

class AssignmentList {
  AssignmentList({
    this.contentId,
    this.title,
    this.description,
    this.startDate,
    this.endDate,
    this.submissionDate,
    this.allowMultiple,
    this.isGraded,
    this.submissionMode,
    this.maximumMarks,
    this.contentType,
    this.languageId,
    this.moduleId,
    this.totalAttempts,
    this.score,
    this.status,
    this.file,
  });

  int? contentId;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  int? submissionDate;
  int? allowMultiple;
  int? isGraded;
  int? submissionMode;
  int? maximumMarks;
  String? contentType;
  int? languageId;
  int? moduleId;
  int? totalAttempts;
  dynamic score;
  String? status;
  String? file;

  factory AssignmentList.fromJson(Map<String, dynamic> json) => AssignmentList(
        contentId: json["content_id"],
        title: json["title"],
        description: json["description"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        submissionDate: json["submission_date"],
        allowMultiple: json["allow_multiple"],
        isGraded: json["is_graded"],
        submissionMode: json["submission_mode"],
        maximumMarks: json["maximum_marks"],
        contentType: json["content_type"],
        languageId: json["language_id"],
        moduleId: json["module_id"],
        totalAttempts: json["total_attempts"],
        score: json["score"],
        status: json["status"],
        file: json["file"],
      );

  Map<String, dynamic> toJson() => {
        "content_id": contentId,
        "title": title,
        "description": description,
        "start_date": startDate,
        "end_date": endDate,
        "submission_date": submissionDate,
        "allow_multiple": allowMultiple,
        "is_graded": isGraded,
        "submission_mode": submissionMode,
        "maximum_marks": maximumMarks,
        "content_type": contentType,
        "language_id": languageId,
        "module_id": moduleId,
        "total_attempts": totalAttempts,
        "score": score,
        "status": status,
        "file": file,
      };
}
