

class FeeAgreementResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  FeeAgreementResponse({this.status, this.data, this.error});

  FeeAgreementResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = List<dynamic>.from(json["error"].map((x) => x));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (this.error != null) {
      data['error'] = this.error!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? id;
  String? mecRegdId;
  int? isAccepted;
  String? acceptanceTime;
  String? agreementFile;
  String? createdAt;
  String? updatedAt;

  Data(
      {this.id,
        this.mecRegdId,
        this.isAccepted,
        this.acceptanceTime,
        this.agreementFile,
        this.createdAt,
        this.updatedAt});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    mecRegdId = json['mec_regd_id'];
    isAccepted = json['is_accepted'];
    acceptanceTime = json['acceptance_time'];
    agreementFile = json['agreement_file'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['mec_regd_id'] = this.mecRegdId;
    data['is_accepted'] = this.isAccepted;
    data['acceptance_time'] = this.acceptanceTime;
    data['agreement_file'] = this.agreementFile;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

