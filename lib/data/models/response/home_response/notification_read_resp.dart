// To parse this JSON data, do
//
//     final notificationReadResp = notificationReadRespFromJson(jsonString);

import 'dart:convert';

NotificationReadResp notificationReadRespFromJson(String str) =>
    NotificationReadResp.fromJson(json.decode(str));

String notificationReadRespToJson(NotificationReadResp data) =>
    json.encode(data.toJson());

class NotificationReadResp {
  Datum? data;
  List<String>? error;
  int? status;
  DateTime? currentDateTime;

  NotificationReadResp({
    this.data,
    this.error,
    this.status,
    this.currentDateTime,
  });

  factory NotificationReadResp.fromJson(Map<String, dynamic> json) =>
      NotificationReadResp(
        data: Datum.fromJson(json["data"]),
        error: List<String>.from(json["error"].map((x) => x)),
        status: json["status"],
        currentDateTime: DateTime.parse(json["currentDateTime"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "error": List<dynamic>.from(error!.map((x) => x)),
        "status": status,
        "currentDateTime": currentDateTime?.toIso8601String(),
      };
}

class Datum {
  Datum();

  factory Datum.fromJson(Map<String, dynamic> json) => Datum();

  Map<String, dynamic> toJson() => {};
}