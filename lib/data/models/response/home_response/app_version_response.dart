import 'dart:convert';

AppVersionResp appVersionRespFromJson(String str) =>
    AppVersionResp.fromJson(json.decode(str));

String appVersionRespToJson(AppVersionResp data) => json.encode(data.toJson());

class AppVersionResp {
  AppVersionResp({
    this.status,
    this.data,
    this.error,
  });

  int? status;
  Data? data;
  List<dynamic>? error;

  factory AppVersionResp.fromJson(Map<String, dynamic> json) => AppVersionResp(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        error: json["error"] == ""
            ? null
            : List<dynamic>.from(json["error"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null || error!.isEmpty
            ? []
            : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  Data(
      {this.deviceType,
      this.latestVersion,
      this.updateType,
      this.enablePi,
      this.termsAndConUrl});

  int? deviceType;
  String? latestVersion;
  int? updateType;
  int? enablePi;
  String? termsAndConUrl;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        deviceType: int.tryParse('${json["device_type"]}') ?? 0,
        latestVersion: json["latest_version"],
        updateType: int.tryParse('${json["update_type"]}') ?? 0,
        enablePi: int.tryParse('${json["enable_pi_ap"]}') ?? 0,
        termsAndConUrl: json["terms_url"] == null || json["terms_url"] == ""
            ? []
            : json["terms_url"],
      );

  Map<String, dynamic> toJson() => {
        "device_type": deviceType,
        "latest_version": latestVersion,
        "update_type": updateType,
        "enable_pi_ap": enablePi
      };
}
