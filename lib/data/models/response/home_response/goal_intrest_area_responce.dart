
class GoalInterestAreaResponse {
  final int? status;
  final List<GoalInterestArea>? data;
  final List<dynamic>? error;

  GoalInterestAreaResponse({
     this.status,
     this.data,
     this.error,
  });

  factory GoalInterestAreaResponse.fromJson(Map<String, dynamic> json) {
    return GoalInterestAreaResponse(
      status: json['status'],
      data: (json['data'] as List).map((job) => GoalInterestArea.fromJson(job)).toList(),
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data!.map((job) => job.toJson()).toList(),
      'error': error,
    };
  }
}

class GoalInterestArea {
   int? id;
   //String? industryDomain;
  // String? functionalDomain;
   String? jobRole;
   /*String jobDescription;
   String? experienceRange;
   String? salaryRange;
   String? responsibilities;
   String? qualifications;
   String? postedDate;
   String? keywords;
   String? createdBy;
   String? createdDate;
   String? updatedBy;
   String? lastUpdated;
   String? status;*/
   String? bannerImage;
   //String? bannerVideo;
   /*int? parentId;
   int? organizationId;
   String? growth;
   String? growthType;
   int? approvalNeeded;*/
   dynamic isMapped;

  GoalInterestArea({
    this.id,
    //this.industryDomain,
    //this.functionalDomain,
    this.jobRole,
    // required this.jobDescription,
    // this.experienceRange,
    // this.salaryRange,
    // this.responsibilities,
    // this.qualifications,
    // this.postedDate,
    // this.keywords,
    // this.createdBy,
    // this.createdDate,
    // this.updatedBy,
    // this.lastUpdated,
    // this.status,
    this.bannerImage,
    //this.bannerVideo,
    /*this.parentId,
    this.organizationId,
    this.growth,
    this.growthType,
    this.approvalNeeded,*/
    this.isMapped,
  });

  factory GoalInterestArea.fromJson(Map<String, dynamic> json) {
    return GoalInterestArea(
      id: json["id"],
      //industryDomain: json['industry_domain'] ? '' : json['industry_domain'],
      //functionalDomain:  json['functional_domain'] ? '' : json['functional_domain'],
      jobRole: json["job_role"],
      // jobDescription: json['job_description'] ? '' : json['job_description'],
      // experienceRange: json['experience_range'] ? '' : json['experience_range'],
      // salaryRange: json['salary_range'] ? '' : json['salary_range'],
      // responsibilities: json['responsibilities'] ? '' : json['responsibilities'],
      // qualifications: json['qualifications'] ? '' : json['qualifications'],
      // postedDate: json['posted_date'] ? '' : json['posted_date'],
      // keywords: json['keywords'] ? '' : json['keywords'],
      // createdBy: json['created_by'] ? '' : json['created_by'],
      // createdDate: json['created_date'] ? '' : json['created_date'],
      // updatedBy: json['updated_by'] ? '' : json['updated_by'],
      // lastUpdated: json['lastupdated'] ? '' : json['lastupdated'],
      // status: json['status'] ? '' : json['status'],
      bannerImage: json["banner_image"],
      //bannerVideo: json['banner_video'],
      /*parentId: json['parent_id'],
      organizationId: json['organization_id'],
      growth: json['growth'] ? '' : json['growth'],
      growthType: json['growth_type'] ? '' : json['growth_type'],
      approvalNeeded: json['approval_needed'],*/
      isMapped: json['is_mapped'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      //'industry_domain': industryDomain,
      //'functional_domain': functionalDomain,
       "job_role": jobRole,
      // 'job_description': jobDescription,
      // 'experience_range': experienceRange,
      // 'salary_range': salaryRange,
      // 'responsibilities': responsibilities,
      // 'qualifications': qualifications,
      // 'posted_date': postedDate,
      // 'keywords': keywords,
      // 'created_by': createdBy,
      // 'created_date': createdDate,
      // 'updated_by': updatedBy,
      // 'lastupdated': lastUpdated,
      // 'status': status,
      "banner_image": bannerImage,
      //'banner_video': bannerVideo,
      /*'parent_id': parentId,
      'organization_id': organizationId,
      'growth': growth,
      'growth_type': growthType,
      'approval_needed': approvalNeeded,*/
      'is_mapped': isMapped,
    };
  }
}

