

import 'dart:convert';

TopScoringResponse topScoringResponseFromJson(String str) => TopScoringResponse.fromJson(json.decode(str));

String topScoringResponseToJson(TopScoringResponse data) => json.encode(data.toJson());

class TopScoringResponse {
    TopScoringResponse({
        required this.status,
        required this.data,
        required this.message,
    });

    int status;
    List<Datum?>? data;
    String message;

    factory TopScoringResponse.fromJson(Map<String, dynamic> json) => TopScoringResponse(
        status: json["status"],
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        message: json["message"],
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data!.map((x) => x!.toJson())),
        "message": message,
    };
}

class Datum {
    Datum({
        this.id,
        this.name,
        this.email,
        this.profileImage,
        this.score,
        this.rank,
        this.rankOutOf,
        this.scoreRange,
        this.gainedFrom,
    });

    int? id;
    String? name;
    String? email;
    String? profileImage;
    dynamic score;
    int? rank;
    int? rankOutOf;
    int? scoreRange;
    int? gainedFrom;

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: int.parse('${json["id"] ?? 0}'),
        name: json["name"],
        email: json["email"],
        profileImage: json["profile_image"] ?? "",
        score: json["score"] == "" ? 0 : json["score"],
        rank: json["rank"] == "" ? 0 : json["rank"] ,
        rankOutOf: json["rank_out_of"] == "" ? 0 : json["rank_out_of"],
        scoreRange : json['score_range'] == "" ? 0 : json['score_range'],
        gainedFrom : json['gained_from'] == "" ? 0 : json['gained_from'],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "profile_image": profileImage,
        "score": score,
        "rank": rank,
        "rank_out_of": rankOutOf,
        'score_range' : scoreRange,
        'gained_from' : gainedFrom
    };
}
