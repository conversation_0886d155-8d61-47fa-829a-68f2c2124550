// To parse this JSON data, do
//
//     final deleteVideoResumeResponse = deleteVideoResumeResponseFromJson(jsonString);

import 'dart:convert';

DeleteVideoResumeResponse deleteVideoResumeResponseFromJson(String str) => DeleteVideoResumeResponse.fromJson(json.decode(str));

String deleteVideoResumeResponseToJson(DeleteVideoResumeResponse data) => json.encode(data.toJson());

class DeleteVideoResumeResponse {
    int? status;
    List<String>? data;
    List<dynamic>? error;

    DeleteVideoResumeResponse({
        this.status,
        this.data,
        this.error,
    });

    factory DeleteVideoResumeResponse.fromJson(Map<String, dynamic> json) => DeleteVideoResumeResponse(
        status: json["status"],
        data: List<String>.from(json["data"].map((x) => x)),
        error: List<dynamic>.from(json["error"].map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data!.map((x) => x)),
        "error": List<dynamic>.from(error!.map((x) => x)),
    };
}
