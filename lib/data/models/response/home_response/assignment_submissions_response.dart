class AssignmentSubmissionResponse {
  int? status;
  Data? data;
  List<String?>? error;

  AssignmentSubmissionResponse({this.status, this.data, this.error});

  AssignmentSubmissionResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<AssessmentDetails>? assessmentDetails;

  Data({this.assessmentDetails});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['assessment_details'] != null) {
      assessmentDetails = <AssessmentDetails>[];
      json['assessment_details'].forEach((v) {
        assessmentDetails!.add(AssessmentDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (assessmentDetails != null) {
      data['assessment_details'] =
          assessmentDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AssessmentDetails {
  int? id;
  String? title;
  String? description;
  String? startDate;
  String? endDate;
  int? submissionDate;
  String? file;
  int? allowMultiple;
  int? isGraded;
  int? submissionMode;
  String? learnerName;
  int? checkPlagiarism;
  List<SubmissionDetails>? submissionDetails;

  AssessmentDetails(
      {this.id,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.submissionDate,
      this.file,
      this.allowMultiple,
      this.isGraded,
      this.submissionMode,
      this.learnerName,
      this.checkPlagiarism,
      this.submissionDetails,});

  AssessmentDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    submissionDate = json['submission_date'];
    file = json['file'];
    allowMultiple = json['allow_multiple'];
    isGraded = json['is_graded'];
    submissionMode = json['submission_mode'];
    learnerName = json['learner_name'];
    checkPlagiarism = json['check_plagiarism'];
    if (json['submission_details'] != null) {
      submissionDetails = <SubmissionDetails>[];
      json['submission_details'].forEach((v) {
        submissionDetails!.add(SubmissionDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['submission_date'] = submissionDate;
    data['file'] = file;
    data['allow_multiple'] = allowMultiple;
    data['is_graded'] = isGraded;
    data['submission_mode'] = submissionMode;
    data['learner_name'] = learnerName;
    data['check_plagiarism'] = checkPlagiarism;
    if (submissionDetails != null) {
      data['submission_details'] =
          submissionDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SubmissionDetails {
  int? id;
  int? contentId;
  int? userId;
  String? marksObtained;
  int? isPassed;
  String? userNotes;
  String? teacherNotes;
  String? teacherFile;
  int? reviewStatus;
  String? file;
  int? createdAt;
  int? updatedAt;
  String? title;
  String? extension;
  String? pdfReportUrl;
  String? turnitinSubmissionID;

  SubmissionDetails(
      {this.id,
      this.contentId,
      this.userId,
      this.marksObtained,
      this.isPassed,
      this.userNotes,
      this.teacherNotes,
      this.reviewStatus,
      this.file,
      this.createdAt,
      this.title,
      this.updatedAt,
      this.teacherFile,
      this.extension,
      this.pdfReportUrl,
      this.turnitinSubmissionID
      });

  SubmissionDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    contentId = json['content_id'];
    userId = json['user_id'];
    marksObtained = json['marks_obtained'];
    isPassed = json['is_passed'];
    userNotes = json['user_notes'];
    teacherNotes = json['teacher_notes'];
    reviewStatus = json['review_status'];
    file = json['file'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    title = json['title'];
    teacherFile = json['teacher_file'];
    extension = json['extension'];
    pdfReportUrl = json['pdf_report_url'];
    turnitinSubmissionID = json['turnitin_submission_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['content_id'] = contentId;
    data['user_id'] = userId;
    data['marks_obtained'] = marksObtained;
    data['is_passed'] = isPassed;
    data['user_notes'] = userNotes;
    data['teacher_notes'] = teacherNotes;
    data['review_status'] = reviewStatus;
    data['file'] = file;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['title'] = title;
    data['teacher_file'] = teacherFile;
    data['extension'] = extension;
    data['pdf_report_url'] = pdfReportUrl;
    data['turnitin_submission_id'] = turnitinSubmissionID;
    return data;
  }
}
