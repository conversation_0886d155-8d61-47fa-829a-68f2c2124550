class DownloadAssessmentReportResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  DownloadAssessmentReportResponse({this.status, this.data, this.error});

  DownloadAssessmentReportResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    error = List<dynamic>.from(json["error"].map((x) => x));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (this.error != null) {
      data['error'] = this.error!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? message;
  String? fileName;
  String? file;

  Data({this.message, this.fileName, this.file});

  Data.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    fileName = json['file_name'];
    file = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['file_name'] = this.fileName;
    data['file'] = this.file;
    return data;
  }
}