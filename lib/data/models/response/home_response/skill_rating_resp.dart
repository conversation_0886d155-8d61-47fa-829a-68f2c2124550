import 'package:flutter/foundation.dart';

class SkillRatingResponse {
  int? status;
  List<Skill>? data;
  List<dynamic>? error;

  SkillRatingResponse({this.status, this.data, this.error});

  SkillRatingResponse.fromJson(Map<String, dynamic> json) {
    debugPrint('from json called');
    status = json['status'];
    if (json['data'] != null) {
      data = <Skill>[];
      json['data'].forEach((v) {
        data?.add(Skill.fromJson(v));
      });
      if (data?.length != 0) {
        data?.sort((a, b) => b.selfProficiency!.compareTo(a.selfProficiency!));
      }
    }
    if (json['error'] != null) {
      error = <dynamic>[];
      json['error'].forEach((v) {
        error?.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    debugPrint('to json called');

    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data?.map((v) => v.toJson()).toList();
    }
    if (error != null) {
      data['error'] = error?.map((v) => v).toList();
    }
    return data;
  }
}

class Skill {
  int? id;
  int? organizationId;
  int? userId;
  int? skillId;
  int? selfProficiency;
  dynamic assesedProficiency;
  dynamic assessmentContentId;
  dynamic assessmentScore;
  dynamic assesedDate;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? name;
  String? description;

  Skill(
      {this.id,
      this.organizationId,
      this.userId,
      this.skillId,
      this.selfProficiency,
      this.assesedProficiency,
      this.assessmentContentId,
      this.assessmentScore,
      this.assesedDate,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.name,
      this.description});

  Skill.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    organizationId = json['organization_id'];
    userId = json['user_id'];
    skillId = json['skill_id'];
    selfProficiency = json['self_proficiency'];
    assesedProficiency = json['assesed_proficiency'];
    assessmentContentId = json['assessment_content_id'];
    assessmentScore = json['assessment_score'];
    assesedDate = json['assesed_date'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    name = json['name'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['organization_id'] = organizationId;
    data['user_id'] = userId;
    data['skill_id'] = skillId;
    data['self_proficiency'] = selfProficiency;
    data['assesed_proficiency'] = assesedProficiency;
    data['assessment_content_id'] = assessmentContentId;
    data['assessment_score'] = assessmentScore;
    data['assesed_date'] = assesedDate;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['name'] = name;
    data['description'] = description;
    return data;
  }
}
