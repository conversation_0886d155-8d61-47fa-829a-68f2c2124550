import 'dart:convert';

CompanyListResponse companyListResponseFromJson(String str) => CompanyListResponse.fromJson(json.decode(str));

String companyListResponseToJson(CompanyListResponse data) => json.encode(data.toJson());

class CompanyListResponse {
    int? status;
    List<Company>? data;
    List<dynamic>? error;

    CompanyListResponse({
        this.status,
        this.data,
        this.error,
    });

    factory CompanyListResponse.fromJson(Map<String, dynamic> json) => CompanyListResponse(
        status: json["status"],
        data: json["data"] == null ? [] : List<Company>.from(json["data"]!.map((x) => Company.fromJson(x))),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Company {
    int? id;
    String? name;
    String? email;
    String? description;
    String? mobile;
    String? address;
    String? siteUrl;
    String? status;
    int? organizationId;
    DateTime? createdAt;
    DateTime? updatedAt;
    String? thumbnail;
    String? location;
    int? jobCount;

    Company({
        this.id,
        this.name,
        this.email,
        this.description,
        this.mobile,
        this.address,
        this.siteUrl,
        this.status,
        this.organizationId,
        this.createdAt,
        this.updatedAt,
        this.thumbnail,
        this.location,
        this.jobCount,
    });

    factory Company.fromJson(Map<String, dynamic> json) => Company(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        description: json["description"],
        mobile: json["mobile"],
        address: json["address"],
        siteUrl: json["site_url"],
        status: json["status"],
        organizationId: json["organization_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        thumbnail: json["thumbnail"],
        location: json["location"],
        jobCount: json["job_count"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "description": description,
        "mobile": mobile,
        "address": address,
        "site_url": siteUrl,
        "status": status,
        "organization_id": organizationId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "thumbnail": thumbnail,
        "location": location,
        "job_count": jobCount,
    };
}
