// To parse this JSON data, do
//
//     final assessmentCertificateResponse = assessmentCertificateResponseFromJson(jsonString);

import 'dart:convert';

AssessmentCertificateResponse assessmentCertificateResponseFromJson(String str) => AssessmentCertificateResponse.fromJson(json.decode(str));

String assessmentCertificateResponseToJson(AssessmentCertificateResponse data) => json.encode(data.toJson());

class AssessmentCertificateResponse {
    int? status;
    String? url;
    List<dynamic>? error;

    AssessmentCertificateResponse({
        this.status,
        this.url,
        this.error,
    });

    factory AssessmentCertificateResponse.fromJson(Map<String, dynamic> json) => AssessmentCertificateResponse(
        status: json["status"],
        url: json["url"],
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "url": url,
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}
