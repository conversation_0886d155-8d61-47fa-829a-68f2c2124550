class TrainingModuleResponse {
  int? status;
  Data? data;
  List<String>? error;

  TrainingModuleResponse({this.status, this.data, this.error});

  TrainingModuleResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<Module>? module;
  dynamic shortContent;

  Data({this.module, this.shortContent});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      module = <Module>[];
      json['list'].forEach((v) {
        module!.add(Module.fromJson(v));
      });
    }
    if (json['short_content'] != null) {
      shortContent = json['short_content'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (module != null) {
      data['module'] = module!.map((v) => v.toJson()).toList();
    }
    if (shortContent != null) {
      data['short_content'] = shortContent;
    }
    return data;
  }
}

class Module {
  int? id;
  String? name;
  String? image;
  int? startDate;
  int? endDate;
  String? description;
  int? durationInDays;
  Object? completion;
  Content? content;

  Module(
      {this.id,
      this.name,
      this.image,
      this.startDate,
      this.endDate,
      this.description,
      this.durationInDays,
      this.completion,
      this.content});

  Module.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    description = json['description'];
    durationInDays = json['duration_in_days'];
    completion = json['completion'];
    content =
        json['content'] != null ? Content.fromJson(json['content']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['description'] = description;
    data['duration_in_days'] = durationInDays;
    data['completion'] = completion;
    if (content != null) {
      data['content'] = content!.toJson();
    }
    return data;
  }
}

class Content {
  List<Sessions>? sessions;
  List<LearningShots>? learningShots;
  List<Assessments>? assessments;
  List<Assignments>? assignments;
  List<Polls>? polls;
  List<Polls>? survey;
  List<Scorm>? scorm;
  List<InteractiveContent>? interactiveContent;

  Content(
      {this.sessions,
      this.learningShots,
      this.assessments,
      this.assignments,
      this.polls,
      this.survey,
      this.scorm,
      this.interactiveContent});

  Content.fromJson(Map<String, dynamic> json) {
    if (json['sessions'] != null) {
      sessions = <Sessions>[];
      json['sessions'].forEach((v) {
        sessions!.add(Sessions.fromJson(v));
      });
    }
    if (json['learning_shots'] != null) {
      learningShots = <LearningShots>[];
      json['learning_shots'].forEach((v) {
        learningShots!.add(LearningShots.fromJson(v));
      });
    }
    if (json['assessments'] != null) {
      assessments = <Assessments>[];
      json['assessments'].forEach((v) {
        assessments!.add(Assessments.fromJson(v));
      });
    }
    if (json['assignments'] != null) {
      assignments = <Assignments>[];
      json['assignments'].forEach((v) {
        assignments!.add(Assignments.fromJson(v));
      });
    }
    if (json['polls'] != null) {
      polls = <Polls>[];
      json['polls'].forEach((v) {
        polls!.add(Polls.fromJson(v));
      });
    }
    if (json['survey'] != null) {
      survey = <Polls>[];
      json['survey'].forEach((v) {
        survey!.add(Polls.fromJson(v));
      });
    }
    // scorm = json['scorm'] != null ? new Scorm.fromJson(json['scorm']) : null;
    if (json['scorm'] != null) {
      scorm = <Scorm>[];
      json['scorm'].forEach((v) {
        scorm!.add(Scorm.fromJson(v));
      });
    }

    if (json['interactive_content'] != null) {
      interactiveContent = <InteractiveContent>[];
      json['interactive_content'].forEach((v) {
        interactiveContent!.add(InteractiveContent.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (sessions != null) {
      data['sessions'] = sessions!.map((v) => v.toJson()).toList();
    }
    if (learningShots != null) {
      data['learning_shots'] =
          learningShots!.map((v) => v.toJson()).toList();
    }
    if (assessments != null) {
      data['assessments'] = assessments!.map((v) => v.toJson()).toList();
    }
    if (assignments != null) {
      data['assignments'] = assignments!.map((v) => v.toJson()).toList();
    }
    if (polls != null) {
      data['polls'] = polls!.map((v) => v.toJson()).toList();
    }
    if (survey != null) {
      data['survey'] = survey!.map((v) => v.toJson()).toList();
    }
    if (scorm != null) {
      // data['scorm'] = this.scorm.toJson();
      data['scorm'] = scorm!.map((v) => v.toJson()).toList();
    }

    if (interactiveContent != null) {
      // data['scorm'] = this.scorm.toJson();
      data['interactive_content'] =
          interactiveContent!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Sessions {
  int? programContentId;
  String? title;
  String? description;
  String? image;
  int? startDate;
  int? endDate;
  int? durationInMinutes;
  String? liveclassAction;
  String? liveclassUrl;
  int? startsInMinutes;
  String? liveclassSubHeading;
  String? liveclassStatus;
  String? liveclassDescription;
  bool? isAttended;
  bool? isLive;
  String? url;
  String? status;
  String? trainerName;
  String? trainerProfilePic;
  String? contentType;
  String? zoomUrl;
  String? openUrl;

  Sessions(
      {this.programContentId,
      this.title,
      this.description,
      this.image,
      this.startDate,
      this.endDate,
      this.durationInMinutes,
      this.liveclassAction,
      this.liveclassUrl,
      this.startsInMinutes,
      this.liveclassSubHeading,
      this.liveclassDescription,
      this.isAttended,
      this.isLive,
      this.status,
      this.url,
      this.trainerName,
      this.trainerProfilePic,
      this.contentType,
      this.liveclassStatus,
      this.zoomUrl,
      this.openUrl});

  Sessions.fromJson(Map<String, dynamic> json) {
    programContentId = json['program_content_id'];
    title = json['title'];
    description = json['description'];
    image = json['image'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    durationInMinutes = json['duration_in_minutes'];
    liveclassAction = json['liveclass_action'];
    liveclassStatus = json['liveclass_status'];
    liveclassUrl = json['liveclass_url'];
    startsInMinutes = json['starts_in_minutes'];
    liveclassSubHeading = json['liveclass_sub_heading'];
    liveclassDescription = json['liveclass_description'];
    isAttended = json['is_attended'];
    isLive = json['is_live'];
    url = json['url'];
    status = json['status'];
    trainerName = json['trainer_name'];
    trainerProfilePic = json['trainer_profile_pic'];
    contentType = json['content_type'];
    zoomUrl = json['zoom_url'];
    openUrl = json['open_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_content_id'] = programContentId;
    data['title'] = title;
    data['description'] = description;
    data['image'] = image;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['duration_in_minutes'] = durationInMinutes;
    data['liveclass_action'] = liveclassAction;
    data['liveclass_status'] = liveclassStatus;
    data['liveclass_url'] = liveclassUrl;
    data['starts_in_minutes'] = startsInMinutes;
    data['liveclass_sub_heading'] = liveclassSubHeading;
    data['liveclass_description'] = liveclassDescription;
    data['is_attended'] = isAttended;
    data['is_live'] = isLive;
    data['url'] = url;
    data['status'] = status;
    data['trainer_name'] = trainerName;
    data['trainer_profile_pic'] = trainerProfilePic;
    data['content_type'] = contentType;
    return data;
  }
}

class LearningShots {
  int? programContentId;
  String? title;
  String? image;
  int? createdAt;
  String? description;
  int? dueDate;
  double? completion;
  String? contentType;
  String? url;
  String? content;
  int? noPages;
  int? durationInMinutes;

  LearningShots(
      {this.programContentId,
      this.title,
      this.image,
      this.createdAt,
      this.description,
      this.dueDate,
      this.completion,
      this.contentType,
      this.content,
      this.url,
      this.noPages,
      this.durationInMinutes});

  LearningShots.fromJson(Map<String, dynamic> json) {
    programContentId = json['program_content_id'];
    title = json['title'];
    image = json['image'];
    createdAt = json['created_at'];
    description = json['description'];
    dueDate = json['due_date'];
    completion = double.tryParse('${json['completion']}') ?? 0.0;
    contentType = json['content_type'];
    content = json['content'];
    url = json['url'];
    noPages = json['no_pages'];
    durationInMinutes = json['duration_in_minutes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_content_id'] = programContentId;
    data['title'] = title;
    data['image'] = image;
    data['created_at'] = createdAt;
    data['description'] = description;
    data['due_date'] = dueDate;
    data['completion'] = completion;
    data['content_type'] = contentType;
    data['content'] = content;
    data['url'] = url;
    data['no_pages'] = noPages;
    data['duration_in_minutes'] = durationInMinutes;
    return data;
  }
}

class Assessments {
  int? programContentId;
  int? questionsAttempted;
  bool? isCompleted;
  dynamic score;
  int? attemptDate;
  String? action;
  String? actionTitle;
  int? attemptsRemaining;
  int? programId;
  String? title;
  String? description;
  String? status;
  String? contentType;
  int? assessmentId;
  int? durationInMinutes;
  int? negativeMarks;
  int? attemptAllowed;
  int? maximumMarks;
  int? queCount;
  Object? completion;
  int? startDate;
  int? endDate;
  int? overallScore;
  int? displayScorecard;
  String? overallResult;
  String? url;
  String? assesStatus;

  Assessments(
      {this.programContentId,
      this.questionsAttempted,
      this.isCompleted,
      this.score,
      this.attemptDate,
      this.action,
      this.actionTitle,
      this.attemptsRemaining,
      this.programId,
      this.title,
      this.description,
      this.status,
      this.contentType,
      this.assessmentId,
      this.durationInMinutes,
      this.negativeMarks,
      this.attemptAllowed,
      this.maximumMarks,
      this.queCount,
      this.completion,
      this.startDate,
      this.endDate,
      this.overallScore,
      this.displayScorecard,
      this.overallResult,
      this.url,
      this.assesStatus});

  Assessments.fromJson(Map<String, dynamic> json) {
    programContentId = json['program_content_id'];
    questionsAttempted = json['questions_attempted'];
    isCompleted = json['is_completed'];
    score = json['score'];
    attemptDate = json['attempt_date'];
    action = json['action'];
    actionTitle = json['action_title'];
    attemptsRemaining = json['attempts_remaining'];
    programId = json['program_id'];
    title = json['title'];
    description = json['description'];
    status = json['status'];
    contentType = json['content_type'];
    assessmentId = json['assessment_id'];
    durationInMinutes = json['duration_in_minutes'];
    negativeMarks = json['negative_marks'];
    attemptAllowed = json['attempt_allowed'];
    maximumMarks = json['maximum_marks'];
    queCount = json['que_count'];
    completion = json['completion'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    overallScore = json['overall_score'];
    displayScorecard = json['display_scorecard'];
    overallResult = json['overall_result'];
    url = json['url'];
    assesStatus = json['asses_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_content_id'] = programContentId;
    data['questions_attempted'] = questionsAttempted;
    data['is_completed'] = isCompleted;
    data['score'] = score;
    data['attempt_date'] = attemptDate;
    data['action'] = action;
    data['action_title'] = actionTitle;
    data['attempts_remaining'] = attemptsRemaining;
    data['program_id'] = programId;
    data['title'] = title;
    data['description'] = description;
    data['status'] = status;
    data['content_type'] = contentType;
    data['assessment_id'] = assessmentId;
    data['duration_in_minutes'] = durationInMinutes;
    data['negative_marks'] = negativeMarks;
    data['attempt_allowed'] = attemptAllowed;
    data['maximum_marks'] = maximumMarks;
    data['que_count'] = queCount;
    data['completion'] = completion;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['overall_score'] = overallScore;
    data['display_scorecard'] = displayScorecard;
    data['overall_result'] = overallResult;
    data['url'] = url;
    data['asses_status'] = assesStatus;
    return data;
  }
}

class Assignments {
  int? programId;
  int? programContentId;
  String? title;
  String? description;
  String? status;
  String? contentType;
  int? maximumMarks;
  int? allowMultiple;
  Object? completion;
  int? startDate;
  int? endDate;
  int? completionTime;
  int? overallScore;
  String? overallResult;
  int? totalAttempts;
  int? isGraded;
  String? url;
  int? submissionDate;

  Assignments(
      {this.programId,
      this.programContentId,
      this.title,
      this.description,
      this.status,
      this.contentType,
      this.maximumMarks,
      this.completion,
      this.startDate,
      this.endDate,
      this.completionTime,
      this.overallScore,
      this.overallResult,
      this.url,
      this.allowMultiple,
      this.isGraded,
      this.totalAttempts,
      this.submissionDate});

  Assignments.fromJson(Map<String, dynamic> json) {
    programId = json['program_id'];
    programContentId = json['program_content_id'];
    title = json['title'];
    description = json['description'];
    status = json['status'];
    contentType = json['content_type'];
    maximumMarks = json['maximum_marks'];
    completion = json['completion'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    completionTime = json['completion_time'];
    overallScore = json['overall_score'];
    overallResult = json['overall_result'];
    url = json['url'];
    allowMultiple = json['allow_multiple'];
    totalAttempts = json['total_attempts'];
    isGraded = json['is_graded'];
    submissionDate = json['submission_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_id'] = programId;
    data['program_content_id'] = programContentId;
    data['title'] = title;
    data['description'] = description;
    data['status'] = status;
    data['content_type'] = contentType;
    data['maximum_marks'] = maximumMarks;
    data['completion'] = completion;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['completion_time'] = completionTime;
    data['overall_score'] = overallScore;
    data['overall_result'] = overallResult;
    data['url'] = url;
    data['allow_multiple'] = allowMultiple;
    data['total_attempts'] = totalAttempts;
    data['is_graded'] = isGraded;
    data['submission_date'] = submissionDate;
    return data;
  }
}

class Polls {
  int? programId;
  int? programContentId;
  String? title;
  String? description;
  String? status;
  String? contentType;
  Object? completion;
  int? startDate;
  int? endDate;
  String? url;

  Polls(
      {this.programId,
      this.programContentId,
      this.title,
      this.description,
      this.status,
      this.contentType,
      this.completion,
      this.startDate,
      this.endDate,
      this.url});

  Polls.fromJson(Map<String, dynamic> json) {
    programId = json['program_id'];
    programContentId = json['program_content_id'];
    title = json['title'];
    description = json['description'];
    status = json['status'];
    contentType = json['content_type'];
    completion = json['completion'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_id'] = programId;
    data['program_content_id'] = programContentId;
    data['title'] = title;
    data['description'] = description;
    data['status'] = status;
    data['content_type'] = contentType;
    data['completion'] = completion;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['url'] = url;
    return data;
  }
}

class Scorm {
  int? programContentId;
  int? programId;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  String? contentType;
  String? url;

  Scorm(
      {this.programContentId,
      this.programId,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.contentType,
      this.url});

  Scorm.fromJson(Map<String, dynamic> json) {
    programContentId = json['program_content_id'];
    programId = json['program_id'];
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    contentType = json['content_type'];
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['program_content_id'] = programContentId;
    data['program_id'] = programId;
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['content_type'] = contentType;
    data['url'] = url;
    return data;
  }
}

class InteractiveContent {
  int? programContentId;
  int? h5PContentId;
  int? programId;
  String? title;
  dynamic description;
  int? startDate;
  int? endDate;
  String? contentType;
  dynamic languageId;
  dynamic languageReferenceId;
  dynamic order;
  String? sortedContent;

  InteractiveContent({
    this.programContentId,
    this.h5PContentId,
    this.programId,
    this.title,
    this.description,
    this.startDate,
    this.endDate,
    this.contentType,
    this.languageId,
    this.languageReferenceId,
    this.order,
    this.sortedContent,
  });

  factory InteractiveContent.fromJson(Map<String, dynamic> json) =>
      InteractiveContent(
        programContentId: json["program_content_id"],
        h5PContentId: json["h5p_content_id"],
        programId: json["program_id"],
        title: json["title"],
        description: json["description"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        contentType: json["content_type"],
        languageId: json["language_id"],
        languageReferenceId: json["language_reference_id"],
        order: json["order"],
        sortedContent: json["sorted_content"],
      );

  Map<String, dynamic> toJson() => {
        "program_content_id": programContentId,
        "h5p_content_id": h5PContentId,
        "program_id": programId,
        "title": title,
        "description": description,
        "start_date": startDate,
        "end_date": endDate,
        "content_type": contentType,
        "language_id": languageId,
        "language_reference_id": languageReferenceId,
        "order": order,
        "sorted_content": sortedContent,
      };
}
