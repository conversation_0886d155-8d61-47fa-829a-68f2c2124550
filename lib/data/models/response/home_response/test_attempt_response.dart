import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AttemptTestResponse {
  int? status;
  Data? data;
  List<String?>? error;

  AttemptTestResponse({this.status, this.data, this.error});

  AttemptTestResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  AssessmentDetails? assessmentDetails;

  Data({this.assessmentDetails});

  Data.fromJson(Map<String, dynamic> json) {
    assessmentDetails = json['assessment_details'] != null
        ? AssessmentDetails.fromJson(json['assessment_details'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (assessmentDetails != null) {
      data['assessment_details'] = assessmentDetails!.toJson();
    }
    return data;
  }
}

class AssessmentDetails {
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  dynamic maximumMarks;
  int? passingMarks;
  int? questionCount;
  int? negativeMarking;
  int? negativeMarks;
  int? totalAttempts;
  int? attemptCount;
  int? durationInMinutes;
  int? disableBackTracking;
  String? questionSequence;
  List<Questions>? questions;

  AssessmentDetails(
      {this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.maximumMarks,
      this.passingMarks,
      this.questionCount,
      this.negativeMarking,
      this.negativeMarks,
      this.totalAttempts,
      this.attemptCount,
      this.durationInMinutes,
      this.disableBackTracking,
      this.questionSequence,
      this.questions});

  AssessmentDetails.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    maximumMarks = json['maximum_marks'];
    passingMarks = json['passing_marks'];
    questionCount = json['question_count'];
    negativeMarking = json['negative_marking'];
    negativeMarks = json['negative_marks'];
    totalAttempts = json['total_attempts'];
    attemptCount = json['attempt_count'];
    durationInMinutes = json['duration_in_minutes'];
    disableBackTracking = json['disable_back_tracking'];
    questionSequence = json['question_sequence'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['maximum_marks'] = maximumMarks;
    data['passing_marks'] = passingMarks;
    data['question_count'] = questionCount;
    data['negative_marking'] = negativeMarking;
    data['negative_marks'] = negativeMarks;
    data['total_attempts'] = totalAttempts;
    data['attempt_count'] = attemptCount;
    data['duration_in_minutes'] = durationInMinutes;
    data['disable_back_tracking'] = disableBackTracking;
    data['question_sequence'] = questionSequence;
    if (questions != null) {
      data['questions'] = questions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Questions {
  int? questionId;
  String? question;
  String? questionType;
  int? questionTypeId;
  int? negativeMarks;
  //int? marks;
  dynamic marks;
  int? attempted;
  String? difficultyLevel;
  List<int?> selectedOption = [];
  List<String>? fillStringValue = [];
  List<dynamic>? selectedMatchingOption = [];
  List<dynamic>? correctAnswerStatement = [];
  Map<int, List<String>> selectedMatchingOptionForIndex = {};
  int? timeTaken;
  List<Options>? options;
  bool? bookMark;
  List<dynamic>? questionImage = [];
  String? responseMedium;
  String? userFile;
  String? answerStatement;
  int? questionTimer;
  QuestionLabel? questionLabel;
  String? blankHtml;

  Questions(
      {this.questionId,
      this.question,
      this.questionType,
      this.questionTypeId,
      this.negativeMarks,
      this.marks,
      this.attempted,
      this.difficultyLevel,
      this.correctAnswerStatement,
      this.timeTaken,
      this.options,
      this.questionImage,
      this.responseMedium,
      this.userFile,
      this.answerStatement,
      this.questionTimer,
      this.questionLabel,
      this.blankHtml});

  Questions.fromJson(Map<String, dynamic> json) {
    questionId = json['question_id'];
    question = json['question'];
    questionType = json['question_type'];
    questionTypeId = json['question_type_id'];
    negativeMarks = json['negative_marks'];
    marks = json['marks'];
    attempted = json['attempted'];
    difficultyLevel =
        '${tr('${json['difficulty_level'].toString().toLowerCase()}')}';
    //correctAnswerStatement = json['correct_answer_statement'];
    if (json['correct_answer_statement'] != null &&
        json['correct_answer_statement'] is List) {
      correctAnswerStatement =
          json['correct_answer_statement'] as List<dynamic>;
    }
    timeTaken = json['time_taken'];
    questionImage = json['question_image'];
    responseMedium = json['response_medium'];
    userFile = json.containsKey('userFile') ? json['userFile'] : null;
    answerStatement =
        json.containsKey('answer_statement') ? json['answer_statement'] : null;
    if (json['options'] != null) {
      options = <Options>[];
      json['options'].forEach((v) {
        options!.add(new Options.fromJson(v));
      });
    }
    if (json['question_image'] != null) {
      questionImage = <String>[];
      json['question_image'].forEach((v) {
        questionImage!.add(v);
      });
    }
    questionTimer = json['question_timer'];
    if (json['question_label'] != null) {
      questionLabel = QuestionLabel.fromJson(json['question_label']);
    }

    blankHtml = json.containsKey('blank_html') ? json['blank_html'] : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['question_id'] = questionId;
    data['question'] = question;
    data['question_type'] = questionType;
    data['question_type_id'] = questionTypeId;
    data['negative_marks'] = negativeMarks;
    data['marks'] = marks;
    data['attempted'] = attempted;
    data['difficulty_level'] = difficultyLevel;
    data['correct_answer_statement'] = correctAnswerStatement;
    data['time_taken'] = timeTaken;
    data['response_medium'] = responseMedium;
    data['user_file'] = userFile;
    data['answer_statement'] = answerStatement;
    if (questionImage != null) {
      data['question_image'] = questionImage!.map((v) => v).toList();
    }
    if (options != null) {
      data['options'] = options!.map((v) => v.toJson()).toList();
    }
    data['question_timer'] = this.questionTimer;
    data['question_label'] = this.questionLabel;
    data['blank_html'] = this.blankHtml;
    return data;
  }
}

class Options {
  int? optionId;
  String? optionStatement;
  String? optionImage;
  int? attempted;
  bool selected = false;

  Options(
      {this.optionId, this.optionStatement, this.optionImage, this.attempted});

  Options.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id'];
    optionStatement = json['option_statement'];
    optionImage = json['option_image'];
    attempted = json['attempted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['option_id'] = optionId;
    data['option_statement'] = optionStatement;
    data['option_image'] = optionImage;
    data['attempted'] = attempted;
    return data;
  }
}

class QuestionLabel {
  final int? id;
  final String? name;
  final String? description;
  final String? image;
  final int? contentId;
  final String? createdAt;
  final String? updatedAt;

  QuestionLabel({
    this.id,
    this.name,
    this.description,
    this.image,
    this.contentId,
    this.createdAt,
    this.updatedAt,
  });

  factory QuestionLabel.fromJson(Map<String, dynamic> json) {
    return QuestionLabel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      image: json['image'],
      contentId: json['content_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

class TestAttemptBean {
  Questions? question;
  int? id;
  Color? color;
  int? isVisited = 0;
  String? title;
  bool isBookmark = false;

  TestAttemptBean({
    this.question,
    this.isBookmark = false,
    this.id,
    this.isVisited,
    this.color,
    this.title,
  });
}
