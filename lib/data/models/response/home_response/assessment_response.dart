import 'package:easy_localization/easy_localization.dart';

class AssessmentInstructionResponse {
  int? status;
  Data? data;
  List<String>? error;

  AssessmentInstructionResponse({this.status, this.data, this.error});

  AssessmentInstructionResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  Instruction? instruction;

  Data({this.instruction});

  Data.fromJson(Map<String, dynamic> json) {
    instruction = json['instruction'] != null
        ? Instruction.fromJson(json['instruction'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (instruction != null) {
      data['instruction'] = instruction!.toJson();
    }
    return data;
  }
}

class Instruction {
  List<String>? statement;
  Details? details;

  Instruction({this.statement, this.details});

  Instruction.fromJson(Map<String, dynamic> json) {
    statement = json['statement'].cast<String>();
    details =
        json['details'] != null ? Details.fromJson(json['details']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['statement'] = statement;
    if (details != null) {
      data['details'] = details!.toJson();
    }
    return data;
  }
}

class Details {
  int? certificateId;
  String? certificateUrl;
  String? certificateHtml;
  String? certificateHtmlentities;
  String? contentId;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  int? maximumMarks;
  int? passingMarks;
  int? questionCount;
  int? attemptAllowed;
  int? durationInMinutes;
  String? difficultyLevel;
  int? attemptCount;
  int? isAttempted;
  dynamic score;
  int? displayScorecard;
  int? isReviewAllowed;
  int? submittedOnDate;
  int? isPassed;
  int? allowAfterPassing;
  int? certificate;
  String? quizType;
  int? assessmentId;
  int? isCertificate;
  String? certificateHtmlUrl;
  String? passcode;
  int? showDiagnostic;

  Details(
      {this.certificateId,
      this.certificateUrl,
      this.certificateHtml,
      this.certificateHtmlentities,
      this.contentId,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.maximumMarks,
      this.passingMarks,
      this.questionCount,
      this.attemptAllowed,
      this.durationInMinutes,
      this.difficultyLevel,
      this.attemptCount,
      this.isAttempted,
      this.score,
      this.displayScorecard,
      this.isReviewAllowed,
      this.submittedOnDate,
      this.isPassed,
      this.isCertificate,
      this.allowAfterPassing,
      this.certificate,
      this.quizType,
      this.assessmentId,
      this.certificateHtmlUrl,
      this.passcode,
      this.showDiagnostic});

  Details.fromJson(Map<String, dynamic> json) {
    certificateId = json["certificate_id"];
    certificateUrl = json["certificate_url"];
    certificateHtml = json["certificate_html"];
    certificateHtmlentities = json["certificate_htmlentities"];
    contentId = json['content_id'];
    title = json['title'];
    description = json['description'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    maximumMarks = json['maximum_marks'];
    passingMarks = json['passing_marks'];
    questionCount = json['question_count'];
    attemptAllowed = json['attempt_allowed'];
    durationInMinutes = json['duration_in_minutes'];
    difficultyLevel =
        '${tr('${json['difficulty_level'].toString().toLowerCase()}')}';
    attemptCount = json['attempt_count'];
    isAttempted = json['is_attempted'];
    score = json['score'];
    displayScorecard = json['display_scorecard'];
    isReviewAllowed = json['is_review_allowed'];
    submittedOnDate = json['submitted_on_date'];
    isPassed = json['is_passed'];
    allowAfterPassing = json['allow_after_passing'];
    certificate = json['certificate'];
    quizType = json['quiz_type'];
    assessmentId = json['assessment_id'];
    isCertificate = json['is_certificate'];
    certificateHtmlUrl = json['certificate_html_url'];
    passcode = json['passcode'];
    showDiagnostic = json['show_diagnostic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['certificate_id'] = contentId;
    data['certificate_url'] = contentId;
    data['certificate_html'] = contentId;
    data['certificate_htmlentities'] = contentId;
    data['is_certificate'] = isCertificate;
    data['content_id'] = contentId;
    data['title'] = title;
    data['description'] = description;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['maximum_marks'] = maximumMarks;
    data['passing_marks'] = passingMarks;
    data['question_count'] = questionCount;
    data['attempt_allowed'] = attemptAllowed;
    data['duration_in_minutes'] = durationInMinutes;
    data['difficulty_level'] = difficultyLevel;
    data['attempt_count'] = attemptCount;
    data['is_attempted'] = isAttempted;
    data['score'] = score;
    data['display_scorecard'] = displayScorecard;
    data['is_review_allowed'] = isReviewAllowed;
    data['submitted_on_date'] = submittedOnDate;
    data['is_passed'] = isPassed;
    data['allow_after_passing'] = allowAfterPassing;
    data['certificate'] = certificate;
    data['quiz_type'] = quizType;
    data['assessment_id'] = assessmentId;
    data['certificate_html_url'] = certificateHtmlUrl;
    data['passcode'] = passcode;
    data['show_diagnostic'] = showDiagnostic;
    return data;
  }
}
