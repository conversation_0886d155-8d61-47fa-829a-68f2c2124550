class GameResponse {
  int? status;
  Data? data;
  List<String>? error;

  GameResponse({this.status, this.data, this.error});

  GameResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    if (json['error'] != null) {
      error = <String>[];
      json['error'].forEach((v) {
        error!.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (error != null) {
      data['error'] = error!.map((v) => v).toList();
    }
    return data;
  }
}

class Data {
  List<ListData>? listData;

  Data({this.listData});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      listData = <ListData>[];
      json['list'].forEach((v) {
        listData!.add(ListData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listData != null) {
      data['list'] = listData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListData {
  int? id;
  String? title;
  String? description;
  int? coins;
  String? image;
  String? startDate;
  String? endDate;
  String? visibility;
  String? visibilityValue;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? gameUrl;

  ListData(
      {this.id,
      this.title,
      this.description,
      this.coins,
      this.image,
      this.startDate,
      this.endDate,
      this.visibility,
      this.visibilityValue,
      this.status,
      this.gameUrl,
      this.createdAt,
      this.updatedAt});

  ListData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    coins = json['coins'];
    image = json['image'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    visibility = json['visibility'];
    visibilityValue = json['visibility_value'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    gameUrl = json['game_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['coins'] = coins;
    data['image'] = image;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['visibility'] = visibility;
    data['visibility_value'] = visibilityValue;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['game_url'] = gameUrl;
    return data;
  }
}
