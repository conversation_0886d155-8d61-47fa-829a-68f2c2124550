import 'package:flutter/material.dart';

class OnBoardSessions {
  int? status;
  Data? data;
  List<dynamic>? error;

  OnBoardSessions({this.status, this.data, this.error});

  OnBoardSessions.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    if (json['error'] != null) {
      error = List<dynamic>.from(json["error"].map((x) => x));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (error != null) {
      data['error'] = List<dynamic>.from(error!.map((x) => x));
    }
    return data;
  }
}

class Data {
  Modules? modules;

  Data({this.modules});

  Data.fromJson(Map<String, dynamic> json) {
    modules =
        json['modules'] != null ? Modules.fromJson(json['modules']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (modules != null) {
      data['modules'] = modules!.toJson();
    }
    return data;
  }
}

class Modules {
  List<Liveclass>? liveclass;

  Modules({this.liveclass});

  Modules.fromJson(Map<String, dynamic> json) {
    if (json['liveclass'] != null) {
      liveclass = <Liveclass>[];
      json['liveclass'].forEach((v) {
        liveclass!.add(Liveclass.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (liveclass != null) {
      data['liveclass'] = liveclass!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Liveclass extends ChangeNotifier {
  int? id;
  String? contentType;
  String? name;
  String? description;
  int? duration;
  int? fromDate;
  String? zoomUrl;
  String? zoomPasskey;
  String? url;
  String? openUrl;
  dynamic callToAction;
  int? endDate;
  String? liveclassStatus;
  String? startTime;
  String? endTime;
  int? startTimeTs;
  int? endTimeTs;
  String? passkey;
  String? trainerName;

  Liveclass(
      {this.id,
      this.contentType,
      this.name,
      this.description,
      this.duration,
      this.fromDate,
      this.zoomUrl,
      this.zoomPasskey,
      this.url,
      this.callToAction,
      this.endDate,
      this.liveclassStatus,
      this.startTime,
      this.endTime,
      this.startTimeTs,
      this.endTimeTs,
      this.passkey,
      this.trainerName,
      this.openUrl});

  Liveclass.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    contentType = json['content_type'];
    name = json['name'];
    description = json['description'];
    duration = json['duration'];
    fromDate = json['from_date'];
    zoomUrl = json['zoom_url'];
    zoomPasskey = json['zoom_passkey'];
    url = json['url'];
    callToAction = json['call_to_action'];
    endDate = json['end_date'];
    liveclassStatus = json['liveclass_status'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    startTimeTs = json['starttime_ts'];
    endTimeTs = json['endtime_ts'];
    passkey = json['passkey'];
    openUrl = json['open_url'];
    trainerName = json['trainer_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['content_type'] = contentType;
    data['name'] = name;
    data['description'] = description;
    data['duration'] = duration;
    data['from_date'] = fromDate;
    data['zoom_url'] = zoomUrl;
    data['zoom_passkey'] = zoomPasskey;
    data['url'] = url;
    data['call_to_action'] = callToAction;
    data['end_date'] = endDate;
    data['liveclass_status'] = liveclassStatus;
    data['start_time'] = startTime;
    data['end_time'] = endTimeTs;
    data['starttime_ts'] = startTimeTs;
    data['ed_time_ts'] = endTime;
    data['passkey'] = passkey;
    data['open_url'] = openUrl;
    data['trainer_name'] = trainerName;
    return data;
  }
}

class LiveclassModel extends ChangeNotifier {
  List<Liveclass>? liveclass = [];
  List<Liveclass>? get list => liveclass;

  LiveclassModel(List<Liveclass>? liveclass) {
    this.liveclass = liveclass;
    notifyListeners();
  }

  void refreshList(List<Liveclass>? list) {
    if (list == null) {
      liveclass = [];
    } else {
      liveclass = list;
    }
    notifyListeners();
  }
}
