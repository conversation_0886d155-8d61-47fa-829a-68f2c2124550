// To parse this JSON data, do
//
//     final assessmentDetailsResponse = assessmentDetailsResponseFromJson(jsonString);

import 'dart:convert';

AssessmentDetailsResponse assessmentDetailsResponseFromJson(String str) =>
    AssessmentDetailsResponse.fromJson(json.decode(str));

String assessmentDetailsResponseToJson(AssessmentDetailsResponse data) =>
    json.encode(data.toJson());

class AssessmentDetailsResponse {
  int? status;
  Data? data;
  List<dynamic>? error;

  AssessmentDetailsResponse({
    this.status,
    this.data,
    this.error,
  });

  factory AssessmentDetailsResponse.fromJson(Map<String, dynamic> json) =>
      AssessmentDetailsResponse(
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null
            ? []
            : List<dynamic>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  AssessmentDetails? assessmentDetails;

  Data({
    this.assessmentDetails,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        assessmentDetails: json["assessment_details"] == null
            ? null
            : AssessmentDetails.fromJson(json["assessment_details"]),
      );

  Map<String, dynamic> toJson() => {
        "assessment_details": assessmentDetails?.toJson(),
      };
}

class AssessmentDetails {
  //String? certificateId;
  String? certificateUrl;
  String? certificateHtml;
  //String? certificateHtmlentities;
  String? certificatehtmlUrl;
  String? title;
  String? description;
  int? startDate;
  int? endDate;
  dynamic maximumMarks;
  int? passingMarks;
  int? questionCount;
  int? negativeMarking;
  int? negativeMarks;
  int? totalAttempts;
  int? attemptCount;
  int? durationInMinutes;
  List<Question>? questions;

  AssessmentDetails(
      {
      //this.certificateId,
      this.certificateUrl,
      this.certificateHtml,
      //this.certificateHtmlentities,
      this.title,
      this.description,
      this.startDate,
      this.endDate,
      this.maximumMarks,
      this.passingMarks,
      this.questionCount,
      this.negativeMarking,
      this.negativeMarks,
      this.totalAttempts,
      this.attemptCount,
      this.durationInMinutes,
      this.questions,
      this.certificatehtmlUrl});

  factory AssessmentDetails.fromJson(Map<String, dynamic> json) =>
      AssessmentDetails(
        //certificateId: json["certificate_id"],
        certificateUrl: json["certificate_url"],
        certificateHtml: json["certificate_html"],
        //certificateHtmlentities: json["certificate_htmlentities"],
        certificatehtmlUrl: json['certificate_html_url'],
        title: json["title"],
        description: json["description"],
        startDate: json["start_date"],
        endDate: json["end_date"],
        maximumMarks: json["maximum_marks"],
        passingMarks: json["passing_marks"],
        questionCount: json["question_count"],
        negativeMarking: json["negative_marking"],
        negativeMarks: json["negative_marks"],
        totalAttempts: json["total_attempts"],
        attemptCount: json["attempt_count"],
        durationInMinutes: json["duration_in_minutes"],
        questions: json["questions"] == null
            ? []
            : List<Question>.from(
                json["questions"]!.map((x) => Question.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        //"certificate_id": certificateId,
        "certificate_url": certificateUrl,
        "certificate_html": certificateHtml,
        //"certificate_htmlentities": certificateHtmlentities,
        " certificatehtmlUrl": certificatehtmlUrl,
        "title": title,
        "description": description,
        "start_date": startDate,
        "end_date": endDate,
        "maximum_marks": maximumMarks,
        "passing_marks": passingMarks,
        "question_count": questionCount,
        "negative_marking": negativeMarking,
        "negative_marks": negativeMarks,
        "total_attempts": totalAttempts,
        "attempt_count": attemptCount,
        "duration_in_minutes": durationInMinutes,
        "questions": questions == null
            ? []
            : List<dynamic>.from(questions!.map((x) => x.toJson())),
      };
}

class Question {
  int? questionId;
  String? question;
  List<dynamic>? questionImage;
  dynamic questionType;
  int? questionTypeId;
  dynamic responseMedium;
  int? negativeMarks;
  dynamic marks;
  int? attempted;
  DifficultyLevel? difficultyLevel;
  int? timeTaken;
  List<Option>? options;

  Question({
    this.questionId,
    this.question,
    this.questionImage,
    this.questionType,
    this.questionTypeId,
    this.responseMedium,
    this.negativeMarks,
    this.marks,
    this.attempted,
    this.difficultyLevel,
    this.timeTaken,
    this.options,
  });

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        questionId: json["question_id"],
        question: json["question"],
        questionImage: json["question_image"] == null
            ? []
            : List<dynamic>.from(json["question_image"]!.map((x) => x)),
        questionType: questionTypeValues.map[json["question_type"]],
        questionTypeId: json["question_type_id"],
        responseMedium: json["response_medium"],
        negativeMarks: json["negative_marks"],
        marks: json["marks"],
        attempted: json["attempted"],
        difficultyLevel: difficultyLevelValues.map[json["difficulty_level"]]!,
        timeTaken: json["time_taken"],
        options: json["options"] == null
            ? []
            : List<Option>.from(
                json["options"]!.map((x) => Option.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "question": question,
        "question_image": questionImage == null
            ? []
            : List<dynamic>.from(questionImage!.map((x) => x)),
        "question_type": questionTypeValues.reverse[questionType],
        "question_type_id": questionTypeId,
        "response_medium": responseMedium,
        "negative_marks": negativeMarks,
        "marks": marks,
        "attempted": attempted,
        "difficulty_level": difficultyLevelValues.reverse[difficultyLevel],
        "time_taken": timeTaken,
        "options": options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
      };
}

enum DifficultyLevel { EASY }

final difficultyLevelValues = EnumValues({"easy": DifficultyLevel.EASY});

class Option {
  int? optionId;
  String? optionStatement;
  int? attempted;

  Option({
    this.optionId,
    this.optionStatement,
    this.attempted,
  });

  factory Option.fromJson(Map<String, dynamic> json) => Option(
        optionId: json["option_id"],
        optionStatement: json["option_statement"],
        attempted: json["attempted"],
      );

  Map<String, dynamic> toJson() => {
        "option_id": optionId,
        "option_statement": optionStatement,
        "attempted": attempted,
      };
}

enum QuestionType { MCQ }

final questionTypeValues = EnumValues({"MCQ": QuestionType.MCQ});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
