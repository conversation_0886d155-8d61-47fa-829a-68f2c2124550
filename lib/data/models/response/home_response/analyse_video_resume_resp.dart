// To parse this JSON data, do
//
//     final analyseVideoResumeResponse = analyseVideoResumeResponseFromJson(jsonString);

import 'dart:convert';

AnalyseVideoResumeResponse analyseVideoResumeResponseFromJson(String str) => AnalyseVideoResumeResponse.fromJson(json.decode(str));

String analyseVideoResumeResponseToJson(AnalyseVideoResumeResponse data) => json.encode(data.toJson());

class AnalyseVideoResumeResponse {
    int? status;
    Data? data;
    List<dynamic>? error;

    AnalyseVideoResumeResponse({
        this.status,
        this.data,
        this.error,
    });

    factory AnalyseVideoResumeResponse.fromJson(Map<String, dynamic> json) => AnalyseVideoResumeResponse(
        status: json["status"],
        data: Data.fromJson(json["data"]),
        error: List<dynamic>.from(json["error"].map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
        "error": List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    ResumeDt? resumeDt;
    ResumeDtOptional? resumeDtOptional;

    Data({
        this.resumeDt,
        this.resumeDtOptional,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        resumeDt: ResumeDt.fromJson(json["resumeDt"]),
        resumeDtOptional: ResumeDtOptional.fromJson(json["resumeDtOptional"]),
    );

    Map<String, dynamic> toJson() => {
        "resumeDt": resumeDt?.toJson(),
        "resumeDtOptional": resumeDtOptional?.toJson(),
    };
}

class ResumeDt {
    int? name;
    int? emailAddress;
    int? professional;
    int? phoneNumber;
    int? address;
    int? resumeObjective;
    int? educationAndQualifications;
    int? professionalExperience;
    int? skillsAndAbilities;

    ResumeDt({
        this.name,
        this.emailAddress,
        this.professional,
        this.phoneNumber,
        this.address,
        this.resumeObjective,
        this.educationAndQualifications,
        this.professionalExperience,
        this.skillsAndAbilities,
    });

    factory ResumeDt.fromJson(Map<String, dynamic> json) => ResumeDt(
        name: json["name"],
        emailAddress: json["email address"],
        professional: json["professional"],
        phoneNumber: json["phone number"],
        address: json["address"],
        resumeObjective: json["resume objective"],
        educationAndQualifications: json["education and qualifications"],
        professionalExperience: json["professional experience"],
        skillsAndAbilities: json["skills and abilities"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "email address": emailAddress,
        "professional": professional,
        "phone number": phoneNumber,
        "address": address,
        "resume objective": resumeObjective,
        "education and qualifications": educationAndQualifications,
        "professional experience": professionalExperience,
        "skills and abilities": skillsAndAbilities,
    };
}

class ResumeDtOptional {
    int? achievementsOrRewards;
    int? certifications;
    int? professional;
    int? publications;
    int? languages;

    ResumeDtOptional({
        this.achievementsOrRewards,
        this.certifications,
        this.professional,
        this.publications,
        this.languages,
    });

    factory ResumeDtOptional.fromJson(Map<String, dynamic> json) => ResumeDtOptional(
        achievementsOrRewards: json["achievements or rewards"],
        certifications: json["certifications"],
        professional: json["professional"],
        publications: json["publications"],
        languages: json["languages"],
    );

    Map<String, dynamic> toJson() => {
        "achievements or rewards": achievementsOrRewards,
        "certifications": certifications,
        "professional": professional,
        "publications": publications,
        "languages": languages,
    };
}
