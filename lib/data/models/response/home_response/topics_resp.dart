class TopicsResp {
  int? status;
  Data? data;
  List<String>? error;

  TopicsResp({this.status, this.data, this.error});

  TopicsResp.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data?.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<ListTopics>? listTopics;

  Data({this.listTopics});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      listTopics = <ListTopics>[];
      json['list'].forEach((v) {
        listTopics?.add(ListTopics.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (listTopics != null) {
      data['list'] = listTopics?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListTopics {
  int? id;
  String? title;
  String? description;

  ListTopics({this.id, this.title, this.description});

  ListTopics.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    return data;
  }
}
