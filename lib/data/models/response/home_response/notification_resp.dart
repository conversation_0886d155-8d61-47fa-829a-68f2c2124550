class NotificationResp {
  int? status;
  Data? data;
  List<String>? error;

  NotificationResp({this.status, this.data, this.error});

  NotificationResp.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    if (json['error'] != null) {
      error = <String>[];
      json['error'].forEach((v) {
        error!.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    if (error != null) {
      data['error'] = error!.map((v) => v).toList();
    }
    return data;
  }
}

class Data {
  List<ListData>? list;

  Data({this.list});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <ListData>[];
      json['list'].forEach((v) {
        list!.add(ListData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListData {
  int? id;
  String? title;
  String? description;
  String? contentType;
  String? contentTypeId;
  int? categoryId;
  String? category;
  String? createdAt;

  ListData(
      {this.id,
      this.title,
      this.description,
      this.contentType,
      this.contentTypeId,
      this.categoryId,
      this.category,
      this.createdAt});

  ListData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    contentType = json['content_type'];
    contentTypeId = json['content_type_id'];
    categoryId = json['category_id'];
    category = json['category'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['content_type'] = contentType;
    data['content_type_id'] = contentTypeId;
    data['category_id'] = categoryId;
    data['category'] = category;
    data['created_at'] = createdAt;
    return data;
  }
}

