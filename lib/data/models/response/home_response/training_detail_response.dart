class TrainingDetailResponse {
  int? status;
  Data? data;
  List<String>? error;

  TrainingDetailResponse({this.status, this.data, this.error});

  TrainingDetailResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<DetailProgram>? list;
  dynamic skills;

  Data({this.list, this.skills});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <DetailProgram>[];
      json['list'].forEach((v) {
        list!.add(DetailProgram.fromJson(v));
      });
    }

    skills = json.containsKey('skills') ? json['skills'] : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list!.map((v) => v.toJson()).toList();
    }

    data['skills'] = skills;
    return data;
  }
}

class DetailProgram {
  Object? id;
  String? name;
  int? startDate;
  int? endDate;
  String? description;
  int? durationInDays;
  String? image;
  Object? completion;
  List<Modules>? modules;
  int? totalModules;
  String? organizedBy;
  String? competitionLevel;
  int? gscore;
  dynamic score;
  String? type;
  int? enableContentLock;
  int? certificateId;

  DetailProgram(
      {this.id,
      this.name,
      this.startDate,
      this.endDate,
      this.description,
      this.durationInDays,
      this.image,
      this.completion,
      this.modules,
      this.totalModules,
      this.organizedBy,
      this.competitionLevel,
      this.gscore,
      this.score,
      this.type,
      this.enableContentLock,
      this.certificateId});

  DetailProgram.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    description = json['description'];
    durationInDays = json['duration_in_days'];
    image = json['image'];
    completion = json['completion'];
    if (json['modules'] != null) {
      modules = <Modules>[];
      json['modules'].forEach((v) {
        modules!.add(Modules.fromJson(v));
      });
    }
    totalModules = json['total_modules'];
    organizedBy = json['organized_by'];
    competitionLevel = json['competition_level'];
    gscore = json['g_score'] ?? 0;
    //score = int.tryParse('${json['score']}') ?? 0;
    score = json['score'];
    type = json['type'].toString();
    enableContentLock = json['enable_content_lock'];
    certificateId = json['certificate_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['description'] = description;
    data['duration_in_days'] = durationInDays;
    data['image'] = image;
    data['completion'] = completion;
    if (modules != null) {
      data['modules'] = modules!.map((v) => v.toJson()).toList();
    }
    data['total_modules'] = totalModules;
    data['organized_by'] = organizedBy;
    data['competition_level'] = competitionLevel;
    data['g_score'] = gscore;
    data['score'] = score;
    data['type'] = type;
    data['enable_content_lock'] = enableContentLock;
    data['certificate_id'] = certificateId;

    return data;
  }
}

class Modules {
  int? id;
  String? name;
  String? image;
  int? startDate;
  int? endDate;
  String? description;
  int? durationInDays;
  Object? completion;
  String? url;
  int? note;
  int? video;
  int? sessions;
  int? assignments;
  int? assessments;
  int? poll;
  int? scorms;
  int? surverys;
  int? skillId;

  Modules(
      {this.id,
      this.name,
      this.image,
      this.startDate,
      this.endDate,
      this.description,
      this.durationInDays,
      this.completion,
      this.url,
      this.assignments,
      this.note,
      this.poll,
      this.sessions,
      this.video,
      this.surverys,
      this.assessments,
      this.skillId});

  Modules.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    description = json['description'];
    durationInDays = json['duration_in_days'];
    completion = json['completion'];
    url = json['url'];
    assignments = json['assignments'];
    note = json['notes'];
    poll = json['poll'];
    sessions = json['sessions'];
    video = json['videos'];
    surverys = json['surverys'];
    assessments = json['assessments'];
    skillId = json['skill_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['description'] = description;
    data['duration_in_days'] = durationInDays;
    data['completion'] = completion;
    data['url'] = url;
    data['assignments'] = assignments;
    data['note'] = note;
    data['poll'] = poll;
    data['sessions'] = sessions;
    data['video'] = video;
    data['surverys'] = surverys;
    data['assessments'] = assessments;
    data['skill_id'] = skillId;
    return data;
  }
}
