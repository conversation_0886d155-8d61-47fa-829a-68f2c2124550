class SkillSuggestionResponse {
  SkillSuggestionResponse({
    this.status,
    this.data,
    this.error,
  });
  int? status;
  List<Data>? data;
  List<dynamic>? error;

  SkillSuggestionResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = List.from(json['data']).map((e) => Data.fromJson(e)).toList();
    error = List.castFrom<dynamic, dynamic>(json['error']);
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['status'] = status;
    _data['data'] = data?.map((e) => e.toJson()).toList();
    _data['error'] = error;
    return _data;
  }
}

class Data {
  Data({
    this.id,
    this.name,
    this.description,
    this.parentId,
    this.status,
    this.organizationId,
    this.createdAt,
    this.updatedAt,
    this.salary,
    this.growth,
    this.growthType,
  });
  int? id;
  String? name;
  String? description;
  int? parentId;
  String? status;
  int? organizationId;
  String? createdAt;
  String? updatedAt;
  String? salary;
  String? growth;
  String? growthType;

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = null;
    parentId = json['parent_id'];
    status = json['status'];
    organizationId = json['organization_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    salary = null;
    growth = null;
    growthType = null;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['parent_id'] = parentId;
    data['status'] = status;
    data['organization_id'] = organizationId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['salary'] = salary;
    data['growth'] = growth;
    data['growth_type'] = growthType;
    return data;
  }
}
