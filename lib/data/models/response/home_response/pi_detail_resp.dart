
class PiDetailResponse {
    PiDetailResponse({
        this.status,
        this.message,
        this.data,
        this.error,
    });

    int? status;
    String? message;
    Data? data;
    List<dynamic>? error;

    factory PiDetailResponse.fromJson(Map<String, dynamic> json) => PiDetailResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        error: json["error"] == null ? [] : List<dynamic>.from(json["error"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
    };
}

class Data {
    Data({
        this.id,
        this.refId,
        this.name,
        this.email,
        this.mobile,
        this.status,
        this.createdAt,
        this.updatedAt,
    });

    int? id;
    int? refId;
    String? name;
    String? email;
    String? mobile;
    String? status;
    DateTime? createdAt;
    DateTime? updatedAt;

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        refId: json["ref_id"],
        name: json["name"],
        email: json["email"],
        mobile: json["mobile"],
        status: json["status"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "ref_id": refId,
        "name": name,
        "email": email,
        "mobile": mobile,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
    };
}
