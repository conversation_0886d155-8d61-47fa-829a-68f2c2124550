class TrainingProgramsResponse {
  int? status;
  Data? data;
  List<String>? error;

  TrainingProgramsResponse({this.status, this.data, this.error});

  TrainingProgramsResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    error = json['error'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data?.toJson();
    }
    data['error'] = error;
    return data;
  }
}

class Data {
  List<Program>? list;

  Data({this.list});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <Program>[];
      json['list'].forEach((v) {
        list?.add(Program.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Program {
  int? id;
  String? name;
  String? level;
  String? description;
  String? image;
  int? startDate;
  int? endDate;
  String? url;
  int? completion;

  Program(
      {this.id,
      this.name,
      this.level,
      this.description,
      this.image,
      this.startDate,
      this.endDate,
      this.url,
      this.completion});

  Program.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    level = json['level'];
    description = json['description'];
    image = json['image'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    url = json['url'];
    completion = json['completion'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['level'] = level;
    data['description'] = description;
    data['image'] = image;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['url'] = url;
    data['completion'] = completion;
    return data;
  }
}
