class SaveAnswerRequest {
  String? contentId;
  String? questionId;
  //List<int?>? optionId;
  List? optionId;
  int? markReview = 1;
  String? durationSec;
  String? userFile;
  String? answerStatement;
  int? skip;
  String? questionSequence;

  SaveAnswerRequest(
      {this.contentId,
      this.questionId,
      this.optionId,
      this.markReview = 1,
      this.durationSec,
      this.userFile,
      this.answerStatement,
      this.skip,
      this.questionSequence});

  SaveAnswerRequest.fromJson(Map<String, dynamic> json) {
    contentId = json['content_id'];
    questionId = json['question_id'];
    optionId = json['option_id'];
    markReview = json['mark_review'];
    durationSec = json['durationSec'];
    userFile = json['user_file'];
    answerStatement = json['answer_statement'];
    skip = json['skip'];
    questionSequence = json['question_sequence'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['content_id'] = contentId;
    data['question_id'] = questionId;
    data['option_id'] = optionId;
    data['mark_review'] = markReview;
    data['durationSec'] = durationSec;
    data['user_file'] = userFile;
    data['answer_statement'] = answerStatement;
    data['skip'] = skip;
    data['question_sequence'] = questionSequence;
    return data;
  }
}
