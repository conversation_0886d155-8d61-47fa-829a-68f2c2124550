class EmailRequest {
  String email;
  String optKey;
  String? deviceId;
  int? deviceType;
  String? deviceToken;
  String? locale;
  String mobileNo;
  int? skipLogin;

  EmailRequest(
      {this.email = "",
      this.optKey = "",
      this.mobileNo = "",
      this.deviceId,
      this.deviceToken,
      this.deviceType,
      this.locale,
      this.skipLogin,});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email;
    data['otp_key'] = optKey;
    data['mobile_no'] = mobileNo;
    data['device_token'] = deviceToken;
    data['device_id'] = deviceId;
    data['device_type'] = deviceType;
    data['locale'] = locale;
    data['skip_login'] = skipLogin;
    return data;
  }
}
