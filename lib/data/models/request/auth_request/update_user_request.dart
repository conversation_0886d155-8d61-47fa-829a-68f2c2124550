import 'dart:convert';

import 'package:masterg/data/models/response/home_response/user_info_response.dart';

class UpdateUserRequest {
  String? firstName;
  String? lastName;
  String? gender;
  String? mobileNo;
  String? alternateMobileNo;
  String? emailAddress;
  String? dateOfBirth;
  String? whatsappId;
  String? facebookId;
  String? twitter;
  String? instagram;
  String? spouseFirstName;
  String? spouseLastName;
  String? spouseDob;
  String? spouseOccupation;
  String? educationQualification;
  String? address1;
  String? address2;
  String? landmark;
  String? postOffice;
  int? state;
  int? city;
  String? maritalStatus;
  String? anniversaryDate;
  String? isChildren;
  String? isAgree;
  String? gstNumber;
  String? panNumber;
  String? profilePic;
  List<RelationData>? relationData;

  UpdateUserRequest(
      {this.firstName,
      this.lastName,
      this.gender,
      this.mobileNo,
      this.alternateMobileNo,
      this.emailAddress,
      this.dateOfBirth,
      this.whatsappId,
      this.facebookId,
      this.spouseFirstName,
      this.spouseLastName,
      this.spouseDob,
      this.spouseOccupation,
      this.educationQualification,
      this.address1,
      this.address2,
      this.landmark,
      this.postOffice,
      this.state,
      this.city,
      this.maritalStatus,
      this.anniversaryDate,
      this.isChildren,
      this.isAgree,
      this.twitter,
      this.instagram,
      this.profilePic,
      this.gstNumber,
      this.panNumber,
      this.relationData});

  UpdateUserRequest.fromJson(Map<String?, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    gender = json['gender'];
    mobileNo = json['mobile_no'];
    alternateMobileNo = json['alternate_mobile_no'];
    emailAddress = json['email_address'];
    dateOfBirth = json['date_of_birth'];
    whatsappId = json['whatsapp_id'];
    facebookId = json['facebook_id'];
    twitter = json['twitter'];
    instagram = json['instagram'];
    spouseFirstName = json['spouse_first_name'];
    spouseLastName = json['spouse_last_name'];
    spouseDob = json['spouse_dob'];
    spouseOccupation = json['spouse_occupation'];
    educationQualification = json['education_qualification'];
    address1 = json['Address1'];
    address2 = json['Address2'];
    landmark = json['landmark'];
    postOffice = json['post_office'];
    state = json['state'];
    profilePic = json['profile_pic'];
    city = json['city'];
    maritalStatus = json['marital_status'];
    anniversaryDate = json['anniversary_date'];
    isChildren = json['is_children'];
    isAgree = json['is_agree'];
    gstNumber = json['gst_number'];
    panNumber = json['pan_number'];
    if (json['relation'] != null) {
      relationData = <RelationData>[];
      json['relation'].forEach((v) {
        relationData?.add(RelationData.fromJson(v));
      });
    }
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['gender'] = gender;
    data['mobile_no'] = mobileNo;
    data['alternate_mobile_no'] = alternateMobileNo;
    data['email_address'] = emailAddress;
    data['date_of_birth'] = dateOfBirth;
    data['whatsapp_id'] = whatsappId;
    data['facebook_id'] = facebookId;
    data['spouse_first_name'] = spouseFirstName;
    data['spouse_last_name'] = spouseLastName;
    data['spouse_dob'] = spouseDob;
    data['spouse_occupation'] = spouseOccupation;
    data['education_qualification'] = educationQualification;
    data['Address1'] = address1;
    data['Address2'] = address2;
    data['post_office'] = postOffice;
    data['landmark'] = landmark;
    data['state'] = state;
    data['city'] = city;
    data['marital_status'] = maritalStatus;
    data['anniversary_date'] = anniversaryDate;
    data['is_children'] = isChildren;
    data['is_agree'] = isAgree;
    data['gst_number'] = gstNumber;
    data['pan_number'] = panNumber;
    data['profile_pic'] = profilePic;
    data['twitter'] = twitter;
    data['instagram'] = instagram;
    if (relationData != null) {
      final Map<String?, dynamic> relations = <String?, dynamic>{};
      relations['relation'] = relationData!.map((v) => v.toJson()).toList();
      data['relation'] = json.encode(relations).toString();
    }
    return data;
  }
}
