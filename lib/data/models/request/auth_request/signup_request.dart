class SignUpRequest {
  String? firmName;
  String? firstName;
  String? lastName;
  String? gender;
  String? mobileNo;
  String? alternateMobileNo;
  String? emailAddress;
  String? dateOfBirth;
  String? profilePic;
  String? dbCode;
  String? username;
  String? password;
  String? locale;
  String? whoiam;
  String? countryCode;

  SignUpRequest(
      {this.firmName,
      this.firstName,
      this.lastName,
      this.gender,
      this.mobileNo,
      this.alternateMobileNo,
      this.dbCode,
      this.emailAddress,
      this.dateOfBirth,
      this.profilePic,
      this.username,
      this.password,
      required this.locale,
      this.whoiam,
      this.countryCode
      });

  SignUpRequest.fromJson(Map<String, dynamic> json) {
    firmName = json['firm_name'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    gender = json['gender'];
    mobileNo = json['mobile_no'];
    alternateMobileNo = json['alternate_mobile_no'];
    emailAddress = json['email_address'];
    dateOfBirth = json['date_of_birth'];
    profilePic = json['profile_pic'];
    dbCode = json['db_code'];
    username = json['username'];
    password = json['password'];
    locale = json['locale'];
    whoiam = json['who_am_i'];
    countryCode = json['country_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['firm_name'] = firmName;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['gender'] = gender;
    data['mobile_no'] = mobileNo;
    data['alternate_mobile_no'] = alternateMobileNo;
    data['email_address'] = emailAddress;
    data['date_of_birth'] = dateOfBirth;
    data['profile_pic'] = profilePic;
    data['db_code'] = dbCode;
    data['username'] = username;
    data['password'] = password;
    data['locale'] = locale;
    data['who_am_i'] = whoiam;
    data['country_code'] = countryCode;
    return data;
  }
}
