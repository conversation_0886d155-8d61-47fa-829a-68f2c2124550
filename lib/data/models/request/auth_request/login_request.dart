import 'package:masterg/local/pref/Preference.dart';

class LoginRequest {
  String? mobileNo;
  String device_id;
  String? mobile_exist_skip;

  LoginRequest(
      {this.mobileNo,
      this.device_id = "123456789098765432112",
      this.mobile_exist_skip});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mobile_no'] = mobileNo;
    data['mobile_exist_skip'] = mobile_exist_skip;
    data['locale'] =
        Preference.getString(Preference.APP_ENGLISH_NAME).toString();

    return data;
  }
}
