class PollSubmitRequest {
  int? contentId;
  List<int>? optionId;
  int? questionId;

  PollSubmitRequest({this.contentId, this.optionId, this.questionId});

  PollSubmitRequest.fromJson(Map<String, dynamic> json) {
    contentId = json['content_id'];
    optionId = json['option_id'].cast<int>();
    questionId = json['question_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['content_id'] = contentId;
    data['option_id'] = optionId;
    data['question_id'] = questionId;
    return data;
  }
}
