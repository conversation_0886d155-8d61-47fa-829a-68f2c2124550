class FeedbackReq {
  String? title;
  String? topic;
  String? email;
  String? description;
  int? type;
  String? filePath;

  FeedbackReq(
      {this.title,
      this.email,
      this.description,
      this.topic,
      this.type,
      this.filePath});

  FeedbackReq.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    topic = json['topic'];
    email = json['email'];
    description = json['description'];
    type = json['type'];
    filePath = json['file'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['topic'] = topic;
    data['email'] = email;
    data['description'] = description;
    data['type'] = type;
    data['file'] = filePath;
    return data;
  }
}
