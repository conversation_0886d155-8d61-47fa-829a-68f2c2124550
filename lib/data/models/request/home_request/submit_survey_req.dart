class SubmitSurveyReq {
  int? contentId;
  bool? isSubmitted;
  List<QuestionSubmitted>? questionSubmitted;

  SubmitSurveyReq({this.contentId, this.questionSubmitted, this.isSubmitted});

  SubmitSurveyReq.fromJson(Map<String, dynamic> json) {
    contentId = json['content_id'];
    isSubmitted = json['is_submitted'];
    if (json['question_submitted'] != null) {
      questionSubmitted = <QuestionSubmitted>[];
      json['question_submitted'].forEach((v) {
        questionSubmitted?.add(QuestionSubmitted.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['content_id'] = contentId;
    data['is_submitted'] = isSubmitted;
    if (questionSubmitted != null) {
      data['question_submitted'] =
          questionSubmitted?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class QuestionSubmitted {
  List<int>? optionId;
  int? questionId;

  QuestionSubmitted({this.optionId, this.questionId});

  QuestionSubmitted.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id'].cast<int>();
    questionId = json['question_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['option_id'] = optionId;
    data['question_id'] = questionId;
    return data;
  }
}
