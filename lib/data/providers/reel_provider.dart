import 'package:flutter/material.dart';

enum ReelUploadingState{
  initialed, started, uploading, uploaded, error
}

class ReelUploadProvider extends ChangeNotifier {
  ReelUploadingState? reelState;
  ReelUploadProvider(){
    if(reelState != null) return;
    reelState = ReelUploadingState.initialed;
  }

  void changeState(ReelUploadingState state){
    reelState = state;
    notifyListeners();
  }
}