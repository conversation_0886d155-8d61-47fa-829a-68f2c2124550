import 'package:flutter/material.dart';

class ReelsProvider extends ChangeNotifier {
  late bool isPaused;
  late bool isMuted;
  late bool volumneIconInView = false;
  ReelsProvider(bool isPaused, isMuted) {
    this.isPaused = isPaused;
    this.isMuted = isMuted;
    notifyListeners();
  }

  void pause() {
    isPaused = true;
    notifyListeners();
  }

  void play() {
    isPaused = false;
    notifyListeners();
  }

  void mute() {
    isMuted = true;
    notifyListeners();
  }

  void unMute() {
    isMuted = false;
    notifyListeners();
  }

  void updateValue(bool isPausedNew) {
    isPaused = isPausedNew;
    notifyListeners();
  }

  void showVolumnIcon() {
    volumneIconInView = true;
    notifyListeners();
  }

  void hideVolumneIcon() {
    volumneIconInView = false;
    notifyListeners();
  }
}
