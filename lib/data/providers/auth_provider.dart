import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:masterg/data/api/api_constants.dart';
import 'package:masterg/data/api/api_response.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/email_request.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/request/auth_request/signup_request.dart';
import 'package:masterg/data/models/request/auth_request/swayam_login_request.dart';
import 'package:masterg/data/models/request/auth_request/update_user_request.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/config.dart';

class AuthProvider {
  AuthProvider({required this.api});

  ApiService api;

  Map<String, dynamic> get defaultParams => {
        "key": api.env.apiKey,
      };

  Future<ApiResponse?> loginCall({required LoginRequest request}) async {
    try {
      final response = await api.dio.post(ApiConstants.LOGIN,
          data: json.encode(request.toJson()),
          options: Options(
              method: 'POST',
              headers: {ApiConstants.API_KEY: ApiConstants().APIKeyValue()},
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      if (e is DioException && e.response != null) {
        return ApiResponse.error(e.response!.data);
      }
    }
    return null;
  }

  Future<ApiResponse?> updateUser(UpdateUserRequest request) async {
    try {
      Log.v(request.toJson());
      Map<String, dynamic>? data = request.toJson().cast<String, String>();
      if (request.profilePic != null && request.profilePic!.isNotEmpty) {
        String fileName = request.profilePic!.split('/').last;
        data['profile_pic'] = await MultipartFile.fromFile(
            '${request.profilePic}',
            filename: fileName);
      }
      Log.v(data);
      final response = await api.dio.post(ApiConstants.UPDATE_PROFILE_API,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> signUpCall(SignUpRequest request) async {
    try {
      Map<String, dynamic> data = request.toJson();
      try {
        String fileName = request.profilePic!.split('/').last;
        data['profile_pic'] = await MultipartFile.fromFile(request.profilePic!,
            filename: fileName);
      } catch (e) {}
      log('The request is $data');
      final response = await api.dio.post(
          '${ApiConstants.SIGNUP}${APK_DETAILS['package_name'] == 'com.learn_build' ? '?org=1' : ''}',
          data: FormData.fromMap(data),
          options: Options(
            method: 'POST',
            headers: {
              ApiConstants.API_KEY: ApiConstants().APIKeyValue(),
            },
          ));
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getStateList() async {
    try {
      final response = await api.dio.get(ApiConstants.STATE_API,
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));

      Log.v("response : ${response.data}");
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> getAppVerison({String? deviceType}) async {
    try {
      final response = await api.dio.get(ApiConstants.UPDATE_API,
          queryParameters: {"device_type": deviceType},
          options: Options(
              method: 'GET',
              headers: {
                ApiConstants.API_KEY: ApiConstants().APIKeyValue(),
              },
              responseType: ResponseType.json));
      log('version api resp: ${response.data}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      Log.v("23145555");
    }
    return null;
  }

  Future<ApiResponse?> getCityList(int stateId) async {
    try {
      final response = await api.dio.get("${ApiConstants.CITY_API}/$stateId",
          options: Options(
              method: 'GET',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              contentType: "application/json",
              responseType: ResponseType.json));
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {}
    return null;
  }

  Future<ApiResponse?> swayamLoginCall({SwayamLoginRequest? request}) async {
    try {
      final response = await api.dio.post(ApiConstants.SWAYAM_LOGIN,
          data: json.encode(request?.toJson()),
          options: Options(
              method: 'POST',
              headers: {ApiConstants.API_KEY: ApiConstants().APIKeyValue()},
              contentType: "application/json",
              responseType: ResponseType.json));

      // if (response.statusCode == 200 || response.statusCode == 201) {
      //   return ApiResponse.success(response);
      // }
      return ApiResponse.success(response);
    } catch (e) {
      log("return null respoe ${(e as DioException).response}");
    }
    log("return null respoe");
    return null;
  }

  Future<ApiResponse?> verifyOTP(EmailRequest request) async {
    try {
      final response = await api.dio.post(ApiConstants.VERIFY_OTP,
          data: json.encode(request.toJson()),
          options: Options(
              method: 'POST',
              headers: {ApiConstants.API_KEY: ApiConstants().APIKeyValue()},
              responseType: ResponseType.json));

      Log.v('api responseee is $response');
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data.containsKey('error') &&
            (response.data["error"] as List).isNotEmpty) {
          return ApiResponse.error(response.data);
        } else {
          return ApiResponse.success(response);
        }
      }
    } catch (e) {
      Log.v('api expecation $e');
    }
    return null;
  }

  Future<ApiResponse?> changePassword(Map<String, String> data) async {
    try {
      final response = await api.dio.post(ApiConstants.CHANGE_PASSWORD,
          data: FormData.fromMap(data),
          options: Options(
              method: 'POST',
              headers: {ApiConstants.API_KEY: ApiConstants().APIKeyValue()},
              responseType: ResponseType.json));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ApiResponse.success(response);
      }
    } catch (e) {
      Log.v('api expecation $e');
    }
    return null;
  }
}
