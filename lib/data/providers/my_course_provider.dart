import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class MyCourseProvider extends ChangeNotifier {
  late bool isPaused;
  late bool isMuted;
  late String videoUrl;

  // late VideoPlayerController _controller;

  MyCourseProvider(VideoPlayerController controller) {
    notifyListeners();
  }

  void changeController(VideoPlayerController controller) {
    // this._controller = controller;
    notifyListeners();
  }

  void pause() {
    isPaused = true;
    notifyListeners();
  }

  void play() {
    isPaused = false;
    notifyListeners();
  }

  void mute() {
    isMuted = true;
    notifyListeners();
  }

  void unMute() {
    isMuted = false;
    notifyListeners();
  }

  void updateValue(bool isPausedNew) {
    isPaused = isPausedNew;
    notifyListeners();
  }

  void changeUrl(String url) {
    videoUrl = url;
    notifyListeners();
  }
}
