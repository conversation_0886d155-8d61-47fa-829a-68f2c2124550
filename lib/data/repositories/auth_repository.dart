import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:masterg/data/models/request/auth_request/email_request.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/request/auth_request/signup_request.dart';
import 'package:masterg/data/models/request/auth_request/swayam_login_request.dart';
import 'package:masterg/data/models/request/auth_request/update_user_request.dart';
import 'package:masterg/data/models/response/auth_response/change_password_response.dart';
import 'package:masterg/data/models/response/auth_response/login_response.dart';
import 'package:masterg/data/models/response/auth_response/sign_up_response.dart';
import 'package:masterg/data/models/response/auth_response/swayam_login_response.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/data/models/response/auth_response/verify_otp_resp.dart';
import 'package:masterg/data/models/response/home_response/app_version_response.dart';
import 'package:masterg/data/models/response/home_response/city_state_response.dart';
import 'package:masterg/data/providers/auth_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/utils/Log.dart';
import '../models/response/auth_response/only_verify_otp_response.dart';

class AuthRepository {
  AuthRepository({required this.userProvider});

  final AuthProvider userProvider;

  Future<LoginResponse> loginCall({required LoginRequest request}) async {
    final response = await userProvider.loginCall(request: request);
    if (response!.success) {
      Log.v("Success DATA : ${response.body}");
      LoginResponse user = LoginResponse();
      try {
        user = LoginResponse.fromJson(response.body);
      } catch (e, stackTrace) {
        debugPrint('$stackTrace');
      }
      return user;
    } else {
      Log.v("Fail ====> ${response.body}");
      return LoginResponse.fromJson(response.body);
    }
  }

  Future<SignUpResponse> signUpCall({required SignUpRequest request}) async {
    final response = await userProvider.signUpCall(request);

    if (response!.success) {
      Log.v("response.success : ${response.body}");
      SignUpResponse users = SignUpResponse.fromJson(response.body);
      return users;
    } else {
      Log.v("response.Fail : ${response.body}");
      return SignUpResponse(error: response.body);
    }
  }

  Future<SignUpResponse?> updateUser({UpdateUserRequest? request}) async {
    final response = await userProvider.updateUser(request!);
    if (response!.success) {
      Log.v("response.success : ${response.body}");
      SignUpResponse verifyOtpResp = SignUpResponse.fromJson(response.body);
      return verifyOtpResp;
    } else {
      Log.v("====> ${response.body}");
      return SignUpResponse();
    }
  }

  Future<CityStateResp?> getStateList() async {
    final response = await userProvider.getStateList();
    if (response!.success) {
      Log.v("response.success : ${response.body}");
      CityStateResp categoryResp = CityStateResp.fromJson(response.body);
      return categoryResp;
    } else {
      Log.v("====> ${response.body}");
      return CityStateResp();
    }
  }

  Future<CityStateResp> getCityList(int stateId) async {
    final response = await userProvider.getCityList(stateId);
    if (response!.success) {
      Log.v("response.success : ${response.body}");
      CityStateResp categoryResp = CityStateResp.fromJson(response.body);
      return categoryResp;
    } else {
      Log.v("====> ${response.body}");
      return CityStateResp();
    }
  }

  Future<SwayamLoginResponse?> swayamLoginCall(
      {SwayamLoginRequest? request}) async {
    final response = await userProvider.swayamLoginCall(request: request);
    log("response data is $response");
    if (response!.success) {
      Log.v("Success DATA : ${response.body}");
      SwayamLoginResponse user = SwayamLoginResponse();

      try {
        user = SwayamLoginResponse.fromJson(response.body);
      } catch (e, stacktrace) {
        Log.v('$stacktrace');
      }
      return user;
    } else {
      return SwayamLoginResponse.fromJson(response.body);
    }
  }

  Future<VerifyOtpResp> verifyOtp({required EmailRequest request}) async {
    final response = await userProvider.verifyOTP(request);
    if (response!.success) {
      Log.v("!response.success : ${response.body}");
      VerifyOtpResp verifyOtpResp = VerifyOtpResp.fromJson(response.body);
      saveUserToken(verifyOtpResp);
      return verifyOtpResp;
    } else {
      Log.v("====> ${response.body}");
      return VerifyOtpResp(error: response.body, status: response.status);
    }
  }

  void saveUserToken(VerifyOtpResp verifyOtpResp) {
    Preference.setString(Preference.USER_TOKEN, '${verifyOtpResp.data?.token}');
    UserSession.userToken = verifyOtpResp.data!.token;
  }

  Future<OnlyVerifyOtpResponse> onlyVerifyOtp(
      {required EmailRequest request}) async {
    final response = await userProvider.verifyOTP(request);
    if (response!.success) {
      Log.v("!response.success : ${response.body}");
      OnlyVerifyOtpResponse verifyOtpResp =
          OnlyVerifyOtpResponse.fromJson(response.body);
      return verifyOtpResp;
    } else {
      Log.v("====> ${response.body}");
      return OnlyVerifyOtpResponse(
          error: response.body, status: response.status);
    }
  }

  Future<AppVersionResp> getAppVeriosn({String? deviceType}) async {
    final response = await userProvider.getAppVerison(deviceType: deviceType);
    log('version api resp: not ${response?.body}');

    try {
      if (response!.success) {
        log('version api resp: not1 ${response.body}');
        AppVersionResp resp = AppVersionResp.fromJson(response.body);
        return resp;
      } else {
        log('version api resp: not 2${response.body}');
        return AppVersionResp();
      }
    } catch (e, stackTrace) {
      log('version api resp: not 3 ${response?.body}');
      log("stacktrace:: $stackTrace");
      return AppVersionResp();
    }
  }

  Future<ChangePasswordResponse> changePassword(
      Map<String, String> data) async {
    final response = await userProvider.changePassword(data);
    if (response!.success) {
      Log.v("!response.success : ${response.body}");
      ChangePasswordResponse resp =
          ChangePasswordResponse.fromJson(response.body);
      return resp;
    } else {
      Log.v("Else ====> ${response.body}");
      return ChangePasswordResponse.fromJson(response.body);
    }
  }
}
