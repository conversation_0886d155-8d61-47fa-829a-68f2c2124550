import 'package:masterg/utils/config.dart';

class ApiConstants {
  /**Server url*/
  ///todo change Env to production when release to the client
  ///For Developer
  // static const PRODUCTION_BASE_URL = "https://learningoxygen.com/";
  //static const PRODUCTION_BASE_URL = "https://mylearning.learnandbuild.in/";
  static const DEV_BASE_URL = "https://learningoxygen.com/";
  static const IMAGE_BASE_URL = 'https://learningoxygen.com/joy_content/';
  //todo before share this change it to production

  static const API_KEY = "nlms-api-key";
  static const API_KEY_VALUE = "0612b32b39f4b29f48c5c5363028ee916bb99TECH";
  static const PI_DETAIL = 'https://pi.noesislearnings.com/api/get_user_pi/';
  static const STR_TO_DATE = 'api/convert-timestamp';

  String APIKeyValue() {
    return APK_DETAILS['nlms_api_key']!;
  } //Demo test Client

  String PRODUCTION_BASE_URL() {
    return APK_DETAILS['domain_url']!;
  }

  ///APIs
  static const LOGIN = "api/joy/signin";
  static const SIGNUP = "api/joy/signup";
  static const UPDATE_API = "api/swayam/os";
  static const VERIFY_OTP = "api/joy/validate-opt";
  static const GET_CONTENT_API = "api/joy/content/";
  static const GET_ASSESSMENT_API = "api/assessment-list";
  static const GET_ASSIGNMENT_API = "api/assignment/";
  static const LANGUAGE_API = "api/joy/language";
  static const APP_LANGUAGE_API = "api/joy/app-language";
  static const MASTER_LANGUAGE_API = "api/master-language";
  static const TRACK_ANNOUNCMENT_API = "api/joy/content-tracking";
  static const SUBSCRIBE_PROGRAM = "api/program/subscribe";
  static const TRACK_USER_ACTIVITY = "api/user-track-activity";
  static const BRAND_SEARCH = "api/master-brand-search";
  static const USER_PROFILE_API = "/api/profilev1";
  static const USER_PROFILE_SWAYAM_API = "api/joy/profile";
  static const USER_PROFILE_IMAGE_API = "/api/profile-update_v1";
  static const GET_PARTNER_API = "api/joy/rewards/partner_benefits";
  static const SUBMIT_REWARD_API = "api/joy/redeem-request";
  static const CATEGORY_API = "api/joy/category";
  static const ACTIVITY_ATTEMPT_API = "api/joy/activity-attempt";
  static const PROGRAMS_LIST = "api/learner/programs";
  static const SUBMIT_ASSIGNMENT = 'api/assignmentsubmit';
  static const CREATE_POST = "/api/create-post";
  static const MODULES = "api/learner/programs/modules";
  static const CONTENT_DETAILS = "api/learner/content";
  static const USER_ANALYTICS = "api/user-analytics";
  static const LEARNINGSPACE_DATA = "api/learning-space";
  static const ASSIGNMENT_DETAILS = 'api/assignment/';
  static const ASSESSMENT_INSTRUCTIONS = 'api/assessment-instructions/';
  static const ATTEMPT_ASSESSMENT = '/api/assessment-detail/';
  static const SAVE_ANSWER = '/api/assessment-submit';
  static const SUBMMIT_ANSWER = '/api/assessment-onfinish';
  static const ASSESSMENT_REVIEW = '/api/assessment-review/';
  static const ASSIGNMENT_SUBMISSION_DETAILS = '/api/assignmentdetails';
  static const UPDATE_COURSE_COMPLETION = "/api/program-content-attempt";
  static const SEND_EMAIL_CODE = "/api/send_hscode";
  static const VERIFY_EMAIL_CODE = "/api/verify_hscode";
  static const JOY_CATEGORY = "/api/joy/category";
  static const JOY_CONTENT_LIST = "/api/joy/content";
  static const JOY_CONTENTS = "/api/joy/contents/";
  static const LIVECLASSLIST = "/api/onboard-sessions";
  static const COURSE_CATEGORY = "/api/category";
  static const INTEREST_PROGRAM_LIST = "/api/joy/category";
  static const MAP_INTEREST = "/api/courses/user-mapping";
  static const POPULARCOURSES = "/api/courses/popular";
  static const GCARVAAN_POST = "/api/carvan";
  static const GREELS_POST = "/api/reels";
  static const LIKE_CONTENT = "/api/user-view-tracking";
  static const REPORT_CONTENT = "/api/post-report";
  static const CREATE_PORTFOLIO = "/api/create-portfolio";
  static const USER_BRAND_CREATE = "/api/user-brand-create";
  static const MASTER_BRAND_CREATE = "/api/master-brand-create";
  static const PORTFOLIO = "/api/portfolio";
  static const COMMENT_LIST = "/api/comments";
  static const POST_COMMENT_LIST = "/api/user-comment-tracking";
  static const DELETE_POST = "/api/joy/content/delete/";
  static const COURSE_LIST = "/api/courses-list";
  static const FEATURED_VIDEOS = "/api/joy/content?is_featured=1";
  static const ANNOUNCEMENT_TYPE = "Announcement";
  static const TRAININGS = "Trainings";
  static const settings = "/api/settings";
  static const DASHBOARD_CONTENT = "/api/g-dashboard";
  static const NOTIFICATION_API = "api/joy/notifications";
  static const SWAYAM = 3;
  static const REPORT_PROGRAMS_LIST = "api/learner/programs-user";
  static const KPI_LIST = "api/learner/kpi";
  static const LEADERBOARD_LIST = "api/learner/course";
  static const REPORT_LEADERBOARD_LIST = "api/learner/reported-course";
  static const CERTIFICATES_LIST = "api/learner/kpi/certificates";
  static const MODULE_WISE_LEADERBOARD_LIST = "api/learner/module";
  static const REPORT_MODULE_WISE_LEADERBOARD_LIST =
      "api/learner/reported-module";
  static const FEEDBACK_API = "api/joy/feedback";
  static const TOPIC_API = "api/joy/feedback-topics";
  static const UPDATE_PROFILE_API = "api/joy/profile-update";
  static const STATE_API = "api/joy/state";
  static const CITY_API = "api/joy/city";
  static const TAGS_API = "api/joy/tags";
  static const LIBRARY_TYPE = "Library";
  static const SURVEY_API = "api/joy/survey";
  static const POLL_API = 'api/poll';
  static const SWAYAM_LOGIN = 'api/login';
  static const REMOVE_ACCOUNT = '/api/user/delete';
  static const USER_JOBS_LIST = '/api/user_jobs/list';
  static const COMPETITION_MODULE_DATA = '/api/competition-list';
  static const COMPETITION_CONTENT_LIST = '/api/learner-competition-detail/';
  static const LEADERBOARD = '/api/competition-leaderboard';
  static const USER_PORTFOLIO = '/api/user-portfolio';
  static const ADD_PORTFOLIO = '/api/addPortfolio';
  static const ADD_PROFESSIONAL = 'api/addProfessional';
  static const PORTFOLIO_DELETE = '/api/deletePortfolio';
  static const ADD_PORTFOLIO_PROFILE = '/api/add_portfolio_profile';
  static const ADD_RESUME = '/api/addResume';
  static const UPDATE_PROFILE = '/api/portfolio_image_upload';
  static const ADD_SOCIAL = '/api/addPortfolioSocial';
  static const GET_PORTFOLIO_COMPETITION = '/api/competition-list-portfolio';
  static const TOP_SCORING_USER = '/api/top-scoring-users';
  static const COMPETITION_MY_ACTIVITY = '/api/my-activities';
  static const DOMAIN_LIST = '/api/getDomain';
  static const JOB_DOMAIN_DETAIL = '/api/job-domain-detail/';
  static const PASSWORD_UPDATE = '/api/password-update/';
  static const CHANGE_PASSWORD = '/api/change-password';
  static const GET_OPEN_URL = '/api/add-participant';
  static const GENERATE_CERTIFICATE = "/api/generate-certificate";
  static const POPULAR_JOB_INTERNSHIP = "/api/job-list-wow?is_popular=1";
  static const COMPANY_LIST = "/api/companies-list";
  static const COMPANY_JOB_LIST = "api/job-list-wow?company_name=";
  static const INTERNSHIP_LIST = "/api/job-list-wow?is_internship=1";
  static const SKILL_SUGGESTION = "/api/skills-list";
  static const ADD_SKILL = "/api/add-skill-mapping";
  static const MY_SKILL = "/api/skills-mapping-list";
  static const DELETE_SKILL = "/api/delete-skill-mapping";
  static const OPEN_TO_WORK = "/api/add-open-work";
  static const ORG_PROGRAM_LIST = "/api/get-interestarea";
  static const UPLOAD_VIDEO_RESUME = "/api/upload-video-resume";
  static const LIST_VIDEO_RESUME = "api/video-resume-list";
  static const DELETE_VIDEO_RESUME = "/api/video-resume-delete";
  static const SET_PRIMART_VIDEO_RESUME = "/api/set-video-resume-primary";
  static const ANALYSE_VIDEO_RESUME = "api/video-resume-analyse";
  static const EXPLORE_JOBS = "/api/explore-jobs";
  static const EXPLORE_JOBS_REFRESH = "/api/refresh_now";
  static const JOB_DETAILS_COMPLETE = "/api/job-details-complete";
  static const EXPLORE_APPLY_JOB = "/api/apply_job_entry";
  static const NOTIFICATION_LIST = "/api/ContentNotificationApi";
  static const NOTIFICATION_READ = "/api/ContentNotificationReadApi";
  static const ASSESSMENT_REPORTS = '/api/soft-skill-diagnosties?';
  static const FACULTY_BATCH_DETAILS = 'api/faculty-batch-detail';
  static const FACULTY_BATCH_CLASS = '/api/faculty-batch-classes';
  static const FACULTY_BATCH_ASSIGNMENT = '/api/faculty-batch-assignments';
  static const FACULTY_BATCH_ASSESSMENT = '/api/faculty-batch-assessments';
  static const ATTENDANCE_PERCENTAGE = '/api/attendance-pecentage/';
  static const PROGRAM_COMPLETION = '/api/program-completion/';
  static const MARK_ATTENDANCE = '/api/mark-attendence/';
  static const UPDATE_ATTENDANCE = '/api/update-attendence/';
  static const ASSIGN_LEARNER = '/api/assign-learner';
  static const FACULTY_MODULE_LIST = '/api/faculty-modules-list';
  static const MODULE_LEADER_PROGRAM_LIST = '/api/module-leader-program-list';
  static const HOD_PROGRAM_LIST = '/api/hod-program-list';
  static const EVENT_PARTICIAPTION = '/api/participation';
  static const WOW_DASHBOARD = 'api/wow-details';
  static const Assessment_Details = 'api/assessment-detail/';
  static const Assessment_certificate = '/api/generate-certificate';
  static const MATCHING_JOB = '/api/matching-jobs';
  static const GENERATE_SIMILARITY = '/api/generate-similarity';
  static const LAUNCH_URL = '/api/launch-url';
  static const SEMESTER_LIST = '/api/learner/semester-list';
  static const DOWNLOAD_ASSESSMENT_REPORT = '/api/download-assessment-report';
  static const FEE_AGREEMENT = '/api/fee-agreement';
  static const ACCEPT_FEE_AGREEMENT = '/api/accept-fee-agreement';
}
