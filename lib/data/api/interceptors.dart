import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/utility.dart';
import 'environment.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;

InterceptorsWrapper requestInterceptor(Dio dio, Environment env) {
  dynamic startTime;
  return InterceptorsWrapper(onRequest: (RequestOptions options, handler) {
    final uri = options.uri.toString();
    Log.v("Api - URL: ${uri.toString()}");
    Log.v("Api - headers:\n\n");
    // Log.v("Api - headers: ${options.headers}");
    options.headers.entries.forEach((element) {
      Log.v('${element.key}: ${element.value}\n');
    });
    // if (options.data is FormData) {
    //   FormData fromData = options.data;
    //   Log.v("Api - Request Body: ${fromData.fields}");
    // } else
    //   Log.v("Api - Request Body: ${options.data}");

    String? region = '';
    try {
      region =
          Utility.getCurrentTimeInTimeZone('${Preference.getString('region')}');
    } catch (e) {
      Log.v("Exception: $e");
    }

    if (options.data is FormData) {
      FormData formData = options.data;
      formData.fields.add(MapEntry('created_timezone', '$region'));
      options.data = formData;
      Log.v("Api - Request Body: ${formData.fields} ");
    } else {
      // if (options.data == null) {
      //   options.data = MapEntry('created_timezone', '$region');
      // }
      // Log.v("Api - Request Body: ${options.data}");
    }

    log('LOG: API URL: made call');

    startTime = DateTime.now().millisecondsSinceEpoch;
    //show pop when no internet connected @Prince
    // Utility.checkNetwork();

    handler.next(options);
  }, onResponse: (Response response, handler) {
    Log.v("Api - Response headers");

    response.headers.forEach((k, v) {
      v.forEach((s) {
        Log.v("key su= $k , $s");
        try {
          if (k == 'date') {
            final gtmDateString = s;
            // print("GT Date String: $s");
            DateTime dateTime = DateTime.now();
            try{
              final format = DateFormat("EEE, dd MMM yyyy HH:mm:ss", 'en_IN');
               dateTime = format.parseUtc('${gtmDateString.toLocale()}');
            }catch(e){
              try{
                final format = DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", "en_IN");
                dateTime = format.parseUtc('${gtmDateString.toLocale()}');
              }catch(e){
                try{
                  final format = DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", "en_US");
                  dateTime = format.parseUtc('${gtmDateString.toLocale()}');
                }catch(e){}
              }
            }

            tz.initializeTimeZones();

            // Define the India time zone
            final indiaTimeZone = tz.getLocation('Asia/Kolkata');
            // print("your local nam e3 ${WidgetsBinding.instance.window.locale}");

            // Get the current time in India
            final indiaNow = tz.TZDateTime.fromMillisecondsSinceEpoch(
                indiaTimeZone, dateTime.millisecondsSinceEpoch);
            final formattedIndianTime =
                DateFormat('yyyy-MM-dd HH:mm:ss.SSS', 'en_IN').format(indiaNow);
            // print("your local nam e4 ${WidgetsBinding.instance.window.locale}");

            // currentIndiaTime = DateTime.parse(formattedIndianTime);

            Utility.strtotime(formattedIndianTime).then((value) {
              int timeDiff = (value * 1000) -
                  DateTime.parse('$formattedIndianTime').millisecondsSinceEpoch;
              currentIndiaTime = DateTime.fromMillisecondsSinceEpoch(
                  (value * 1000) - timeDiff);

              Preference.setInt(Preference.timestampDiffToIndia, timeDiff);

              // print('time diff1 $timeDiff and ${formattedIndianTime}');
            });
          }
        } catch (e, stacktrace) {
          print(
            "error in parsing date  $stacktrace",
          );
        }
      });
    });
    Log.v("Api - Response: ${response.data}");
    final endTime = DateTime.now().millisecondsSinceEpoch;

    final duration = endTime - startTime;
    log('LOG: Response time: $duration ms');

    log('LOG: API URL: ${response.requestOptions.sendTimeout} and Response time: $duration ms');

    return handler.next(response);
  });
}
