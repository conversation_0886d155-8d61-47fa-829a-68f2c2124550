#!/bin/bash

# Display the menu
echo "Menu:"
echo "0. Quit"
echo "1. <PERSON><PERSON><PERSON>"
echo "2. MEC"
echo "3. AID"
echo "4. MESC"
echo "5. JUMEIRA"


# Get user input
read -p "Enter your choice: " choice
current_directory=$(pwd)
source_file="$current_directory"

script_directory=$(cd "$(dirname "$0")" && pwd)
# Get the parent directory
parent_directory=$(dirname "$script_directory")

# Destination directory path
google_service_file_dest="$parent_directory/android/app/google-services.json"
config_file_dest="$parent_directory/lib/utils/config.dart"

# Process user choice
case $choice in
    0)
        echo "Quitting..."
        ;;
    1)
        echo "Singularis Selected"
        flutter pub run change_app_package_name:main com.singulariswow
        echo "Package updated"
        cp "$source_file/singularis/google-services.json" "$google_service_file_dest"
        cp "$source_file/singularis/config.dart" "$config_file_dest"
        dart icon_update.dart Singularis wow
        ;;
    2)
        echo "MEC Selected"
        flutter pub run change_app_package_name:main com.singulariswow.mec
        echo "Package updated"
        cp "$source_file/mec/google-services.json" "$google_service_file_dest"
        cp "$source_file/mec/config.dart" "$config_file_dest"
        dart icon_update.dart MECFuture mec
        # Add your commands for Option 3 here
        ;;
    3)
        echo "AID Selected"
        # Add your commands for Option 1 here
        flutter pub run change_app_package_name:main com.singulariswow.aid
        echo "Package updated"
        cp "$source_file/aid/google-services.json" "$google_service_file_dest"
        cp "$source_file/aid/config.dart" "$config_file_dest"
        dart icon_update.dart AID aid
        ;;

    4)
        echo "MESC Selected"
        # Add your commands for Option 1 here
        flutter pub run change_app_package_name:main com.singularis.mesc
        echo "Package updated"
        cp "$source_file/mesc/google-services.json" "$google_service_file_dest"
        cp "$source_file/mesc/config.dart" "$config_file_dest"
        dart icon_update.dart "MESC Digital" "mesc"
        ;;

    5)
        echo "JUMEIRA Selected"
        # Add your commands for Option 1 here
        flutter pub run change_app_package_name:main com.singularis.jumeira
        echo "Package updated"
        cp "$source_file/jumeira/google-services.json" "$google_service_file_dest"
        cp "$source_file/jumeira/config.dart" "$config_file_dest"
        dart icon_update.dart JUMEIRA jumeira
        ;;
    
    *)
        echo "Invalid"
        ;;
esac




