const APK_DETAILS = {
  "full_screen_preboarding": "1",
  "gradient_icon": "1",
  "app_name": "JUFuture",
  "appId": "1673720572",
  "package_name": "com.singularis.jumeira",
  "logo_url": "singularis_logo_appbar.png",
  "theme_image_url": "jumeira_login_logo.png",
  "theme_image_url2": "common_theme_2.png",
  "theme_color": "BD9D50",
  "theme_color_gradient": "263367", //BD9D50
  "theme_forground_color": "FFFFFF",
  "element_color": "FFFFFF",
  "gradient_right": "263367",
  "gradient_left": "263367",
  "nlms_api_key": "0612b32b39f4b29f48c5c5363028ee916bb99Jumeira",
  "domain_url": "https://lms.singulariswow.com/",
  "enable_boarding_screen": "1",
  "splash_image": "jumeira_splash.svg",
  "isBrandEnabled": "1",
  "faqEnabled": "0",
  "college_modules": "0",
  "login_by_pass": "1",
  "register_in_app": "1",
  "login_toggle": "1",
  "register_now": "1",
  "offline_video_download": "0",
  "set_goal": "0",
  "set_goal_dashboard": "0",
  "policy_url": "https://lms.singulariswow.com/policy",
  "about_url": "https://mescindia.org/introduction",
  "preboarding1": "assets/images/preboarding/jumeira_preboarding1.png",
  "preboarding2": "assets/images/preboarding/jumeira_preboarding2.png",
  "preboarding3": "assets/images/preboarding/jumeira_preboarding3.png",
  "preboarding_title1": "jumeira_preboarding_title1",
  "preboarding_title2": "jumeira_preboarding_title2",
  "preboarding_title3": "jumeira_preboarding_title3",
  "preboarding_desc1": "jumeira_preboarding_desc1",
  "preboarding_desc2": "jumeira_preboarding_desc2",
  "preboarding_desc3": "jumeira_preboarding_desc3",
  "mecat_url": "",
  "pmkvy_url": "",
  "sankalp_url": "p",
  "international_awards_url": "",
  "press_release_url": "",
  "mescindia_url": "",
  "wow-dashboard": "https://mecfuture.mec.edu.om/wow-dashboard/?user_id=",

};
