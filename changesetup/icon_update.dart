import 'dart:io';

void main(List<String> args) {
  Directory parentPath = Directory.current.parent;
  String androidXMLPath =
      '${parentPath.path}/android/app/src/main/AndroidManifest.xml';
  String contents = new File('$androidXMLPath').readAsStringSync();
  // print(contents);

  RegExp androidLabel = RegExp(
      'android:label\s*=\s*("([^"]*)"|\'([^\']*)\')'); // android:label="newname"
  RegExp androidIcon = RegExp(
      'android:icon\s*=\s*("([^"]*)"|\'([^\']*)\')'); // android:icon="@mipmap/ic_launcher_mec"
  RegExp androidroundIcon = RegExp(
      'android:roundIcon\s*=\s*("([^"]*)"|\'([^\']*)\')'); // android:roundIcon="@mipmap/ic_launcher_mec"
  // RegExp androidHost = RegExp(
  //     'android:host\s*=\s*("([^"]*)"|\'([^\']*)\')'); // android:roundIcon="@mipmap/ic_launcher_mec"

  Match? labelMatch = androidLabel.firstMatch(contents);
  Match? iconMatch = androidIcon.firstMatch(contents);
  Match? roundIconMatch = androidroundIcon.firstMatch(contents);
  // Match? androidHostMatch = androidHost.firstMatch(contents);

  String? matchStr = labelMatch?.group(0);
  if (matchStr != null) {
    String newName = matchStr.split("=").first + "=" + '"${args[0]}"';
    contents = contents.replaceAll(matchStr, newName);
  }

  matchStr = iconMatch?.group(0);
  if (matchStr != null) {
    String newName =
        matchStr.split("=").first + "=" + '"@mipmap/ic_launcher_${args[1]}"';
    contents = contents.replaceAll(matchStr, newName);
  }

  matchStr = roundIconMatch?.group(0);
  if (matchStr != null) {
    String newName =
        matchStr.split("=").first + "=" + '"@mipmap/ic_launcher_${args[1]}"';
    contents = contents.replaceAll(matchStr, newName);
  }

  File('$androidXMLPath').writeAsString(contents);
}
