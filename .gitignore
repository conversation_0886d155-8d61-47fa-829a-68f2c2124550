# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
nodemon.json
.fvm

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
.vscode/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
# /changesetup

# Web related

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# FVM Version Cache
.fvm/


# Platform
/macos/
/linux/
/.qodo/
CRUSH.md
/.crush/