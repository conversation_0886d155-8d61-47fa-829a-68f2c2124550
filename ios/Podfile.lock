PODS:
  - app_links (0.0.2):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - CwlCatchException (2.2.1):
    - CwlCatchExceptionSupport (~> 2.2.1)
  - CwlCatchExceptionSupport (2.2.1)
  - device_info_plus (0.0.1):
    - Flutter
  - devicelocale (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/AnalyticsWithoutAdIdSupport (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/DynamicLinks (11.15.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_analytics (11.6.0):
    - Firebase/AnalyticsWithoutAdIdSupport (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_core (3.15.2):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_dynamic_links (6.1.10):
    - Firebase/DynamicLinks (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.10):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics/WithoutAdIdSupport (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseDynamicLinks (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_isolate (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_video_trimmer (1.0.0):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - get_thumbnail_video (0.0.1):
    - Flutter
    - libwebp
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - install_referrer (1.0.0):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - location (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - OrderedSet (6.0.3)
  - otp_autofill (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - record_ios (1.0.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - simple_barcode_scanner (0.1.7):
    - Flutter
  - speech_to_text (7.2.0):
    - CwlCatchException
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - devicelocale (from `.symlinks/plugins/devicelocale/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_isolate (from `.symlinks/plugins/flutter_isolate/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_video_trimmer (from `.symlinks/plugins/flutter_native_video_trimmer/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - get_thumbnail_video (from `.symlinks/plugins/get_thumbnail_video/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - install_referrer (from `.symlinks/plugins/install_referrer/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - otp_autofill (from `.symlinks/plugins/otp_autofill/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - simple_barcode_scanner (from `.symlinks/plugins/simple_barcode_scanner/ios`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - CwlCatchException
    - CwlCatchExceptionSupport
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  devicelocale:
    :path: ".symlinks/plugins/devicelocale/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_isolate:
    :path: ".symlinks/plugins/flutter_isolate/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_video_trimmer:
    :path: ".symlinks/plugins/flutter_native_video_trimmer/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  get_thumbnail_video:
    :path: ".symlinks/plugins/get_thumbnail_video/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  install_referrer:
    :path: ".symlinks/plugins/install_referrer/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  otp_autofill:
    :path: ".symlinks/plugins/otp_autofill/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  simple_barcode_scanner:
    :path: ".symlinks/plugins/simple_barcode_scanner/ios"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  app_tracking_transparency: 3d84f147f67ca82d3c15355c36b1fa6b66ca7c92
  audio_waveforms: a6dde7fe7c0ea05f06ffbdb0f7c1b2b2ba6cedcf
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CwlCatchException: 7acc161b299a6de7f0a46a6ed741eae2c8b4d75a
  CwlCatchExceptionSupport: 54ccab8d8c78907b57f99717fb19d4cc3bce02dc
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  devicelocale: bd64aa714485a8afdaded0892c1e7d5b7f680cf8
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_analytics: faf4abd990c99309b1af7cf7ae22e4c55b19ab19
  firebase_core: 995454a784ff288be5689b796deb9e9fa3601818
  firebase_dynamic_links: 043464a753bb1b442fc038885f18f392064a32c4
  firebase_messaging: f4a41dd102ac18b840eba3f39d67e77922d3f707
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseDynamicLinks: 639fc743dc9c9f01709139bf74319536a674f012
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: 78da0da1084e709cbfd3b723c7ea349c71681f09
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_isolate: ca1a284bbb8dcfeb26c4764794e0206f58ffa984
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_video_trimmer: 11f6c679971dc3844519b485b9c878df8675ae3a
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  flutter_timezone: 7c838e17ffd4645d261e87037e5bebf6d38fe544
  geocoding_ios: 33776c9ebb98d037b5e025bb0e7537f6dd19646e
  get_thumbnail_video: 1a754d46b860dffefcc57b7290a43089cd5d7e58
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_cropper: c4326ea50132b1e1564499e5d32a84f01fb03537
  image_picker_ios: 85f2b3c9fb98c09d63725c4d12ebd585b56ec35d
  install_referrer: 65a23f2ad88fdda6d80cf275ec463355309bb34b
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  location: 155caecf9da4f280ab5fe4a55f94ceccfab838f8
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  otp_autofill: a50d5ea8173b9a6fd930a4f1252416793a4e1906
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 608fcb11be570ce83519b076ab6a1fffe2474f05
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  record_ios: fee1c924aa4879b882ebca2b4bce6011bcfc3d8b
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  simple_barcode_scanner: f1217b91fbad5848d37fa6d794f443416fd3e44d
  speech_to_text: 3b313d98516d3d0406cea424782ec25470c59d19
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: a1c1f2bf89969cb13cce46d0333c7257d346e169
  video_compress: f2133a07762889d67f0711ac831faa26f956980e
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  video_thumbnail: b637e0ad5f588ca9945f6e2c927f73a69a661140
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 369b6b06ac4ab15280b97ae3fe99bb9f9a1bf7de

COCOAPODS: 1.16.2
