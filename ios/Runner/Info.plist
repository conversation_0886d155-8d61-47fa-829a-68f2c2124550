<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>MinimumOSVersion</key>
    <string>15.6</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>MECFuture</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>MECFuture</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FDAllFilesDownloadedMessage</key>
	<string>All files have been downloaded</string>
	<key>FDMaximumConcurrentTasks</key>
	<integer>5</integer>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>FlutterDeepLinkingEnabled</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>LSApplicationQueriesSchemes</key>
    <array>
      <string>sms</string>
      <string>tel</string>
    </array>
	<key>NSAppleMusicUsageDescription</key>
	<string>Required</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Required</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Required</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Required</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
    <array>
        <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    </array>
	<key>NSCameraUsageDescription</key>
	<string>Enable your camera to take a profile photo/video and participate in live classes.</string>
	<key>NSContactsUsageDescription</key>
	<string>Required</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location allows us to find college/university near by you and help us serve you more personalised experience.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow microphone permission for live session</string>
	<key>NSMotionUsageDescription</key>
	<string>Required</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>PhotoLibrary Usage for download document</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Required</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>We use tracking for personalised course recommendation. Your data is handled securely and used only for course recommendation purpose.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
