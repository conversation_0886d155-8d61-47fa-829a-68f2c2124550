client:
  name: basic
  version: 0
  file-system: default

targets:
  "": ["<all>"]

nodes:
  "/Users/<USER>/flutterProjects/swayam_exp/build/ios": {"is-mutated":true}
  "/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator": {"is-mutated":true}
  "/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"is-mutated":true}
  "/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner": {"is-mutated":true}
  "<TRIGGER: Ld /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>": {"is-command-timestamp":true}
  "<TRIGGER: MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>": {"is-command-timestamp":true}

commands:
  "::CreateBuildDirectory /Users/<USER>/flutterProjects/swayam_exp/build/ios": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/flutterProjects/swayam_exp/build/ios","inputs":[],"outputs":["<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","/Users/<USER>/flutterProjects/swayam_exp/build/ios"]}
  "::CreateBuildDirectory /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios"],"outputs":["<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator"]}
  "<all>": {"tool":"phony","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--end>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"],"outputs":["<all>"]}
  "<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>": {"tool":"stale-file-removal","expectedOutputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"roots":["/tmp/Runner.dst","/Users/<USER>/flutterProjects/swayam_exp/build/ios","/Users/<USER>/flutterProjects/swayam_exp/build/ios"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CodeSign /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<RegisterExecutionPolicyException /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Touch /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CopySwiftStdlib /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--end": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftABIBaselineGenerationTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestHostTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<CodeSign /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","<CopySwiftStdlib /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<Linked Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>","<execute-shell-script-f63005216924d54aadf0824537d3d3ca9eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-f63005216924d54aadf0824537d3d3caf1eee2015e8ff5ebcd27678f788c2826-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","<RegisterExecutionPolicyException /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","<Touch /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--end>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--generated-headers": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-Debug-iphonesimulator-x86_64-build-headers-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios>","<CreateBuildDirectory-/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--modules-ready": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<execute-shell-script-f63005216924d54aadf0824537d3d3ca9eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-f63005216924d54aadf0824537d3d3caf1eee2015e8ff5ebcd27678f788c2826-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-f63005216924d54aadf0824537d3d3ca9eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<Linked Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Assets.car","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-f63005216924d54aadf0824537d3d3caf1eee2015e8ff5ebcd27678f788c2826-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<CopySwiftStdlib /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h","<Linked Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner>","<execute-shell-script-f63005216924d54aadf0824537d3d3ca9eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","<execute-shell-script-f63005216924d54aadf0824537d3d3caf1eee2015e8ff5ebcd27678f788c2826-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"]}
  "Gate target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign": {"tool":"phony","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--unsigned-product-ready>"],"outputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CodeSign /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"shell","description":"CodeSign /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/flutterProjects/swayam_exp/GoogleService-Info.plist/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent/","/Users/<USER>/flutterProjects/swayam_exp/ios/Flutter/AppFrameworkInfo.plist/","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/AppDelegate.swift/","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Assets.xcassets/","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/LaunchScreen.storyboard/","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/Main.storyboard/","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/GeneratedPluginRegistrant.m/","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>","<TRIGGER: Ld /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>","<TRIGGER: MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/_CodeSignature","<CodeSign /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"args":["/usr/bin/codesign","--force","--sign","-","--entitlements","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","--timestamp=none","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app"],"env":{"CODESIGN_ALLOCATE":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/codesign_allocate"},"can-safely-interrupt":false,"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"3646ba8eacd885c4a604fa9e79e2561b"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileAssetCatalog /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Assets.xcassets": {"tool":"shell","description":"CompileAssetCatalog /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Assets.xcassets","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Assets.xcassets/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Assets.car"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/actool","--output-format","human-readable-text","--notices","--warnings","--export-dependency-info","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies","--output-partial-info-plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","--app-icon","AppIcon","--compress-pngs","--enable-on-demand-resources","YES","--filter-for-device-model","iPod9,1","--filter-for-device-os-version","14.5","--development-region","en","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","11.0","--platform","iphonesimulator","--compile","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Assets.xcassets"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"deps":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_dependencies"],"deps-style":"dependency-info","signature":"623627ae7ff5ab5ac02d8a292c8788f4"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/GeneratedPluginRegistrant.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","x86_64-apple-ios11.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DPB_FIELD_32BIT=1","-DPB_NO_PACKED_STRUCTS=1","-DPB_ENABLE_MALLOC=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","-fasm-blocks","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-fobjc-abi-version=2","-fobjc-legacy-dispatch","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hdxgloccxsrdvufbivzwjqolvrbu/Index/DataStore","-iquote","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources","-I/Sources/FBLPromises/include","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement","-MMD","-MT","dependencies","-MF","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","deps":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"730681b381d700bc0869d2d72ad07785"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","x86_64-apple-ios11.0-simulator","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DPB_FIELD_32BIT=1","-DPB_NO_PACKED_STRUCTS=1","-DPB_ENABLE_MALLOC=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","-fasm-blocks","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hdxgloccxsrdvufbivzwjqolvrbu/Index/DataStore","-iquote","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources","-I/Sources/FBLPromises/include","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement","-MMD","-MT","dependencies","-MF","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.dia","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","deps":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.d"],"deps-style":"makefile","signature":"6b852a5b502faae89f59bbf99ac5c287"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/LaunchScreen.storyboard","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/LaunchScreen.storyboard","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","11.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/LaunchScreen.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"7790f27149e3ccd285e3631efee9bded"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/Main.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/Main.storyboard","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/Main.storyboard","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","11.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Base.lproj/Main.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"79ac89d86ae80f8db5c93e1effb5eaed"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal x86_64 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/AppDelegate.swift","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","-D","COCOAPODS","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","-target","x86_64-apple-ios11.0-simulator","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hdxgloccxsrdvufbivzwjqolvrbu/Index/DataStore","-swift-version","5","-I","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter","-F","/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks","-F","/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics","-F","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement","-parse-as-library","-c","-j8","-output-file-map","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources","-Xcc","-I/Sources/FBLPromises/include","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources-normal/x86_64","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/x86_64","-Xcc","-I/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-Xcc","-DCOCOAPODS=1","-Xcc","-DDEBUG=1","-Xcc","-DPB_FIELD_32BIT=1","-Xcc","-DPB_NO_PACKED_STRUCTS=1","-Xcc","-DPB_ENABLE_MALLOC=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","-import-objc-header","/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/flutterProjects/swayam_exp/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/flutterProjects/swayam_exp/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk"},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","deps":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.d"],"deps-style":"makefile","signature":"4c27b3aaf21c262f25ce093c5f08212d"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/flutterProjects/swayam_exp/ios/Flutter/AppFrameworkInfo.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist /Users/<USER>/flutterProjects/swayam_exp/ios/Flutter/AppFrameworkInfo.plist","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Flutter/AppFrameworkInfo.plist","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/AppFrameworkInfo.plist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/flutterProjects/swayam_exp/GoogleService-Info.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist /Users/<USER>/flutterProjects/swayam_exp/GoogleService-Info.plist","inputs":["/Users/<USER>/flutterProjects/swayam_exp/GoogleService-Info.plist","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/GoogleService-Info.plist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopySwiftLibs /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"embed-swift-stdlib","description":"CopySwiftLibs /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--package-copy-files-phase>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["<CopySwiftStdlib /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"deps":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/SwiftStdLibToolInputDependencies.dep"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"1e1f970c1c7f71972df30bf162e38f73"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftsourceinfo","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/Project/x86_64.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"285622f3da8857d405f2494d56ffa39a"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"94e02d265d575bcffba1c5c9ced322d0"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"ea83dadff28e60e2f769b397a9d1a288"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftdoc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"1d17f3b809e3e27e05e4162f7480fed2"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.swiftmodule/x86_64.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"5a2e7173445f18d5979eb39b7250f759"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h": {"tool":"shell","description":"Ditto /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-Swift.h","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner-Swift.h"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"dff630589b618179ab75c53cea5ab782"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner normal": {"tool":"shell","description":"Ld /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner normal","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_vers.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/GeneratedPluginRegistrant.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/AppDelegate.o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner","<Linked Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner>","<TRIGGER: Ld /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner normal>"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","x86_64-apple-ios11.0-simulator","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","-L/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics","-F/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement","-filelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-Xlinker","-rpath","-Xlinker","@loader_path/Frameworks","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-Xlinker","-objc_abi_version","-Xlinker","2","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.swiftmodule","-ObjC","-lc++","-lsqlite3","-lz","-framework","AVFoundation","-framework","AVKit","-framework","CoreTelephony","-framework","DKImagePickerController","-framework","DKPhotoGallery","-framework","FBLPromises","-framework","FirebaseAnalytics","-framework","FirebaseCore","-framework","FirebaseCoreDiagnostics","-framework","FirebaseCrashlytics","-framework","FirebaseInstallations","-framework","FirebaseMessaging","-framework","Foundation","-framework","GoogleAppMeasurement","-framework","GoogleDataTransport","-framework","GoogleUtilities","-framework","ImageIO","-framework","OrderedSet","-framework","Photos","-framework","SDWebImage","-framework","Security","-framework","StoreKit","-framework","SwiftyGif","-framework","SystemConfiguration","-framework","UIKit","-framework","device_info","-framework","downloads_path_provider","-framework","esys_flutter_share","-framework","file_picker","-framework","firebase_analytics","-framework","firebase_core","-framework","firebase_crashlytics","-framework","firebase_messaging","-framework","flutter_downloader","-framework","flutter_inappwebview","-framework","flutter_local_notifications","-framework","image_picker","-framework","nanopb","-framework","open_file","-framework","package_info","-framework","path_provider","-framework","permission_handler","-framework","shared_preferences","-framework","url_launcher","-framework","video_player","-framework","wakelock","-framework","webview_flutter","-weak_framework","UserNotifications","-Xlinker","-sectcreate","-Xlinker","__TEXT","-Xlinker","__entitlements","-Xlinker","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","-lsqlite3","-framework","Pods_Runner","-Xlinker","-no_adhoc_codesign","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat","-o","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Runner"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","deps":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"464ef8490dc5c1d1273f283f582d67c7"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:LinkStoryboards": {"tool":"shell","description":"LinkStoryboards","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Base.lproj/Main.storyboardc"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--target-device","iphone","--target-device","ipad","--minimum-deployment-target","11.0","--output-format","human-readable-text","--link","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main.storyboardc"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"59a8f3021143565086e458db79d4df07"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"mkdir","description":"MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>","<TRIGGER: MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks": {"tool":"mkdir","description":"MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks","<MkDir /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks>"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Run Script /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"shell","description":"PhaseScriptExecution Run Script /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-f63005216924d54aadf0824537d3d3ca9eb60ff613d36b3b9942d1a6e1e7c6aa-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"akaankshkanchanapalli","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.5","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"PV9HH96Q3U.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CCHROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"-","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.5.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.5","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"12","CURRENT_VARIANT":"normal","DART_DEFINES":"RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.2 10.0 10.2 11.0 11.2 11.4 12.1 12.3 13.0 13.2 13.4 13.6 14.1 14.3 14.5","DERIVED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"PV9HH96Q3U","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/flutterProjects/swayam_exp","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.1","FLUTTER_BUILD_NUMBER":"3","FLUTTER_ROOT":"/Users/<USER>/development/flutter","FLUTTER_TARGET":"/Users/<USER>/flutterProjects/swayam_exp/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement\"","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase\"  /Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"akaankshkanchanapalli","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"11.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios11.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","LOCSYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20F71","MAC_OS_X_VERSION_ACTUAL":"110400","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110400","MARKETING_VERSION":"1.0.12","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"arm64","NATIVE_ARCH_32_BIT":"arm","NATIVE_ARCH_64_BIT":"arm64","NATIVE_ARCH_ACTUAL":"arm64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"AVKit\" -framework \"CoreTelephony\" -framework \"DKImagePickerController\" -framework \"DKPhotoGallery\" -framework \"FBLPromises\" -framework \"FirebaseAnalytics\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"Foundation\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleUtilities\" -framework \"ImageIO\" -framework \"OrderedSet\" -framework \"Photos\" -framework \"SDWebImage\" -framework \"Security\" -framework \"StoreKit\" -framework \"SwiftyGif\" -framework \"SystemConfiguration\" -framework \"UIKit\" -framework \"device_info\" -framework \"downloads_path_provider\" -framework \"esys_flutter_share\" -framework \"file_picker\" -framework \"firebase_analytics\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"flutter_downloader\" -framework \"flutter_inappwebview\" -framework \"flutter_local_notifications\" -framework \"image_picker\" -framework \"nanopb\" -framework \"open_file\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"shared_preferences\" -framework \"url_launcher\" -framework \"video_player\" -framework \"wakelock\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/flutterProjects/swayam_exp/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18E182","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","PODS_PODFILE_DIR_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/.","PODS_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.at.perfettiswayamm","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/ios","PROJECT_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator14_5":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_NAME":"iphonesimulator14.5","SDK_NAMES":"iphonesimulator14.5","SDK_PRODUCT_BUILD_VERSION":"18E182","SDK_VERSION":"14.5","SDK_VERSION_ACTUAL":"140500","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"140500","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","SRCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"6FD05FBF-40B5-48AD-B342-2519F674638D","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.5","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"PV9HH96Q3U.","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"akaankshkanchanapalli","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"akaankshkanchanapalli","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-12\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12E507","XCODE_VERSION_ACTUAL":"1251","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1250","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"1982b38b47a20a7733264bf33085f58f"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Thin Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"shell","description":"PhaseScriptExecution Thin Binary /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-f63005216924d54aadf0824537d3d3caf1eee2015e8ff5ebcd27678f788c2826-target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49->"],"args":["/bin/sh","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"akaankshkanchanapalli","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.5","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"PV9HH96Q3U.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CCHROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"-","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.5.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.5","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"12","CURRENT_VARIANT":"normal","DART_DEFINES":"RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.2 10.0 10.2 11.0 11.2 11.4 12.1 12.3 13.0 13.2 13.4 13.6 14.1 14.3 14.5","DERIVED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"PV9HH96Q3U","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/flutterProjects/swayam_exp","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.1","FLUTTER_BUILD_NUMBER":"3","FLUTTER_ROOT":"/Users/<USER>/development/flutter","FLUTTER_TARGET":"/Users/<USER>/flutterProjects/swayam_exp/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement\"","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase\"  /Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"akaankshkanchanapalli","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"11.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios11.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","LOCSYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20F71","MAC_OS_X_VERSION_ACTUAL":"110400","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110400","MARKETING_VERSION":"1.0.12","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"arm64","NATIVE_ARCH_32_BIT":"arm","NATIVE_ARCH_64_BIT":"arm64","NATIVE_ARCH_ACTUAL":"arm64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"AVKit\" -framework \"CoreTelephony\" -framework \"DKImagePickerController\" -framework \"DKPhotoGallery\" -framework \"FBLPromises\" -framework \"FirebaseAnalytics\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"Foundation\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleUtilities\" -framework \"ImageIO\" -framework \"OrderedSet\" -framework \"Photos\" -framework \"SDWebImage\" -framework \"Security\" -framework \"StoreKit\" -framework \"SwiftyGif\" -framework \"SystemConfiguration\" -framework \"UIKit\" -framework \"device_info\" -framework \"downloads_path_provider\" -framework \"esys_flutter_share\" -framework \"file_picker\" -framework \"firebase_analytics\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"flutter_downloader\" -framework \"flutter_inappwebview\" -framework \"flutter_local_notifications\" -framework \"image_picker\" -framework \"nanopb\" -framework \"open_file\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"shared_preferences\" -framework \"url_launcher\" -framework \"video_player\" -framework \"wakelock\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/flutterProjects/swayam_exp/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18E182","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","PODS_PODFILE_DIR_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/.","PODS_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.at.perfettiswayamm","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/ios","PROJECT_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator14_5":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_NAME":"iphonesimulator14.5","SDK_NAMES":"iphonesimulator14.5","SDK_PRODUCT_BUILD_VERSION":"18E182","SDK_VERSION":"14.5","SDK_VERSION_ACTUAL":"140500","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"140500","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","SRCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"6FD05FBF-40B5-48AD-B342-2519F674638D","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.5","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"PV9HH96Q3U.","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"akaankshkanchanapalli","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"akaankshkanchanapalli","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-12\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12E507","XCODE_VERSION_ACTUAL":"1251","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1250","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"5d4f447c386091a8805edab13d9a5ad2"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Podfile.lock/","/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Manifest.lock/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt"],"args":["/bin/sh","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"akaankshkanchanapalli","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.5","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"PV9HH96Q3U.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CCHROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"-","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.5.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.5","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"12","CURRENT_VARIANT":"normal","DART_DEFINES":"RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.2 10.0 10.2 11.0 11.2 11.4 12.1 12.3 13.0 13.2 13.4 13.6 14.1 14.3 14.5","DERIVED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"PV9HH96Q3U","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/flutterProjects/swayam_exp","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.1","FLUTTER_BUILD_NUMBER":"3","FLUTTER_ROOT":"/Users/<USER>/development/flutter","FLUTTER_TARGET":"/Users/<USER>/flutterProjects/swayam_exp/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement\"","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase\"  /Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"akaankshkanchanapalli","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"11.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios11.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","LOCSYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20F71","MAC_OS_X_VERSION_ACTUAL":"110400","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110400","MARKETING_VERSION":"1.0.12","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"arm64","NATIVE_ARCH_32_BIT":"arm","NATIVE_ARCH_64_BIT":"arm64","NATIVE_ARCH_ACTUAL":"arm64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"AVKit\" -framework \"CoreTelephony\" -framework \"DKImagePickerController\" -framework \"DKPhotoGallery\" -framework \"FBLPromises\" -framework \"FirebaseAnalytics\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"Foundation\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleUtilities\" -framework \"ImageIO\" -framework \"OrderedSet\" -framework \"Photos\" -framework \"SDWebImage\" -framework \"Security\" -framework \"StoreKit\" -framework \"SwiftyGif\" -framework \"SystemConfiguration\" -framework \"UIKit\" -framework \"device_info\" -framework \"downloads_path_provider\" -framework \"esys_flutter_share\" -framework \"file_picker\" -framework \"firebase_analytics\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"flutter_downloader\" -framework \"flutter_inappwebview\" -framework \"flutter_local_notifications\" -framework \"image_picker\" -framework \"nanopb\" -framework \"open_file\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"shared_preferences\" -framework \"url_launcher\" -framework \"video_player\" -framework \"wakelock\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/flutterProjects/swayam_exp/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18E182","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","PODS_PODFILE_DIR_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/.","PODS_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.at.perfettiswayamm","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/ios","PROJECT_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Users/<USER>/flutterProjects/swayam_exp/ios/Podfile.lock","SCRIPT_INPUT_FILE_1":"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Manifest.lock","SCRIPT_INPUT_FILE_COUNT":"2","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","SCRIPT_OUTPUT_FILE_COUNT":"1","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator14_5":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_NAME":"iphonesimulator14.5","SDK_NAMES":"iphonesimulator14.5","SDK_PRODUCT_BUILD_VERSION":"18E182","SDK_VERSION":"14.5","SDK_VERSION_ACTUAL":"140500","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"140500","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","SRCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"6FD05FBF-40B5-48AD-B342-2519F674638D","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.5","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"PV9HH96Q3U.","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"akaankshkanchanapalli","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"akaankshkanchanapalli","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-12\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12E507","XCODE_VERSION_ACTUAL":"1251","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1250","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"f9864665b4d4b310234c41516deef0ee"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks-Debug-input-files.xcfilelist/","/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks-Debug-output-files.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKImagePickerController.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/DKPhotoGallery.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCore.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCoreDiagnostics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseCrashlytics.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseInstallations.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FirebaseMessaging.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleDataTransport.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/GoogleUtilities.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/OrderedSet.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/FBLPromises.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/SwiftyGif.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/device_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/downloads_path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/esys_flutter_share.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/file_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_downloader.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_inappwebview.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/flutter_local_notifications.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/image_picker.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/nanopb.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/open_file.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/package_info.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/video_player.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/wakelock.framework","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Frameworks/webview_flutter.framework"],"args":["/bin/sh","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"YES","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"akaankshkanchanapalli","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"x86_64","ARCHS_STANDARD":"arm64 x86_64","ARCHS_STANDARD_32_64_BIT":"arm64 i386 x86_64","ARCHS_STANDARD_32_BIT":"i386","ARCHS_STANDARD_64_BIT":"arm64 x86_64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 x86_64","ARCHS_UNIVERSAL_IPHONE_OS":"arm64 i386 x86_64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","ASSETCATALOG_FILTER_FOR_DEVICE_MODEL":"iPod9,1","ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION":"14.5","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","AppIdentifierPrefix":"PV9HH96Q3U.","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"YES","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CCHROOT":"/var/folders/cb/sg90yv3s54gcwd91khdm928r0000gn/C/com.apple.DeveloperTools/12.5.1-12E507/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER":"NO","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","COCOAPODS_PARALLEL_CODE_SIGN":"true","CODESIGNING_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneSimulatorCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"-","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","CONFIGURATION_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_DEVICE_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","CORRESPONDING_DEVICE_PLATFORM_NAME":"iphoneos","CORRESPONDING_DEVICE_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.5.sdk","CORRESPONDING_DEVICE_SDK_NAME":"iphoneos14.5","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"12","CURRENT_VARIANT":"normal","DART_DEFINES":"RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"mios-simulator-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-mios-simulator-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_simulator_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.2 10.0 10.2 11.0 11.2 11.4 12.1 12.3 13.0 13.2 13.4 13.6 14.1 14.3 14.5","DERIVED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"PV9HH96Q3U","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","EFFECTIVE_PLATFORM_NAME":"-iphonesimulator","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_DESTINATION":"__entitlements","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_ARCHS":"arm64 i386","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","EXPANDED_CODE_SIGN_IDENTITY":"-","EXPANDED_CODE_SIGN_IDENTITY_NAME":"-","FILE_LIST":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/flutterProjects/swayam_exp","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.1","FLUTTER_BUILD_NUMBER":"3","FLUTTER_ROOT":"/Users/<USER>/development/flutter","FLUTTER_TARGET":"/Users/<USER>/flutterProjects/swayam_exp/lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/FirebaseAnalytics/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/GoogleAppMeasurement/Frameworks\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/FirebaseAnalytics\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates/GoogleAppMeasurement\"","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OBJC_LEGACY_DISPATCH":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/GeneratedModuleMaps-iphonesimulator","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/include  \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKImagePickerController/DKImagePickerController.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DKPhotoGallery/DKPhotoGallery.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCore/FirebaseCore.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseInstallations/FirebaseInstallations.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/FirebaseMessaging/FirebaseMessaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleDataTransport/GoogleDataTransport.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/GoogleUtilities/GoogleUtilities.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/OrderedSet/OrderedSet.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/PromisesObjC/FBLPromises.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/SwiftyGif/SwiftyGif.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/device_info/device_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/downloads_path_provider/downloads_path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/esys_flutter_share/esys_flutter_share.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/file_picker/file_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_analytics/firebase_analytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_core/firebase_core.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_crashlytics/firebase_crashlytics.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/firebase_messaging/firebase_messaging.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_downloader/flutter_downloader.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_inappwebview/flutter_inappwebview.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/flutter_local_notifications/flutter_local_notifications.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/image_picker/image_picker.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/nanopb/nanopb.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/open_file/open_file.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/package_info/package_info.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/video_player/video_player.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/wakelock/wakelock.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/webview_flutter/webview_flutter.framework/Headers\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public\" \"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Headers/Public/Firebase\"  /Users/<USER>/flutterProjects/swayam_exp/ios/Pods/Firebase/CoreOnly/Sources \"/Sources/FBLPromises/include\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"akaankshkanchanapalli","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"11.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_ENTITLEMENTS_SECTION":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios11.0","LLVM_TARGET_TRIPLE_SUFFIX":"-simulator","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZATION_EXPORT_SUPPORTED":"YES","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","LOCSYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"20F71","MAC_OS_X_VERSION_ACTUAL":"110400","MAC_OS_X_VERSION_MAJOR":"110000","MAC_OS_X_VERSION_MINOR":"110400","MARKETING_VERSION":"1.0.12","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"arm64","NATIVE_ARCH_32_BIT":"arm","NATIVE_ARCH_64_BIT":"arm64","NATIVE_ARCH_ACTUAL":"arm64","NO_COMMON":"YES","OBJC_ABI_VERSION":"2","OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","ONLY_ACTIVE_ARCH":"YES","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"c++\" -l\"sqlite3\" -l\"z\" -framework \"AVFoundation\" -framework \"AVKit\" -framework \"CoreTelephony\" -framework \"DKImagePickerController\" -framework \"DKPhotoGallery\" -framework \"FBLPromises\" -framework \"FirebaseAnalytics\" -framework \"FirebaseCore\" -framework \"FirebaseCoreDiagnostics\" -framework \"FirebaseCrashlytics\" -framework \"FirebaseInstallations\" -framework \"FirebaseMessaging\" -framework \"Foundation\" -framework \"GoogleAppMeasurement\" -framework \"GoogleDataTransport\" -framework \"GoogleUtilities\" -framework \"ImageIO\" -framework \"OrderedSet\" -framework \"Photos\" -framework \"SDWebImage\" -framework \"Security\" -framework \"StoreKit\" -framework \"SwiftyGif\" -framework \"SystemConfiguration\" -framework \"UIKit\" -framework \"device_info\" -framework \"downloads_path_provider\" -framework \"esys_flutter_share\" -framework \"file_picker\" -framework \"firebase_analytics\" -framework \"firebase_core\" -framework \"firebase_crashlytics\" -framework \"firebase_messaging\" -framework \"flutter_downloader\" -framework \"flutter_inappwebview\" -framework \"flutter_local_notifications\" -framework \"image_picker\" -framework \"nanopb\" -framework \"open_file\" -framework \"package_info\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"shared_preferences\" -framework \"url_launcher\" -framework \"video_player\" -framework \"wakelock\" -framework \"webview_flutter\" -weak_framework \"UserNotifications\"","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":"/Users/<USER>/flutterProjects/swayam_exp/.dart_tool/package_config.json","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","PLATFORM_DISPLAY_NAME":"iOS Simulator","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphonesimulator","PLATFORM_PREFERRED_ARCH":"x86_64","PLATFORM_PRODUCT_BUILD_VERSION":"18E182","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","PODS_PODFILE_DIR_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/.","PODS_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios/Pods","PODS_XCFRAMEWORKS_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/XCFrameworkIntermediates","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.at.perfettiswayamm","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/flutterProjects/swayam_exp/ios","PROJECT_FILE_PATH":"/Users/<USER>/flutterProjects/swayam_exp/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_0":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","SCRIPT_INPUT_FILE_LIST_COUNT":"1","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_0":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","SCRIPT_OUTPUT_FILE_LIST_COUNT":"1","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_DIR_iphonesimulator14_5":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk","SDK_NAME":"iphonesimulator14.5","SDK_NAMES":"iphonesimulator14.5","SDK_PRODUCT_BUILD_VERSION":"18E182","SDK_VERSION":"14.5","SDK_VERSION_ACTUAL":"140500","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"140500","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","SRCROOT":"/Users/<USER>/flutterProjects/swayam_exp/ios","STRINGS_FILE_INFOPLIST_RENAME":"YES","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"NO","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_x86_64":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","SWIFT_VERSION":"5.0","SYMROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1,2","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator","TARGET_DEVICE_IDENTIFIER":"6FD05FBF-40B5-48AD-B342-2519F674638D","TARGET_DEVICE_MODEL":"iPod9,1","TARGET_DEVICE_OS_VERSION":"14.5","TARGET_DEVICE_PLATFORM_NAME":"iphonesimulator","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build","TEMP_ROOT":"/Users/<USER>/flutterProjects/swayam_exp/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.5.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"true","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"false","TeamIdentifierPrefix":"PV9HH96Q3U.","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"akaankshkanchanapalli","USER_APPS_DIR":"/Users/<USER>/Applications","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e i386 x86_64","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"akaankshkanchanapalli","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-12\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12E507","XCODE_VERSION_ACTUAL":"1251","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1250","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","control-enabled":false,"signature":"a49a3e7902c40015610c2e305069737b"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessInfoPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist": {"tool":"info-plist-processor","description":"ProcessInfoPlistFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist /Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","inputs":["/Users/<USER>/flutterProjects/swayam_exp/ios/Runner/Info.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/assetcatalog_generated_info.plist","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app/Info.plist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessProductPackaging  /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent": {"tool":"process-product-entitlements","description":"ProcessProductPackaging  /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app-Simulated.xcent"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessProductPackaging  /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent": {"tool":"process-product-entitlements","description":"ProcessProductPackaging  /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.app.xcent"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:RegisterExecutionPolicyException /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"register-execution-policy-exception","description":"RegisterExecutionPolicyException /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<RegisterExecutionPolicyException /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:Touch /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app": {"tool":"shell","description":"Touch /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","inputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--will-sign>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<Touch /Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app>"],"args":["/usr/bin/touch","-c","/Users/<USER>/flutterProjects/swayam_exp/build/ios/Debug-iphonesimulator/Runner.app"],"env":{},"working-directory":"/Users/<USER>/flutterProjects/swayam_exp/ios","signature":"ab68dc381dc20e4be0d94c9f51f8dfb1"}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements-Simulated.plist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Entitlements.plist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/DerivedSources/Runner_vers.c"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/InputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-input-files-cc5f4450a9aebb36925bd2f84cfd1b94-resolved.xcfilelist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner-OutputFileMap.json"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.LinkFileList"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Objects-normal/x86_64/Runner.SwiftFileList"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/OutputFileList-E2A1F8D7626E34135BD10168-Pods-Runner-frameworks-Debug-output-files-b3a2fc2aeda8217054a31d4b70665f89-resolved.xcfilelist"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-non-framework-target-headers.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-all-target-headers.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-generated-files.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-own-target-headers.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner-project-headers.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Runner.hmap"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-D2704DD35A40AE3FAF8C3964.sh"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/Script-E2A1F8D7626E34135BD10168.sh"]}
  "target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml","inputs":["<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-f63005216924d54aadf0824537d3d3ca88a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/flutterProjects/swayam_exp/build/ios/Runner.build/Debug-iphonesimulator/Runner.build/all-product-headers.yaml"]}

